'use client';

import { useState, useEffect } from 'react';

export default function DebugCookiesPage() {
  const [loginResult, setLoginResult] = useState<any>(null);
  const [profileResult, setProfileResult] = useState<any>(null);
  const [cookies, setCookies] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Get all cookies from document
  useEffect(() => {
    setCookies(document.cookie);
  }, []);

  const handleLogin = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Important for cookies
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
        }),
      });

      const data = await response.json();
      setLoginResult({ status: response.status, data });
      
      // Update cookies after login
      setCookies(document.cookie);
    } catch (error) {
      setLoginResult({ error: error.message });
    }
    setLoading(false);
  };

  const handleGetProfile = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/v1/auth/me', {
        method: 'GET',
        credentials: 'include', // Important for cookies
      });

      const data = await response.json();
      setProfileResult({ status: response.status, data });
    } catch (error) {
      setProfileResult({ error: error.message });
    }
    setLoading(false);
  };

  const clearCookies = () => {
    // Clear all cookies for localhost
    document.cookie.split(";").forEach((c) => {
      const eqPos = c.indexOf("=");
      const name = eqPos > -1 ? c.substr(0, eqPos) : c;
      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
    });
    setCookies('');
  };

  const refreshPage = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🍪 Cookie Debug Page</h1>
        
        {/* Current Cookies */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Current Cookies</h2>
          <div className="bg-gray-100 p-4 rounded font-mono text-sm">
            {cookies || 'No cookies found'}
          </div>
          <button
            onClick={() => setCookies(document.cookie)}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh Cookies
          </button>
        </div>

        {/* Actions */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">Actions</h2>
          <div className="space-x-4">
            <button
              onClick={handleLogin}
              disabled={loading}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Login (<EMAIL>)'}
            </button>
            
            <button
              onClick={handleGetProfile}
              disabled={loading}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Get Profile'}
            </button>
            
            <button
              onClick={clearCookies}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Clear All Cookies
            </button>
            
            <button
              onClick={refreshPage}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Refresh Page
            </button>
          </div>
        </div>

        {/* Login Result */}
        {loginResult && (
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <h2 className="text-xl font-semibold mb-4">Login Result</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(loginResult, null, 2)}
            </pre>
          </div>
        )}

        {/* Profile Result */}
        {profileResult && (
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <h2 className="text-xl font-semibold mb-4">Profile Result</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(profileResult, null, 2)}
            </pre>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">🔧 Debug Instructions</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>Open Chrome DevTools (F12)</li>
            <li>Go to Application tab → Cookies → http://localhost:3000</li>
            <li>Clear all existing cookies first</li>
            <li>Click "Login" button above</li>
            <li>Check if cookies appear in DevTools</li>
            <li>Click "Refresh Page" and see if cookies persist</li>
            <li>Try "Get Profile" to test if cookies are sent</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
