<!DOCTYPE html>
<html lang="en">
    <head>
        <title>InstantView</title>
        <link rel="icon" href="%PUBLIC_URL%/favicon.svg" />
        <!-- Meta Tags-->
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
        <meta name="theme-color" content="#2296f3" />
        <meta name="title" content="InstantView - Internal report by VSII" />
        <meta
            name="description"
            content="InstantView is a complete and accurate request description system for reports that are done manually at VSII, based on data named Redmine."
        />
        <meta name="keywords" content="report, vsii, vietsoftware international" />
        <meta name="author" content="VSII" />
        <meta property="og:image" content="%PUBLIC_URL%/background-login.png" />
        <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->

        <link rel="preconnect" href="https://fonts.gstatic.com" />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap"
            rel="stylesheet"
        />
    </head>
    <body>
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div id="root"></div>
        <!--
          This HTML file is a template.
          If you open it directly in the browser, you will see an empty page.

          You can add webfonts, meta tags, or analytics to this file.
          The build step will place the bundled scripts into the <body> tag.

          To begin the development, run `npm start` or `yarn start`.
          To create a production bundle, use `npm run build` or `yarn build`.
        -->
    </body>
</html>
