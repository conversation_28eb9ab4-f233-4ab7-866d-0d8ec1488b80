{"name": "qad-report", "version": "0.0.1", "private": true, "dependencies": {"@date-io/date-fns": "^2.16.0", "@emotion/cache": "^11.10.3", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@hookform/resolvers": "^2.9.7", "@mui/icons-material": "^5.10.6", "@mui/lab": "^5.0.0-alpha.98", "@mui/material": "^5.10.4", "@mui/system": "^5.10.4", "@mui/utils": "^5.10.3", "@mui/x-data-grid": "^5.17.1", "@mui/x-date-pickers": "^5.0.0", "@reduxjs/toolkit": "^1.8.5", "@tabler/icons": "^1.92.0", "apexcharts": "^3.35.5", "axios": "^0.27.2", "axios-mock-adapter": "^1.21.2", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.1.1", "csstype": "^3.1.0", "date-fns": "^2.29.3", "file-saver": "^2.0.5", "firebase": "^9.9.4", "framer-motion": "^7.2.1", "lodash": "^4.17.21", "material-ui-popup-state": "^4.0.2", "moment": "^2.29.4", "qs": "^6.11.0", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-app-rewired": "^2.2.1", "react-beautiful-dnd": "^13.1.1", "react-beautiful-dnd-grid": "^0.1.3-alpha", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.34.2", "react-intl": "^6.1.1", "react-multi-email": "^1.0.9", "react-number-format": "^5.1.3", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "react-slick": "^0.29.0", "react-timer-hook": "^3.0.5", "react18-input-otp": "^1.1.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-saga": "^1.2.2", "remark-gfm": "^3.0.1", "stylis-plugin-rtl": "^2.1.1", "typescript": "^4.8.2", "web-vitals": "^3.0.1", "yup": "^0.32.11"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "lint": "eslint --ext .ts,.tsx ./src", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && yarn install && yarn start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@types/crypto-js": "^4.1.1", "@types/file-saver": "^2.0.7", "@types/react": "^18.0.20", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.5", "@types/react-redux": "^7.1.24", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.10", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "babel-eslint": "^10.1.0", "babel-plugin-direct-import": "^1.0.0", "customize-cra": "^1.0.0", "eslint": "^8.19.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.0", "eslint-import-resolver-typescript": "^3.5.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.7.1", "sass": "^1.55.0"}}