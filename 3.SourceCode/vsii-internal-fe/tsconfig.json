{"compilerOptions": {"target": "es5", "module": "esnext", "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noUnusedLocals": false, "allowSyntheticDefaultImports": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "types": ["node", "webpack-env"], "typeRoots": ["./types"], "baseUrl": "src"}, "exclude": ["node_modules"], "include": ["src"]}