import { Grid } from '@mui/material';
import { Input, Select } from 'components/extended/Form';
import { TEXT_CONDITIONS } from 'constants/Common';
import React from 'react';

interface ITextConditionProps {
    conditionName: string;
    valueName: string;
}

const TextCondition = ({ conditionName, valueName }: ITextConditionProps) => {
    return (
        <Grid container spacing={1}>
            <Grid item xs={3}>
                <Select name={conditionName} selects={TEXT_CONDITIONS} />
            </Grid>
            <Grid item xs={4.5}>
                <Input name={valueName} />
            </Grid>
        </Grid>
    );
};

export default TextCondition;
