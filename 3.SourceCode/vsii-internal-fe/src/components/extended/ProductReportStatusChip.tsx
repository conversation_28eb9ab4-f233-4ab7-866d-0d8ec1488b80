import React from 'react';
import { Chip } from '@mui/material';

import { STATUS_BG_COLOR } from 'constants/ProductReport';

type Props = {
    status: string;
    color?: string;
};

const ProductReportStatusChip: React.FC<Props> = ({ status, color }) => {
    return (
        <Chip
            sx={{
                color: color || STATUS_BG_COLOR[status as keyof typeof STATUS_BG_COLOR],
                borderRadius: '5px',
                borderColor: 'transparent',
                height: '20px',
                fontSize: '10px',
                backgroundColor: (color || STATUS_BG_COLOR[status as keyof typeof STATUS_BG_COLOR]) + '25',
                '& .MuiChip-label': {
                    px: '5px'
                }
            }}
            label={status}
            variant="outlined"
        />
    );
};

export default ProductReportStatusChip;
