import { SxProps, TableRow } from '@mui/material';
import React, { useRef } from 'react';
/* eslint-disable import/no-extraneous-dependencies */
import { useDrag, useDrop, DropTargetMonitor } from 'react-dnd';

// Define the draggable type
const DraggableTypes = {
    REORDERABLE_ROW: 'reorderable_row'
};

// Define the interface for the item being dragged
interface DragItem {
    index: number;
    id: string | number;
    type: string;
}

// Define props interface
interface ReorderableRowProps {
    id: string | number;
    children: React.ReactNode;
    index: number;
    moveRow: (dragIndex: number, hoverIndex: number) => void;
    sx?: SxProps;
}

const style: React.CSSProperties = {
    border: '1px solid gray',
    padding: '10px',
    marginBottom: '5px',
    backgroundColor: 'white',
    cursor: 'move'
};

const ReorderableRow: React.FC<ReorderableRowProps> = ({ id, children, index, moveRow, sx }) => {
    const ref = useRef<HTMLTableRowElement>(null);

    const [, drop] = useDrop<DragItem>({
        accept: DraggableTypes.REORDERABLE_ROW,
        hover(item, monitor: DropTargetMonitor) {
            if (!ref.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;

            if (dragIndex === hoverIndex) {
                return;
            }

            const hoverBoundingRect = ref.current?.getBoundingClientRect();
            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
            const clientOffset = monitor.getClientOffset();

            if (!clientOffset) {
                return;
            }

            const hoverClientY = clientOffset.y - hoverBoundingRect.top;

            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                return;
            }

            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                return;
            }

            moveRow(dragIndex, hoverIndex);
            item.index = hoverIndex;
        }
    });

    const [{ isDragging }, drag] = useDrag({
        type: DraggableTypes.REORDERABLE_ROW,
        item: { id, index, type: DraggableTypes.REORDERABLE_ROW },
        collect: (monitor) => ({
            isDragging: monitor.isDragging()
        })
    });

    const opacity = isDragging ? 0 : 1;
    drag(drop(ref));

    return (
        <TableRow ref={ref} style={{ ...style, opacity }} sx={sx}>
            {children}
        </TableRow>
    );
};

export default ReorderableRow;
