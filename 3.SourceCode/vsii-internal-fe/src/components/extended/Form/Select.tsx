import React, { ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';
import { isEmpty } from 'lodash';

// material-ui
import { FormHelperText, MenuItem, Select as MuiSelect, SelectChangeEvent, SelectProps, styled, SxProps, Typography } from '@mui/material';

// react-hook-form
import { Controller, useFormContext } from 'react-hook-form';

// project imports
import Label from './Label';
import { IOption } from 'types';

interface ISelectProps {
    name: string;
    label?: string | ReactNode;
    disabled?: boolean;
    handleChange?: (e: SelectChangeEvent<unknown>) => void;
    selects: IOption[];
    other?: SelectProps;
    handleChangeFullOption?: (option: IOption) => void;
    isMultipleLanguage?: boolean;
    required?: boolean;
    isControl?: boolean;
    valueSelect?: string;
    defaultValue?: string;
    inRow?: boolean;
    selectWidth?: number;
    sx?: SxProps;
    placeholder?: string;
}

const SelectWrapper = styled('div')({
    position: 'relative',
    width: '100%'
});

const Select = (props: ISelectProps) => {
    const {
        name,
        label,
        handleChange,
        handleChangeFullOption,
        selects,
        disabled,
        isMultipleLanguage,
        required,
        isControl = true,
        valueSelect,
        inRow,
        selectWidth,
        sx,
        placeholder,
        ...other
    } = props;
    const methods = useFormContext();

    // Events
    const handleChangeSelect = (event: SelectChangeEvent<unknown>) => {
        handleChange && handleChange(event);
    };

    return isControl ? (
        <Controller
            name={name}
            control={methods.control}
            render={({ field: { value, ref, onChange, ...field }, fieldState: { error } }) => (
                <SelectWrapper
                    sx={
                        inRow
                            ? {
                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  gap: 5
                              }
                            : undefined
                    }
                >
                    <Label name={name} label={label} required={required} />
                    <MuiSelectStyle
                        {...field}
                        {...other}
                        disabled={disabled}
                        displayEmpty
                        size="small"
                        onChange={(event) => {
                            handleChangeSelect(event);
                            onChange(event.target.value);
                        }}
                        error={!!error}
                        fullWidth
                        value={value}
                        MenuProps={MenuProps}
                        renderValue={
                            isEmpty(value) && placeholder
                                ? () => (
                                      <Typography>
                                          <FormattedMessage id={placeholder} />
                                      </Typography>
                                  )
                                : undefined
                        }
                        ref={ref}
                        sx={
                            selectWidth
                                ? {
                                      width: selectWidth,
                                      ...sx
                                  }
                                : sx
                        }
                    >
                        {selects?.map((option: IOption, key) => (
                            <MenuItem
                                key={key}
                                value={option.value}
                                disabled={option?.disabled}
                                onClick={() => handleChangeFullOption && handleChangeFullOption(option)}
                            >
                                {isMultipleLanguage || !option.value ? <FormattedMessage id={option.label} /> : option.label}
                            </MenuItem>
                        ))}
                    </MuiSelectStyle>
                    <FormHelperText sx={{ color: '#f44336' }}>{error && <FormattedMessage id={error.message} />}</FormHelperText>
                </SelectWrapper>
            )}
        />
    ) : (
        <SelectWrapper>
            <Label name={name} label={label} required={required} />
            <MuiSelectStyle
                {...other}
                disabled={disabled}
                displayEmpty
                size="small"
                onChange={handleChangeSelect}
                fullWidth
                value={valueSelect}
                MenuProps={MenuProps}
                sx={sx}
                renderValue={
                    isEmpty(valueSelect) && placeholder
                        ? () => (
                              <Typography>
                                  <FormattedMessage id={placeholder} />
                              </Typography>
                          )
                        : undefined
                }
            >
                {selects?.map((option: IOption, key) => (
                    <MenuItem
                        key={key}
                        value={option.value}
                        disabled={option?.disabled}
                        onClick={() => handleChangeFullOption && handleChangeFullOption(option)}
                    >
                        {isMultipleLanguage || !option.value ? <FormattedMessage id={option.label} /> : option.label}
                    </MenuItem>
                ))}
            </MuiSelectStyle>
        </SelectWrapper>
    );
};

const MenuProps = {
    PaperProps: {
        style: {
            maxHeight: 250
        }
    }
};

const MuiSelectStyle = styled(MuiSelect)({});

export default Select;
