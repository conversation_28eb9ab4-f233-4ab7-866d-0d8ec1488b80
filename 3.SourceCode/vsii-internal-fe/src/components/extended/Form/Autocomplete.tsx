import React, { ReactNode } from 'react';

// react hook form
import { Controller, useFormContext } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';

// material-ui
import { Chip, Autocomplete as MuiAutocomplete, PopperProps, TextField } from '@mui/material';
import { Box } from '@mui/system';

// project imports
import { IOption } from 'types';
import Label from './Label';

interface IAutocompleteProps {
    name: string;
    label?: string | ReactNode;
    options: IOption[];
    disabled: boolean;
    handleChange?: (data: any) => void;
    handleClose?: () => void;
    groupBy?: any;
    isDisableClearable: boolean;
    isDefaultAll?: boolean;
    required?: boolean;
    multiple?: boolean;
    PopperComponent?: React.JSXElementConstructor<PopperProps>;
}

const Autocomplete = (props: IAutocompleteProps) => {
    const intl = useIntl();
    const {
        name,
        label,
        options,
        disabled,
        handleChange,
        handleClose,
        groupBy,
        isDisableClearable,
        isDefaultAll,
        required,
        multiple,
        PopperComponent,
        ...other
    } = props;

    const methods = useFormContext();
    const placeholder = isDefaultAll ? intl.formatMessage({ id: 'select-option' }) : intl.formatMessage({ id: 'select-all' });

    return (
        <Controller
            name={name}
            control={methods.control}
            render={({ field: { value, ref, onChange, ...field }, fieldState: { error } }) => (
                <>
                    <Label name={name} label={label} required={required} />
                    <MuiAutocomplete
                        id={name}
                        {...other}
                        disablePortal={false}
                        disabled={disabled}
                        value={value}
                        PopperComponent={PopperComponent}
                        onChange={(_, data: IOption) => {
                            onChange(data);
                            handleChange && handleChange(data);
                            if (!data) {
                                handleClose?.();
                            }
                        }}
                        renderTags={(tagValue, getTagProps) => (
                            <Box sx={{ display: 'flex', flexWrap: 'nowrap', overflowX: 'scroll' }}>
                                {tagValue.map((option, index) => {
                                    const { key, ...tagProps } = getTagProps({ index });
                                    return <Chip key={key} label={option.value} {...tagProps} />;
                                })}
                            </Box>
                        )}
                        multiple={multiple}
                        autoComplete={true}
                        disableClearable={isDisableClearable}
                        options={options}
                        renderOption={(props, item) => {
                            return (
                                <span
                                    {...props}
                                    key={props.id + item.value}
                                    style={
                                        item?.color
                                            ? {
                                                  color: item?.color === '#FFFFFF' ? 'inherit' : item?.color
                                              }
                                            : undefined
                                    }
                                >
                                    {item.label}
                                </span>
                            );
                        }}
                        groupBy={groupBy}
                        isOptionEqualToValue={(option, value) => option.value === value.value}
                        getOptionLabel={(option) => option.label}
                        renderInput={(params) => (
                            <TextField
                                error={!!error}
                                helperText={error && <FormattedMessage id="required" />}
                                {...params}
                                {...field}
                                inputRef={ref}
                                size="small"
                                fullWidth
                                placeholder={placeholder}
                            />
                        )}
                    />
                </>
            )}
        />
    );
};

Autocomplete.defaultProps = {
    disabled: false,
    isDisableClearable: false
};

export default Autocomplete;
