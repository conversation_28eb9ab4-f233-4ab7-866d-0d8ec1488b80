import { SyntheticEvent } from 'react';
import { FormattedMessage } from 'react-intl';

// material-ui
import { Tabs as MuiTabs, Tab as MuiTab, styled, ButtonBase } from '@mui/material';
// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { ITabs } from 'types';
import HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';
import StarIcon from '@mui/icons-material/Star';
interface ITabsProps {
    tabList: ITabs[];
    onChange: (event: SyntheticEvent, value: any) => void;
    tabValue: number;
    otherAction?: React.ReactNode;
    handlDeletetab?: (value: number) => void;
    isNotMultiLanguage?: boolean;
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        'aria-controls': `simple-tabpanel-${index}`
    };
}

const Tabs = (props: ITabsProps) => {
    const { tabList, onChange, tabValue, otherAction, isNotMultiLanguage, handlDeletetab } = props;
    const { userInfo } = useAppSelector(authSelector);

    return (
        <MuiTabsWrapper value={tabValue} onChange={onChange} variant="scrollable">
            {tabList.map((item: ITabs, key) => {
                if (item.permission_key) {
                    const isAllowed = checkAllowedPermission(item.permission_key) || !userInfo?.isLdap;
                    return (
                        isAllowed && (
                            <MuiTab
                                key={key}
                                label={!isNotMultiLanguage ? <FormattedMessage id={item.name} /> : item.name}
                                {...a11yProps(key)}
                                icon={
                                    item.erasable ? (
                                        <ButtonBase
                                            onClick={() => {
                                                handlDeletetab?.(tabList[key].value);
                                            }}
                                        >
                                            <HighlightOffOutlinedIcon
                                                sx={{ fontSize: 15, color: 'red', position: 'absolute', bottom: 3, left: 1 }}
                                            />
                                        </ButtonBase>
                                    ) : item.isDefault ? (
                                        <StarIcon sx={{ color: '#FFDD00', fontSize: 15, position: 'absolute', right: 1, top: 15 }} />
                                    ) : undefined
                                }
                                iconPosition="end"
                            />
                        )
                    );
                }
                return (
                    <MuiTab
                        key={key}
                        label={!isNotMultiLanguage ? <FormattedMessage id={item.name} /> : item.name}
                        {...a11yProps(key)}
                        sx={{ minHeight: 50 }}
                        icon={
                            item.erasable ? (
                                <ButtonBase
                                    onClick={() => {
                                        handlDeletetab?.(tabList[key].value);
                                    }}
                                >
                                    <HighlightOffOutlinedIcon
                                        sx={{ fontSize: 15, color: 'red', position: 'absolute', bottom: 3, left: 1 }}
                                    />
                                </ButtonBase>
                            ) : item.isDefault ? (
                                <StarIcon sx={{ color: '#FFDD00', fontSize: 15, position: 'absolute', right: 1, top: 15 }} />
                            ) : undefined
                        }
                        iconPosition="end"
                    />
                );
            })}

            {otherAction}
        </MuiTabsWrapper>
    );
};

const MuiTabsWrapper = styled(MuiTabs)({});

export default Tabs;
