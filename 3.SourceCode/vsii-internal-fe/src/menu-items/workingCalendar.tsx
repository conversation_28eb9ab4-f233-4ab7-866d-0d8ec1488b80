// third-party
import { FormattedMessage } from 'react-intl';

// project imports
import { RegisterWorkingCalendar } from 'components/icons';
import { MAIN_FUNCTIONS } from 'constants/Permission';
import { ROUTER } from 'constants/Routers';
import { NavItemType } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// constant
const icons = {
    RegisterWorkingCalendar
};

const { sidebar } = TEXT_CONFIG_SCREEN;

// ==============================|| EXTRA WORKING CALENDAR MENU ITEMS ||============================== //

const workingCalendar: NavItemType = {
    id: 'workingCalender',
    title: <FormattedMessage id={sidebar + 'working-calender'} />,
    type: 'group',
    access: MAIN_FUNCTIONS.workingCalendar.root,
    children: [
        {
            id: 'register-working-calendar',
            title: <FormattedMessage id={sidebar + 'hr-working-calendar'} />,
            type: 'item',
            url: `/${ROUTER.workingCalendar.register_working_calendar}`,
            icon: icons.RegisterWorkingCalendar,
            access: MAIN_FUNCTIONS.workingCalendar.registerWorkingCalendar,
            breadcrumbs: true
        },
        {
            id: 'manage-leave-days',
            title: <FormattedMessage id={sidebar + 'manage-leave-days'} />,
            type: 'item',
            url: `/${ROUTER.workingCalendar.manage_leave_days}`,
            icon: icons.RegisterWorkingCalendar,
            access: MAIN_FUNCTIONS.workingCalendar.manageLeaveDays,
            breadcrumbs: true
        },
        {
            id: 'manage-ot-requests',
            title: <FormattedMessage id="manage-ot-requests" />,
            type: 'item',
            url: `/${ROUTER.workingCalendar.manage_ot}`,
            icon: icons.RegisterWorkingCalendar,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.workingCalendar.manageOt
        },
        {
            id: 'manage-leaves',
            title: <FormattedMessage id={sidebar + 'manage-leaves'} />,
            type: 'item',
            url: `/${ROUTER.workingCalendar.manage_leaves}`,
            icon: icons.RegisterWorkingCalendar,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.workingCalendar.manageLeaves
        },
        {
            id: 'manage-resignation',
            title: <FormattedMessage id={sidebar + 'manage-resignation'} />,
            type: 'item',
            url: `/${ROUTER.workingCalendar.manage_resignation}`,
            icon: icons.RegisterWorkingCalendar,
            breadcrumbs: true,
            access: MAIN_FUNCTIONS.workingCalendar.manageResignation
        }
    ]
};

export default workingCalendar;
