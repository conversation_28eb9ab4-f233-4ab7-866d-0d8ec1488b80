import { memo, useMemo } from 'react';

// material-ui
import { Typography } from '@mui/material';

// project imports
import menuItem from 'menu-items';
import { NavItemType } from 'types';
import { userAuthorization } from 'utils/authorization';
import NavGroup from './NavGroup';
import { useAppSelector } from 'app/hooks';
import { authSelector } from 'store/slice/authSlice';

// ==============================|| SIDEBAR MENU LIST ||============================== //

const MenuList = () => {
    // last menu-item to show in horizontal menu bar
    const { userInfo } = useAppSelector(authSelector);

    const navItems = useMemo(() => {
        const lastItem = null;

        let lastItemIndex = menuItem.items.length - 1;
        let remItems: NavItemType[] = [];
        let lastItemId = '';

        if (lastItem && lastItem < menuItem.items.length) {
            lastItemId = menuItem.items[lastItem - 1].id!;
            lastItemIndex = lastItem - 1;
            remItems = menuItem.items.slice(lastItem - 1, menuItem.items.length).map((item: NavItemType) => ({
                title: item.title,
                elements: item.children
            }));
        }
        return menuItem.items.slice(0, lastItemIndex + 1).map((item: NavItemType) => {
            const { isAllowFunctions } = userAuthorization(item.access);

            switch (item.type) {
                case 'group':
                    return (
                        (isAllowFunctions || !item.access) && (
                            <NavGroup key={item.id} item={item} lastItem={lastItem!} remItems={remItems} lastItemId={lastItemId} />
                        )
                    );
                default:
                    return (
                        <Typography key={item.id} variant="h6" color="error" align="center">
                            Menu Items Error
                        </Typography>
                    );
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userInfo]);

    return <>{navItems}</>;
};

export default memo(MenuList);
