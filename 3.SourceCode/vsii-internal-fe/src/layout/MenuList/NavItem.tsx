import { ForwardRefExoticComponent, RefAttributes, forwardRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

// material-ui
import { Avatar, ButtonBase, Chip, ListItemButton, ListItemIcon, ListItemText, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { activeID, activeItem, openDrawer } from 'store/slice/menuSlice';
import { useAppSelector, useAppDispatch } from 'app/hooks';
import useConfig from 'hooks/useConfig';

// assets
import { closeDeniedPermission, openDeniedPermission } from 'store/slice/deniedPermissionSlice';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { checkAllowedPermission } from 'utils/authorization';
import { SEARCH_TIMESTAMP } from 'constants/Config';
import { LinkTarget, NavItemType } from 'types';

// ==============================|| SIDEBAR MENU LIST ITEMS ||============================== //

interface NavItemProps {
    item: NavItemType;
    level: number;
    parentId: string;
}

const NavItem = ({ item, level, parentId }: NavItemProps) => {
    const theme = useTheme();
    const matchesSM = useMediaQuery(theme.breakpoints.down('lg'));

    const dispatch = useAppDispatch();
    const { pathname } = useLocation();
    const { borderRadius } = useConfig();

    const { selectedItem, drawerOpen } = useAppSelector((state) => state.menu);
    const isSelected = selectedItem.findIndex((id) => id === item.id) > -1;

    const Icon = item?.icon!;
    const itemIcon = item?.icon ? (
        <Icon
            stroke={1.5}
            size={drawerOpen ? '20px' : '24px'}
            style={{ color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary }}
        />
    ) : (
        <FiberManualRecordIcon
            sx={{
                color: isSelected ? theme.palette.secondary.main : theme.palette.text.primary,
                width: selectedItem.findIndex((id) => id === item?.id) > -1 ? 8 : 6,
                height: selectedItem.findIndex((id) => id === item?.id) > -1 ? 8 : 6
            }}
            fontSize={level > 0 ? 'inherit' : 'medium'}
        />
    );

    let itemTarget: LinkTarget = '_self';
    if (item.target) {
        itemTarget = '_blank';
    }
    // giữ lại thời gian khi chuyển màn hình ( áp dụng với các màn con trong 1 báo cáo)
    const getSegment = (url: string, index: number) => {
        const path = url.split('?')[0].split('/');
        return path[index];
    };
    const setUrl = (url: any) => {
        const currentUrlSegment = getSegment(window.location.href, 3);
        const itemUrlSegment = getSegment(url, 1);
        const timeSearch = localStorage.getItem(SEARCH_TIMESTAMP);
        if (currentUrlSegment !== itemUrlSegment) {
            return url;
        } else {
            if (timeSearch) {
                return `${url}${timeSearch}`;
            } else {
                return url;
            }
        }
    };
    const checkAndRemoveLocalStorage = (url: string) => {
        const currentUrlSegment = getSegment(window.location.href, 3);
        const itemUrlSegment = getSegment(url, 1);

        if (currentUrlSegment !== itemUrlSegment) {
            localStorage.removeItem(SEARCH_TIMESTAMP);
        }
    };

    let listItemProps: {
        component: ForwardRefExoticComponent<RefAttributes<HTMLAnchorElement>> | string;
        href?: string;
        target?: LinkTarget;
    } = {
        component: forwardRef((props, ref) => (
            <Link
                ref={ref}
                {...props}
                to={item.defaultUrl ? item.url : setUrl(item.url!)}
                onClick={(e) => {
                    checkAndRemoveLocalStorage(item.url!);
                    if (item.access && !checkAllowedPermission(item.access[0])) {
                        dispatch(openDeniedPermission());
                    } else {
                        dispatch(closeDeniedPermission());
                    }
                }}
                target={itemTarget}
            />
        ))
    };
    if (item?.external) {
        listItemProps = { component: 'a', href: item.url, target: itemTarget };
    }

    const itemHandler = (id: string) => {
        dispatch(activeItem([id]));
        if (matchesSM) dispatch(openDrawer(false));
        dispatch(activeID(parentId));
    };

    // active menu item on page load
    useEffect(() => {
        const currentIndex = document.location.pathname
            .toString()
            .split('/')
            .findIndex((id) => id === item.id);
        if (currentIndex > -1) {
            dispatch(activeItem([item.id]));
        }
        // eslint-disable-next-line
    }, [pathname]);

    const textColor = theme.palette.mode === 'dark' ? 'grey.400' : 'text.primary';
    const iconSelectedColor = theme.palette.mode === 'dark' && drawerOpen ? 'text.primary' : 'secondary.main';

    return (
        <>
            <ListItemButton
                {...listItemProps}
                disabled={item.disabled}
                disableRipple={!drawerOpen}
                sx={{
                    zIndex: 1201,
                    borderRadius: `${borderRadius}px`,
                    mb: 0.5,
                    pl: drawerOpen ? `${level * 24}px` : 1.25,
                    ...(drawerOpen &&
                        level === 1 &&
                        theme.palette.mode !== 'dark' && {
                            '&:hover': {
                                background: theme.palette.secondary.light
                            },
                            '&.Mui-selected': {
                                background: theme.palette.secondary.light,
                                color: iconSelectedColor,
                                '&:hover': {
                                    color: iconSelectedColor,
                                    background: theme.palette.secondary.light
                                }
                            }
                        }),
                    ...((!drawerOpen || level !== 1) && {
                        py: level === 1 ? 0 : 1,
                        '&:hover': {
                            bgcolor: 'transparent'
                        },
                        '&.Mui-selected': {
                            '&:hover': {
                                bgcolor: 'transparent'
                            },
                            bgcolor: 'transparent'
                        }
                    })
                }}
                selected={isSelected}
                onClick={() => itemHandler(item.id!)}
            >
                <ButtonBase sx={{ borderRadius: `${borderRadius}px` }} disableRipple={drawerOpen}>
                    <ListItemIcon
                        sx={{
                            minWidth: level === 1 ? 30 : 18,
                            color: isSelected ? iconSelectedColor : textColor,
                            ...(!drawerOpen &&
                                level === 1 && {
                                    borderRadius: `${borderRadius}px`,
                                    width: 46,
                                    height: 46,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    '&:hover': {
                                        bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light'
                                    },
                                    ...(isSelected && {
                                        bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 25 : 'secondary.light',
                                        '&:hover': {
                                            bgcolor: theme.palette.mode === 'dark' ? theme.palette.secondary.main + 30 : 'secondary.light'
                                        }
                                    })
                                })
                        }}
                    >
                        {itemIcon}
                    </ListItemIcon>
                </ButtonBase>

                {(drawerOpen || (!drawerOpen && level !== 1)) && (
                    <ListItemText
                        primary={
                            <Typography variant={isSelected ? 'h5' : 'body1'} color="inherit" sx={{ whiteSpace: 'break-spaces' }}>
                                {item.title}
                            </Typography>
                        }
                        secondary={
                            item.caption && (
                                <Typography variant="caption" sx={{ ...theme.typography.subMenuCaption }} display="block" gutterBottom>
                                    {item.caption}
                                </Typography>
                            )
                        }
                    />
                )}

                {drawerOpen && item.chip && (
                    <Chip
                        color={item.chip.color}
                        variant={item.chip.variant}
                        size={item.chip.size}
                        label={item.chip.label}
                        avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}
                    />
                )}
            </ListItemButton>
        </>
    );
};

export default NavItem;
