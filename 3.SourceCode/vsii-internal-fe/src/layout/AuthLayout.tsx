import { createRef, forwardRef, useImperativeHandle, useState } from 'react';
import { Link, Outlet } from 'react-router-dom';

// material-ui
import { Grid, Stack, Typography, useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { AuthCardWrapper } from 'containers/authentication';
import AuthFooter from 'components/cards/AuthFooter';
import { PUBLIC_URL } from 'constants/Common';
import Logo from 'components/Logo';

interface IAuthLayoutRef {
    setState: ({ title, maxWidth }: { title: string; maxWidth?: number }) => void;
}

export const authLayoutRef = createRef<IAuthLayoutRef>();

const AuthLayout = forwardRef((_, ref) => {
    const [maxWidth, setMaxWidth] = useState<number>();
    const [title, setTitle] = useState('');

    const theme = useTheme();

    const matchDownSM = useMediaQuery(theme.breakpoints.down('md'));

    useImperativeHandle<unknown, IAuthLayoutRef>(ref, () => ({
        setState({ title, maxWidth }) {
            setTitle(title);
            setMaxWidth(maxWidth);
        }
    }));

    return (
        <Grid
            container
            direction="column"
            justifyContent="flex-end"
            sx={{
                minHeight: '100vh',
                background: `linear-gradient(0deg, rgba(47, 102, 184, 0.2), rgba(47, 102, 184, 0.2)), url("${PUBLIC_URL}/background-login.png")`,
                backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.default : theme.palette.primary.light,
                backgroundSize: 'cover',
                backgroundPosition: { xs: 'center', sm: 'unset' }
            }}
        >
            <Grid item xs={12}>
                <Grid container justifyContent="center" alignItems="center" sx={{ minHeight: 'calc(100vh - 68px)' }}>
                    <Grid item>
                        <AuthCardWrapper maxWidth={maxWidth}>
                            <Grid container spacing={2} alignItems="center" justifyContent="center">
                                <Grid item sx={{ mb: 3 }}>
                                    <Link to="#">
                                        <Logo width={200} height={60} />
                                    </Link>
                                </Grid>
                                <Grid item xs={12}>
                                    <Grid
                                        container
                                        direction={matchDownSM ? 'column-reverse' : 'row'}
                                        alignItems="center"
                                        justifyContent="center"
                                    >
                                        <Grid item>
                                            <Stack alignItems="center" justifyContent="center" spacing={1}>
                                                <Typography gutterBottom variant="h2">
                                                    {title}
                                                </Typography>
                                            </Stack>
                                        </Grid>
                                    </Grid>
                                </Grid>
                                <Grid item xs={12}>
                                    <Outlet />
                                </Grid>
                            </Grid>
                        </AuthCardWrapper>
                    </Grid>
                </Grid>
            </Grid>
            <Grid item xs={12} sx={{ m: 3, mt: 1 }}>
                <AuthFooter />
            </Grid>
        </Grid>
    );
});

export default AuthLayout;
