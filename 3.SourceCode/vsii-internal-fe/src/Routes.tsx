import { lazy } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';

// Project imports
import { ROUTER } from 'constants/Routers';
import { MAIN_FUNCTIONS } from 'constants/Permission';

/** Layout */
import Loadable from 'components/Loadable';
import MainLayout from 'layout/MainLayout';
import AuthLayout, { authLayoutRef } from 'layout/AuthLayout';

/** Guard Routes */
import AuthGuard from 'utils/route-guard/AuthGuard';
import GuestGuard from 'utils/route-guard/GuestGuard';
import NotFound from 'utils/route-guard/NotFound';

/** Public Routes */
import AuthLogin from 'pages/Login';
import AuthRegister from 'pages/Register';
import CreatePassword from 'pages/CreatePassword';
import AuthForgotPassword from 'pages/ForgotPassword';

/** Private Routes */
// Dasboard
const Dashboard = Loadable(lazy(() => import('pages/Dashboard')));

// Administration
const User = Loadable(lazy(() => import('pages/administration/User')));
const Project = Loadable(lazy(() => import('pages/administration/Project')));
const Holiday = Loadable(lazy(() => import('pages/administration/Holiday')));
const SpecialHours = Loadable(lazy(() => import('pages/administration/SpecialHours')));
const Group = Loadable(lazy(() => import('pages/administration/Group')));
const Rank = Loadable(lazy(() => import('pages/administration/Rank')));
const SystemConfig = Loadable(lazy(() => import('pages/administration/SystemConfig')));
const EmailConfig = Loadable(lazy(() => import('pages/administration/EmailConfig')));
const CVConfig = Loadable(lazy(() => import('pages/administration/CVConfig')));
const ExchangeRateConfig = Loadable(lazy(() => import('pages/administration/ExchangeRateConfig')));
const Department = Loadable(lazy(() => import('pages/administration/Department')));
const ProjectTypeConfig = Loadable(lazy(() => import('pages/administration/ProjectTypeConfig')));
const TitleConfig = Loadable(lazy(() => import('pages/administration/TitleConfig')));
const NonBillablesConfig = Loadable(lazy(() => import('pages/administration/NonBillablesConfig')));
const ColumnConfig = Loadable(lazy(() => import('pages/administration/flexible-report/ColumnConfig')));
const TextConfig = Loadable(lazy(() => import('pages/administration/flexible-report/TextConfig')));
const FlexibleReportingConfig = Loadable(lazy(() => import('pages/administration/flexible-report/FlexibleReportingConfig')));

// Working calendar
const RegisterWorkingCalendar = Loadable(lazy(() => import('pages/register-working-calendar/RegisterWorkingCalendar')));
const ManageLeaveDays = Loadable(lazy(() => import('pages/manage-leave-days/ManageLeaveDays')));
const ManageOTRequests = Loadable(lazy(() => import('pages/manage-ot-requests/OtRequests')));
const Leaves = Loadable(lazy(() => import('pages/manage-leaves/Leaves')));
const Resignation = Loadable(lazy(() => import('pages/manage-resignation/Resignation')));

// Report
const WeeklyEffort = Loadable(lazy(() => import('pages/weekly-effort')));
const MonthlyEffortSummary = Loadable(lazy(() => import('pages/monthly-effort/Summary')));
const MonthlyEffortProject = Loadable(lazy(() => import('pages/monthly-effort/Project')));
const MonthlyEffortDepartmentMember = Loadable(lazy(() => import('pages/monthly-effort/DepartmentMember')));
const ResourcesInProjects = Loadable(lazy(() => import('pages/ResourcesInProjects')));
const MonthlyCostMonitoring = Loadable(lazy(() => import('pages/cost-monitoring/MonthlyCostMonitoring')));
const WeeklyCostMonitoring = Loadable(lazy(() => import('pages/cost-monitoring/WeeklyCostMonitoring')));
const MonthlyProjectCostSummary = Loadable(lazy(() => import('pages/monthly-project-cost/Summary')));
const DetailReportByMonth = Loadable(lazy(() => import('pages/monthly-project-cost/DetailReportByMonth')));
const MonthlyCostData = Loadable(lazy(() => import('pages/monthly-project-cost/MonthlyCostData')));
const NonBillByMember = Loadable(lazy(() => import('pages/non-billable-monitoring/non-bill-by-member')));
const NonbillCostByWeek = Loadable(lazy(() => import('pages/non-billable-monitoring/NonbillCostByWeek')));
const ProductReport = Loadable(lazy(() => import('pages/ProductReport')));
const MonthlyEffortProjectReport = Loadable(lazy(() => import('pages/monthly-effort/ProjectReport')));
const MonthlyEffortORMReport = Loadable(lazy(() => import('pages/monthly-effort/ORMReport')));

// Skills
const SkillsUpdate = Loadable(lazy(() => import('pages/skills-manage/SkillsUpdate')));
const SkillsReport = Loadable(lazy(() => import('pages/skills-manage/SkillsReport')));
const CV = Loadable(lazy(() => import('pages/skills-manage/CV')));

// Sale report
const MonthlyProductionPerformance = Loadable(lazy(() => import('pages/sales/MonthlyProductionPerformance')));
const SalesLead = Loadable(lazy(() => import('pages/sales/SalesLead')));
const ProjectReference = Loadable(lazy(() => import('pages/sales/ProjectReference')));

// Sale pipeline
const SalePipelineSummary = Loadable(lazy(() => import('pages/sales/Summary')));
const OnGoing = Loadable(lazy(() => import('pages/sales/OnGoing')));
const Bidding = Loadable(lazy(() => import('pages/sales/Bidding')));
const BudgetingPlan = Loadable(lazy(() => import('pages/sales/BudgetingPlan')));
const MonitorBiddingPackages = Loadable(lazy(() => import('pages/sales/MonitorBiddingPackages')));

// ==============================|| MAIN ROUTES RENDER ||============================== //

const MainRoutes = () => (
    <Routes>
        {/** Protected Routes */}
        {/** Wrap all Route under AuthGuard element */}
        <Route path={ROUTER.home.index} element={<AuthGuard />}>
            <Route path={ROUTER.home.index} element={<MainLayout />}>
                {/* ================== Dashboard ================== */}
                <Route path={ROUTER.home.index} element={<Dashboard />} />

                {/* ======================== ADMINISTRATION ======================== */}

                {/* ================== Manage user ================== */}
                <Route path={ROUTER.administration.manage_user} element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.user} />}>
                    <Route index element={<User />} />
                </Route>

                {/* ================== Manage project ================== */}
                <Route
                    path={ROUTER.administration.manage_project}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.project} />}
                >
                    <Route index element={<Project />} />
                </Route>

                {/* ================== Manage holidays ================== */}
                <Route
                    path={ROUTER.administration.manage_holiday}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.holidays} />}
                >
                    <Route index element={<Holiday />} />
                </Route>

                {/* ================== Manage holidays ================== */}
                <Route
                    path={ROUTER.administration.manage_special_hours}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.specialHours} />}
                >
                    <Route index element={<SpecialHours />} />
                </Route>

                {/* ================== System config ================== */}
                <Route
                    path={ROUTER.administration.system_config}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.systemConfig} />}
                >
                    <Route index element={<SystemConfig />} />
                </Route>

                {/* ================== Email config ================== */}
                <Route
                    path={ROUTER.administration.email_config}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.emailConfig} />}
                >
                    <Route index element={<EmailConfig />} />
                </Route>

                {/* ================== CV config ================== */}
                <Route path={ROUTER.administration.cv_config} element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.cvConfig} />}>
                    <Route index element={<CVConfig />} />
                </Route>

                {/* ================== Exchange rate config ================== */}
                <Route
                    path={ROUTER.administration.exchange_rate_config}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.exChangeRateConfig} />}
                >
                    <Route index element={<ExchangeRateConfig />} />
                </Route>

                {/* ================== Manage department ================== */}
                <Route
                    path={ROUTER.administration.manage_department}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.department} />}
                >
                    <Route index element={<Department />} />
                </Route>

                {/* ================== Project type config ================== */}
                <Route
                    path={ROUTER.administration.project_type_config}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.projectTypeConfig} />}
                >
                    <Route index element={<ProjectTypeConfig />} />
                </Route>

                {/* ================== Title config ================== */}
                <Route
                    path={ROUTER.administration.title_config}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.titleConfig} />}
                >
                    <Route index element={<TitleConfig />} />
                </Route>

                {/* ================== non-billables config ================== */}
                <Route
                    path={ROUTER.administration.non_billables_config}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.nonBillablesConfig} />}
                >
                    <Route index element={<NonBillablesConfig />} />
                </Route>
                {/* ================== Flexible reporting ================== */}
                <Route
                    path={ROUTER.administration.flexible_reporting.index}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.flexibleReporting.root} />}
                >
                    {/* ================== column config  ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.flexibleReporting.columnConfig} />}>
                        <Route path={ROUTER.administration.flexible_reporting.column_config} element={<ColumnConfig />} />
                    </Route>
                    {/* ================== language config/ text config  ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.flexibleReporting.textConfig} />}>
                        <Route path={ROUTER.administration.flexible_reporting.text_config} element={<TextConfig />} />
                    </Route>
                    {/* ================== flexible config  ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.flexibleReporting.flexibleReportingConfig} />}>
                        <Route
                            path={ROUTER.administration.flexible_reporting.flexible_reporting_config}
                            element={<FlexibleReportingConfig />}
                        />
                    </Route>
                </Route>

                {/* ================== Manage group ================== */}
                <Route path={ROUTER.administration.manage_group} element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.group} />}>
                    <Route index element={<Group />} />
                </Route>

                {/* ================== Manage rank ================== */}
                <Route path={ROUTER.administration.manage_rank} element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.rank} />}>
                    <Route index element={<Rank />} />
                </Route>

                {/* ================== Manage special hours ================== */}
                <Route
                    path={ROUTER.administration.manage_special_hours}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.admin.specialHours} />}
                >
                    <Route index element={<SpecialHours />} />
                </Route>

                {/* ======================== WORKING CALENDAR ======================== */}

                {/* ================== Register working calendar ================== */}
                <Route
                    path={ROUTER.workingCalendar.register_working_calendar}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.workingCalendar.registerWorkingCalendar} />}
                >
                    <Route index element={<RegisterWorkingCalendar />} />
                </Route>
                {/* ================== Manage Leave Days ================== */}
                <Route
                    path={ROUTER.workingCalendar.manage_leave_days}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.workingCalendar.manageLeaveDays} />}
                >
                    <Route index element={<ManageLeaveDays />} />
                </Route>

                {/* ================== Manage OT Requests ================== */}
                <Route
                    path={ROUTER.workingCalendar.manage_ot}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.workingCalendar.manageOt} />}
                >
                    <Route index element={<ManageOTRequests />} />
                </Route>
                {/* ================== Manage leaves ================== */}
                <Route
                    path={ROUTER.workingCalendar.manage_leaves}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.workingCalendar.manageLeaves} />}
                >
                    <Route index element={<Leaves />} />
                </Route>

                {/* ================== Manage resignation ================== */}
                <Route
                    path={ROUTER.workingCalendar.manage_resignation}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.workingCalendar.manageResignation} />}
                >
                    <Route index element={<Resignation />} />
                </Route>

                {/* ======================== REPORTS ======================== */}

                {/* ================== Weekly effort ================== */}
                <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.weeklyEffort.root} />}>
                    <Route path={ROUTER.reports.weekly_effort} element={<WeeklyEffort />} />
                </Route>

                {/* ================== Monthly effort ================== */}
                <Route
                    path={ROUTER.reports.monthly_effort.index}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyEffort.root} />}
                >
                    {/* ================== Summary ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyEffort.summary} />}>
                        <Route index element={<Navigate replace to={ROUTER.reports.monthly_effort.summary} />} />
                        <Route path={ROUTER.reports.monthly_effort.summary} element={<MonthlyEffortSummary />} />
                    </Route>

                    {/* ================== Project ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyEffort.project} />}>
                        <Route path={ROUTER.reports.monthly_effort.project} element={<MonthlyEffortProject />} />
                    </Route>

                    {/* ================== Department member ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyEffort.member} />}>
                        <Route path={ROUTER.reports.monthly_effort.department_member} element={<MonthlyEffortDepartmentMember />} />
                    </Route>
                </Route>

                {/* ================== General report ================== */}
                <Route
                    path={ROUTER.reports.general_report.index}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.generalReport.root} />}
                >
                    {/* ================== Project report ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.generalReport.project_report} />}>
                        <Route path={ROUTER.reports.general_report.project_report} element={<MonthlyEffortProjectReport />} />
                    </Route>
                    {/* =============ORM report ===================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.generalReport.reportORM} />}>
                        <Route path={ROUTER.reports.general_report.orm_report} element={<MonthlyEffortORMReport />} />
                    </Route>

                    {/* ================== Product report ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.generalReport.productReport} />}>
                        <Route path={ROUTER.reports.general_report.product} element={<ProductReport />} />
                    </Route>
                </Route>

                {/* ================== Non-billable ================== */}
                <Route
                    path={ROUTER.reports.non_billable_monitoring.index}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.nonBillable.root} />}
                >
                    {/* ================== By member ================== */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.nonBillable.nonBillMember} />}>
                        <Route index element={<Navigate replace to={ROUTER.reports.non_billable_monitoring.non_billable_by_member} />} />
                        <Route path={ROUTER.reports.non_billable_monitoring.non_billable_by_member} element={<NonBillByMember />} />
                    </Route>

                    {/* ================== Cost by keek ================== */}
                    <Route
                        path={ROUTER.reports.non_billable_monitoring.non_billable_cost_by_week}
                        element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.nonBillable.nonBillChart} />}
                    >
                        <Route index element={<NonbillCostByWeek />} />
                    </Route>
                </Route>

                {/* ================== Resources In Projects ================== */}
                <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.resourcesInProjects} />}>
                    <Route path={ROUTER.reports.resources_in_project} element={<ResourcesInProjects />} />
                </Route>

                {/* ================== Cost & effort monitoring ================== */}
                <Route
                    path={ROUTER.reports.cost_monitoring.index}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.costMonitoring.root} />}
                >
                    {/* ========= Weekly cost ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.costMonitoring.weeklyCost} />}>
                        <Route path={ROUTER.reports.cost_monitoring.weekly_cost} element={<WeeklyCostMonitoring />} />
                    </Route>

                    {/* ========= Monthly cost ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.costMonitoring.monthlyCost} />}>
                        <Route index element={<Navigate replace to={ROUTER.reports.cost_monitoring.monthly_cost} />} />
                        <Route path={ROUTER.reports.cost_monitoring.monthly_cost} element={<MonthlyCostMonitoring />} />
                    </Route>
                </Route>

                {/* ================== Monthly project cost ================== */}
                <Route
                    path={ROUTER.reports.monthly_project_cost.index}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyProjectCost.root} />}
                >
                    {/* ========= Summary ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyProjectCost.summary} />}>
                        <Route index element={<Navigate replace to={ROUTER.reports.monthly_project_cost.summary} />} />
                        <Route path={ROUTER.reports.monthly_project_cost.summary} element={<MonthlyProjectCostSummary />} />
                    </Route>

                    {/* ========= Detail report by month ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyProjectCost.detailByMonth} />}>
                        <Route path={ROUTER.reports.monthly_project_cost.detail_report_by_month} element={<DetailReportByMonth />} />
                    </Route>

                    {/* ========= Monthly cost data ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.monthlyProjectCost.monthlyCost} />}>
                        <Route path={ROUTER.reports.monthly_project_cost.monthly_cost_data} element={<MonthlyCostData />} />
                    </Route>
                </Route>

                {/* ================== Skills manage ================== */}
                <Route
                    path={ROUTER.reports.skills_manage.index}
                    element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.skillManage.root} />}
                >
                    {/* ========= Skills update ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.skillManage.skillsUpdate} />}>
                        <Route index element={<Navigate replace to={ROUTER.reports.skills_manage.skills_update} />} />
                        <Route path={ROUTER.reports.skills_manage.skills_update} element={<SkillsUpdate />} />
                    </Route>
                    <Route
                        path={ROUTER.reports.skills_manage.skills_update}
                        element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.skillManage.skillsUpdate} />}
                    >
                        <Route path={ROUTER.reports.skills_manage.cv} element={<CV />} />
                    </Route>
                    {/* ========= Skills report ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.reports.skillManage.skillsReport} />}>
                        <Route path={ROUTER.reports.skills_manage.skills_report} element={<SkillsReport />} />
                    </Route>
                </Route>

                {/* ============= SALES REPORTS ============= */}
                <Route path={ROUTER.reports.sales.index} element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.root} />}>
                    {/* ========= Monthly production performance ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.monthlyProductionPerformance} />}>
                        <Route index element={<Navigate replace to={ROUTER.reports.sales.monthly_production_performance} />} />
                        <Route path={ROUTER.reports.sales.monthly_production_performance} element={<MonthlyProductionPerformance />} />
                    </Route>
                    {/* ========= Project reference ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.projectReference} />}>
                        <Route path={ROUTER.reports.sales.project_reference} element={<ProjectReference />} />
                    </Route>
                    {/* ========= Sales lead ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.saleLead} />}>
                        <Route path={ROUTER.reports.sales.sales_lead} element={<SalesLead />} />
                    </Route>
                    {/* ========= Monitor Bidding Packages ========= */}
                    <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.monitorBiddingPackage} />}>
                        <Route path={ROUTER.reports.sales.monitor_bidding_package} element={<MonitorBiddingPackages />} />
                    </Route>
                    {/* ========= Sales pipeline ========= */}
                    <Route
                        path={ROUTER.reports.sales.sales_pipeline.index}
                        element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.salePipeline.root} />}
                    >
                        {/* ========= Summary ========= */}
                        <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.salePipeline.summary} />}>
                            <Route path={ROUTER.reports.sales.sales_pipeline.summary} element={<SalePipelineSummary />} />
                        </Route>
                        {/* ========= On Going ========= */}
                        <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.salePipeline.onGoing} />}>
                            <Route path={ROUTER.reports.sales.sales_pipeline.on_going} element={<OnGoing />} />
                        </Route>
                        {/* ========= Bidding ========= */}
                        <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.salePipeline.bidding} />}>
                            <Route path={ROUTER.reports.sales.sales_pipeline.bidding} element={<Bidding />} />
                        </Route>
                        {/* ========= BudgetingPlan ========= */}
                        <Route element={<AuthGuard permissionRequired={MAIN_FUNCTIONS.sale.salePipeline.budget} />}>
                            <Route path={ROUTER.reports.sales.sales_pipeline.budgeting_plan} element={<BudgetingPlan />} />
                        </Route>
                    </Route>
                </Route>
            </Route>
        </Route>

        {/** Public Routes */}
        {/** Wrap all Route under GuestGuard element */}
        <Route element={<GuestGuard />}>
            <Route element={<AuthLayout ref={authLayoutRef} />}>
                <Route path={ROUTER.authentication.login} element={<AuthLogin />} />
                <Route path={ROUTER.authentication.confirmEmail} element={<CreatePassword />} />
                <Route path={ROUTER.authentication.forgotPassword} element={<CreatePassword />} />
                <Route path={ROUTER.authentication.register} element={<AuthRegister />} />
                <Route path={ROUTER.authentication.forgot} element={<AuthForgotPassword />} />
            </Route>
        </Route>
        {/** Page not found route */}
        <Route path="*" element={<NotFound />} />
    </Routes>
);

export default MainRoutes;
