import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { Provider } from 'react-redux';
import CryptoJS from 'crypto-js';

import { ConfigProvider } from 'contexts/ConfigContext';
import { injectStore } from 'services/ApiService';
import * as serviceWorker from 'serviceWorker';
import reportWebVitals from 'reportWebVitals';
import { PUBLIC_URL } from 'constants/Common';
import { BASE_PATH } from 'constants/Config';
import { store } from './app/store';
import App from 'App';

// style + assets
import 'assets/scss/style.scss';

// ==============================|| REACT DOM RENDER  ||============================== //

export let reportUrl = '';
export let authUrl = '';
export let syncUrl = '';
export let privateKeyHasLogin: CryptoJS.lib.WordArray | string = '';
export let cryptoJSConfigMode = {};

const container = document.getElementById('root');
const root = createRoot(container!); // createRoot(container!) if you use TypeScript

(async () => {
    const config = await fetch(`${PUBLIC_URL}/config.json`).then((res) => res.json());

    reportUrl = config.REPORT_SERVICE_URL_API;
    authUrl = config.AUTH_SERVICE_URL_API;
    syncUrl = config.BASE_URL_SYNC;
    privateKeyHasLogin = CryptoJS.enc.Utf8.parse(config.PRIVATE_KEY_HASH_LOGIN);
    cryptoJSConfigMode = {
        iv: CryptoJS.enc.Utf8.parse(config.INITIALIZATION_VECTOR),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    };

    injectStore(store);

    return root.render(
        <Provider store={store}>
            <ConfigProvider>
                <BrowserRouter basename={BASE_PATH}>
                    <App />
                </BrowserRouter>
            </ConfigProvider>
        </Provider>
    );
})();

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
