import React, { SyntheticEvent, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

// projec import
import { useAppDispatch } from 'app/hooks';
import { TabCustom, TableToolbar } from 'containers';
import {
    AddOrEditTechnologyConfig,
    LanguageConfigThead,
    LanguageConfigTBody,
    TechnologyConfigTBody,
    TechnologyConfigThead,
    AddOrEditLanguageConfig,
    ReferenceConfigThead,
    ReferenceConfigTBody,
    AddOrEditReferenceConfig
} from 'containers/administration';
import { checkAllowedPermission, checkAllowedTab } from 'utils/authorization';
import { getSearchParam, transformObject } from 'utils/common';
import Api from 'constants/Api';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, cvReportTabs, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import MainCard from 'components/cards/MainCard';
import { TabPanel } from 'components/extended/Tabs';
import { Table, TableFooter } from 'components/extended/Table';
import sendRequest from 'services/ApiService';
import { ILanguage, IPaginationParam, IPaginationResponse, IReference, IResponseList, ITechnology } from 'types';
import { addOrEditLanguageConfigFormDefault, addOrEditReferenceConfigFormDefault, addOrEditTechnologyConfigFormDefault } from './Config';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { FormattedMessage } from 'react-intl';
import { PERMISSIONS } from 'constants/Permission';

const CVConfig = () => {
    const dispatch = useAppDispatch();
    const { cVConfigTechnologyPermission, cVConfigLanguagePermission, cVConfigReferencePermission } = PERMISSIONS.admin;

    const { cv_config } = TEXT_CONFIG_SCREEN.administration;

    const [searchParams, setSearchParams] = useSearchParams();
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);
    const [technologies, setTechnologies] = useState<ITechnology[]>([]);
    const [technology, setTechnology] = useState<ITechnology>();
    const [languages, setLanguages] = useState<ILanguage[]>([]);
    const [language, setLanguage] = useState<ILanguage>();
    const [references, setReferences] = useState<IReference[]>([]);
    const [reference, setReference] = useState<IReference>();

    const [onAddOrEditTechnology, setOnAddOrEditTechnology] = useState<boolean>(false);
    const [onAddOrEditLanguage, setOnAddOrEditLanguage] = useState<boolean>(false);
    const [onAddOrEditReference, setOnAddOrEditReference] = useState<boolean>(false);

    // Params
    const keyParams = [SEARCH_PARAM_KEY.tab, SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size];
    const keyParamsArray = [SEARCH_PARAM_KEY.timeStatus];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams, keyParamsArray);
    transformObject(params);

    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });

    const [tabValue, setTabValue] = useState(checkAllowedTab(cvReportTabs, params.tab)[0]);
    const [conditions, setConditions] = useState<IPaginationParam>({ ...paginationParamDefault, ...params });

    const getDataTable = async () => {
        setLoading(true);

        const response = await sendRequest(
            tabValue === 0
                ? Api.cv_config_technology.getAll
                : tabValue === 1
                ? Api.cv_config_language.getAll
                : Api.cv_config_reference.getAll,
            {
                ...conditions,
                page: conditions.page + 1
            }
        );
        if (response) {
            const { status, result } = response;
            if (status) {
                const { content, pagination } = result;
                tabValue === 0
                    ? setTechnologies(content as any[])
                    : tabValue === 1
                    ? setLanguages(content as any[])
                    : setReferences(content as any[]);

                setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                setLoading(false);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    // post Add Or Edit Technology
    const postAddOrEditCVConfig = async (value: any) => {
        setAddOrEditLoading(true);
        const response: IResponseList<any> = await sendRequest(
            tabValue === 0
                ? Api.cv_config_technology.postSaveOrUpdateCVConfig
                : tabValue === 1
                ? Api.cv_config_language.postSaveOrUpdateCVConfig
                : Api.cv_config_reference.postSaveOrUpdateCVConfig,
            value
        );
        const { status, result } = response;
        if (response) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: status ? (isEdit ? 'update-success' : 'add-success') : result.content,
                    variant: 'alert',
                    alert: { color: status ? 'success' : 'warning' }
                })
            );
            setAddOrEditLoading(false);
            if (status) {
                getDataTable();
                tabValue === 0
                    ? setOnAddOrEditTechnology(false)
                    : tabValue === 1
                    ? setOnAddOrEditLanguage(false)
                    : setOnAddOrEditReference(false);
            }
        }
    };

    // Call API  delete
    const deleteCvConfigInfo = async (id: string) => {
        let requestData;
        if (tabValue === 0) {
            requestData = { techType: id };
        } else {
            requestData = { id };
        }
        const response = await sendRequest(
            tabValue === 0
                ? Api.cv_config_technology.deleteTechnology
                : tabValue === 1
                ? Api.cv_config_language.deleteLanguage
                : Api.cv_config_reference.deleteReference,
            requestData
        );

        const { status } = response;
        if (status) {
            dispatch(openSnackbar({ open: true, message: 'delete-success', variant: 'alert', alert: { color: 'success' } }));
            dispatch(closeConfirm());
            getDataTable();
        }
    };

    // Add Technology Config
    const handleAddTechnologyConfig = (technologyConfigValue: ITechnology) => {
        postAddOrEditCVConfig(technologyConfigValue);
    };

    // Edit Technology Config
    const handleEditTechnologyConfig = (technologyConfigValue: ITechnology) => {
        postAddOrEditCVConfig(technologyConfigValue);
    };

    const handleDeleteTechnology = (technologyConfigValue: ITechnology) => {
        const id = technologyConfigValue.techType;
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-record" />,
                handleConfirm: () => deleteCvConfigInfo(id as string)
            })
        );
    };

    // Add Language Config
    const handleAddLanguageConfig = (technologyLanguageValue: ILanguage) => {
        postAddOrEditCVConfig(technologyLanguageValue);
    };

    // Edit Language Config
    const handleEditLanguageConfig = (technologyLanguageValue: ILanguage) => {
        postAddOrEditCVConfig(technologyLanguageValue);
    };

    // Delete Language Config
    const handleDeleteLanguage = (technologyLanguageValue: ILanguage) => {
        const id = technologyLanguageValue.idHexString;
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-record" />,
                handleConfirm: () => deleteCvConfigInfo(id as string)
            })
        );
    };

    // Add Reference Config
    const handleAddReferenceConfig = (technologyReferenceValue: IReference) => {
        postAddOrEditCVConfig(technologyReferenceValue);
    };

    // Edit Reference Config
    const handleEditReferenceConfig = (technologyReferenceValue: IReference) => {
        postAddOrEditCVConfig(technologyReferenceValue);
    };

    // Delete Reference Config
    const handleDeleteReference = (technologyReferenceValue: IReference) => {
        const id = technologyReferenceValue.idHexString;
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-record" />,
                handleConfirm: () => deleteCvConfigInfo(id as string)
            })
        );
    };

    const setDataEmpty = () => {
        setTechnologies([]);
        setLoading(false);
    };

    //switch tabs
    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {
        setTabValue(newTabValue);
        setSearchParams({ tab: newTabValue } as any);
        setConditions({ ...paginationParamDefault });
    };

    const handleOpenDialog = (item: any) => {
        setIsEdit(item ? true : false);
        tabValue === 0
            ? setTechnology(item ? item : addOrEditTechnologyConfigFormDefault)
            : tabValue === 1
            ? setLanguage(item ? item : addOrEditLanguageConfigFormDefault)
            : setReference(item ? item : addOrEditReferenceConfigFormDefault);
        tabValue === 0 ? setOnAddOrEditTechnology(true) : tabValue === 1 ? setOnAddOrEditLanguage(true) : setOnAddOrEditReference(true);
    };

    const handleCloseDialog = () => {
        tabValue === 0 ? setOnAddOrEditTechnology(false) : tabValue === 1 ? setOnAddOrEditLanguage(false) : setOnAddOrEditReference(false);
    };

    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tabValue, conditions]);

    return (
        <>
            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={cvReportTabs} />
            <MainCard>
                {/* Technology */}

                <TabPanel value={tabValue} index={0}>
                    {checkAllowedPermission(cVConfigTechnologyPermission.add) && (
                        <TableToolbar handleOpen={handleOpenDialog} addLabel={cv_config + 'add-new'} />
                    )}
                    <Table heads={<TechnologyConfigThead />} isLoading={loading} data={technologies}>
                        <TechnologyConfigTBody
                            pageNumber={conditions.page}
                            pageSize={conditions.size}
                            technologies={technologies}
                            handleOpen={handleOpenDialog}
                            handleDelete={handleDeleteTechnology}
                        />
                    </Table>
                </TabPanel>
                {/* Language */}
                <TabPanel value={tabValue} index={1}>
                    {checkAllowedPermission(cVConfigLanguagePermission.add) && <TableToolbar handleOpen={handleOpenDialog} />}
                    <Table heads={<LanguageConfigThead />} isLoading={loading} data={languages}>
                        <LanguageConfigTBody
                            pageNumber={conditions.page}
                            pageSize={conditions.size}
                            languages={languages}
                            handleOpen={handleOpenDialog}
                            handleDelete={handleDeleteLanguage}
                        />
                    </Table>
                </TabPanel>
                {/* Reference */}
                <TabPanel value={tabValue} index={2}>
                    {checkAllowedPermission(cVConfigReferencePermission.add) && <TableToolbar handleOpen={handleOpenDialog} />}
                    <Table heads={<ReferenceConfigThead />} isLoading={loading} data={references}>
                        <ReferenceConfigTBody
                            pageNumber={conditions.page}
                            pageSize={conditions.size}
                            references={references}
                            handleOpen={handleOpenDialog}
                            handleDelete={handleDeleteReference}
                        />
                    </Table>
                </TabPanel>
            </MainCard>

            {/* add or edit Technology */}
            <AddOrEditTechnologyConfig
                isEdit={isEdit}
                technology={technology}
                loading={addOrEditLoading}
                open={onAddOrEditTechnology}
                handleClose={handleCloseDialog}
                handleAddTechnologyConfig={handleAddTechnologyConfig}
                handleEditTechnologyConfig={handleEditTechnologyConfig}
            />

            {/* add or edit  Language  */}
            <AddOrEditLanguageConfig
                isEdit={isEdit}
                loading={addOrEditLoading}
                language={language}
                open={onAddOrEditLanguage}
                handleClose={handleCloseDialog}
                handleAddLanguageConfig={handleAddLanguageConfig}
                handleEditLanguageConfig={handleEditLanguageConfig}
            />

            {/* add or edit  Reference */}
            <AddOrEditReferenceConfig
                isEdit={isEdit}
                loading={addOrEditLoading}
                reference={reference}
                open={onAddOrEditReference}
                handleClose={handleCloseDialog}
                handleAddReferenceConfig={handleAddReferenceConfig}
                handleEditReferenceConfig={handleEditReferenceConfig}
            />

            <TableFooter
                pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
            />
        </>
    );
};
export default CVConfig;
