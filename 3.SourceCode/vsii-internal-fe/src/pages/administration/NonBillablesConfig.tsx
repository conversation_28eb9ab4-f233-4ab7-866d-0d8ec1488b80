import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { getNonbillableConfig, nonBIllableConfigSelector } from 'store/slice/nonBillableConfigSlice';
import NonBillablesCongfigTBody from 'containers/administration/NonBillablesCongfigTBody';
import NonBillablesCongfigTHead from 'containers/administration/NonBillablesCongfigTHead';
import EditNonBillablesConfig from 'containers/administration/EditNonBillablesConfig';
import { INonBillablesConfig } from 'types/non-billables-config';
import { ITitleFilterConfig, titleFilterConfig } from './Config';
import { getSearchParam, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { searchFormConfig } from 'containers/search/Config';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import MainCard from 'components/cards/MainCard';

const NonBillablesConfig = () => {
    const [searchParams, setSearchParams] = useSearchParams();

    const params: { [key: string]: any } = getSearchParam(
        [searchFormConfig.titleCode.name, SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size],
        searchParams
    );

    transformObject(params);

    const [conditions, setConditions] = useState<ITitleFilterConfig>({ ...titleFilterConfig, ...params });
    const [title, setTitle] = useState<INonBillablesConfig>();
    const [open, setOpen] = useState<boolean>(false);
    const { config, loading } = useAppSelector(nonBIllableConfigSelector);
    const dispatch = useAppDispatch();

    const handleChangePage = (_: any, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: 1, size: parseInt(event.target.value, 10) }));
        setSearchParams((params) => ({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any));
    };

    const handleOpenDialog = (item?: INonBillablesConfig) => {
        setTitle(item);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
        setTitle(undefined);
    };

    useEffect(() => {
        dispatch(getNonbillableConfig(conditions));
    }, [dispatch, conditions]);

    return (
        <>
            {/* Table and Toolbar */}
            <MainCard>
                <Table heads={<NonBillablesCongfigTHead />} isLoading={loading[getNonbillableConfig.typePrefix]} data={config?.content}>
                    <NonBillablesCongfigTBody conditions={conditions} data={config?.content || []} handleOpen={handleOpenDialog} />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!loading[getNonbillableConfig.typePrefix] && (
                <TableFooter
                    pagination={{ total: config?.pagination?.totalElement || 0, page: conditions.page - 1, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
            {open && <EditNonBillablesConfig conditions={conditions} config={title} open={open} handleClose={handleCloseDialog} />}
        </>
    );
};

export default NonBillablesConfig;
