import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import AddOrEditTitleConfig from 'containers/administration/AddOrEditTitleConfig';
import { TitleConfigTBody, TitleConfigTHead } from 'containers/administration';
import { getSearchTitle, titleConfigSelector } from 'store/slice/titleSlice';
import TitleConfigSearch from 'containers/administration/TitleConfigSearch';
import { getSearchParam, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { ITitleFilterConfig, titleFilterConfig } from './Config';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { searchFormConfig } from 'containers/search/Config';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import MainCard from 'components/cards/MainCard';
import { ITitleConfig } from 'types/titleConfig';
import { TableToolbar } from 'containers';

const TitleConfig = () => {
    const [searchParams, setSearchParams] = useSearchParams();

    const params: { [key: string]: any } = getSearchParam(
        [searchFormConfig.titleCode.name, SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size],
        searchParams
    );

    transformObject(params);

    const [conditions, setConditions] = useState<ITitleFilterConfig>({ ...titleFilterConfig, ...params });
    const [title, setTitle] = useState<ITitleConfig>();
    const [open, setOpen] = useState<boolean>(false);

    const { titles, loading } = useAppSelector(titleConfigSelector);

    const dispatch = useAppDispatch();

    const handleChangePage = (_: any, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: 1, size: parseInt(event.target.value, 10) }));
        setSearchParams((params) => ({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any));
    };

    const handleOpenDialog = (item?: ITitleConfig) => {
        setTitle(item);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
        setTitle(undefined);
    };

    useEffect(() => {
        dispatch(getSearchTitle(conditions));
    }, [dispatch, conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <TitleConfigSearch conditions={conditions} setConditions={setConditions} />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                {checkAllowedPermission(PERMISSIONS.admin.titleConfigPermission.add) && <TableToolbar handleOpen={handleOpenDialog} />}
                <Table heads={<TitleConfigTHead />} isLoading={loading[getSearchTitle.typePrefix]} data={titles?.content}>
                    <TitleConfigTBody conditions={conditions} data={titles?.content || []} handleOpen={handleOpenDialog} />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!loading[getSearchTitle.typePrefix] && (
                <TableFooter
                    pagination={{ total: titles?.pagination?.totalElement || 0, page: conditions.page - 1, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {open && <AddOrEditTitleConfig open={open} title={title} conditions={conditions} handleClose={handleCloseDialog} />}
        </>
    );
};

export default TitleConfig;
