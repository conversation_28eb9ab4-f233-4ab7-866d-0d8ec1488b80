// react
import React, { useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { useSearchParams } from 'react-router-dom';

// projec import
import { openSnackbar } from 'store/slice/snackbarSlice';
import { useAppDispatch } from 'app/hooks';
import { Table, TableFooter } from 'components/extended/Table';
import MainCard from 'components/cards/MainCard';
import { TableToolbar } from 'containers';
import {
    AddOrEditExchangeRateConfig,
    ExchangeRateConfigSearch,
    ExchangeRateConfigTBody,
    ExchangeRateConfigThead
} from 'containers/administration';
import { FilterCollapse } from 'containers/search';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { getSearchParam, transformObject } from 'utils/common';
import { IExchangeRate, IPaginationResponse, IResponseList } from 'types';
import { IExchangeRateConfig, exChangeRateConfig } from 'pages/Config';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { addOrEditExchangeRateConfigFormDefault } from './Config';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';

const ExchangeRateConfig = () => {
    const { exchange_rate_config } = TEXT_CONFIG_SCREEN.administration;
    const dispatch = useAppDispatch();
    const { exchangeRatePermission } = PERMISSIONS.admin;

    const [loading, setLoading] = useState<boolean>(false);
    const [onAddOrEdit, setOnAddOrEdit] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [exchangeRates, setExchangeRates] = useState<IExchangeRate[]>([]);
    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);
    const [exchangeRate, setExchangeRate] = useState<IExchangeRate>(addOrEditExchangeRateConfigFormDefault);
    const [searchParams, setSearchParams] = useSearchParams();

    // Params
    const keyParams = [SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size];
    const keyParamsArray = [SEARCH_PARAM_KEY.timeStatus];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams, keyParamsArray);
    transformObject(params);
    const { ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...exChangeRateConfig,
        ...cloneParams,
        userId: params.userId ? { value: params.userId, label: params.fullname } : null,
        projectId: params.projectId ? { value: params.projectId, label: params.projectName } : null
    };
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [conditions, setConditions] = useState<IExchangeRateConfig>(defaultConditions);

    const getDataTable = async () => {
        setLoading(true);
        const response = await sendRequest(Api.exchange_rate_config.getAll, {
            ...conditions,
            year: conditions.year,
            currency: conditions.currency,
            page: conditions.page + 1
        });
        if (response) {
            const { status, result } = response;
            if (status) {
                const { content, pagination } = result;
                setExchangeRates(content as any[]);
                setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                setLoading(false);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    // post Add Or Edit Echange rate
    const postAddOrEditExchangeRate = async (value: any) => {
        setAddOrEditLoading(true);
        const response: IResponseList<any> = await sendRequest(Api.exchange_rate_config.postSaveOrUpdateExchangeRateConfig, value);
        if (response) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: isEdit ? 'update-success' : 'add-success',
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            setAddOrEditLoading(false);
            setOnAddOrEdit(false);
            getDataTable();
        } else {
            setAddOrEditLoading(false);
        }
    };

    // Call API  delete
    const deleteExchangeRateConfigInfo = async (id: string) => {
        const response = await sendRequest(Api.exchange_rate_config.deleteExchangeRate, { id });

        const { status } = response;
        if (status) {
            dispatch(openSnackbar({ open: true, message: 'delete-success', variant: 'alert', alert: { color: 'success' } }));
            dispatch(closeConfirm());
            getDataTable();
        }
    };

    // Add Exchange Rate Config
    const handleAddExchangeRateConfig = (exchangeRateValue: IExchangeRate) => {
        postAddOrEditExchangeRate(exchangeRateValue);
    };

    // Edit Exchange Rate Config
    const handleEditExchangeRateConfig = (exchangeRateValue: IExchangeRate) => {
        postAddOrEditExchangeRate(exchangeRateValue);
    };

    // Delete Exchange Rate Config
    const handleDeleteExchangeRate = (eXhangeRateConfigValue: IExchangeRate) => {
        const id = eXhangeRateConfigValue.idHexString;
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-record" />,
                handleConfirm: () => deleteExchangeRateConfigInfo(id as string)
            })
        );
    };

    const setDataEmpty = () => {
        setExchangeRates([]);
        setLoading(false);
    };
    const handleOpenDialog = (item: IExchangeRate) => {
        setExchangeRate(item);
        setIsEdit(item ? true : false);
        setOnAddOrEdit(true);
    };

    const handleCloseDialog = () => {
        setOnAddOrEdit(false);
    };

    // Search
    const handleSearch = (value: any) => {
        transformObject(value);
        const exchangeRate = { ...value };
        setSearchParams(exchangeRate);
        setConditions({ ...value, page: paginationParamDefault.page });
    };

    //next page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };
    //next page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse>
                <ExchangeRateConfigSearch handleSearch={handleSearch} />
            </FilterCollapse>

            <MainCard>
                {checkAllowedPermission(exchangeRatePermission.add) && (
                    <TableToolbar handleOpen={handleOpenDialog} addLabel={exchange_rate_config + 'add-new'} />
                )}
                <Table heads={<ExchangeRateConfigThead />} isLoading={loading} data={exchangeRates}>
                    <ExchangeRateConfigTBody
                        exchangeRates={exchangeRates}
                        handleOpen={handleOpenDialog}
                        handleDelete={handleDeleteExchangeRate}
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                    />
                </Table>
            </MainCard>

            {/* Add or edit exchange rate */}
            <AddOrEditExchangeRateConfig
                open={onAddOrEdit}
                isEdit={isEdit}
                handleClose={handleCloseDialog}
                loading={addOrEditLoading}
                exchangeRate={exchangeRate}
                handleAdd={handleAddExchangeRateConfig}
                handleEdit={handleEditExchangeRateConfig}
                year={conditions.year}
            />
            {/* Pagination  */}
            <TableFooter
                pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
            />
        </>
    );
};

export default ExchangeRateConfig;
