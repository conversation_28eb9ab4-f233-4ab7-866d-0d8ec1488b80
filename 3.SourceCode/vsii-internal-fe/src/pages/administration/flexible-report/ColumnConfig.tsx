import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { flexiableReportSelector, getListColumns, resetColumnsData } from 'store/slice/flexiableReportSlice';
import { IColumnConfig, ISearchColumnConfigParams } from 'types/flexible-report';
import ColumnConfigSearch from 'containers/administration/ColumnConfigSearch';
import ColumnConfigTBody from 'containers/administration/ColumnConfigTBody';
import ColumnConfigTHead from 'containers/administration/ColumnConfigTHead';
import EditColumnConfig from 'containers/administration/EditColumnConfig';
import { getSearchParam, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { searchColumnConfigFormDefault } from '../Config';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import MainCard from 'components/cards/MainCard';

const ColumnConfig = () => {
    const [searchParams, setSearchParams] = useSearchParams();

    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.flexibleReportId,
        SEARCH_PARAM_KEY.flexibleColumnName,
        SEARCH_PARAM_KEY.flexibleColumnId
    ];

    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);

    transformObject(params);

    const [conditions, setConditions] = useState<ISearchColumnConfigParams>({ ...searchColumnConfigFormDefault, ...params });
    const [item, setItem] = useState<IColumnConfig>();
    const [open, setOpen] = useState<boolean>(false);

    const { columnNames, loading } = useAppSelector(flexiableReportSelector);

    const dispatch = useAppDispatch();

    const handleChangePage = (_: any, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: 1, size: parseInt(event.target.value, 10) }));
        setSearchParams((params) => ({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any));
    };

    const handleOpenDialog = (item: IColumnConfig) => {
        setItem(item);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
        setItem(undefined);
    };

    useEffect(() => {
        if (conditions.flexibleReportId) {
            dispatch(getListColumns(conditions));
        }
        return () => {
            dispatch(resetColumnsData());
        };
    }, [dispatch, conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse>
                <ColumnConfigSearch conditions={conditions} setConditions={setConditions} />
            </FilterCollapse>

            <MainCard>
                <Table heads={<ColumnConfigTHead />} isLoading={loading[getListColumns.typePrefix]} data={columnNames?.content || []}>
                    <ColumnConfigTBody
                        data={columnNames?.content}
                        handleOpen={handleOpenDialog}
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                    />
                </Table>
            </MainCard>

            {/* Add or edit exchange rate */}
            {open && <EditColumnConfig open={open} data={item} conditions={conditions} handleClose={handleCloseDialog} />}
            {/* Pagination  */}
            <TableFooter
                pagination={{ total: columnNames?.pagination?.totalElement || 0, page: conditions.page - 1, size: conditions.size }}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
            />
        </>
    );
};

export default ColumnConfig;
