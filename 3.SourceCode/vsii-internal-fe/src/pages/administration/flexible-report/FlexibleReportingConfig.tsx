import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';

import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationResponseDefault } from 'constants/Common';
import AddorEditFlexibleReportConfig from 'containers/administration/addorEditFlexibleReportConfig';
import FlexibleReportingConfigTSearch from 'containers/administration/FlexibleReportingTSearch';
import { flexibleReportConfigDetail, flexibleReportingConfigSearchDefault } from '../Config';
import FlexibleReportingTHead from 'containers/administration/FlexibleReportingTHead';
import FlexibleReportingTBody from 'containers/administration/FlexibleReportingTBody';
import { FlexibleReportSearchConfig, IFlexibleReports } from 'types/flexible-report';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { Table, TableFooter } from 'components/extended/Table';
import { getSearchParam, transformObject } from 'utils/common';
import { checkAllowedPermission } from 'utils/authorization';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import MainCard from 'components/cards/MainCard';
import sendRequest from 'services/ApiService';
import { IPaginationResponse } from 'types';
import { useAppDispatch } from 'app/hooks';
import { TableToolbar } from 'containers';
import Api from 'constants/Api';

const FlexibleReportingConfig = () => {
    const { Flexible_reporting_configuration } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    const { flexibleReportConfig } = PERMISSIONS.admin.flexibleReportingConfigPermission;
    const [searchParams, setSearchParams] = useSearchParams();
    // Params
    const keyParams = [SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size, SEARCH_PARAM_KEY.reportId, SEARCH_PARAM_KEY.textName];
    // Hooks, State, Variable
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    const { ...cloneParams }: any = params;
    const defaultConditions = {
        ...flexibleReportingConfigSearchDefault,
        ...cloneParams
    };
    const [loading, setLoading] = useState<boolean>(false);
    const [onAddOrEdit, setOnAddOrEdit] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [reports, setReports] = useState<IFlexibleReports[]>([]);
    const [reportDetail, setReportDetail] = useState<IFlexibleReports>(flexibleReportConfigDetail);
    const [formReset] = useState<FlexibleReportSearchConfig>(defaultConditions);

    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: 1,
        pageSize: 10
    });
    const [conditions, setConditions] = useState<FlexibleReportSearchConfig>(defaultConditions);

    const dispatch = useAppDispatch();

    const getDataTable = async () => {
        setLoading(true);
        const response = await sendRequest(Api.flexible_report.getAllReports, {
            ...conditions
        });
        if (response) {
            const { status, result } = response;
            if (status) {
                const { content, pagination } = result;
                setReports(content as any[]);
                setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                setLoading(false);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    const setDataEmpty = () => {
        setReports([]);
        setLoading(false);
    };
    const handleOpenDialog = (item: IFlexibleReports) => {
        setReportDetail(item);
        setIsEdit(item ? true : false);
        setOnAddOrEdit(true);
    };

    const handleCloseDialog = () => {
        setOnAddOrEdit(false);
    };

    // Search
    const handleSearch = (value: any) => {
        transformObject(value);
        setSearchParams({ ...value });
        setConditions({ ...value });
    };

    //next page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    //next page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: 1, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any);
    };

    // delete config
    const deleteConfig = async (id: string) => {
        const res = await sendRequest(Api.flexible_report.deleteConfig(id));
        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? res.result.content : res.result.content.message,
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );
        res.status && dispatch(closeConfirm()) && getDataTable();
    };

    const handleDeleteConfig = async (id: string) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-config" />,
                handleConfirm: () => deleteConfig(id as string)
            })
        );
    };

    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse>
                <FlexibleReportingConfigTSearch handleSearch={handleSearch} formReset={formReset} />
            </FilterCollapse>

            <MainCard>
                {checkAllowedPermission(flexibleReportConfig.add) && (
                    <TableToolbar handleOpen={handleOpenDialog} addLabel={Flexible_reporting_configuration + 'add-new'} />
                )}
                <Table heads={<FlexibleReportingTHead />} isLoading={loading} data={reports}>
                    <FlexibleReportingTBody reports={reports} handleOpen={handleOpenDialog} handleDeleteConfig={handleDeleteConfig} />
                </Table>
            </MainCard>

            {/* Add or edit */}
            <AddorEditFlexibleReportConfig
                open={onAddOrEdit}
                isEdit={isEdit}
                handleClose={handleCloseDialog}
                reportDetail={reportDetail}
                getDataTable={getDataTable}
            />
            {/* Pagination  */}
            <TableFooter
                pagination={{ total: paginationResponse.totalElement, page: conditions.page - 1, size: conditions.size }}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
            />
        </>
    );
};

export default FlexibleReportingConfig;
