import React, { SyntheticEvent, useEffect, useMemo, useState } from 'react';
import { ButtonBase, useTheme } from '@mui/material';
import { useSearchParams } from 'react-router-dom';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { FormattedMessage } from 'react-intl';

import { flexiableReportSelector, getListTextConfig, resetTextConfigData } from 'store/slice/flexiableReportSlice';
import { getLanguage, languageConfigSelector } from 'store/slice/languageConfigSlice';
import TextConfigSearch from 'containers/administration/TextConfigSearch';
import TextConfigTHead from 'containers/administration/TextConfigTHead';
import TextConfigTBody from 'containers/administration/TextConfigTBody';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import AddLanguage from 'containers/administration/AddLanguage';
import { ISearchTextConfigParams, ITextConfig } from 'types/flexible-report';
import { getSearchParam, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { editTexSchema, searchTextConfigFormDefault } from '../Config';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import MainCard from 'components/cards/MainCard';
import { Tabs } from 'components/extended/Tabs';
import { gridSpacing } from 'store/constant';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import { ITabs } from 'types';
import { FormProvider } from 'components/extended/Form';
import { useForm } from 'react-hook-form';
import { IEditLanguage } from 'types/languageConfig';
import { yupResolver } from '@hookform/resolvers/yup';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';

const TextConfig = () => {
    const [searchParams, setSearchParams] = useSearchParams();

    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.flexibleReportId,
        SEARCH_PARAM_KEY.textName,
        SEARCH_PARAM_KEY.code
    ];

    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);

    transformObject(params);

    const [conditions, setConditions] = useState<ISearchTextConfigParams>({ ...searchTextConfigFormDefault, ...params });
    const [open, setOpen] = useState<boolean>(false);

    const { textConfig, loading } = useAppSelector(flexiableReportSelector);
    const { laguageConfigList } = useAppSelector(languageConfigSelector);

    const { flexibleReportingConfigPermission } = PERMISSIONS.admin;

    const dispatch = useAppDispatch();

    const tablist = useMemo(() => {
        const tabs: ITabs[] = laguageConfigList.map((item, index) => ({
            ...item,
            name: item.languageName,
            value: index,
            isDefault: !index ? true : false,
            erasable: index && checkAllowedPermission(flexibleReportingConfigPermission.textConfig.delete) ? true : false
        }));
        return tabs;
    }, [laguageConfigList, flexibleReportingConfigPermission]);

    const handleChangePage = (_: any, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: 1, size: parseInt(event.target.value, 10) }));
        setSearchParams((params) => ({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any));
    };

    const handleOpenDialog = () => {
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const [value, setValue] = useState(0);
    const theme = useTheme();

    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {
        setValue(newTabValue);
        const tabCode = tablist.find((item, index) => index === newTabValue)?.languageCode;
        setConditions((prev) => ({ ...prev, page: 1, size: 10, code: tabCode as string }));
        setSearchParams(
            (params) => ({ ...params, page: 1, size: 10, code: tabCode, flexibleReportId: conditions.flexibleReportId } as any)
        );
    };

    const deleteTabLanguage = async (tabValue: number) => {
        const tabId = tablist.find((item, index) => index === tabValue)?.id;
        const res = await sendRequest(Api.flexible_textConfig.deleteLanguage(tabId as string));
        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? res.result.content : res.result.content.message,
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );
        if (res.status) {
            setValue(0);
            dispatch(getLanguage());
            dispatch(closeConfirm());
            setConditions(searchTextConfigFormDefault);
            // setSearchParams(searchTextConfigFormDefault)
        }
    };

    const handleOpenConfirmDelete = (tabValue: number) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-language" />,
                handleConfirm: () => deleteTabLanguage(tabValue)
            })
        );
    };

    const handleAddTab = () => {
        handleOpenDialog();
    };

    const handlekAfterAdd = (languageCode: string) => {
        dispatch(getLanguage());
        setValue(tablist.length);
        setConditions((prev) => ({
            ...prev,
            page: 1,
            size: 10,
            code: languageCode as string,
            flexibleReportId: conditions.flexibleReportId
        }));
        setSearchParams(
            (params) => ({ ...params, page: 1, size: 10, code: languageCode, flexibleReportId: conditions.flexibleReportId } as any)
        );
    };
    useEffect(() => {
        dispatch(getListTextConfig(conditions));
        return () => {
            dispatch(resetTextConfigData());
        };
    }, [dispatch, conditions]);

    const methods = useForm({
        defaultValues: { languageCode: '', newText: '', note: '' },
        resolver: yupResolver(editTexSchema)
    });
    const [dataEditLanguage, setDataEditLanguage] = useState<ITextConfig>();

    const handleSubmit = async (value: IEditLanguage) => {
        const configOtherLanguage = dataEditLanguage?.languageConfigs?.filter((item) => item.languageCode !== value.languageCode) || [];
        const data = {
            ...dataEditLanguage,
            languageConfigs: [
                ...configOtherLanguage,
                {
                    languageCode: value.languageCode,
                    newText: value.newText
                }
            ],
            note: value.note
        };
        const res = await sendRequest(Api.flexible_textConfig.edit, data);

        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? res.result.content : res.result.content.message,
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );

        if (res.status) dispatch(getListTextConfig(conditions));
    };

    return (
        <>
            {/* Search form */}

            <MainCard
                sx={{
                    marginBottom: theme.spacing(gridSpacing),
                    border: 'none',
                    '& .MuiCardContent-root': {
                        padding: '0 !important'
                    }
                }}
            >
                <Tabs
                    tabValue={value}
                    tabList={tablist}
                    onChange={handleChangeTab}
                    otherAction={
                        checkAllowedPermission(flexibleReportingConfigPermission.textConfig.add) && (
                            <ButtonBase onClick={handleAddTab} sx={{ ml: 2 }}>
                                <AddCircleOutlineIcon />
                            </ButtonBase>
                        )
                    }
                    isNotMultiLanguage
                    handlDeletetab={handleOpenConfirmDelete}
                />
            </MainCard>
            <FilterCollapse>
                <TextConfigSearch conditions={conditions} setConditions={setConditions} />
            </FilterCollapse>

            <MainCard>
                <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                    <Table heads={<TextConfigTHead />} isLoading={loading[getListTextConfig.typePrefix]} data={textConfig?.content || []}>
                        <TextConfigTBody data={textConfig?.content} conditions={conditions} setDataEditLanguage={setDataEditLanguage} />
                    </Table>
                </FormProvider>
            </MainCard>

            {/* Add or edit exchange rate */}
            {open && <AddLanguage open={open} handleClose={handleCloseDialog} handlekAfterAdd={handlekAfterAdd} />}
            {/* Pagination  */}
            <TableFooter
                pagination={{ total: textConfig?.pagination?.totalElement || 0, page: conditions.page - 1, size: conditions.size }}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
            />
        </>
    );
};

export default TextConfig;
