import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

// store
import { useAppDispatch, useAppSelector } from 'app/hooks';

// project imports
import MainCard from 'components/cards/MainCard';
import { Table, TableFooter } from 'components/extended/Table';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationParamDefault } from 'constants/Common';
import { AddOrEditProject, ManageProjectSearch, ManageProjectTBody, ManageProjectThead } from 'containers/administration';
import { FilterCollapse } from 'containers/search';
import { getAllProject, loadingSelector, projectListSelector, projectpaginationSelector, resetProjectData } from 'store/slice/projectSlice';
import { exportDocument, getSearchParam, transformObject } from 'utils/common';
import { IProjectSearchConfig, projectSearchConfig } from './Config';
import Api from 'constants/Api';
import { PERMISSIONS } from 'constants/Permission';
import { TableToolbar } from 'containers';
import { checkAllowedPermission } from 'utils/authorization';

// ==============================|| Manage Project ||============================== //
/**
 *  URL Params
 *  page
 *  size
 *  projectType
 *  projectId
 *  projectName
 *  projectManager
 *  status
 */
const Project = () => {
    const { manage_project } = TEXT_CONFIG_SCREEN.administration;
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.projectType,
        SEARCH_PARAM_KEY.projectId,
        SEARCH_PARAM_KEY.projectName,
        SEARCH_PARAM_KEY.status,
        SEARCH_PARAM_KEY.projectManager,
        SEARCH_PARAM_KEY.fullname
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { projectName, fullname, ...cloneParams } = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...projectSearchConfig,
        ...cloneParams,
        projectId: params.projectId ? { value: params.projectId, label: params.projectName } : null,
        projectManager: params.projectManager ? { value: params.projectManager, label: params.fullname } : null,
        projectType: params.projectType ? params.projectType : '',
        status: params.status ? params.status : ''
    };
    const dispatch = useAppDispatch();
    const projectList = useAppSelector(projectListSelector);
    const loading = useAppSelector(loadingSelector);
    const projectPagination = useAppSelector(projectpaginationSelector);
    const [conditions, setConditions] = useState<IProjectSearchConfig>(defaultConditions);
    const [formReset] = useState<IProjectSearchConfig>(defaultConditions);
    const [open, setOpen] = useState<boolean>(false);
    const [isEditProject, setIsEditProject] = useState<boolean>(false);
    const { projectPermission } = PERMISSIONS.admin;
    // Function
    const getDataTable = () => {
        dispatch(
            getAllProject({
                ...conditions,
                projectId: conditions.projectId?.value as any,
                projectManager: conditions.projectManager?.value as any,
                projectAuthorization: 'false',
                page: conditions.page + 1
            })
        );
    };

    // Event
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    const handleOpenDialog = (isEdit?: boolean) => {
        if (isEdit) {
            setIsEditProject(true);
        }
        setOpen(true);
    };

    const handleCloseDialog = () => {
        dispatch(resetProjectData());
        setOpen(false);
        setIsEditProject(false);
    };

    const handleExportDocument = () => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { page, size, ...cloneConditions } = conditions;
        transformObject(cloneConditions);
        exportDocument(Api.project.getDownload.url, {
            ...cloneConditions,
            projectId: conditions.projectId?.value as any,
            projectManager: conditions.projectManager?.value as any
        });
    };

    // Handle submit
    const handleSearch = (value: any) => {
        const { projectId, projectManager } = value;
        transformObject(value);
        const searchParams = {
            page: paginationParamDefault.page,
            size: conditions.size,
            ...value
        };

        if (projectId && projectManager) {
            searchParams.projectId = projectId.value;
            searchParams.projectName = projectId.label;
            searchParams.projectManager = projectManager.value;
            searchParams.fullname = projectManager.label;
        } else if (projectId) {
            searchParams.projectId = projectId.value;
            searchParams.projectName = projectId.label;
        } else if (projectManager) {
            searchParams.projectManager = projectManager.value;
            searchParams.fullname = projectManager.label;
        }

        setSearchParams(searchParams);
        setConditions({ ...value, page: paginationParamDefault.page, size: conditions.size });
    };

    // Effect
    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse handleExport={handleExportDocument}>
                <ManageProjectSearch formReset={formReset} handleSearch={handleSearch} />
            </FilterCollapse>

            {/* Table */}
            <MainCard>
                {checkAllowedPermission(projectPermission.add) && (
                    <TableToolbar handleOpen={handleOpenDialog} addLabel={manage_project + 'add-new'} />
                )}
                <Table heads={<ManageProjectThead />} isLoading={loading} data={projectList}>
                    <ManageProjectTBody
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        projects={projectList}
                        handleOpen={handleOpenDialog}
                    />
                </Table>
            </MainCard>
            {/* Pagination  */}
            {!loading && (
                <TableFooter
                    pagination={{ total: projectPagination?.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
            {/* Edit Project */}
            <AddOrEditProject open={open} isEdit={isEditProject} onClose={handleCloseDialog} dataTable={getDataTable} />
        </>
    );
};

export default Project;
