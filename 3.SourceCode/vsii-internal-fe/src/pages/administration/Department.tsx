import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { AddOrEditDepartment, DepartmentSearch, DepartmentTBody, DepartmentTHead } from 'containers/administration';
import { departmentSelector, getSearchDepartment } from 'store/slice/departmentSlice';
import { IDepartmentFilterConfig, departmentFilterConfig } from './Config';
import { getSearchParam, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { searchFormConfig } from 'containers/search/Config';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import MainCard from 'components/cards/MainCard';
import { IDepartment } from 'types/department';
import { TableToolbar } from 'containers';

const Department = () => {
    const { manage_department } = TEXT_CONFIG_SCREEN.administration;
    const [searchParams, setSearchParams] = useSearchParams();

    const params: { [key: string]: any } = getSearchParam(
        [searchFormConfig.department.manage.name, SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size],
        searchParams
    );

    transformObject(params);

    const [conditions, setConditions] = useState<IDepartmentFilterConfig>({ ...departmentFilterConfig, ...params });
    const [department, setDepartment] = useState<IDepartment>();
    const [open, setOpen] = useState<boolean>(false);

    const { departments, loading } = useAppSelector(departmentSelector);

    const dispatch = useAppDispatch();

    const handleChangePage = (_: any, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: 1, size: parseInt(event.target.value, 10) }));
        setSearchParams((params) => ({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any));
    };

    const handleOpenDialog = (item?: IDepartment) => {
        setDepartment(item);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
        setDepartment(undefined);
    };

    useEffect(() => {
        dispatch(getSearchDepartment(conditions));
    }, [dispatch, conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <DepartmentSearch conditions={conditions} setConditions={setConditions} />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                {checkAllowedPermission(PERMISSIONS.admin.departmentPermission.add) && (
                    <TableToolbar handleOpen={handleOpenDialog} addLabel={manage_department + 'add-new'} />
                )}
                <Table heads={<DepartmentTHead />} isLoading={loading[getSearchDepartment.typePrefix]} data={departments?.content}>
                    <DepartmentTBody conditions={conditions} data={departments?.content || []} handleOpen={handleOpenDialog} />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!loading[getSearchDepartment.typePrefix] && (
                <TableFooter
                    pagination={{ total: departments?.pagination?.totalElement || 0, page: conditions.page - 1, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {open && <AddOrEditDepartment open={open} department={department} conditions={conditions} handleClose={handleCloseDialog} />}
        </>
    );
};

export default Department;
