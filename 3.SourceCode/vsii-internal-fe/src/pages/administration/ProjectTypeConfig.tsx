import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { AddOrEditProjectType, ProjectTypeConfigSearch, ProjectTypeConfigTBody, ProjectTypeConfigTHead } from 'containers/administration';
import { projectTypeSelector, getSearchProjectType } from 'store/slice/projectTypeSlice';
import { IProjectTypeFilterConfig, projectTypeFilterConfig } from './Config';
import { getSearchParam, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { searchFormConfig } from 'containers/search/Config';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import MainCard from 'components/cards/MainCard';
import { IProjectType } from 'types/projectType';
import { TableToolbar } from 'containers';

const ProjectTypeConfig = () => {
    const { project_type_config } = TEXT_CONFIG_SCREEN.administration;
    const [searchParams, setSearchParams] = useSearchParams();

    const params: { [key: string]: any } = getSearchParam(
        [searchFormConfig.projectType.manage.name, SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size],
        searchParams
    );

    transformObject(params);

    const [conditions, setConditions] = useState<IProjectTypeFilterConfig>({ ...projectTypeFilterConfig, ...params });
    const [projectType, setProjectType] = useState<IProjectType>();
    const [open, setOpen] = useState<boolean>(false);

    const { projectTypes, loading } = useAppSelector(projectTypeSelector);

    const dispatch = useAppDispatch();

    // Event
    const handleChangePage = (_: any, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: 1, size: parseInt(event.target.value, 10) }));
        setSearchParams((params) => ({ ...params, page: 1, size: parseInt(event.target.value, 10) } as any));
    };

    const handleOpenDialog = (item?: IProjectType) => {
        setProjectType(item);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
        setProjectType(undefined);
    };

    useEffect(() => {
        dispatch(getSearchProjectType(conditions));
    }, [dispatch, conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <ProjectTypeConfigSearch conditions={conditions} setConditions={setConditions} />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                {checkAllowedPermission(PERMISSIONS.admin.projectTypeConfigPermission.add) && (
                    <TableToolbar handleOpen={handleOpenDialog} addLabel={project_type_config + 'add'} />
                )}
                <Table heads={<ProjectTypeConfigTHead />} isLoading={loading[getSearchProjectType.typePrefix]} data={projectTypes?.content}>
                    <ProjectTypeConfigTBody conditions={conditions} data={projectTypes?.content || []} handleOpen={handleOpenDialog} />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!loading[getSearchProjectType.typePrefix] && (
                <TableFooter
                    pagination={{
                        total: projectTypes?.pagination?.totalElement || 0,
                        page: conditions.page - 1,
                        size: conditions.size
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {open && <AddOrEditProjectType open={open} projectType={projectType} conditions={conditions} handleClose={handleCloseDialog} />}
        </>
    );
};

export default ProjectTypeConfig;
