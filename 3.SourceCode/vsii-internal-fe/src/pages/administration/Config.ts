// yup
import * as yup from 'yup';

// project import
import {
    IHoliday,
    IOption,
    IPaginationParam,
    IRank,
    ISystemConfig,
    IRankCostHistory,
    IQuotaUpdateHistory,
    IEmailConfig,
    ITechnology,
    ILanguage,
    IReference,
    IExchangeRate
} from 'types';
import { DEFAULT_VALUE_OPTION_SELECT, EMAIL_TYPE, paginationParamDefault } from 'constants/Common';
import { dateFormatComparison, getCurrentMonth, getCurrentYear } from 'utils/date';
import { REGEX_CONSTANTS } from 'constants/Validation';
import { VALIDATE_MESSAGES } from 'constants/Message';

// third party
import { IMember, IUserBillableHistory, IUserOnboardHistory, IUserRankHistory } from 'types/member';
import moment from 'moment';
import { FlexibleReportSearchConfig, IFlexibleReports, ISearchColumnConfigParams, ISearchTextConfigParams } from 'types/flexible-report';

// Manage user
export const userFormSchema = yup.object().shape({
    departmentId: yup.string().nullable(),
    firstName: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    lastName: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    memberCode: yup.string().nullable(),
    onboardDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    outboardDate: yup.date().nullable().min(yup.ref('onboardDate'), VALIDATE_MESSAGES.OUTBOARDDATE),
    rankId: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    status: yup.string().nullable(),
    titleCode: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    userId: yup.string().nullable(),
    userName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    userType: yup.string().nullable(),
    contractor: yup.boolean().nullable(),
    logtime: yup.boolean().nullable(),
    groups: yup.array().of(
        yup.object().shape({
            groupId: yup.number(),
            groupName: yup.string()
        })
    )
});

export const addProjectReportSchema = yup.object().shape({
    projectReportInfo: yup.object().shape({
        year: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
        month: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
        projectId: yup.object().required(VALIDATE_MESSAGES.REQUIRED),
        userNamePM: yup.string().nullable(),
        milestoneApproveEntityList: yup.array().of(
            yup.object().shape({
                date: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED).typeError(VALIDATE_MESSAGES.REQUIRED),
                milestone: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                releasePackage: yup.string().nullable(),
                status: yup.number().nullable()
            })
        )
    }),
    monthlyReport: yup.object().shape({
        progressMilestone: yup.object().shape({
            implPhase: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
            progressAssesment: yup.string().when('implPhase', {
                is: 'Deployment',
                then: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
            }),
            finishedTasks: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
            completedMilestones: yup.string().nullable(),
            workCompleted: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
            delayNotFinishPlan: yup.string().nullable(),
            nextMilestone: yup.string().nullable()
        }),

        resource: yup.object().shape({
            totalHC: yup.number().nullable(),
            reviewEntityList: yup.array().of(
                yup.object().shape({
                    description: yup.string().nullable(),
                    resourceReview: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED)
                })
            )
        }),

        issueRiskEntityList: yup.array().of(
            yup.object().shape({
                description: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                type: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                rootCause: yup.string().nullable(),
                proposedSolution: yup.string().nullable()
            })
        ),
        nextPlan: yup.array().of(
            yup.object().shape({
                task: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                comment: yup.string().nullable(),
                startDate: yup.date().nullable().typeError(VALIDATE_MESSAGES.REQUIRED),
                dueDate: yup.date().nullable().typeError(VALIDATE_MESSAGES.REQUIRED)
            })
        ),
        saleUpSalesEntityList: yup.array().of(
            yup.object().shape({
                oppotunitiesExpand: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                changedOfSale: yup.string().nullable(),
                comment: yup.string().nullable(),
                potential_revenueVND: yup.number().nullable()
            })
        )
    })
});

export const userFormDefault: IMember = {
    id: {},
    userId: '',
    userName: '',
    userType: '',
    lastName: '',
    firstName: '',
    departmentId: '',
    userDept: '',
    idHexString: null,
    onboardDate: null,
    outboardDate: null,
    created: '',
    creator: '',
    lastUpdate: '',
    userUpdate: '',
    isCustomer: false,
    status: '1',
    titleCode: '',
    rankId: '',
    rankCost: 0,
    allocationCost: 0,
    memberCode: '',
    contractor: false,
    logtime: false,
    userLevel: '',
    groups: [],
    effortArise: ''
};

export interface IUserFilterConfig extends IPaginationParam {
    departmentId: string;
    memberCode: string;
    userName: string;
    status: string;
    contractor: boolean | string | null;
}

export const userFilterConfig: IUserFilterConfig = {
    ...paginationParamDefault,
    departmentId: '',
    memberCode: '',
    userName: '',
    status: '1',
    contractor: ''
};

export const userFilterSchema = yup.object().shape({
    departmentId: yup.string(),
    memberCode: yup.string(),
    userName: yup.string(),
    status: yup.string(),
    contractor: yup.string()
});

// On/outboard info

export const userOnboardHistoryFormDefault: IUserOnboardHistory = {
    fromDate: null,
    toDate: null,
    officialOnboardDate: null,
    contractor: null
};
export const userRankHistoryFormDefault: IUserRankHistory = {
    fromDate: null,
    toDate: null,
    rankId: '',
    titleCode: '',
    timesheetEnough: false
};

// Billble
export const userBillableFormDefault: IUserBillableHistory = {
    fromDate: null,
    toDate: null
};

export const editOnboardHistoryFormSchema = yup.object().shape({
    // user info
    memberCode: yup.string().nullable().matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS),
    userName: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS)
        .required(VALIDATE_MESSAGES.REQUIRED),
    password: yup
        .string()
        .nullable()
        .transform((originalValue) => (originalValue === '' ? null : originalValue))
        .min(8, VALIDATE_MESSAGES.INVALID_PASSWORD_MIN)
        .max(24, VALIDATE_MESSAGES.INVALID_PASSWORD_MAX)
        .matches(REGEX_CONSTANTS.REGEX_PASSWORD, VALIDATE_MESSAGES.INVALID_PASSWORD_SPECIAL_CHAR),
    firstName: yup
        .string()
        .nullable()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .matches(REGEX_CONSTANTS.REGEX_NAME, VALIDATE_MESSAGES.SPECIAL_CHARACTERS),
    lastName: yup
        .string()
        .nullable()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .matches(REGEX_CONSTANTS.REGEX_NAME, VALIDATE_MESSAGES.SPECIAL_CHARACTERS),
    departmentId: yup
        .string()
        .nullable()
        .when('isCustomer', {
            is: false,
            then: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
        }),

    status: yup.string().nullable(),
    logtime: yup.boolean().nullable(),
    userLevel: yup
        .string()
        .nullable()
        .when('isCustomer', {
            is: false,
            then: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
        }),
    isCustomer: yup.boolean().required(),

    // group
    groups: yup.array().of(
        yup.object().shape({
            groupId: yup.string(),
            groupName: yup.string()
        })
    ),

    userOnboardHistoryList: yup
        .array()
        .when('status', {
            is: '3',
            then: yup.array().of(
                yup.object().shape({
                    contractor: yup.string().nullable().typeError(VALIDATE_MESSAGES.REQUIRED),
                    fromDate: yup
                        .date()
                        .nullable()
                        .required(VALIDATE_MESSAGES.REQUIRED)
                        .typeError(VALIDATE_MESSAGES.REQUIRED)
                        .test('date-fromDate', VALIDATE_MESSAGES.ABOUT_DAYS, function (value, { from, path }: any) {
                            const userOnboardHistoryList = from[1].value.userOnboardHistoryList;
                            const index = Number(path.split('[')[1].split(']')[0]);
                            if (index === 0) return true;
                            let hasErrorDay = true;
                            userOnboardHistoryList.forEach((element: IUserOnboardHistory, i: number) => {
                                if (i === index) {
                                    const toDate = dateFormatComparison(userOnboardHistoryList[i - 1].toDate);
                                    const fromDate = dateFormatComparison(element.fromDate!);
                                    if (fromDate <= toDate) {
                                        hasErrorDay = false;
                                        return;
                                    }
                                }
                            });
                            return hasErrorDay;
                        }),
                    toDate: yup
                        .date()
                        .nullable()
                        .test('different-from-fromDate', VALIDATE_MESSAGES.ENDDATE, function (value) {
                            const fromDate = this.resolve(yup.ref('fromDate'));
                            return !value || !fromDate || !moment(value).isSame(fromDate, 'day');
                        })
                        .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                            const fromDate = this.resolve(yup.ref('fromDate'));
                            return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
                        })
                        .test('date-toDate-one', VALIDATE_MESSAGES.REQUIRED, function (value, { from, path }: any) {
                            const userOnboardHistoryList = from[1].value.userOnboardHistoryList;
                            const currentIndex = parseInt(path.split('[')[1]);
                            if (userOnboardHistoryList.length > 1 && currentIndex !== userOnboardHistoryList.length - 1 && !value) {
                                return false;
                            }
                            return true;
                        })
                        .required(VALIDATE_MESSAGES.REQUIRED)
                        .typeError(VALIDATE_MESSAGES.REQUIRED),
                    officialOnboardDate: yup.date().nullable()
                })
            )
        })
        .of(
            yup.object().shape({
                contractor: yup.string().nullable().typeError(VALIDATE_MESSAGES.REQUIRED),
                fromDate: yup
                    .date()
                    .nullable()
                    .required(VALIDATE_MESSAGES.REQUIRED)
                    .typeError(VALIDATE_MESSAGES.REQUIRED)
                    .test('date-fromDate', VALIDATE_MESSAGES.ABOUT_DAYS, function (value, { from, path }: any) {
                        const userOnboardHistoryList = from[1].value.userOnboardHistoryList;
                        const index = Number(path.split('[')[1].split(']')[0]);
                        if (index === 0) return true;
                        let hasErrorDay = true;
                        userOnboardHistoryList.forEach((element: IUserOnboardHistory, i: number) => {
                            if (i === index) {
                                const toDate = dateFormatComparison(userOnboardHistoryList[i - 1].toDate);
                                const fromDate = dateFormatComparison(element.fromDate!);
                                if (fromDate <= toDate) {
                                    hasErrorDay = false;
                                    return;
                                }
                            }
                        });
                        return hasErrorDay;
                    }),
                toDate: yup
                    .date()
                    .nullable()
                    .test('different-from-fromDate', VALIDATE_MESSAGES.ENDDATE, function (value) {
                        const fromDate = this.resolve(yup.ref('fromDate'));
                        return !value || !fromDate || !moment(value).isSame(fromDate, 'day');
                    })
                    .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                        const fromDate = this.resolve(yup.ref('fromDate'));
                        return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
                    })
                    .test('date-toDate-one', VALIDATE_MESSAGES.REQUIRED, function (value, { from, path }: any) {
                        const userOnboardHistoryList = from[1].value.userOnboardHistoryList;
                        const currentIndex = parseInt(path.split('[')[1]);
                        if (userOnboardHistoryList.length > 1 && currentIndex !== userOnboardHistoryList.length - 1 && !value) {
                            return false;
                        }
                        return true;
                    })
                    .typeError(VALIDATE_MESSAGES.REQUIRED),
                officialOnboardDate: yup.date().nullable()
            })
        ),
    // title
    userRankHistoryList: yup
        .array()
        .when('isCustomer', {
            is: false,
            then: yup.array().of(
                yup.object().shape({
                    rankId: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                    titleCode: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                    timesheetEnough: yup.boolean().nullable(),
                    fromDate: yup
                        .date()
                        .nullable()
                        .required(VALIDATE_MESSAGES.REQUIRED)
                        .typeError(VALIDATE_MESSAGES.REQUIRED)
                        .test('date-fromDate', VALIDATE_MESSAGES.ABOUT_DAYS, function (value, { from, path }: any) {
                            const userRankHistoryList = from[1].value.userRankHistoryList;
                            const index = Number(path.split('[')[1].split(']')[0]);
                            if (index === 0) return true;
                            let hasErrorDay = true;
                            userRankHistoryList.forEach((element: IUserRankHistory, i: number) => {
                                if (i === index) {
                                    const toDate = dateFormatComparison(userRankHistoryList[i - 1].toDate);
                                    const fromDate = dateFormatComparison(element.fromDate!);
                                    if (fromDate <= toDate) {
                                        hasErrorDay = false;
                                        return;
                                    }
                                }
                            });
                            return hasErrorDay;
                        }),
                    toDate: yup
                        .date()
                        .nullable()
                        .test('different-from-fromDate', VALIDATE_MESSAGES.ENDDATE, function (value) {
                            const fromDate = this.resolve(yup.ref('fromDate'));
                            return !value || !fromDate || !moment(value).isSame(fromDate, 'day');
                        })
                        .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                            const fromDate = this.resolve(yup.ref('fromDate'));
                            return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
                        })
                        .test('date-toDate-one', VALIDATE_MESSAGES.REQUIRED, function (value, { from, path }: any) {
                            const userRankHistoryList = from[1].value.userRankHistoryList;
                            const currentIndex = parseInt(path.split('[')[1]);
                            if (userRankHistoryList.length > 1 && currentIndex !== userRankHistoryList.length - 1 && !value) {
                                return false;
                            }
                            return true;
                        })
                        .typeError(VALIDATE_MESSAGES.DATE_FORMAT)
                })
            )
        })
        .of(
            yup.object().shape({
                rankId: yup.string().nullable(),
                titleCode: yup.string().nullable(),
                fromDate: yup
                    .date()
                    .nullable()
                    .typeError(VALIDATE_MESSAGES.REQUIRED)
                    .test('date-fromDate', VALIDATE_MESSAGES.ABOUT_DAYS, function (value, { from, path }: any) {
                        const userRankHistoryList = from[1].value.userRankHistoryList;
                        const index = Number(path.split('[')[1].split(']')[0]);
                        if (index === 0) return true;
                        let hasErrorDay = true;
                        userRankHistoryList.forEach((element: IUserRankHistory, i: number) => {
                            if (i === index) {
                                const toDate = dateFormatComparison(userRankHistoryList[i - 1].toDate);
                                const fromDate = dateFormatComparison(element.fromDate!);
                                if (fromDate <= toDate) {
                                    hasErrorDay = false;
                                    return;
                                }
                            }
                        });
                        return hasErrorDay;
                    }),
                toDate: yup
                    .date()
                    .nullable()
                    .test('different-from-fromDate', VALIDATE_MESSAGES.ENDDATE, function (value) {
                        const fromDate = this.resolve(yup.ref('fromDate'));
                        return !value || !fromDate || !moment(value).isSame(fromDate, 'day');
                    })
                    .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                        const fromDate = this.resolve(yup.ref('fromDate'));
                        return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
                    })
                    .test('date-toDate-one', VALIDATE_MESSAGES.REQUIRED, function (value, { from, path }: any) {
                        const userRankHistoryList = from[1].value.userRankHistoryList;
                        const currentIndex = parseInt(path.split('[')[1]);
                        if (userRankHistoryList.length > 1 && currentIndex !== userRankHistoryList.length - 1 && !value) {
                            return false;
                        }
                        return true;
                    })
                    .typeError(VALIDATE_MESSAGES.DATE_FORMAT)
            })
        ),
    // billable
    userBillableHistoryList: yup.array().of(
        yup.object().shape({
            fromDate: yup
                .date()
                .nullable()
                .required(VALIDATE_MESSAGES.REQUIRED)
                .typeError(VALIDATE_MESSAGES.DATE_FORMAT)
                .test('date_of_existence', VALIDATE_MESSAGES.DATE_OF_EXISTENCE, function (value, { from, path }: any) {
                    const userBillableHistoryList = from[1].value.userBillableHistoryList;
                    const index = Number(path.split('[')[1].split(']')[0]);
                    if (index === 0) return true;
                    let hasErrorDay = true;
                    userBillableHistoryList.forEach((element: IUserBillableHistory, i: number) => {
                        if (i === index) {
                            const fromDate = dateFormatComparison(element.fromDate);
                            const toDate = dateFormatComparison(element.toDate);

                            for (let j = 0; j < i; j++) {
                                const prevFromDate = dateFormatComparison(userBillableHistoryList[j].fromDate);
                                const prevToDate = dateFormatComparison(userBillableHistoryList[j].toDate);

                                if (
                                    (fromDate >= prevFromDate && fromDate <= prevToDate) ||
                                    (fromDate < prevFromDate && toDate > prevToDate)
                                ) {
                                    hasErrorDay = false;
                                    return;
                                }
                            }
                        }
                    });

                    return hasErrorDay;
                }),
            toDate: yup
                .date()
                .nullable()
                .required(VALIDATE_MESSAGES.REQUIRED)
                .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                    const fromDate = this.resolve(yup.ref('fromDate'));
                    return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
                })
                .test('date_of_existence', VALIDATE_MESSAGES.DATE_OF_EXISTENCE, function (value, { from, path }: any) {
                    const userBillableHistoryList = from[1].value.userBillableHistoryList;
                    const index = Number(path.split('[')[1].split(']')[0]);
                    if (index === 0) return true;
                    let hasErrorDay = true;
                    userBillableHistoryList.forEach((element: IUserBillableHistory, i: number) => {
                        if (i === index) {
                            const fromDateNew = dateFormatComparison(element.fromDate!);
                            const toDateNew = dateFormatComparison(element.toDate!);

                            for (let j = 0; j < i; j++) {
                                const prevFromDate = dateFormatComparison(userBillableHistoryList[j].fromDate);
                                const prevToDate = dateFormatComparison(userBillableHistoryList[j].toDate);

                                if (
                                    (toDateNew >= prevFromDate && toDateNew <= prevToDate) ||
                                    (fromDateNew < prevFromDate && toDateNew > prevToDate)
                                ) {
                                    hasErrorDay = false;
                                    return;
                                }
                            }
                        }
                    });

                    return hasErrorDay;
                })
        })
    ),
    // project permission
    projectPermissionEntity: yup.object({
        type: yup.string().nullable(),
        assignedProjectList: yup
            .array()
            .of(
                yup.object().shape({
                    projectId: yup.number(),
                    projectName: yup.string()
                })
            )
            .nullable()
    })
});

// Manage project
export interface IProjectSearchConfig extends IPaginationParam {
    projectType: string;
    status: number | string;
    projectId: IOption | null;
    projectManager: IOption | null;
    projectAuthorization?: string;
}

export const projectSearchConfig: IProjectSearchConfig = {
    ...paginationParamDefault,
    status: '',
    projectType: '',
    projectId: null,
    projectManager: null
};

export const projectSearchSchema = yup.object().shape({
    status: yup
        .number()
        .transform((value) => (isNaN(value) || value === null || value === undefined ? null : value))
        .nullable(),
    projectType: yup.string(),
    projectId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable(),
    projectManager: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
});

export const quotaUpdateHistoryDefault: IQuotaUpdateHistory[] = [
    {
        id: {},
        projectId: null,
        totalQuota: null,
        updateDate: '',
        userUpdate: ''
    }
];

// Holiday
export interface IHolidaySearchConfig extends IPaginationParam {
    type: number | string;
}
export const holidaySearchConfig: IHolidaySearchConfig = {
    ...paginationParamDefault,
    type: ''
};

export const holidaySchema = yup.object().shape({
    status: yup.string()
});

export interface IProjectEditConfig {
    projectId?: number | null;
    projectName: string;
    departmentId: string;
    contractNo: string;
    billable: string;
    projectType: string;
    startDate: Date | null;
    endDate: Date | null;
    contractSize: number | null;
    licenseAmount: number | null;
    projectCostLimit: number | null;
    totalQuota: number | null;
    percentageComplete: any;
    userName: IOption | null;
    status: string;
    note: string;
    client?: string;
    technology?: string;
    desc?: string;
    domain?: string;
    effort?: string;
}

export const ormReportSchema = yup.object().shape({
    department: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    reportName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    year: yup.number().required(VALIDATE_MESSAGES.REQUIRED),
    month: yup.number().required(VALIDATE_MESSAGES.REQUIRED),
    file: yup.string().required(VALIDATE_MESSAGES.REQUIRED).matches(REGEX_CONSTANTS.REGEX_NAME_FILE, VALIDATE_MESSAGES.INVALID_NAME_FILE)
});

export const defaultFieldsUploadORM = {
    reportName: '',
    department: '',
    file: '',
    month: getCurrentMonth(),
    year: getCurrentYear()
};

export const projectEditSchema = yup.object().shape({
    projectId: yup.string().nullable(),
    projectName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    departmentId: yup.string().required(VALIDATE_MESSAGES.REQUIRED).nullable(),
    contractNo: yup.string().nullable(),
    billable: yup.string().required(VALIDATE_MESSAGES.REQUIRED).nullable(),
    projectType: yup.string().required(VALIDATE_MESSAGES.REQUIRED).nullable(),
    startDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    endDate: yup.date().min(yup.ref('startDate'), VALIDATE_MESSAGES.ENDDATE).nullable(),
    contractSize: yup
        .number()
        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .transform((_, val) => (val !== '' ? Number(val) : null))
        .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .nullable(),
    licenseAmount: yup
        .number()
        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .transform((_, val) => (val !== '' ? Number(val) : null))
        .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .nullable(),
    projectCostLimit: yup
        .number()
        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .transform((_, val) => (val !== '' ? Number(val) : null))
        .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .nullable(),
    totalQuota: yup
        .number()
        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .transform((_, val) => (val !== '' ? Number(val) : null))
        .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .test('decimal', VALIDATE_MESSAGES.ONE_DECIMAL, (value: any) => value === Math.round(value * 10) / 10)
        .nullable(),
    percentageComplete: yup
        .number()
        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
        .transform((_, val) => (val !== '' ? Number(val) : null))
        .min(0, ({ min }) => `${VALIDATE_MESSAGES.LARGER_OR_EQUAL} ${min}`)
        .max(100, ({ max }) => `${VALIDATE_MESSAGES.LESS_OR_EQUAL} ${max}`)
        .nullable(),
    userName: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable(),
    status: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    note: yup.string().nullable()
});

export interface ISaveOrUpdateProjectUserConfig {
    projectId: string;
    userId: IOption;
    roleId: string;
    memberCode: string;
    userName: string;
    projectName: string;
    projectType: string;
    fromDate: Date | null;
    toDate: Date | null;
    firstName: string;
    lastName: string;
}

export const saveOrUpdateProjectUserConfig: ISaveOrUpdateProjectUserConfig = {
    projectId: '',
    userId: DEFAULT_VALUE_OPTION_SELECT,
    roleId: '',
    memberCode: '',
    userName: '',
    projectName: '',
    projectType: '',
    fromDate: null,
    toDate: null,
    firstName: '',
    lastName: ''
};

export const saveOrUpdateProjectUserSchema = yup.object().shape({
    projectId: yup.string().nullable(),
    userId: yup
        .object()
        .shape({
            value: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
            label: yup.string()
        })
        .nullable(),
    roleId: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    memberCode: yup.string().nullable(),
    userName: yup.string().nullable(),
    projectName: yup.string(),
    projectType: yup.string(),
    fromDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    toDate: yup.date().min(yup.ref('fromDate'), VALIDATE_MESSAGES.ENDDATE).nullable(),
    firstName: yup.string().nullable(),
    lastName: yup.string().nullable()
});
// Manage holiday

export const holidayFormDefault: IHoliday = {
    idHexString: '',
    fromDate: null,
    toDate: null,
    type: '',
    note: '',
    dateCreate: '',
    userCreate: '',
    userUpdate: '',
    lastUpdate: ''
};

export const holidaySearchSchema = yup.object().shape({
    type: yup.string()
});

export interface ISaveOrUpdateHolidayConfig {
    toDate: Date | null;
    fromDate: Date | null;
    type: string;
    note: string;
}

export const saveOrUpdateHolidayConfig: ISaveOrUpdateHolidayConfig = {
    fromDate: null,
    toDate: null,
    type: '',
    note: ''
};

export const saveOrUpdateHolidaySchema = yup.object().shape({
    fromDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    toDate: yup
        .date()
        .nullable()
        .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
            const fromDate = this.resolve(yup.ref('fromDate'));
            return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
        })
        .required(VALIDATE_MESSAGES.REQUIRED),
    type: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    note: yup.string().required(VALIDATE_MESSAGES.REQUIRED).trim().min(1, VALIDATE_MESSAGES.REQUIRED)
});
// Manage config

export const configFormDefault: ISystemConfig = {
    idHexString: '',
    key: '',
    value: '',
    valueTypeDate: '',
    description: '',
    userCreate: '',
    userUpdate: '',
    lastUpdate: '',
    dateCreate: '',
    note: ''
};
export interface IUpdateSystemConfig {
    key: string;
    value: string;
    note?: string;
}

export const updateSystemConfig: IUpdateSystemConfig = {
    key: '',
    value: '',
    note: ''
};

export const updateSystemConfigSchema = yup.object().shape({
    key: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    value: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    note: yup.string()
});

// Manage rank
export const rankCostHistoryFormDefault: IRankCostHistory = {
    fromDate: null,
    toDate: null,
    amount: null
};

export const rankValueDefault: IRank = {
    rankId: '',
    rankName: '',
    description: '',
    created: '',
    creator: '',
    lastUpdate: '',
    userUpdate: '',
    rankCost: 0,
    rankCostHistoryList: rankCostHistoryFormDefault,
    idHexString: ''
};

export interface IEditRank {
    rankCostHistoryList: IRankCostHistory[];
}

export const editRankFormSchema = yup.object().shape({
    rankCostHistoryList: yup.array().of(
        yup.object().shape({
            amount: yup
                .number()
                .nullable()
                .required(VALIDATE_MESSAGES.REQUIRED)
                .typeError(VALIDATE_MESSAGES.REQUIRED)
                .min(0, VALIDATE_MESSAGES.POSITIVE_NUMBER),
            fromDate: yup
                .date()
                .nullable()
                .required(VALIDATE_MESSAGES.REQUIRED)
                .typeError(VALIDATE_MESSAGES.REQUIRED)
                .test('date-fromDate', VALIDATE_MESSAGES.ABOUT_DAYS, function (value, { from, path }: any) {
                    const rankCostHistoryList = from[1].value.rankCostHistoryList;
                    const index = Number(path.split('[')[1].split(']')[0]);
                    if (index === 0) return true;
                    let hasErrorDay = true;
                    rankCostHistoryList.forEach((element: IRankCostHistory, i: number) => {
                        if (i === index) {
                            const toDate = dateFormatComparison(rankCostHistoryList[i - 1].toDate);
                            const fromDate = dateFormatComparison(element.fromDate!);
                            if (fromDate <= toDate) {
                                hasErrorDay = false;
                                return;
                            }
                        }
                    });
                    return hasErrorDay;
                }),
            toDate: yup
                .date()
                .nullable()
                .test('different-from-fromDate', VALIDATE_MESSAGES.ENDDATE, function (value) {
                    const fromDate = this.resolve(yup.ref('fromDate'));
                    return !value || !fromDate || !moment(value).isSame(fromDate, 'day');
                })
                .test('toDate-after-fromDate', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                    const fromDate = this.resolve(yup.ref('fromDate'));
                    return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
                })
                .test('date-toDate-one', VALIDATE_MESSAGES.REQUIRED, function (value, { from, path }: any) {
                    const rankCostHistoryList = from[1].value.rankCostHistoryList;
                    const currentIndex = parseInt(path.split('[')[1]);
                    if (rankCostHistoryList.length > 1 && currentIndex !== rankCostHistoryList.length - 1 && !value) {
                        return false;
                    }
                    return true;
                })
                .typeError(VALIDATE_MESSAGES.REQUIRED)
        })
    )
});

// Manage special hours

export interface ISpecialHoursSearchConfig extends IPaginationParam {
    type: number | string;
    userIdHexString: IOption | null;
}
export const specialHoursSearchConfig: ISpecialHoursSearchConfig = {
    ...paginationParamDefault,
    type: '',
    userIdHexString: null
};
export interface ISaveOrUpdateSpecialHoursConfig {
    idHexString: string;
    userIdHexString: IOption | null;
    userName: string;
    fromDate: string | null;
    toDate: string | null;
    type: string;
    hourPerDay: string;
    note: string;
    totalHour?: string;
    created?: string;
    creator?: string;
    lastUpdate?: string;
    userUpdate?: string;
    lastName?: string;
    firstName?: string;
    memberCode?: string;
}

export const saveOrUpdateSpecialHoursConfig: ISaveOrUpdateSpecialHoursConfig = {
    idHexString: '',
    userIdHexString: null,
    userName: '',
    fromDate: '',
    toDate: '',
    type: '',
    hourPerDay: '',
    note: '',
    totalHour: '',
    created: '',
    creator: '',
    lastUpdate: '',
    userUpdate: '',
    lastName: '',
    firstName: '',
    memberCode: ''
};

export const saveOrUpdateSpecialHoursSchema = yup.object().shape({
    userIdHexString: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .required(VALIDATE_MESSAGES.REQUIRED),
    fromDate: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    toDate: yup
        .string()
        .test('is-greater-than-fromDate', VALIDATE_MESSAGES.ENDDATE, function (value) {
            const fromDate = this.parent.fromDate;
            return !value || !fromDate || moment(value).isSameOrAfter(fromDate, 'day');
        })
        .required(VALIDATE_MESSAGES.REQUIRED),
    type: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    note: yup.string().nullable(),
    hourPerDay: yup
        .mixed()
        .test('isNumber', VALIDATE_MESSAGES.NUMBER, (value) => {
            if (value === null || value === '') {
                return true;
            }
            return /^\d+\.?\d*$/.test(value);
        })
        .nullable()
        .required(VALIDATE_MESSAGES.REQUIRED)
});

// Manage group
export interface IGroupSearchConfig extends IPaginationParam {
    code: string;
    name: string;
}

export const groupSearchConfig: IGroupSearchConfig = {
    ...paginationParamDefault,
    code: '',
    name: ''
};

export const groupSearchSchema = yup.object().shape({
    code: yup.string(),
    name: yup.string()
});

export interface ISaveOrUpdateGroupConfig {
    groupId: string;
    groupName: string;
    note: string;
    groupType: string;
    idHexString?: string;
    functions?: string[];
}

export const saveOrUpdateGroupConfig: ISaveOrUpdateGroupConfig = {
    groupId: '',
    groupName: '',
    groupType: '',
    note: ''
};

export const saveOrUpdateGroupSchema = yup.object().shape({
    groupId: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    groupName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    groupType: yup.string().nullable(),
    note: yup.string().nullable()
});

//Email Config
export const addOrEditEmailConfigFormDefault: IEmailConfig = {
    emailType: '',
    emailCode: '',
    template: '',
    sendTo: [],
    sendCC: [],
    sendBCC: [],
    content: '',
    userName: '',
    subject: '',
    nameFile: '',
    status: '',
    timeSendMail: {
        day: '',
        hour: '',
        weekdays: ''
    }
};

export const addOrEditEmailConfigSchema = yup.object().shape({
    emailType: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    emailCode: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    sendTo: yup.array().nullable().of(yup.string()).required(VALIDATE_MESSAGES.REQUIRED).min(1, VALIDATE_MESSAGES.REQUIRED),
    sendCC: yup.array().nullable().of(yup.string()),
    sendBCC: yup.array().nullable().of(yup.string()),
    nameFile: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .matches(REGEX_CONSTANTS.REGEX_NAME_FILE, VALIDATE_MESSAGES.INVALID_NAME_FILE),
    timeSendMail: yup
        .object()
        .shape({
            day: yup.string(),
            hour: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
            weekdays: yup.string()
        })
        .when('emailType', {
            is: (emailType: string) => emailType === EMAIL_TYPE.RP_MONTH,
            then: yup.object().shape({
                day: yup.string(),
                hour: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
                weekdays: yup.string()
            })
        })
        .when('emailType', {
            is: (emailType: string) => emailType === EMAIL_TYPE.RP_WEEK,
            then: yup.object().shape({
                day: yup.string(),
                hour: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
                weekdays: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
            })
        }),
    template: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    subject: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    content: yup.string().required(VALIDATE_MESSAGES.REQUIRED).trim().min(1, VALIDATE_MESSAGES.REQUIRED),
    status: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED)
});

// cv

export const skillListFormDefault = {
    idHexString: '',
    name: '',
    type: '',
    userUpdate: '',
    lastUpdate: ''
};

export const addOrEditTechnologyConfigFormDefault: ITechnology = {
    techType: '',
    skillList: [skillListFormDefault],
    userUpdate: ''
};

export const addOrEditTechnologylConfigSchema = yup.object().shape({
    techType: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    skillList: yup.array().of(
        yup.object().shape({
            idHexString: yup.string().nullable(),
            name: yup
                .string()
                .nullable()
                .required(VALIDATE_MESSAGES.REQUIRED)
                .test('unique-skill-names', VALIDATE_MESSAGES.SKILL_CV_EXIST, function (value, { from, path }: any) {
                    const fromValue = from[1].value.skillList;
                    const index = Number(path.split('[')[1].split(']')[0]);
                    const currentSkillName = value;
                    const seenNames = new Set();

                    for (let i = 0; i < fromValue.length; i++) {
                        if (i !== index) {
                            const name = fromValue[i].name;
                            if (name === currentSkillName) {
                                return false;
                            }
                            seenNames.add(name);
                        }
                    }
                    return true;
                }),

            type: yup.string().nullable(),
            userUpdate: yup.string().nullable(),
            lastUpdate: yup.string().nullable()
        })
    ),
    userUpdate: yup.string().nullable()
});

export const addOrEditLanguageConfigFormDefault: ILanguage = {
    name: ''
};

export const addOrEditLanguagelConfigSchema = yup.object().shape({
    name: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED)
});

export const addOrEditReferenceConfigFormDefault: IReference = {
    fullName: '',
    organization: '',
    position: '',
    address: '',
    phoneNumber: '',
    email: ''
};

export const addOrEditReferenceConfigSchema = yup.object().shape({
    fullName: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    organization: yup.string().nullable(),
    position: yup.string().nullable(),
    address: yup.string().nullable(),
    phoneNumber: yup.string().nullable(),
    email: yup.lazy((value) => {
        if (value) {
            return yup.string().required(VALIDATE_MESSAGES.REQUIRED).email(VALIDATE_MESSAGES.INVALID_EMAIL);
        }
        return yup.string().nullable();
    })
});

// exchangeRate config
export const addOrEditExchangeRateConfigFormDefault: IExchangeRate = {
    year: null,
    currency: '',
    exchangeRate: 0
};

export const addOrEditExchangeRateConfigSchema = yup.object().shape({
    year: yup.number().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    currency: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    exchangeRate: yup.number().nullable().required(VALIDATE_MESSAGES.REQUIRED)
});

// flexible report column config
export const searchColumnConfigFormDefault: ISearchColumnConfigParams = {
    page: 1,
    size: 10,
    flexibleReportId: '',
    flexibleColumnName: null
};

export const flexibleReportingConfigSchema = yup.object().shape({
    flexibleReportId: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    flexibleColumnName: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
});

export const editColumnConfigSchema = yup.object().shape({
    id: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
        .required(VALIDATE_MESSAGES.REQUIRED),
    isCalculate: yup.boolean().nullable(),
    isPercentage: yup.boolean().nullable(),
    inputType: yup.string().nullable(),
    note: yup.string().nullable()
});

// flexible report text config
export const searchTextConfigFormDefault: ISearchTextConfigParams = {
    page: 1,
    size: 10,
    flexibleReportId: '',
    textName: '',
    code: 'en'
};

export const flexibleReportingTextConfigSchema = yup.object().shape({
    flexibleReportId: yup.string(),
    textName: yup.string()
});

export const addLanguageSchema = yup.object().shape({
    language: yup.object().shape({
        value: yup.string().required(VALIDATE_MESSAGES.REQUIRED).typeError(VALIDATE_MESSAGES.REQUIRED),
        label: yup.string().required(VALIDATE_MESSAGES.REQUIRED).typeError(VALIDATE_MESSAGES.REQUIRED)
    })
});

export const exchangeRateSearchConfig: IExchangeRate = {
    ...paginationParamDefault,
    year: getCurrentYear(),
    currency: ''
};

export const exchangeRateSearchSchema = yup.object().shape({
    year: yup.string(),
    currency: yup.string()
});

// Leave Day
export interface ILeaveDaySearchConfig extends IPaginationParam {
    idHexString: IOption | null;
}

export const leaveDaySearchConfig: ILeaveDaySearchConfig = {
    ...paginationParamDefault,
    idHexString: null
};

export const leaveDaySearchSchema = yup.object().shape({
    idHexString: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
});

export interface IDepartmentFilterConfig extends IPaginationParam {
    deptId: string;
}

export const departmentFilterConfig: IDepartmentFilterConfig = {
    deptId: '',
    page: 1,
    size: 10
};

export const addProjectReportSchemaTime = yup.object().shape({
    year: yup.number().required(VALIDATE_MESSAGES.REQUIRED),
    month: yup.number().required(VALIDATE_MESSAGES.REQUIRED)
});
export const createOrEditDepartmentSchema = yup.object().shape({
    deptId: yup.string().required(VALIDATE_MESSAGES.REQUIRED).matches(REGEX_CONSTANTS.REGEX_NAME_CODE, VALIDATE_MESSAGES.INVALID_CODE_NAME),
    deptName: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
});

export interface IProjectTypeFilterConfig extends IPaginationParam {
    typeCode: string;
}

export const projectTypeFilterConfig: IProjectTypeFilterConfig = {
    typeCode: '',
    page: 1,
    size: 10
};

export const createOrEditProjectTypeSchema = yup.object().shape({
    billable: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    typeCode: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .matches(REGEX_CONSTANTS.REGEX_NAME_CODE, VALIDATE_MESSAGES.INVALID_CODE_NAME),
    projectTypeName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    colorCode: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
});

export interface ITitleFilterConfig extends IPaginationParam {
    titleCode: string;
}

export const titleFilterConfig: ITitleFilterConfig = {
    titleCode: '',
    page: 1,
    size: 10
};

export const createOrEditTitleSchema = yup.object().shape({
    titleCode: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .matches(REGEX_CONSTANTS.REGEX_NAME_CODE, VALIDATE_MESSAGES.INVALID_CODE_NAME),
    titleName: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
});

export const editNonBillablesConfigSchema = yup.object().shape({
    name: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    maxValue: yup
        .string()
        .matches(REGEX_CONSTANTS.REGEX_NUMBER_AND_DECIMAL, VALIDATE_MESSAGES.NUMBER)
        .nullable()
        .transform((value, originalValue) => (originalValue === '' ? null : value))
        .test('is-larger-than-min', VALIDATE_MESSAGES.LARGER_OR_EQUAL, function (value) {
            const { minValue } = this.parent;
            if (value && minValue) {
                return parseFloat(value) >= parseFloat(minValue);
            }
            return true;
        })
        .required(VALIDATE_MESSAGES.REQUIRED),
    minValue: yup
        .string()
        .matches(REGEX_CONSTANTS.REGEX_NUMBER_AND_DECIMAL, VALIDATE_MESSAGES.NUMBER)
        .nullable()
        .transform((value, originalValue) => (originalValue === '' ? null : value))
        .test('is-less-than-max', VALIDATE_MESSAGES.LESS_OR_EQUAL, function (value) {
            const { maxValue } = this.parent;
            if (value && maxValue) {
                return parseFloat(value) <= parseFloat(maxValue);
            }
            return true;
        })
        .required(VALIDATE_MESSAGES.REQUIRED),
    key: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    projectType: yup.string().nullable(),
    note: yup.string().nullable(),
    color: yup.string().nullable()
});

export const nonBillableConfig: IPaginationParam = {
    page: 1,
    size: 10
};

export interface IFlexibleReportTableConfig {
    reportName: string;
    textName: string;
    page: number;
    size: number;
}

export const flexibleReportingConfigSearchDefault: FlexibleReportSearchConfig = {
    page: 1,
    size: 10,
    reportId: '',
    textName: ''
};

export const flexibleReportingConfigSearchSchema = yup.object().shape({
    flexibleReportId: yup.string(),
    flexibleColumnId: yup.string()
});

export const addOrEditFlexibleReportConfigSchema = yup.object().shape({
    reportId: yup
        .object()
        .shape({
            value: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
            label: yup.string()
        })
        .required(VALIDATE_MESSAGES.REQUIRED),
    defaultTextNameENG: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    note: yup.string().nullable(),
    layout: yup.string().nullable(),
    conditions: yup
        .array()
        .of(
            yup
                .array()
                .of(
                    yup.object().shape({
                        code: yup.string(),
                        compare: yup.string(),
                        type: yup.string().oneOf(['number', 'text', 'select', 'date']).required(),
                        conditionSelecteted: yup.boolean(),
                        isPercentage: yup.boolean(),
                        value:
                            // number  isPercentage
                            yup.mixed().when(['type', 'isPercentage', 'conditionSelecteted', 'minValue', 'maxValue'], {
                                is: (
                                    type: string,
                                    isPercentage: boolean,
                                    conditionSelecteted: boolean,
                                    minValue: number,
                                    maxValue: number
                                ) => type === 'number' && isPercentage && conditionSelecteted && !minValue && !maxValue,
                                then: yup
                                    .number()
                                    .typeError(VALIDATE_MESSAGES.REQUIRED)
                                    .transform((_, val) => (val !== '' ? Number(val) : null))
                                    .min(0, ({ min }) => `${VALIDATE_MESSAGES.LARGER_OR_EQUAL} ${min}`)
                                    .max(100, ({ max }) => `${VALIDATE_MESSAGES.LESS_OR_EQUAL} ${max}`),
                                //  not number isPercentage
                                otherwise: yup.mixed().when(['type', 'conditionSelecteted', 'isPercentage', 'minValue', 'maxValue'], {
                                    is: (
                                        type: string,
                                        conditionSelecteted: boolean,
                                        isPercentage: boolean,
                                        minValue: number,
                                        maxValue: number
                                    ) => type === 'number' && conditionSelecteted && !isPercentage && !minValue && !maxValue,
                                    then: yup.number().typeError(VALIDATE_MESSAGES.REQUIRED),
                                    otherwise: yup.mixed().when(['type', 'conditionSelecteted'], {
                                        is: (type: string, conditionSelecteted: boolean) => type === 'select' && conditionSelecteted,
                                        then: yup.array().of(yup.string()).min(1, VALIDATE_MESSAGES.REQUIRED),
                                        otherwise: yup.mixed().when(['type', 'conditionSelecteted', 'minValue', 'maxValue'], {
                                            is: (type: string, conditionSelecteted: boolean, minValue: number, maxValue: number) =>
                                                type === 'date' && conditionSelecteted && !minValue && !maxValue,
                                            then: yup.date().required(VALIDATE_MESSAGES.REQUIRED),
                                            otherwise: yup.mixed().when(['type', 'conditionSelecteted'], {
                                                is: (type: string, conditionSelecteted: boolean) => type === 'text' && conditionSelecteted,
                                                then: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
                                            })
                                        })
                                    })
                                })
                            }),

                        minValue: yup
                            .mixed()
                            .when(['type', 'isPercentage', 'conditionSelecteted'], {
                                is: (type: string, isPercentage: boolean, conditionSelecteted: boolean) =>
                                    type === 'number' && isPercentage && conditionSelecteted,
                                then: yup
                                    .number()
                                    .typeError(VALIDATE_MESSAGES.REQUIRED)
                                    .transform((_, val) => (val !== '' ? Number(val) : null))
                                    .min(0, ({ min }) => `${VALIDATE_MESSAGES.LARGER_OR_EQUAL} ${min}`)
                                    .max(100, ({ max }) => `${VALIDATE_MESSAGES.LESS_OR_EQUAL} ${max}`),
                                //  not number isPercentage
                                otherwise: yup.mixed().when(['type', 'conditionSelecteted', 'isPercentage'], {
                                    is: (type: string, conditionSelecteted: boolean, isPercentage: boolean) =>
                                        type === 'number' && conditionSelecteted && !isPercentage,
                                    then: yup.number().typeError(VALIDATE_MESSAGES.REQUIRED)
                                })
                            })
                            .test('is-less-than-max', VALIDATE_MESSAGES.LESS_THAN, function (value) {
                                const { maxValue, type } = this.parent;
                                if (type === 'date' && value && maxValue) {
                                    return new Date(value) < new Date(maxValue);
                                } else if (value && maxValue && type === 'number') {
                                    return parseFloat(value) < parseFloat(maxValue);
                                }
                                return true;
                            }),

                        maxValue: yup
                            .mixed()
                            .when(['type', 'isPercentage', 'conditionSelecteted'], {
                                is: (type: string, isPercentage: boolean, conditionSelecteted: boolean) =>
                                    type === 'number' && isPercentage && conditionSelecteted,
                                then: yup
                                    .number()
                                    .typeError(VALIDATE_MESSAGES.REQUIRED)
                                    .transform((_, val) => (val !== '' ? Number(val) : null))
                                    .min(0, ({ min }) => `${VALIDATE_MESSAGES.LARGER_OR_EQUAL} ${min}`)
                                    .max(100, ({ max }) => `${VALIDATE_MESSAGES.LESS_OR_EQUAL} ${max}`),
                                //  not number isPercentage
                                otherwise: yup.mixed().when(['type', 'conditionSelecteted', 'isPercentage'], {
                                    is: (type: string, conditionSelecteted: boolean, isPercentage: boolean) =>
                                        type === 'number' && conditionSelecteted && !isPercentage,
                                    then: yup.number().typeError(VALIDATE_MESSAGES.REQUIRED)
                                })
                            })
                            .test('is-larger-than-min', VALIDATE_MESSAGES.LARGER_THAN, function (value) {
                                const { minValue, type, isPercentage } = this.parent;
                                if (type === 'date' && value && minValue) {
                                    return new Date(value) > new Date(minValue);
                                } else if (value && minValue && type === 'number' && isPercentage) {
                                    return parseFloat(value) > parseFloat(minValue);
                                }
                                return true;
                            })
                    })
                )
                .typeError('Please set conditions when you add by Button Or')
                .transform((value) => (value === null ? [] : value))
        )
        .nullable(),

    calculationInputs: yup.array().of(
        yup
            .object()
            .shape({
                sign: yup.string(),
                code: yup
                    .object()
                    .shape({
                        value: yup.string(),
                        label: yup.string()
                    })
                    .required(VALIDATE_MESSAGES.REQUIRED)
            })
            .nullable()
    ),
    otherDataSource: yup.mixed().when(['selectMultipleReport'], {
        is: (selectMultipleReport: boolean) => selectMultipleReport,
        then: yup.array().of(yup.string()).min(1, VALIDATE_MESSAGES.REQUIRED).required(VALIDATE_MESSAGES.REQUIRED)
    }),
    newText: yup.array().of(yup.string()),
    isCalculation: yup.boolean(),
    calculationInputNames: yup.string(),
    selectMultipleReport: yup.boolean(),
    show: yup.boolean()
});

export const flexibleReportConfigDetail: IFlexibleReports = {
    id: '',
    reportId: {
        label: '',
        value: ''
    },
    defaultTextNameVN: '',
    reportName: '',
    defaultTextNameENG: '',
    textNameVN: '',
    textNameENG: '',
    layout: 'Top',
    note: '',
    conditions: [[]],
    calculationInputs: [{}],
    isCalculation: true,
    calculationInputNames: '',
    otherDataSource: [],
    selectMultipleReport: false,
    style: {
        backgroundColor: '#ffffff',
        color: '#000000',
        fontStyle: 'none',
        fontWeight: 'none',
        textDecoration: 'none'
    },
    show: false,
    newText: [],
    languageConfigs: []
};

export const editTexSchema = yup.object().shape({
    languageCode: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    newText: yup.string().nullable(),
    note: yup.string().nullable()
});
export const editTextConfigSchema = yup.object().shape({
    id: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    flexibleReportId: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    textNameENG: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    textNameVN: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    note: yup.string().nullable()
});
