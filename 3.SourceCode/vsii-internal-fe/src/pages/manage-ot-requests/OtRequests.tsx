/* eslint-disable prettier/prettier */
import { useEffect, useState } from 'react';

// third party
import { yupResolver } from '@hookform/resolvers/yup';
import { useSearchParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

// project imports
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { ManageOTSearch, ManageOTTBody, ManageOTTHead } from 'containers/working-calendar';
import { getSearchParam, isEmpty, transformObject } from 'utils/common';
import { IOTItem, IOTList } from 'types/working-calendar';
import { IPaginationResponse, IResponseList } from 'types';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { FilterCollapse } from 'containers/search';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { PERMISSIONS } from 'constants/Permission';
import { useAppDispatch } from 'app/hooks';
import MainCard from 'components/cards/MainCard';
import { TableToolbar } from 'containers';
import sendRequest from 'services/ApiService';
import { dateFormat } from 'utils/date';
import Api from 'constants/Api';
import {
    IManageOtDefaultValues,
    IOvertimeReport,
    IUserDetail,
    manageOtDefaultValues,
    overtimeReportConfig,
    overtimeReportSchema
} from './Config';
import AddOrEditOvertimeReportModal from 'containers/working-calendar/AddOrEditOvertimeReportModal';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { ILeaveRequestData, rejectLeaveRequestSchema } from 'pages/manage-leaves/Config';
import { rejectLeaveRequestConfig } from 'pages/manage-leaves/Config';
import RejectLeaveModal, { IRejectLeaveFormValues } from 'containers/working-calendar/RejectLeaveRequest';

// ==============================|| Manage Ot ||============================== //
/**
 * URL Params
 * page
 * size
 * year
 * fullname
 * idHexstring
 * type
 * status
 */
const ManageOt = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.idHexString,
        SEARCH_PARAM_KEY.fullname,
        SEARCH_PARAM_KEY.type,
        SEARCH_PARAM_KEY.fromDate,
        SEARCH_PARAM_KEY.toDate,
        SEARCH_PARAM_KEY.dept,
        SEARCH_PARAM_KEY.status
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, idHexString, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...manageOtDefaultValues,
        ...cloneParams,
        memberId: params.idHexString ? { value: params.idHexString, label: params.fullname } : null
    };
    const dispatch = useAppDispatch();
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [otList, setOtList] = useState<IOTItem[]>([]);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [userDetail, setUserDetail] = useState<IUserDetail | null>(null);
    const [conditions, setConditions] = useState<IManageOtDefaultValues>(defaultConditions);
    const [formReset] = useState<IManageOtDefaultValues>(defaultConditions);
    const [statusOvertimeTicket, setStatusOvertimeTicket] = useState<string>('');
    const { manageOt } = PERMISSIONS.workingCalendar;
    const [isRejectModalOpen, setIsRejectModalOpen] = useState<boolean>(false);
    const [otItem, setOtItem] = useState<IOTItem>();
    const [rejectData, setRejectData] = useState<any>(null);
    const [editData, setEditData] = useState<IOvertimeReport | null>(null);
    const { manage_ot } = TEXT_CONFIG_SCREEN.workingCalendar;
    // ================= Use form =================

    const rejectLeaveFormReturn = useForm<IRejectLeaveFormValues>({
        defaultValues: rejectLeaveRequestConfig,
        resolver: yupResolver(rejectLeaveRequestSchema)
    });

    // ================= Functions =================
    const setDataEmpty = () => {
        setOtList([]);
        setIsLoading(false);
    };

    // Get ot
    const getOT = async () => {
        setIsLoading(true);
        const response: IResponseList<IOTList> = await sendRequest(Api.manage_ot.getAll, {
            ...conditions,
            fromDate: conditions.fromDate ? dateFormat(conditions.fromDate) : null,
            toDate: conditions.toDate ? dateFormat(conditions.toDate) : null,
            page: conditions.page + 1,
            memberId: conditions.memberId ? conditions.memberId.value : null
        });

        if (response) {
            const { status, result } = response;
            if (status) {
                const { content, pagination } = result;
                if (!isEmpty(content)) {
                    setOtList(content);
                    setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                    setIsLoading(false);
                } else {
                    setDataEmpty();
                }
            }
        } else {
            setDataEmpty();
        }
    };

    // ================= Event =================
    // Handle change page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    // Handle change rows per page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };
    const getUserDetail = async () => {
        const response = await sendRequest(Api.manage_leaves.getDetailUser);
        if (response?.status) {
            const content = response?.result?.content;
            if (!isEmpty(content)) {
                setUserDetail(content);
            }
        } else {
            setDataEmpty();
        }
    };

    const handleSubmit = () => {};

    // Handle add or edit Ot
    const handleAddOrEdit = async (item?: IOTItem) => {
        if (item) {
            setOtItem(item);
            setStatusOvertimeTicket(item.status);
            setIsEdit(true);
            const response = await sendRequest(Api.overtime_report.getDetailOvertimeTicket(item.id));
            if (response?.status && response.result?.content) {
                const data = response.result.content;
                const formData = {
                    idHexString: data.idHexString,
                    userIdHexString: data.userIdHexString,
                    fullName: data.fullName,
                    department: data.department,
                    manager: data.managerIdHexString,
                    nextManager: data.nextManagerIdHexString || '',
                    overtimeReason: data.overTimeType,
                    detail: data.detailReason,
                    otherReason: data.otherReason,
                    totalHours: data.totalHours?.toString() || '0',
                    totalCompensateHours: data.hoursCompensation?.toString() || '0',
                    equivalentDays: data.daysCompensation?.toString() || '0',
                    compensateType: data.compensationLeaveType,
                    compensateFrom: data.leaveFrom ? new Date(data.leaveFrom) : null,
                    compensateTo: data.leaveTo ? new Date(data.leaveTo) : null,
                    projectName: {
                        value: data.projectId,
                        label: data.projectName
                    },
                    status: data.status,
                    overtimeRecords:
                        data.overTimeHistory?.map((record: any) => ({
                            overtimeFrom: new Date(record.fromDate),
                            overtimeTo: new Date(record.toDate),
                            overtimeHours: record.hours?.toString() || '0'
                        })) || []
                };
                setEditData(formData);
            }
            setIsOpen(true);
        } else {
            const formData = {
                ...overtimeReportConfig,
                fullName: userDetail?.fullName || '',
                department: userDetail?.department || '',
                userIdHexString: userDetail?.idHexString || ''
            };
            setEditData(formData);
            setIsOpen(true);
        }
    };

    // Handle close form add or edit ot
    const handleCloseFormAddOrEdit = () => {
        setIsEdit(false);
        setIsOpen(false);
    };

    const handleDelete = (idHexString: string) => {
        dispatch(
            openConfirm({
                title: <FormattedMessage id={manage_ot + 'delete'} />,
                content: <FormattedMessage id={manage_ot + 'delete-ot-request'} />,
                handleConfirm: async () => {
                    const response = await sendRequest(Api.manage_ot.deleteOT(idHexString));
                    dispatch(
                        openSnackbar({
                            open: true,
                            message: response?.status ? response.result.content : response?.result?.content || 'Error',
                            variant: 'alert',
                            alert: { color: response?.status ? 'success' : 'error' }
                        })
                    );
                    response?.status && (await getOT());
                    dispatch(closeConfirm());
                },
                width: 400
            })
        );
    };

    // Handle open reject modal
    const handleOpenRejectModal = (data: ILeaveRequestData) => {
        setRejectData(data);
        setIsRejectModalOpen(true);
    };

    // Handle close reject modal
    const handleCloseRejectModal = () => {
        setIsRejectModalOpen(false);
        setRejectData(null);
    };

    // ================= Handle search =================
    const handleSearch = (values: any) => {
        transformObject(values);
        const { memberId, ...cloneValues } = values;
        const parameters = memberId
            ? {
                  ...cloneValues,
                  idHexString: memberId.value,
                  fullname: memberId.label,
                  fromDate: dateFormat(values.fromDate),
                  toDate: dateFormat(values.toDate)
              }
            : {
                  ...cloneValues,
                  fromDate: dateFormat(values.fromDate),
                  toDate: dateFormat(values.toDate)
              };
        setSearchParams(parameters);
        setConditions({ ...values });
    };
    // ================= Effect =================
    useEffect(() => {
        getUserDetail();
    }, []);
    useEffect(() => {
        getOT();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse title={manage_ot + 'filters'}>
                <ManageOTSearch formReset={formReset} handleSearch={handleSearch} />
            </FilterCollapse>

            {/* Ot list */}
            <MainCard>
                <TableToolbar handleOpen={checkAllowedPermission(manageOt.add) ? handleAddOrEdit : undefined} />
                <Table heads={<ManageOTTHead />} isLoading={isLoading} data={otList}>
                    <ManageOTTBody
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        otList={otList}
                        handleEdit={handleAddOrEdit}
                        handleDelete={handleDelete}
                    />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!isLoading && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {/* Form Add or Edit Ot */}
            {isOpen && (
                <AddOrEditOvertimeReportModal
                    isOpen={isOpen}
                    onClose={handleCloseFormAddOrEdit}
                    onSubmit={handleSubmit}
                    defaultValues={editData || overtimeReportConfig}
                    isEdit={isEdit}
                    statusOverTimeTicket={statusOvertimeTicket}
                    onReject={handleOpenRejectModal}
                    onSuccess={getOT}
                />
            )}
            {/* Reject Modal */}
            {isRejectModalOpen && rejectData && (
                <RejectLeaveModal
                    isOpen={isRejectModalOpen}
                    onClose={handleCloseRejectModal}
                    data={rejectData}
                    formReturn={rejectLeaveFormReturn}
                    onSuccess={getOT}
                    leaveIdHexString={otItem?.id}
                    apiType="ot"
                />
            )}
        </>
    );
};

export default ManageOt;
