// project imports
import { EApproveStatus, paginationParamDefault } from 'constants/Common';
import { VALIDATE_MESSAGES } from 'constants/Message';
import moment from 'moment';
import { IOption, IPaginationParam } from 'types';

// third party
import * as yup from 'yup';

// ============== Manage OT ============== //

// Filter
export interface IManageOtDefaultValues extends IPaginationParam {
    memberId: IOption | null;
    fromDate: Date | null;
    toDate: Date | null;
    type: string;
    dept: string;
    status: string;
}

export const manageOtDefaultValues: IManageOtDefaultValues = {
    ...paginationParamDefault,
    memberId: null,
    fromDate: null,
    toDate: null,
    type: '',
    dept: '',
    status: ''
};

export const manageOtSearchSchema = yup.object().shape({
    fromDate: yup.date().nullable(),
    toDate: yup.date().nullable().min(yup.ref('fromDate'), VALIDATE_MESSAGES.ENDDATE),
    type: yup.string().nullable(),
    status: yup.string().nullable()
});

// Add or edit Overtime Report

export interface IOvertimeRecord {
    overtimeFrom: Date | null;
    overtimeTo: Date | null;
    overtimeHours: number | string;
}

export interface IOvertimeReport {
    idHexString: string;
    userIdHexString: string;
    fullName: string;
    department: string;
    manager: string;
    nextManager: string;
    overtimeReason: string;
    projectName: IOption | null;
    detail: string;
    otherReason: string;
    overtimeRecords: IOvertimeRecord[];
    totalHours: number | string;
    totalCompensateHours: number | string;
    equivalentDays: number | string;
    compensateType: string;
    compensateFrom: Date | null;
    compensateTo: Date | null;
    status: string;
    fromDate?: Date | null;
    toDate?: Date | null;
}

export const overtimeReportConfig: IOvertimeReport = {
    idHexString: '',
    userIdHexString: '',
    fullName: '',
    department: '',
    manager: '',
    nextManager: '',
    overtimeReason: '',
    projectName: null,
    detail: '',
    otherReason: '',
    overtimeRecords: [
        {
            overtimeFrom: new Date(),
            overtimeTo: new Date(),
            overtimeHours: ''
        }
    ],
    totalHours: '',
    totalCompensateHours: '',
    equivalentDays: '',
    compensateType: '',
    compensateFrom: new Date(),
    compensateTo: new Date(),
    status: ''
};

export const overtimeReportSchema = yup.object().shape({
    fullName: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    department: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    manager: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    nextManager: yup.string().test('nextManager-validation', VALIDATE_MESSAGES.REQUIRED, function (value) {
        const { status, userIdHexString } = this.parent;
        const { currentUserId } = this.options.context || {};
        const isCreator = userIdHexString === currentUserId;
        const shouldValidate = status === EApproveStatus.AWAITING_QLTT && !isCreator;

        return shouldValidate ? !!value : true;
    }),
    overtimeReason: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    projectName: yup.mixed().when('overtimeReason', {
        is: 'overtime-reason-1',
        then: (schema) => schema.required(VALIDATE_MESSAGES.REQUIRED),
        otherwise: (schema) => schema.nullable()
    }),
    detail: yup.string(),
    otherReason: yup.string(),
    overtimeRecords: yup.array().of(
        yup.object().shape({
            overtimeFrom: yup.date().required(VALIDATE_MESSAGES.REQUIRED),
            overtimeTo: yup
                .date()
                .required(VALIDATE_MESSAGES.REQUIRED)
                .test('is-after', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                    const { overtimeFrom } = this.parent;
                    const sameOrAfter = moment(value).startOf('day') >= moment(overtimeFrom).startOf('day');
                    return !value || !overtimeFrom || sameOrAfter;
                }),
            overtimeHours: yup
                .number()
                .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
                .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)
                .required(VALIDATE_MESSAGES.REQUIRED)
        })
    ),
    totalHours: yup
        .number()
        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .required(VALIDATE_MESSAGES.REQUIRED),
    totalCompensateHours: yup
        .number()
        .typeError(VALIDATE_MESSAGES.INVALID_NUMBER)
        .positive(VALIDATE_MESSAGES.POSITIVE_NUMBER)
        .required(VALIDATE_MESSAGES.REQUIRED),
    equivalentDays: yup.number().typeError(VALIDATE_MESSAGES.INVALID_NUMBER).required(VALIDATE_MESSAGES.REQUIRED),
    compensateType: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    compensateFrom: yup.date().when('compensateType', {
        is: 'overtime-compensate-1',
        then: (schema) => schema.required(VALIDATE_MESSAGES.REQUIRED),
        otherwise: (schema) => schema.nullable()
    }),
    compensateTo: yup.date().when('compensateType', {
        is: 'overtime-compensate-1',
        then: (schema) =>
            schema.required(VALIDATE_MESSAGES.REQUIRED).test('is-after', VALIDATE_MESSAGES.AFTER_DAY, function (value) {
                const { compensateFrom } = this.parent;
                const sameOrAfter = moment(value).startOf('day') >= moment(compensateFrom).startOf('day');
                return !value || !compensateFrom || sameOrAfter;
            }),
        otherwise: (schema) => schema.nullable()
    })
});

export interface IUserDetail {
    idHexString: string;
    fullName: string;
    memberCode: string;
    position: string;
    department: string;
    group: string;
    typeContract: string;
}
