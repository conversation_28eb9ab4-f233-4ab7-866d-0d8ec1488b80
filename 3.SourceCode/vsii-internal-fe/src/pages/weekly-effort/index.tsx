import { SyntheticEvent, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';

import { getWeeklyEffortProjectDetail, getWeeklyEffortProjectOption } from 'store/slice/weeklyEffortSlice';
import { checkAllowedPermission, checkAllowedTab } from 'utils/authorization';
import { SEARCH_PARAM_KEY, weeklyEffortReportTabs } from 'constants/Common';
import { convertWeekFromToDate, getWeeksPeriodsInYear } from 'utils/date';
import { openDeniedPermission } from 'store/slice/deniedPermissionSlice';
import { IWeeklyEffortConfig, weeklyEffortConfig } from '../Config';
import { getSearchParam, transformObject } from 'utils/common';
import { GetWeeklyEffortRequest, IOption } from 'types';
import WeeklyEffortProjectDetail from './ProjectDetail';
import { TabPanel } from 'components/extended/Tabs';
import WeeklyEffortProjects from './Projects';
import { useAppDispatch } from 'app/hooks';
import WeeklyEffortMember from './Member';
import { TabCustom } from 'containers';

// ==============================|| Weekly Effort ||============================== //
/**
 *  URL Params
 *  tab
 *  page
 *  size
 *  year
 *  week
 *  ====== tab 0 - Member ======
 *  timeStatus
 *  userId
 *  fullname
 *  ====== tab 1 - Project ======
 *  projectId
 *  projectName
 */

const keyParamsArray = [SEARCH_PARAM_KEY.timeStatus];

const WeeklyEffort = () => {
    const [weeks, setWeeks] = useState<IOption[]>([]);

    const [searchParams, setSearchParams] = useSearchParams();

    const keyParams = [
        SEARCH_PARAM_KEY.tab,
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.week,
        SEARCH_PARAM_KEY.userId,
        SEARCH_PARAM_KEY.fullname,
        SEARCH_PARAM_KEY.projectName,
        SEARCH_PARAM_KEY.projectId
    ];

    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams, keyParamsArray);

    transformObject(params);

    const { fullname, projectName, ...cloneParams } = params;

    const defaultConditions = {
        ...weeklyEffortConfig,
        ...cloneParams,
        userId: params.userId ? { value: params.userId, label: fullname } : null,
        projectId: params.projectId ? { value: params.projectId, label: projectName } : null
    };

    const dispatch = useAppDispatch();

    const [formReset, setFormReset] = useState<IWeeklyEffortConfig>(defaultConditions);
    const [tabValue, setTabValue] = useState(params.tab || 0);
    const [weekYearforSearch, setWeekYearForSearch] = useState({});

    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {
        setTabValue(newTabValue);
        setFormReset(weeklyEffortConfig);
        setSearchParams({ tab: newTabValue, ...weekYearforSearch } as any);
    };
    const getWeekandYearWhenSearch = (weekSearch: string, yearSearch: string | number) => {
        setWeekYearForSearch({ week: weekSearch, year: yearSearch as number });
    };

    const setWeeksFunc = async (year: number, week?: string | number) => {
        const items = getWeeksPeriodsInYear(year);

        setWeeks(items);
        if (items.length > 0) {
            let projectId: IOption | null = null;
            const weekSelected = week || items[0].value;
            if (tabValue > 0) {
                const resultAction = await dispatch(getWeeklyEffortProjectOption({ week: weekSelected, color: tabValue === 2 }));
                if (tabValue === 2 && getWeeklyEffortProjectOption.fulfilled.match(resultAction) && resultAction.payload.status) {
                    projectId = resultAction.payload.result?.content.length
                        ? {
                              value: resultAction.payload.result.content[0].projectId,
                              label: resultAction.payload.result.content[0].projectName,
                              typeCode: resultAction.payload.result.content[0].typeCode
                          }
                        : null;

                    if (week) {
                        const weeklyEffortProjects = {
                            ...defaultConditions,
                            year,
                            projectId: projectId ? projectId.value : null
                        };

                        const weekSelected = convertWeekFromToDate(week);

                        dispatch(
                            getWeeklyEffortProjectDetail({
                                ...transformObject({ ...weeklyEffortProjects }, ['tab', 'week', 'size', 'page']),
                                ...weekSelected
                            } as GetWeeklyEffortRequest)
                        );
                    }
                }
            }
            setFormReset((prev) => ({ ...prev, year, week: weekSelected, projectId, timeStatus: [] }));
        }
    };

    const handleChangeYear = (e: SelectChangeEvent<unknown>) => {
        const { value } = e.target;

        setWeeksFunc(Number(value));
    };

    useEffect(() => {
        let allowedTabs = checkAllowedTab(weeklyEffortReportTabs, params.tab);
        const deniedTabs = allowedTabs.filter((item) => {
            const permission_key = weeklyEffortReportTabs.find((tab) => tab.value === item)!.permission_key;
            return permission_key && !checkAllowedPermission(permission_key) && item === params.tab;
        });

        if (deniedTabs.length) {
            dispatch(openDeniedPermission(true));
        }

        setTabValue(allowedTabs[0]);
        if (Number.isInteger(allowedTabs[0])) {
            setSearchParams((prev) => ({
                ...transformObject(getSearchParam(keyParams, prev, keyParamsArray)),
                tab: allowedTabs[0].toString()
            }));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch]);

    useEffect(() => {
        if (Number.isInteger(defaultConditions.year)) {
            setWeeksFunc(defaultConditions.year, defaultConditions.week);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tabValue]);

    return (
        <>
            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={weeklyEffortReportTabs} />

            <TabPanel value={tabValue} index={0}>
                <WeeklyEffortMember
                    weeks={weeks}
                    params={params}
                    formReset={formReset}
                    setFormReset={setFormReset}
                    handleChangeYear={handleChangeYear}
                    defaultConditions={defaultConditions}
                    getWeekandYearWhenSearch={getWeekandYearWhenSearch}
                />
            </TabPanel>
            <TabPanel value={tabValue} index={1}>
                <WeeklyEffortProjects
                    weeks={weeks}
                    params={params}
                    formReset={formReset}
                    setFormReset={setFormReset}
                    handleChangeYear={handleChangeYear}
                    defaultConditions={defaultConditions}
                    getWeekandYearWhenSearch={getWeekandYearWhenSearch}
                />
            </TabPanel>
            <TabPanel value={tabValue} index={2}>
                <WeeklyEffortProjectDetail
                    weeks={weeks}
                    params={params}
                    formReset={formReset}
                    handleChangeYear={handleChangeYear}
                    defaultConditions={defaultConditions}
                    getWeekandYearWhenSearch={getWeekandYearWhenSearch}
                />
            </TabPanel>
        </>
    );
};

export default WeeklyEffort;
