import React, { useState } from 'react';
import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { getWeeklyEffortProjectDetail, getWeeklyEffortProjectOption, weeklyEffortSelector } from 'store/slice/weeklyEffortSlice';
import { GetWeeklyEffortRequest, IOption, IUserVerify, IWeeklyEffortProjectDetail } from 'types';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { authSelector } from 'store/slice/authSlice';
import { convertWeekFromToDate } from 'utils/date';
import { FilterCollapse } from 'containers/search';
import { Table } from 'components/extended/Table';
import MainCard from 'components/cards/MainCard';
import { transformObject } from 'utils/common';
import { IWeeklyEffortConfig } from '../Config';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import {
    WeeklyEffortProjectDetailSearch,
    WeeklyEffortProjectDetailTBody,
    WeeklyEffortProjectDetailThead,
    WeeklyEffortSpentTimeDetail
} from 'containers/weekly-effort';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IProps {
    weeks: IOption[];
    formReset: IWeeklyEffortConfig;
    params: {
        [key: string]: any;
    };
    defaultConditions: IWeeklyEffortConfig;
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    getWeekandYearWhenSearch?: (week: string, year: string | number) => void;
}

const WeeklyEffortProjectDetail = ({ formReset, defaultConditions, weeks, params, handleChangeYear, getWeekandYearWhenSearch }: IProps) => {
    const [conditions, setConditions] = useState<IWeeklyEffortConfig>(defaultConditions);
    const [selected, setSelected] = useState<IUserVerify[]>([]);
    const [open, setOpen] = useState<boolean>(false);
    const [projectDetailUser, setProjectDetailUser] = useState<IWeeklyEffortProjectDetail>({
        firstName: '',
        lastName: '',
        listLogtime: [],
        userId: ''
    });

    const { weeklyEffortProjectDetail, loading } = useAppSelector(weeklyEffortSelector);
    const { userInfo } = useAppSelector(authSelector);

    const [, setSearchParams] = useSearchParams();

    const dispatch = useAppDispatch();

    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;

    const handleGetTableData = (dataSearch?: IWeeklyEffortConfig) => {
        const data = dataSearch || conditions;

        const weekSelected = convertWeekFromToDate(data.week);

        const weeklyEffortProjects = data.projectId ? { ...data, projectId: data.projectId.value } : data;

        dispatch(
            getWeeklyEffortProjectDetail({
                ...transformObject({ ...weeklyEffortProjects }, ['tab', 'week', 'size', 'page']),
                ...weekSelected
            } as GetWeeklyEffortRequest)
        );
    };

    const handleSearch = (value: IWeeklyEffortConfig) => {
        const weeklyEffortProjectDetail = value.projectId
            ? { ...value, projectId: value.projectId.value, projectName: value.projectId.label }
            : value;

        setSearchParams({ ...params, ...transformObject(weeklyEffortProjectDetail) } as unknown as URLSearchParamsInit);
        setConditions({ ...transformObject(value) });

        handleGetTableData({ ...transformObject(value) });
        getWeekandYearWhenSearch?.(value.week as string, value.year);
    };

    const isCheckAll =
        (weeklyEffortProjectDetail?.content?.length || 0) > 0 ? selected.length === weeklyEffortProjectDetail?.content.length : false;
    const isSomeSelected = selected.length > 0 && selected.length < (weeklyEffortProjectDetail?.content.length || 0);

    const handleOpenDialog = (item: IWeeklyEffortProjectDetail) => {
        setProjectDetailUser(item);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const handleCheckOne = (userSelected: IUserVerify) => {
        const index = selected.findIndex((project) => project.userId === userSelected.userId);
        if (index !== -1) {
            setSelected(selected.filter((project) => project.userId !== userSelected.userId));
        } else {
            setSelected([...selected, userSelected]);
        }
    };

    const postVerified = async (type: string, verifyUsers: string[]) => {
        if (verifyUsers.length > 0) {
            const weekSelected = convertWeekFromToDate(conditions.week);
            const response = await sendRequest(Api.weekly_efford.postVerified, {
                ...weekSelected,
                verifiedType: type,
                userNameVerified: userInfo?.userName,
                projectId: conditions.projectId?.value,
                listUserVerified: verifyUsers
            });
            if (response) {
                const { message } = response.result.messages[0];
                const alertColor = response.status ? 'success' : 'warning';
                dispatch(openSnackbar({ open: true, message, variant: 'alert', alert: { color: alertColor } }));
                if (response.status) {
                    dispatch(getWeeklyEffortProjectOption({ week: conditions.week, color: true }));
                    handleGetTableData();
                    setSelected([]);
                }
            }
        } else {
            dispatch(
                openSnackbar({
                    open: true,
                    message: type === 'PM' ? 'pm-and-qa-verified' : 'pm-not-verify',
                    variant: 'alert',
                    alert: { color: 'warning' }
                })
            );
        }
        dispatch(closeConfirm());
    };

    const handleVerifiedConfirm = (type: string) => {
        const verifyUsers: any = selected
            .filter((userVerifiedByTypes) =>
                type === 'PM'
                    ? !userVerifiedByTypes.pmVerifiedDate
                    : type === 'QA'
                    ? !userVerifiedByTypes.qaVerifiedDate && userVerifiedByTypes.pmVerifiedDate
                    : null
            )
            .map((item) => item.userId);

        if (selected.length > 0) {
            dispatch(
                openConfirm({
                    open: true,
                    title: <FormattedMessage id="warning" />,
                    content: type ? <FormattedMessage id="message-verify" /> : '',
                    handleConfirm: () => (type ? postVerified(type, verifyUsers) : '')
                })
            );
        } else {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'project-details-selection',
                    variant: 'alert',
                    alert: { color: 'warning' }
                })
            );
        }
    };

    const handleCheckAll = () => {
        if (selected.length < (weeklyEffortProjectDetail?.content.length || 0)) {
            const userIds: IUserVerify[] =
                weeklyEffortProjectDetail?.content.map((user) => ({
                    userId: user.userId,
                    pmVerifiedDate: user.pmVerifiedDate,
                    qaVerifiedDate: user.qaVerifiedDate
                })) || [];
            setSelected(userIds);
        } else {
            setSelected([]);
        }
    };

    return (
        <>
            <FilterCollapse
                handleVerifiedConfirm={handleVerifiedConfirm}
                qaVerifyLabel={Weeklyeffort + 'qa-verify'}
                pmVerifyLabel={Weeklyeffort + 'pm-verify'}
            >
                <WeeklyEffortProjectDetailSearch
                    weeks={weeks}
                    formReset={formReset}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                <Table
                    heads={
                        <WeeklyEffortProjectDetailThead
                            isCheckAll={isCheckAll}
                            isSomeSelected={isSomeSelected}
                            handleCheckAll={handleCheckAll}
                        />
                    }
                    isLoading={loading[getWeeklyEffortProjectDetail.typePrefix]}
                    data={weeklyEffortProjectDetail?.content || []}
                >
                    <WeeklyEffortProjectDetailTBody
                        selected={selected}
                        isCheckAll={isCheckAll}
                        handleOpen={handleOpenDialog}
                        handleCheckOne={handleCheckOne}
                        projectDetails={weeklyEffortProjectDetail?.content || []}
                    />
                </Table>
            </MainCard>

            <WeeklyEffortSpentTimeDetail open={open} projectDetailUser={projectDetailUser} handleClose={handleCloseDialog} />
        </>
    );
};

export default WeeklyEffortProjectDetail;
