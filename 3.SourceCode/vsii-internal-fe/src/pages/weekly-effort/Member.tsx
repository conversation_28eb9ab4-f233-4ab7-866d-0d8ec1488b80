import React, { useEffect, useState } from 'react';
import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';
import { useIntl } from 'react-intl';

import { WeeklyEffortMemberSearch, WeeklyEffortMemberTBody, WeeklyEffortMemberThead } from 'containers/weekly-effort';
import { getWeeklyEffortMember, weeklyEffortSelector } from 'store/slice/weeklyEffortSlice';
import { convertWeekFromToDate, getNumberOfWeek } from 'utils/date';
import { IWeeklyEffortConfig, weeklyEffortConfig } from '../Config';
import { exportDocument, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { openCommentDialog } from 'store/slice/commentSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { GetWeeklyEffortRequest, IOption } from 'types';
import { authSelector } from 'store/slice/authSlice';
import { PERMISSIONS } from 'constants/Permission';
import { FilterCollapse } from 'containers/search';
import MainCard from 'components/cards/MainCard';
import { REPORT_TYPE, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { IMember } from 'types/member';
import Api from 'constants/Api';

interface IProps {
    weeks: IOption[];
    formReset: IWeeklyEffortConfig;
    defaultConditions: IWeeklyEffortConfig;
    params: {
        [key: string]: any;
    };
    setFormReset: React.Dispatch<React.SetStateAction<IWeeklyEffortConfig>>;
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    getWeekandYearWhenSearch?: (week: string, year: string | number) => void;
}

const WeeklyEffortMember = ({
    formReset,
    weeks,
    params,
    defaultConditions,
    setFormReset,
    handleChangeYear,
    getWeekandYearWhenSearch
}: IProps) => {
    const [conditions, setConditions] = useState<IWeeklyEffortConfig>(defaultConditions);

    const { weeklyEffortMember, loading } = useAppSelector(weeklyEffortSelector);
    const { userInfo } = useAppSelector(authSelector);

    const [, setSearchParams] = useSearchParams();

    const dispatch = useAppDispatch();
    const intl = useIntl();

    const { weeklyEffort } = PERMISSIONS.report;

    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;

    const handleOpenCommentDialog = () => {
        const updatedConditions = { ...conditions, reportType: REPORT_TYPE.RP_WEEK };
        const titleDetail = conditions?.week ? `${getNumberOfWeek(conditions.week)} - ${conditions.year}` : '';
        dispatch(
            openCommentDialog({
                conditions: updatedConditions,
                titleDetail: intl.formatMessage({ id: 'week' }) + ' ' + titleDetail
            })
        );
    };

    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions((prev) => ({ ...prev, page: newPage + 1 }));
        setFormReset((prev) => ({ ...prev, page: newPage + 1 }));
        setSearchParams({ ...params, page: newPage + 1 } as unknown as URLSearchParamsInit);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: weeklyEffortConfig.page, size: parseInt(event.target.value, 10) }));
        setFormReset((prev) => ({ ...prev, page: weeklyEffortConfig.page, size: parseInt(event.target.value, 10) }));
        setSearchParams({
            ...params,
            page: weeklyEffortConfig.page,
            size: parseInt(event.target.value, 10)
        } as unknown as URLSearchParamsInit);
    };

    const handleSearch = (value: IWeeklyEffortConfig) => {
        const weeklyEffortMember = value.userId ? { ...value, userId: value.userId.value, fullname: value.userId.label } : value;

        setSearchParams({
            ...params,
            ...transformObject(weeklyEffortMember),
            page: weeklyEffortConfig.page
        } as unknown as URLSearchParamsInit);
        setConditions({ ...transformObject(value), page: weeklyEffortConfig.page });

        getWeekandYearWhenSearch?.(value.week as string, value.year);
    };

    const handleExportDocument = () => {
        if (!userInfo?.isLdap && !checkAllowedPermission(weeklyEffort.download)) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'required-upgrade-version',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
            return;
        }
        exportDocument(Api.weekly_efford.getDownload.url, {
            ...convertWeekFromToDate(conditions.week),
            year: conditions.year,
            weekNumber: getNumberOfWeek(conditions.week)
        });
    };
    // -----------------------auto filter-------------------
    const handleChangeWeek = (values: string) => {
        setFormReset({
            ...formReset,
            week: values,
            userId: null,
            timeStatus: [],
            ...convertWeekFromToDate(values)
        });
    };
    const handleChangeTimeStatus = (values: string[]) => {
        setFormReset({
            ...formReset,
            timeStatus: values,
            userId: null
        });
    };
    const handleChangeMember = (values: IMember) => {
        if (values) {
            setFormReset({
                ...formReset,
                timeStatus: values.timeStatus ? [values.timeStatus] : [],
                userId: { value: values.userId, label: `${values.firstName} ${values.lastName}` }
            });
        } else {
            setFormReset({
                ...formReset,
                userId: null
            });
        }
    };
    // -----------------------end auto filter-------------------

    useEffect(() => {
        const weekSelected = convertWeekFromToDate(conditions.week);

        const weeklyEffortMember = conditions.userId
            ? { ...conditions, userId: conditions.userId.value, fullname: conditions.userId.label }
            : conditions;

        dispatch(
            getWeeklyEffortMember({
                ...transformObject({ ...weeklyEffortMember }, ['tab', 'week']),
                ...weekSelected
            } as GetWeeklyEffortRequest)
        );
    }, [dispatch, conditions, defaultConditions.page]);
    const monthOnload = weeks.find((item) => item.value === formReset.week);
    useEffect(() => {
        setFormReset((prev) => ({
            ...prev,
            ...convertWeekFromToDate(prev.week)
        }));
    }, [monthOnload, setFormReset]);

    return (
        <>
            <FilterCollapse
                handleExport={!userInfo?.isLdap || checkAllowedPermission(weeklyEffort.download) ? handleExportDocument : undefined}
                handleOpenCommentDialog={checkAllowedPermission(weeklyEffort.comment) ? handleOpenCommentDialog : undefined}
                commentLabel={Weeklyeffort + 'comments'}
                downloadLabel={Weeklyeffort + 'download-report'}
            >
                <WeeklyEffortMemberSearch
                    weeks={weeks}
                    formReset={formReset}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                    handleChangeWeek={handleChangeWeek}
                    handleChangeTimeStatus={handleChangeTimeStatus}
                    handleChangeMember={handleChangeMember}
                />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                <Table
                    heads={<WeeklyEffortMemberThead />}
                    isLoading={loading[getWeeklyEffortMember.typePrefix]}
                    data={weeklyEffortMember?.content || []}
                >
                    <WeeklyEffortMemberTBody
                        pageNumber={
                            weeklyEffortMember?.pagination?.pageNumber ? weeklyEffortMember?.pagination?.pageNumber - 1 : conditions.page
                        }
                        pageSize={weeklyEffortMember?.pagination?.pageSize || conditions.size}
                        members={weeklyEffortMember?.content || []}
                    />
                </Table>
            </MainCard>

            {/* Pagination */}
            {!loading[getWeeklyEffortMember.typePrefix] && (
                <TableFooter
                    pagination={{
                        total: weeklyEffortMember?.pagination?.totalElement || 0,
                        page: weeklyEffortMember?.pagination?.pageNumber ? weeklyEffortMember?.pagination?.pageNumber - 1 : conditions.page,
                        size: weeklyEffortMember?.pagination?.pageSize || conditions.size
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
        </>
    );
};

export default WeeklyEffortMember;
