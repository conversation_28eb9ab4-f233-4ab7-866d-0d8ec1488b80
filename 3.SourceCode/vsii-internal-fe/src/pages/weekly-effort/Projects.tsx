import React, { useEffect, useState } from 'react';
import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';

import { WeeklyEffortProjectSearch, WeeklyEffortProjectTBody, WeeklyEffortProjectThead } from 'containers/weekly-effort';
import { getWeeklyEffortProjects, weeklyEffortSelector } from 'store/slice/weeklyEffortSlice';
import { convertWeekFromToDate, getNumberOfWeek } from 'utils/date';
import { IWeeklyEffortConfig, weeklyEffortConfig } from '../Config';
import { exportDocument, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { GetWeeklyEffortRequest, IOption } from 'types';
import { authSelector } from 'store/slice/authSlice';
import { PERMISSIONS } from 'constants/Permission';
import { getAllRank } from 'store/slice/rankSlice';
import { FilterCollapse } from 'containers/search';
import MainCard from 'components/cards/MainCard';
import Api from 'constants/Api';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IProps {
    weeks: IOption[];
    formReset: IWeeklyEffortConfig;
    defaultConditions: IWeeklyEffortConfig;
    params: {
        [key: string]: any;
    };
    setFormReset: React.Dispatch<React.SetStateAction<IWeeklyEffortConfig>>;
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    getWeekandYearWhenSearch?: (week: string, year: string | number) => void;
}

const WeeklyEffortProjects = ({
    formReset,
    weeks,
    params,
    defaultConditions,
    setFormReset,
    handleChangeYear,
    getWeekandYearWhenSearch
}: IProps) => {
    const [conditions, setConditions] = useState<IWeeklyEffortConfig>(defaultConditions);

    const { weeklyEffortProjects, loading } = useAppSelector(weeklyEffortSelector);
    const { userInfo } = useAppSelector(authSelector);

    const [, setSearchParams] = useSearchParams();

    const dispatch = useAppDispatch();

    const { weeklyEffort } = PERMISSIONS.report;

    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;

    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions((prev) => ({ ...prev, page: newPage + 1 }));
        setFormReset((prev) => ({ ...prev, page: newPage + 1 }));
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions((prev) => ({ ...prev, page: weeklyEffortConfig.page, size: parseInt(event.target.value, 10) }));
        setFormReset((prev) => ({ ...prev, page: weeklyEffortConfig.page, size: parseInt(event.target.value, 10) }));
        setSearchParams({
            ...params,
            page: weeklyEffortConfig.page,
            size: parseInt(event.target.value, 10)
        } as unknown as URLSearchParamsInit);
    };

    const handleSearch = (value: IWeeklyEffortConfig) => {
        const weeklyEffortProjects = value.projectId
            ? { ...value, projectId: value.projectId.value, projectName: value.projectId.label }
            : value;

        setSearchParams({
            ...params,
            ...transformObject(weeklyEffortProjects),
            page: weeklyEffortConfig.page
        } as unknown as URLSearchParamsInit);
        setConditions({ ...transformObject(value), page: weeklyEffortConfig.page });
        getWeekandYearWhenSearch?.(value.week as string, value.year);
    };

    const handleExportDocument = () => {
        if (!userInfo?.isLdap && !checkAllowedPermission(weeklyEffort.download)) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'required-upgrade-version',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
            return;
        }
        exportDocument(Api.weekly_efford.getDownload.url, {
            ...convertWeekFromToDate(conditions.week),
            year: conditions.year,
            weekNumber: getNumberOfWeek(conditions.week)
        });
    };

    useEffect(() => {
        const weekSelected = convertWeekFromToDate(conditions.week);

        const weeklyEffortProjects = conditions.projectId ? { ...conditions, projectId: conditions.projectId.value } : conditions;

        dispatch(
            getWeeklyEffortProjects({
                ...transformObject({ ...weeklyEffortProjects }, ['tab', 'week']),
                ...weekSelected
            } as GetWeeklyEffortRequest)
        );
    }, [dispatch, conditions]);

    useEffect(() => {
        dispatch(getAllRank({ page: 1, size: 1000 }));
    }, [dispatch]);

    return (
        <>
            <FilterCollapse
                handleExport={!userInfo?.isLdap || checkAllowedPermission(weeklyEffort.download) ? handleExportDocument : undefined}
                downloadLabel={Weeklyeffort + 'download-report'}
            >
                <WeeklyEffortProjectSearch
                    weeks={weeks}
                    formReset={formReset}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                <Table
                    heads={<WeeklyEffortProjectThead />}
                    isLoading={loading[getWeeklyEffortProjects.typePrefix]}
                    data={weeklyEffortProjects?.content || []}
                >
                    <WeeklyEffortProjectTBody
                        pageNumber={
                            weeklyEffortProjects?.pagination?.pageNumber
                                ? weeklyEffortProjects?.pagination?.pageNumber - 1
                                : conditions.page
                        }
                        pageSize={weeklyEffortProjects?.pagination?.pageSize || conditions.size}
                        projects={weeklyEffortProjects?.content || []}
                    />
                </Table>
            </MainCard>

            {/* Pagination */}
            {!loading[getWeeklyEffortProjects.typePrefix] && (
                <TableFooter
                    pagination={{
                        total: weeklyEffortProjects?.pagination?.totalElement || 0,
                        page: weeklyEffortProjects?.pagination?.pageNumber
                            ? weeklyEffortProjects?.pagination?.pageNumber - 1
                            : conditions.page,
                        size: weeklyEffortProjects?.pagination?.pageSize || conditions.size
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
        </>
    );
};

export default WeeklyEffortProjects;
