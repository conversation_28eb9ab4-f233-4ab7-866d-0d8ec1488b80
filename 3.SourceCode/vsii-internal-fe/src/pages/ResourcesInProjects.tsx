/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect, useState } from 'react';

// project imports
import MainCard from 'components/cards/MainCard';
import { Table, TableFooter } from 'components/extended/Table';
import Api from 'constants/Api';
import { REPORT_TYPE, SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { ListProjectTeamSearch, ListProjectTeamTBody, ListProjectTeamThead } from 'containers/list-project-team';
import { FilterCollapse } from 'containers/search';
import sendRequest from 'services/ApiService';
import { IOption, IPaginationResponse, IProjectTeam, IProjectTeamList, IRatioJoiningProjectList, IResponseList } from 'types';
import { exportDocument, getSearchParam, transformObject } from 'utils/common';
import { convertWeekFromToDate, getNumberOfWeek, getWeeksPeriodsInYear } from 'utils/date';
import { IListProjectTeamConfig, listProjectTeamConfig } from './Config';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openCommentDialog, isCommentedSelector, changeCommented } from 'store/slice/commentSlice';

// third party
import { useSearchParams } from 'react-router-dom';
import { useIntl } from 'react-intl';
import { IMember } from 'types/member';

// ==============================||Resources In Projects ||============================== //
/**
 *  URL Params
 *  year
 *  month
 *  projectId
 *  projectName
 */
const ResourcesInProjects = () => {
    const { resourcesInProject } = TEXT_CONFIG_SCREEN;
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.tab,
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.week,
        SEARCH_PARAM_KEY.userName,
        SEARCH_PARAM_KEY.fullname,
        SEARCH_PARAM_KEY.departmentId
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...listProjectTeamConfig,
        ...cloneParams,
        userName: params.userName ? { value: params.userName, label: params.fullname } : null
    };
    const dispatch = useAppDispatch();
    const intl = useIntl();

    const [loading, setLoading] = useState<boolean>(false);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [projectTeam, setProjectTeam] = useState<IProjectTeam[]>([]);
    const [conditions, setConditions] = useState<IListProjectTeamConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IListProjectTeamConfig>(defaultConditions);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [weeks, setWeeks] = useState<IOption[]>([]);
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const { ResourcesInProjects } = PERMISSIONS.report;
    const isCommented = useAppSelector(isCommentedSelector);
    // Function
    const getDataTable = async () => {
        setLoading(true);
        const weekSelected = convertWeekFromToDate(conditions.week);

        const response: IResponseList<IProjectTeamList | IRatioJoiningProjectList> = await sendRequest(
            Api.list_project_team.getListProjectTeam,
            {
                ...weekSelected,
                ...conditions,
                userName: conditions.userName ? conditions.userName.value : null,
                reportType: REPORT_TYPE.RP_LIST_PROJECT_TEAM,
                page: conditions.page + 1
            }
        );

        if (response) {
            const { status, result } = response;

            if (status) {
                setLoading(false);
                const { content, pagination } = result;
                setPaginationResponse((prev) => (pagination ? { ...prev, ...pagination } : prev));
                setProjectTeam(content as IProjectTeam[]);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    const setDataEmpty = () => {
        setProjectTeam([]);
        setLoading(false);
    };

    const getWeekInYears = useCallback(async (p: number) => {
        const weekInYears = await getWeeksPeriodsInYear(p);
        return weekInYears;
    }, []);

    // Event
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    const handleChangeYear = (e: any) => {
        const { value } = e.target;
        setYear(value);
        setIsChangeYear(true);
    };

    const handleExportDocument = () => {
        exportDocument(Api.list_project_team.getDownload.url, {
            ...convertWeekFromToDate(conditions.week),
            year: conditions.year,
            weekNumber: getNumberOfWeek(conditions.week)
        });
    };

    const handleOpenCommentDialog = (userId?: string, subTitle?: string) => {
        const updatedConditions = { ...conditions, userId, reportType: REPORT_TYPE.RP_LIST_PROJECT_TEAM };
        const titleDetail = conditions?.week ? `${getNumberOfWeek(conditions.week)} - ${conditions.year}` : '';

        dispatch(openCommentDialog(updatedConditions));
        dispatch(
            openCommentDialog({
                conditions: updatedConditions,
                titleDetail: userId ? subTitle : intl.formatMessage({ id: 'weeks' }) + ' ' + titleDetail
            })
        );
    };

    // Handle submit
    const handleSearch = (value: any) => {
        const { userName } = value;
        transformObject(value);
        setSearchParams(
            userName ? { ...value, userName: userName.value, fullname: userName.label, page: paginationParamDefault.page } : { ...value }
        );
        setConditions({ ...value, page: paginationParamDefault.page });
    };

    // -----------------------auto filter-------------------
    const handleChangeWeek = (values: string) => {
        setFormReset({
            ...formReset,
            week: values,
            ...convertWeekFromToDate(values),
            userName: null
        });
    };
    const handleChangeDepartmentId = (values: string) => {
        setFormReset({
            ...formReset,
            departmentId: values,
            userName: null
        });
    };
    const handleChangeMember = (values: IMember) => {
        if (values) {
            setFormReset({
                ...formReset,
                departmentId: values.departmentId,
                userName: { value: values.userName, label: `${values.firstName} ${values.lastName}` }
            });
        } else {
            setFormReset({
                ...formReset,
                userName: null
            });
        }
    };
    // Effect
    useEffect(() => {
        getDataTable();
    }, [conditions]);

    useEffect(() => {
        getWeekInYears(year).then((items: IOption[]) => {
            setWeeks(items);
            if (items.length > 0 && isChangeYear) {
                setFormReset({ ...formReset, year, week: items[0].value });
            }
        });
    }, [year]);

    useEffect(() => {
        if (isCommented) {
            getDataTable();
            dispatch(changeCommented(false));
        }
    }, [isCommented, dispatch]);

    useEffect(() => {
        setFormReset({
            ...formReset,
            ...convertWeekFromToDate(formReset.week)
        });
    }, []);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse
                handleExport={checkAllowedPermission(ResourcesInProjects.download) ? handleExportDocument : undefined}
                handleOpenCommentDialog={checkAllowedPermission(ResourcesInProjects.comment) ? handleOpenCommentDialog : undefined}
                commentLabel={resourcesInProject + 'comments'}
                downloadLabel={resourcesInProject + 'download'}
            >
                <ListProjectTeamSearch
                    formReset={formReset}
                    weeks={weeks}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                    handleChangeDepartmentId={handleChangeDepartmentId}
                    handleChangeMember={handleChangeMember}
                    handleChangeWeek={handleChangeWeek}
                />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                <Table heads={<ListProjectTeamThead />} isLoading={loading} data={projectTeam}>
                    <ListProjectTeamTBody
                        handleOpenCommentDialog={handleOpenCommentDialog}
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        projectTeam={projectTeam}
                    />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!loading && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
        </>
    );
};

export default ResourcesInProjects;
