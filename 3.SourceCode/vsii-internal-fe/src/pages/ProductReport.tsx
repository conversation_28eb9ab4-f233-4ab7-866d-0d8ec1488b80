import React, { useCallback, useEffect, useState } from 'react';

// project imports
import { getAllProductReport, getProjectReportOption, productReportSelector, syncDataProjectReport } from 'store/slice/productReportSlice';
import { ProductReportSearch, ProductReportThead, ProductReportTbody } from 'containers/product-report';
import ProductDetail from 'containers/product-report/ProductDetail';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { Table, TableFooter } from 'components/extended/Table';
import { getSearchParam, transformObject } from 'utils/common';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import MainCard from 'components/cards/MainCard';
import { IProductReportConfig } from './Config';
import HTTP_CODE from 'constants/HttpCode';

// third party
import { useSearchParams } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';

// ==============================|| Product Report ||============================== //

const ProductReport = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [SEARCH_PARAM_KEY.page, SEARCH_PARAM_KEY.size, SEARCH_PARAM_KEY.projectId];
    const params: IProductReportConfig = getSearchParam(keyParams, searchParams) as IProductReportConfig;
    transformObject(params);
    // delete unnecessary key value

    const defaultConditions: IProductReportConfig = {
        page: params.page || 1,
        size: params.size || 10,
        projectId: { value: params.projectId as unknown as number, label: '' } || null
    };

    const [conditions, setConditions] = useState<IProductReportConfig>(defaultConditions);
    const [openDetailModal, setOpenDetailModal] = useState<boolean>(false);

    const dispatch = useAppDispatch();

    const { productReport, pagination, loading } = useAppSelector(productReportSelector);

    const { ProductReport } = TEXT_CONFIG_SCREEN.generalReport;

    const fetchData = useCallback(() => {
        dispatch(
            getAllProductReport({
                ...conditions,
                page: conditions.page,
                projectId: conditions.projectId?.value ? +conditions.projectId?.value : null
            })
        );
    }, [conditions, dispatch]);

    // Event
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
        setSearchParams({ ...params, page: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: conditions.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: conditions.page, size: parseInt(event.target.value, 10) } as any);
    };

    // Handle submit
    const handleSearch = ({ projectId }: IProductReportConfig) => {
        setSearchParams({
            page: '1',
            size: conditions.size.toString(),
            ...(projectId?.value ? { projectId: projectId.value.toString() } : {})
        });
        setConditions({ ...conditions, projectId: projectId || null, page: 1 });
    };

    const handleSyncData = () => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="confirm-synchronize" />,
                width: '400px',
                handleConfirm: async () => {
                    const resultAction = await dispatch(syncDataProjectReport());
                    if (syncDataProjectReport.fulfilled.match(resultAction)) {
                        Promise.all([fetchData(), dispatch(getProjectReportOption())]);
                        dispatch(
                            openSnackbar({
                                open: true,
                                message: 'sync-successfully-message',
                                variant: 'alert',
                                close: true,
                                alert: { color: 'success' }
                            })
                        );
                    } else if (syncDataProjectReport.rejected.match(resultAction)) {
                        dispatch(
                            openSnackbar({
                                open: true,
                                message: resultAction.error.code === 'ERR_BAD_REQUEST' ? HTTP_CODE[404] : resultAction.error.message,
                                variant: 'alert',
                                close: true,
                                alert: { color: 'error' }
                            })
                        );
                    }
                    dispatch(closeConfirm());
                }
            })
        );
    };

    // Effect
    useEffect(() => {
        fetchData();
    }, [fetchData]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse handleSynchronize={handleSyncData} syncLabel={ProductReport + 'synchronize'}>
                <ProductReportSearch projectDefault={defaultConditions.projectId} handleSearch={handleSearch} />
            </FilterCollapse>

            <MainCard>
                <Table heads={<ProductReportThead />} isLoading={loading[getAllProductReport.typePrefix]} data={productReport}>
                    <ProductReportTbody setOpenDetailModal={setOpenDetailModal} page={conditions.page - 1} size={conditions.size} />
                </Table>
            </MainCard>
            {/* Pagination  */}
            {!loading[getAllProductReport.typePrefix] && (
                <TableFooter
                    pagination={{ total: pagination.totalElement, page: conditions.page - 1, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
            <ProductDetail isOpen={openDetailModal} handleClose={() => setOpenDetailModal(false)} />
        </>
    );
};

export default ProductReport;
