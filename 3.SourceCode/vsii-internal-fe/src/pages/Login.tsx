import { useEffect } from 'react';
import { Location, Navigate, useLocation } from 'react-router-dom';

// project imports
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { AuthLogin } from 'containers/authentication';
import { ESTATUS_LOGIN } from 'constants/Common';
import { DASHBOARD_PATH } from 'constants/Config';
import { authLayoutRef } from 'layout/AuthLayout';

// ================================|| AUTH - LOGIN ||================================ //

const Login = () => {
    const location = useLocation();
    const { status } = useAppSelector(authSelector);

    const { state }: Location = location;

    useEffect(() => {
        authLayoutRef.current?.setState({ title: 'Login' });
    }, []);

    if (status === ESTATUS_LOGIN.SUCCESS) {
        return <Navigate to={state ? `${state.from.pathname}${state.from?.search}` : DASHBOARD_PATH} />;
    }

    return <AuthLogin />;
};

export default Login;
