/* eslint-disable react-hooks/exhaustive-deps */
import { SyntheticEvent, useCallback, useEffect, useState } from 'react';

// project imports
import {
    MonthlyProjectCostSummarySearch,
    MonthlyProjectCostSummaryTBody,
    MonthlyProjectCostSummaryTFooter,
    MonthlyProjectCostSummaryThead,
    ProjectHeadCount
} from 'containers/monthly-project-cost';
import { IMonthlyProjectCostSummary, IMonthlyProjectCostSummaryList, IMonthsMoney, IOption, IProjectHeadCount, IResponseList } from 'types';
import { IMonthlyProjectCostSummaryConfig, monthlyProjectCostSummaryConfig, totalMoneyConfig } from './Config';
import { exportDocument, getSearchParam, setLocalStorageSearchTime, transformObject } from 'utils/common';
import { SEARCH_PARAM_KEY, listMonthlyProjectCostTabs } from 'constants/Common';
import { getMonthsOfYear, getCurrent<PERSON>onth, getCurrentYear } from 'utils/date';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { FilterCollapse } from 'containers/search';
import { Table } from 'components/extended/Table';
import MainCard from 'components/cards/MainCard';
import sendRequest from 'services/ApiService';
import { TabCustom } from 'containers';
import Api from 'constants/Api';

// third party
import { getCostMonitoringProjectOptionByFixCost } from 'store/slice/costAndEffortMonitoringSlice';
import { useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';
import { useAppDispatch } from 'app/hooks';

// ==============================|| Monthy Project Cost - Summary ||============================== //
/**
 *  URL Params
 *  year
 *  month
 *  departmentId
 *  projectType
 *  projectId
 *  projectName
 */
const MonthlyProjectCostSummary = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.tab,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.month,
        SEARCH_PARAM_KEY.departmentId,
        SEARCH_PARAM_KEY.projectType,
        SEARCH_PARAM_KEY.projectId,
        SEARCH_PARAM_KEY.projectName
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { projectName, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...monthlyProjectCostSummaryConfig,
        ...cloneParams,
        projectId: params.projectId ? { value: params.projectId, label: params.projectName } : null
    };
    const [tabValue, setTabValue] = useState(params.tab || 0);
    const [loading, setLoading] = useState<boolean>(false);
    const [loadingHeadCount, setLoadingHeadCount] = useState<boolean>(false);
    const [projects, setProjects] = useState<IMonthlyProjectCostSummary[]>([]);
    const [total, setTotal] = useState<IMonthsMoney>(totalMoneyConfig);
    const [conditions, setConditions] = useState<IMonthlyProjectCostSummaryConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IMonthlyProjectCostSummaryConfig>(defaultConditions);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [months, setMonths] = useState<IOption[]>(getMonthsOfYear(monthlyProjectCostSummaryConfig.year));
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const [monthSearch, setMonthSearch] = useState<number | string>(params.month || getCurrentMonth());
    const [yearSearch, setYearSearch] = useState<number | string>(params.year || getCurrentYear());

    const [open, setOpen] = useState<boolean>(false);
    const [headCounts, setHeadCounts] = useState<IProjectHeadCount[]>([]);
    const { monthlyProjectCost } = PERMISSIONS.report;
    const dispatch = useAppDispatch();
    // Function
    const getDataTable = async (tabValue: number) => {
        setLoading(true);
        const response: IResponseList<IMonthlyProjectCostSummaryList> = await sendRequest(Api.monthly_project_cost.getSummary(!tabValue), {
            ...conditions,
            projectId: conditions.projectId ? conditions.projectId.value : null
        });
        if (response) {
            const { result } = response;
            const { content } = result || {};
            const { summary, total } = content || {};
            if (summary) {
                setProjects(summary);
                setTotal(total);
                setLoading(false);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    const getProjectHeadCounts = async (projectId: string) => {
        setLoadingHeadCount(true);
        const response = await sendRequest(Api.project.getDetail, { projectId });
        if (response) {
            const { content } = response.result;
            setHeadCounts(content.headcount);
            setLoadingHeadCount(false);
        }
    };

    const setDataEmpty = () => {
        setProjects([]);
        setTotal(totalMoneyConfig);
        setLoading(false);
    };

    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {
        setTabValue(newTabValue);
        setSearchParams({ tab: newTabValue, month: monthSearch, year: yearSearch } as any);
        setFormReset({ ...monthlyProjectCostSummaryConfig, month: monthSearch, year: yearSearch as number });
    };

    const getMonthsInYear = useCallback(async (p: number) => {
        const monthInYear = await getMonthsOfYear(p);
        return monthInYear;
    }, []);

    // Event
    const handleChangeYear = (e: any) => {
        const { value } = e.target;
        setYear(value);
        setIsChangeYear(true);
        setFormReset((prev) => ({
            ...prev,
            year: value
        }));
    };
    const handleChangeMonth = (value: string) => {
        setFormReset((prev) => ({
            ...prev,
            month: value
        }));
        const getMonth = months.find((month) => month.value === value);

        dispatch(getCostMonitoringProjectOptionByFixCost({ type: 'month', value: getMonth?.label as string, fixCost: true }));
    };

    const handleChangeProject = (data: any) => {
        if (data)
            setFormReset((prev) => ({
                ...prev,
                departmentId: data.dept,
                projectType: data.typeCode,
                projectId: data
            }));
    };

    const handleChangeDept = (value: string) => {
        setFormReset((prev) => ({
            ...prev,
            projectId: null,
            departmentId: value
        }));
    };

    const handleChangeProjectType = (e: SelectChangeEvent<unknown>) => {
        setFormReset((prev) => ({
            ...prev,
            projectId: null,
            projectType: e.target.value as string
        }));
    };

    const handleExportDocument = () => {
        exportDocument(Api.monthly_project_cost.getDownload.url, { year: conditions.year, month: conditions.month });
    };

    const handleOpenDialog = (projectId: string) => {
        setOpen(true);
        getProjectHeadCounts(projectId);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    // Handle submit
    const handleSearch = (value: any) => {
        const { projectId } = value;
        transformObject(value);
        setSearchParams(projectId ? { ...value, projectId: projectId.value, projectName: projectId.label } : value);
        setConditions(value);
        setMonthSearch(value.month);
        setYearSearch(value.year);
        // lưu thời gian vào localStorage
        setLocalStorageSearchTime({ month: value.month, year: value.year });
    };

    // Effect
    useEffect(() => {
        getDataTable(tabValue);
    }, [conditions, tabValue]);

    useEffect(() => {
        getMonthsInYear(year).then((items: IOption[]) => {
            setMonths(items);
            if (items.length > 0 && isChangeYear) {
                setFormReset({ ...formReset, year, month: months[0].value });
            }
        });
    }, [year]);

    return (
        <>
            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={listMonthlyProjectCostTabs} />

            {/* Search form  */}
            <FilterCollapse handleExport={checkAllowedPermission(monthlyProjectCost.download) ? handleExportDocument : undefined}>
                <MonthlyProjectCostSummarySearch
                    formReset={formReset}
                    months={months}
                    fixCost={!tabValue}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                    handleChangeMonth={handleChangeMonth}
                    handleChangeProject={handleChangeProject}
                    handleChangeDept={handleChangeDept}
                    handleChangeProjectType={handleChangeProjectType}
                />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                <Table
                    heads={<MonthlyProjectCostSummaryThead projectLength={projects.length} year={conditions.year} fixCost={!tabValue} />}
                    footer={projects.length > 0 && <MonthlyProjectCostSummaryTFooter total={total!} fixCost={!tabValue} />}
                    isLoading={loading}
                    data={projects}
                >
                    <MonthlyProjectCostSummaryTBody handleOpen={handleOpenDialog} projects={projects} fixCost={!tabValue} />
                </Table>
            </MainCard>

            {/* Project HeadCount Dialog */}
            {open && <ProjectHeadCount open={open} isLoading={loadingHeadCount} handleClose={handleCloseDialog} headCounts={headCounts} />}
        </>
    );
};

export default MonthlyProjectCostSummary;
