// yup
import * as yup from 'yup';

// project imports
import { IMonthlyEffortAddProjectReport, IOption, IPaginationParam } from 'types';
import { IChangePasswordRequest, IRegisterRequest } from 'types/authentication';
import { getCurrentMonth, getCurrentWeek, getCurrentYear } from 'utils/date';
import { paginationParamDefault } from 'constants/Common';
import { REGEX_CONSTANTS } from 'constants/Validation';
import { VALIDATE_MESSAGES } from 'constants/Message';

// ============== Weekly effort ============== //
export interface IWeeklyEffortConfig extends IPaginationParam {
    year: number;
    week: string | number;
    userId?: IOption | null;
    timeStatus?: string[];
    projectId?: IOption | null;
    fromDate?: string;
    toDate?: string;
    tab?: number;
}

export const weeklyEffortConfig: IWeeklyEffortConfig = {
    page: 1,
    size: 10,
    year: getCurrentYear(),
    week: getCurrentWeek().value,
    userId: null,
    timeStatus: [],
    projectId: null
};
// exchange rate config

export interface IExchangeRateConfig extends IPaginationParam {
    year: number;
    currency: string | null;
}

export const exChangeRateConfig: IExchangeRateConfig = {
    ...paginationParamDefault,
    year: getCurrentYear(),
    currency: null
};
export const weeklyEffortMemberSchema = yup.object().shape({
    year: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    week: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    userId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable(),
    timeStatus: yup.array().nullable()
});

export const weeklyEffortProjectSchema = yup.object().shape({
    year: yup.string(),
    week: yup.string(),
    projectId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
});

// ============== List project team ============== //
export interface IListProjectTeamConfig extends IPaginationParam {
    year: number;
    week: number | string;
    userName: IOption | null;
    departmentId: string;
    fromData?: string;
    toDate?: string;
}

export const listProjectTeamConfig: IListProjectTeamConfig = {
    ...paginationParamDefault,
    year: getCurrentYear(),
    week: getCurrentWeek().value,
    userName: null,
    departmentId: ''
};

export const listProjectTeamSchema = yup.object().shape({
    year: yup.string(),
    week: yup.string(),
    userName: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable(),
    departmentId: yup.string()
});

// ============== Login ============== //
export interface ILoginConfig {
    username: string;
    password: string;
    ldap: boolean;
}

export const loginConfig: ILoginConfig = {
    username: '',
    password: '',
    ldap: false
};

export const loginSchema = yup.object().shape({
    username: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    password: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
});

export interface IForgotConfig {
    email: string;
}
export const forgotConfig: IForgotConfig = {
    email: ''
};

export const forgotSchema = yup.object().shape({
    email: yup.string().required(VALIDATE_MESSAGES.REQUIRED).matches(REGEX_CONSTANTS.REGEX_EMAIL, VALIDATE_MESSAGES.INVALID_EMAIL)
});

export const registerConfig: IRegisterRequest = {
    packageAccountId: '',
    accountType: 'PERSONAL',
    firstName: '',
    lastName: '',
    companyName: '',
    shortName: '',
    username: '',
    phone: '',
    citizenId: '',
    businessId: '',
    email: '',
    taxCode: ''
};

export const registerSchema = yup.object().shape({
    packageAccountId: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    accountType: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    firstName: yup.string().when('accountType', {
        is: 'PERSONAL',
        then: yup
            .string()
            .required(VALIDATE_MESSAGES.REQUIRED)
            .matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS)
    }),
    lastName: yup.string().when('accountType', {
        is: 'PERSONAL',
        then: yup
            .string()
            .required(VALIDATE_MESSAGES.REQUIRED)
            .matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS)
    }),
    companyName: yup.string().when('accountType', {
        is: 'BUSINESS',
        then: yup
            .string()
            .required(VALIDATE_MESSAGES.REQUIRED)
            .matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS, VALIDATE_MESSAGES.SPECIAL_CHARACTERS)
    }),
    shortName: yup.string(),
    username: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .matches(REGEX_CONSTANTS.REGEX_SPECIAL_CHARACTERS_NOT_SPACE, VALIDATE_MESSAGES.SPECIAL_CHARACTERS),
    phone: yup.string().required(VALIDATE_MESSAGES.REQUIRED).matches(REGEX_CONSTANTS.REGEX_PHONE_NUMBER, VALIDATE_MESSAGES.PHONE_NUMBER),
    citizenId: yup.string().when('accountType', {
        is: 'PERSONAL',
        then: yup
            .string()
            .required(VALIDATE_MESSAGES.REQUIRED)
            .matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER)
            .max(12, VALIDATE_MESSAGES.INVALID_CITIZENID_MAX)
    }),
    businessId: yup.string().when('accountType', {
        is: 'BUSINESS',
        then: yup
            .string()
            .required(VALIDATE_MESSAGES.REQUIRED)
            .matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER)
            .max(10, VALIDATE_MESSAGES.INVALID_BUSINESSID_MAX)
    }),
    email: yup.string().required(VALIDATE_MESSAGES.REQUIRED).matches(REGEX_CONSTANTS.REGEX_EMAIL, VALIDATE_MESSAGES.INVALID_EMAIL),
    taxCode: yup.string().when('accountType', {
        is: 'BUSINESS',
        then: yup
            .string()
            .required(VALIDATE_MESSAGES.REQUIRED)
            .matches(REGEX_CONSTANTS.REGEX_NUMBER, VALIDATE_MESSAGES.INVALID_NUMBER)
            .max(13, VALIDATE_MESSAGES.INVALID_TAXCODE_MAX)
    })
});

export interface ICreatePasswordParams {
    password: string;
    confirmPassword: string;
}

export const createPasswordConfig: ICreatePasswordParams = {
    password: '',
    confirmPassword: ''
};

export const createPasswordSchema = yup.object().shape({
    password: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .min(8, VALIDATE_MESSAGES.INVALID_PASSWORD_MIN)
        .max(24, VALIDATE_MESSAGES.INVALID_PASSWORD_MAX)
        .matches(REGEX_CONSTANTS.REGEX_PASSWORD, VALIDATE_MESSAGES.INVALID_PASSWORD_SPECIAL_CHAR),
    confirmPassword: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .oneOf([yup.ref('password')], VALIDATE_MESSAGES.REPASSWORD_NOT_MATCHING)
});

export const changePasswordConfig: IChangePasswordRequest = {
    userId: '',
    passwordNow: '',
    newPassword: '',
    confirmPassword: ''
};

export const changePasswordSchema = yup.object().shape({
    userId: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    passwordNow: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    newPassword: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .min(8, VALIDATE_MESSAGES.INVALID_PASSWORD_MIN)
        .max(24, VALIDATE_MESSAGES.INVALID_PASSWORD_MAX)
        .matches(REGEX_CONSTANTS.REGEX_PASSWORD, VALIDATE_MESSAGES.INVALID_PASSWORD_SPECIAL_CHAR),
    confirmPassword: yup
        .string()
        .required(VALIDATE_MESSAGES.REQUIRED)
        .oneOf([yup.ref('newPassword')], VALIDATE_MESSAGES.REPASSWORD_NOT_MATCHING)
});

// ============== Comment ============== //

export interface ICommentForm {
    note: string;
}

export const commentFormDefault = {
    note: ''
};

export const projectReportDefault: IMonthlyEffortAddProjectReport = {
    id: '',
    projectReportInfo: {
        year: getCurrentYear(),
        month: getCurrentMonth(),
        projectId: null,
        userNamePM: '',
        startDate: '',
        endDate: '',
        projectType: '',
        milestoneApproveEntityList: [
            {
                date: '',
                status: 0,
                milestone: '',
                releasePackage: ''
            }
        ]
    },
    monthlyReport: {
        progressMilestone: {
            implPhase: '',
            progressAssesment: '',
            finishedTasks: '',
            completedMilestones: '',
            workCompleted: 0,
            delayNotFinishPlan: '',
            nextMilestone: ''
        },
        resource: {
            totalHC: 0,
            reviewEntityList: [
                {
                    description: '',
                    resourceReview: ''
                }
            ]
        },
        issueRiskEntityList: [
            {
                description: '',
                type: '',
                rootCause: '',
                proposedSolution: ''
            }
        ],
        nextPlan: [
            {
                task: '',
                comment: '',
                startDate: '',
                dueDate: ''
            }
        ],
        saleUpSalesEntityList: [
            {
                oppotunitiesExpand: '',
                changedOfSale: '',
                comment: '',
                potential_revenueVND: 0
            }
        ]
    },
    userUpdated: ''
};

export const commentFormSchema = yup.object().shape({
    note: yup.string().required(VALIDATE_MESSAGES.REQUIRED).max(5000, VALIDATE_MESSAGES.MAX_LENGTH)
});

// ============== Product report ============== //
export interface IProductReportConfig extends IPaginationParam {
    projectId: IOption;
}

export const productReportSchema = yup.object().shape({
    projectId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
});
