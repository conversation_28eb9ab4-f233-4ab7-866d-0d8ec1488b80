import { useEffect, useState } from 'react';

// project imports
import { yupResolver } from '@hookform/resolvers/yup';
import { useAppDispatch } from 'app/hooks';
import MainCard from 'components/cards/MainCard';
import { Table, TableFooter } from 'components/extended/Table';
import Api from 'constants/Api';
import { EApproveStatus, SEARCH_PARAM_KEY, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { TableToolbar } from 'containers';
import { FilterCollapse } from 'containers/search';
import {
    AddOrEditResignation,
    ApproveModal,
    ManageResignationSearch,
    ManageResignationTBody,
    ManageResignationTHead
} from 'containers/working-calendar';
import sendRequest from 'services/ApiService';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { IPaginationResponse, IResponseList } from 'types';
import { IResignationItem, IResignationList } from 'types/working-calendar';
import { getSearchParam, isEmpty, transformObject } from 'utils/common';
import { dateFormat } from 'utils/date';
import {
    IAddOrEditResignation,
    IManageResignationDefaultValues,
    addOrEditResignationDefaultValues,
    addOrEditResignationSchema,
    manageResignationDefaultValues
} from './Config';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// react-router-dom
import { useSearchParams } from 'react-router-dom';

// react-hook-form
import { useForm } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { IMember } from 'types/member';

// third party

// ==============================|| Manage Resignation ||============================== //
/**
 * URL Params
 * page
 * size
 * year
 * fullname
 * idHexstring
 * type
 * status
 */

const Resignation = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.idHexString,
        SEARCH_PARAM_KEY.fullname,
        SEARCH_PARAM_KEY.status
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, idHexString, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...manageResignationDefaultValues,
        ...cloneParams,
        memberId: params.idHexString ? { value: params.idHexString, label: params.fullname } : null
    };
    const dispatch = useAppDispatch();
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [isOpenApprove, setIsOpenApprove] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [conditions, setConditions] = useState<IManageResignationDefaultValues>(defaultConditions);
    const [formReset] = useState<any>(defaultConditions);
    const [resignations, setResignations] = useState<IResignationItem[]>([]);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [isLoadingForm, setIsLoadingForm] = useState<boolean>(false);
    const [approveResignationIdHexString, setApproveResignationIdHexString] = useState<string>('');
    const { manageResignation } = PERMISSIONS.workingCalendar;
    const intl = useIntl();

    // ================= Use form =================
    const methodsAddOrEdit = useForm({
        defaultValues: addOrEditResignationDefaultValues,
        mode: 'all',
        resolver: yupResolver(addOrEditResignationSchema)
    });

    // ================= Functions =================
    // set data empty
    const setDataEmpty = () => {
        setResignations([]);
        setIsLoading(false);
    };

    // get resignation
    const getResignation = async () => {
        setIsLoading(true);
        const response: IResponseList<IResignationList> = await sendRequest(Api.manage_resignation.getAll, {
            ...conditions,
            page: conditions.page + 1,
            memberId: conditions.memberId ? conditions.memberId.value : null
        });

        if (response) {
            const { status, result } = response;

            if (status) {
                const { content, pagination } = result;
                if (!isEmpty(content)) {
                    setResignations(content);
                    setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                    setIsLoading(false);
                } else {
                    setDataEmpty();
                }
            }
        } else {
            setDataEmpty();
        }
    };

    // Post or put leaves
    const addOrEditResignation = async (payload: IAddOrEditResignation, idHexString?: string) => {
        setIsLoadingForm(true);
        const { member, approver, ...clonePayload } = payload;
        const formatPayload = {
            ...clonePayload,
            memberIdHexString: member?.value,
            approverIdHexString: approver?.value,
            fromDate: dateFormat(payload.fromDate)
        };
        const api = isEdit ? Api.manage_resignation.putUpdate(idHexString!) : Api.manage_resignation.postCreate;
        const response = await sendRequest(api, formatPayload);
        if (response?.status) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: isEdit ? 'update-success' : 'add-success',
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            setIsLoadingForm(false);
            handleCloseFormAddOrEdit();
            getResignation();
        } else {
            dispatch(
                openSnackbar({
                    open: true,
                    isMultipleLanguage: false,
                    message: `${intl.formatMessage({ id: isEdit ? 'edit-failed' : 'add-failed' })}, ${intl.formatMessage({
                        id: 'record-already-exists'
                    })}`,
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
            setIsLoadingForm(false);
        }
    };

    // Approve resignation
    const handleApproveStatus = async (status: string) => {
        const response = await sendRequest(Api.manage_resignation.putApproved(approveResignationIdHexString), { status });
        if (response?.status) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: status === EApproveStatus.APPROVED ? 'approve-success' : 'decline-success',
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            handleCloseApprove();
            getResignation();
        }
    };

    // ================= Event =================
    // Handle change page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    // Handle change rows per page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    // Handle change member
    const handleChangeMember = (member: IMember) => {
        methodsAddOrEdit.setValue('title', member ? member.titleCode : '');
        methodsAddOrEdit.setValue('dept', member ? member.departmentId : '');
    };

    // Handle open add or edit resignation
    const handleOpenFormAddOrEdit = (item?: IResignationItem) => {
        setIsOpen(true);
        if (item) {
            setIsEdit(true);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { status, approvedDate, ...cloneLeave } = item;
            methodsAddOrEdit.reset({
                ...cloneLeave,
                member: { value: item.member.idHexString, label: item.member.fullName },
                approver: { value: item.approver.idHexString, label: item.approver.fullName }
            } as any);
        } else {
            methodsAddOrEdit.reset(addOrEditResignationDefaultValues);
        }
    };

    // Handle close form add or edit leaves
    const handleCloseFormAddOrEdit = () => {
        setIsEdit(false);
        setIsOpen(false);
    };

    // Handle approve
    const handleOpenApprove = (idHexString: string) => {
        setIsOpenApprove(true);
        setApproveResignationIdHexString(idHexString);
    };

    // Handle close approve
    const handleCloseApprove = () => {
        setIsOpenApprove(false);
    };

    // ================= Handle submit =================
    const handleSearch = (values: any) => {
        transformObject(values);
        const { memberId, ...cloneValues } = values;
        setSearchParams(memberId ? { ...cloneValues, idHexString: memberId.value, fullname: memberId.label } : cloneValues);
        setConditions(values);
    };

    // ================= Effect =================
    useEffect(() => {
        getResignation();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse>
                <ManageResignationSearch formReset={formReset} handleSearch={handleSearch} />
            </FilterCollapse>

            {/* Resignation list */}
            <MainCard>
                <TableToolbar handleOpen={checkAllowedPermission(manageResignation.add) ? handleOpenFormAddOrEdit : undefined} />
                <Table heads={<ManageResignationTHead />} isLoading={isLoading} data={resignations}>
                    <ManageResignationTBody
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        resignations={resignations}
                        handleEdit={handleOpenFormAddOrEdit}
                        handleOpenApprove={handleOpenApprove}
                    />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!isLoading && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {/* Form Add or Edit Resignation */}
            {isOpen && (
                <AddOrEditResignation
                    open={isOpen}
                    isEdit={isEdit}
                    handleClose={handleCloseFormAddOrEdit}
                    formReturn={methodsAddOrEdit}
                    addOrEditResignation={addOrEditResignation}
                    loading={isLoadingForm}
                    handleChangeMember={handleChangeMember}
                />
            )}

            {/* Approve Resignation */}
            <ApproveModal
                open={isOpenApprove}
                handleClose={handleCloseApprove}
                handleApprove={handleApproveStatus}
                content="approve-resign-request"
            />
        </>
    );
};

export default Resignation;
