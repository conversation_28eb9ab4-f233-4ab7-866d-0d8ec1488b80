// third party
import * as yup from 'yup';

// project imports
import { IOption, IPaginationParam } from 'types';
import { paginationParamDefault } from 'constants/Common';
import { getCurrentYear } from 'utils/date';
import { VALIDATE_MESSAGES } from 'constants/Message';

// ============== Manage resignation ============== //
// Filter
export interface IManageResignationDefaultValues extends IPaginationParam {
    year: number;
    memberId: IOption | null;
    status: string;
}

export const manageResignationDefaultValues: IManageResignationDefaultValues = {
    ...paginationParamDefault,
    year: getCurrentYear(),
    memberId: null,
    status: ''
};

export const manageResignationSearchSchema = yup.object().shape({
    year: yup.string(),
    memberId: yup
        .object()
        .shape({
            value: yup.string().nullable(),
            label: yup.string()
        })
        .nullable(),
    status: yup.string().nullable()
});

// Add or edit
export interface IAddOrEditResignation {
    member: IOption | null;
    approver: IOption | null;
    title: string;
    dept: string;
    fromDate: Date | null;
    reason: string;
}

export const addOrEditResignationDefaultValues: IAddOrEditResignation = {
    member: null,
    approver: null,
    title: '',
    dept: '',
    fromDate: null,
    reason: ''
};

export const addOrEditResignationSchema = yup.object().shape({
    member: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
        .required(VALIDATE_MESSAGES.REQUIRED),
    approver: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
        .required(VALIDATE_MESSAGES.REQUIRED),
    fromDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    reason: yup.string().nullable()
});
