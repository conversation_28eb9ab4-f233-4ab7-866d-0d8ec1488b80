import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

// material-ui

// project imports
import MainCard from 'components/cards/MainCard';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { IBudgetingPlan, IBudgetingPlanItem, ITotalBudgetingPlan } from 'types';
import { getSearchParam, transformObject } from 'utils/common';
import { IBudgetingPlanSearch, budgetingPlanSearchConfig } from './Config';
import { Table } from 'components/extended/Table';
import Api from 'constants/Api';
import { BudgetingPlanSearch, BudgetingPlanTBody, BudgetingPlanThead, OnGoingTotal } from 'containers/sales';
import EditBudgetingPlan from 'containers/sales/EditBudgetingPlan';
import { FilterCollapse } from 'containers/search';
import sendRequest from 'services/ApiService';
import { useAppDispatch } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { closeConfirm } from 'store/slice/confirmSlice';
import useConfig from 'hooks/useConfig';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

const BudgetingPlan = () => {
    const [loading, setLoading] = useState<boolean>(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.type, SEARCH_PARAM_KEY.pipelineType];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    const dispatch = useAppDispatch();

    const { locale } = useConfig();

    const defaultConditions = { ...budgetingPlanSearchConfig, ...params, language: locale };
    const [formReset] = useState<IBudgetingPlanSearch>(defaultConditions);
    const [conditions, setConditions] = useState<IBudgetingPlanSearch>(defaultConditions);
    const [budgetingPlans, setBudgetingPlans] = useState<IBudgetingPlan[]>([]);
    const [budgetingPlan, setBudgetingPlan] = useState<IBudgetingPlanItem>();
    const [total, setTotal] = useState<ITotalBudgetingPlan[]>([]);
    const [open, setOpen] = useState<boolean>(false);
    const [editLoading, setEditLoading] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(false);

    const [isEditedTotalList, setIsEditedTotalList] = useState(false);

    const { budgetingPermission } = PERMISSIONS.sale.salePipeline;

    const getDataTable = async () => {
        setLoading(true);
        const response = await sendRequest(Api.budgeting_plan.getAll, {
            ...conditions
        });
        if (response) {
            const { status, result } = response;

            if (status && result) {
                setLoading(false);
                const { data } = result.content;
                setBudgetingPlans(data);
            } else {
                setDataEmpty();
            }
        } else {
            setDataEmpty();
        }
    };

    const getTotal = async () => {
        setLoading(true);
        const response = await sendRequest(Api.budgeting_plan.getTotal, {
            ...conditions
        });
        if (response) {
            const { status, result } = response;

            if (status && result) {
                const { total } = result.content;
                setTotal((total as ITotalBudgetingPlan[]).filter((item) => item.show));
                setLoading(false);
            } else {
                setTotal([]);
                setLoading(false);
            }
        } else {
            setTotal([]);
            setLoading(false);
        }
    };

    const setDataEmpty = () => {
        setBudgetingPlans([]);
        setLoading(false);
    };

    // Event
    const handleOpenDialog = (item: IBudgetingPlanItem) => {
        setIsEdit(item ? true : false);
        setBudgetingPlan(item);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    // Handle submit
    const handleSearch = (value: IBudgetingPlanSearch) => {
        transformObject(value);
        setSearchParams(value as any);
        setConditions({ ...value });
    };

    const handleEditBudgetingPlan = async (payload: IBudgetingPlanItem) => {
        setEditLoading(true);
        const response = await sendRequest(Api.budgeting_plan.editBudgetingPlan, payload);
        if (response) {
            if (response.status) {
                setEditLoading(false);
                setOpen(false);
                getDataTable();
                dispatch(
                    openSnackbar({
                        open: true,
                        message: 'update-success',
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
            } else {
                setEditLoading(false);
            }
        } else {
            setEditLoading(false);
        }
        dispatch(closeConfirm());
    };

    const hanldeConfirmEditList = async (list: ITotalBudgetingPlan[]) => {
        setLoading(true);
        const res = await sendRequest(Api.flexible_report.editArrangement, list);
        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? 'update-success' : 'update-fail',
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );
        if (res.status) {
            setTotal(list);
            setIsEditedTotalList(false);
        }
        setLoading(false);
    };

    // Effects
    useEffect(() => {
        getDataTable();
        getTotal();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse>
                <BudgetingPlanSearch formReset={formReset} handleSearch={handleSearch} />
            </FilterCollapse>

            {/* Total */}
            {total?.length > 0 && (
                <OnGoingTotal
                    total={total}
                    loading={loading}
                    isEdited={isEditedTotalList}
                    setIsEdited={setIsEditedTotalList}
                    handleConFirmEdit={checkAllowedPermission(budgetingPermission.editRows) ? hanldeConfirmEditList : undefined}
                />
            )}

            {/* Table */}
            <MainCard>
                <Table heads={<BudgetingPlanThead />} isLoading={loading} data={budgetingPlans}>
                    <BudgetingPlanTBody handleOpen={handleOpenDialog} data={budgetingPlans} />
                </Table>
            </MainCard>

            {/* Edit Budgeting Plan */}
            {open && (
                <EditBudgetingPlan
                    open={open}
                    isEdit={isEdit}
                    budgetingPlan={budgetingPlan}
                    handleClose={handleCloseDialog}
                    loading={editLoading}
                    editBudgetingPlan={handleEditBudgetingPlan}
                />
            )}
        </>
    );
};

export default BudgetingPlan;
