/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';

// project imports
import { useAppDispatch } from 'app/hooks';
import MainCard from 'components/cards/MainCard';
import { Table, TableFooter } from 'components/extended/Table';
import Api from 'constants/Api';
import { SEARCH_PARAM_KEY, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { TableToolbar } from 'containers';
import { AddOrEditBidding, BiddingSearch, BiddingTBody, BiddingThead, BiddingTotal, CommentPopover } from 'containers/sales';
import { FilterCollapse } from 'containers/search';
import sendRequest from 'services/ApiService';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { openSnackbar } from 'store/slice/snackbarSlice';
import {
    IBiddingResponse,
    ICurrencyBidding,
    ICurrencyBiddingList,
    IDetailBidding,
    IDetailBiddingResponse,
    IPaginationResponse,
    IResponseList,
    ISaleBiddingItem,
    ITotalBidding,
    ITotalBiddingResponse
} from 'types';
import { checkAllowedPermission } from 'utils/authorization';
import { getSearchParam, isEmpty, transformObject } from 'utils/common';
import { IBiddingFilterConfig, biddingFilterConfig } from './Config';

// third party
import { FormattedMessage } from 'react-intl';
import { useSearchParams } from 'react-router-dom';

// material-ui
import { PopoverVirtualElement } from '@mui/material';
import useConfig from 'hooks/useConfig';

// ==============================|| Bidding ||============================== //
/**
 *  URL Params
 *  page
 *  size
 *  type
 *  year
 *  projectName
 *  status
 */
const Bidding = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.type,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.status,
        SEARCH_PARAM_KEY.searchMode,
        SEARCH_PARAM_KEY.projectName,
        SEARCH_PARAM_KEY.customer,
        SEARCH_PARAM_KEY.searchModeOfCustomer,
        SEARCH_PARAM_KEY.searchModeOfProject
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);

    const { locale } = useConfig();

    // Hooks, State, Variable
    const dispatch = useAppDispatch();
    const defaultConditions = {
        ...biddingFilterConfig,
        ...params,
        language: locale
    };
    const [loading, setLoading] = useState<boolean>(false);
    const [loadingPost, setLoadingPost] = useState<boolean>(false);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [saleBiddings, setSaleBiddings] = useState<ISaleBiddingItem[]>([]);
    const [detailBidding, setDetailBidding] = useState<IDetailBidding | null>(null);
    const [totalBidding, setTotalBidding] = useState<ITotalBidding[]>([]);
    const [conditions, setConditions] = useState<IBiddingFilterConfig>(defaultConditions);
    const [formReset] = useState<IBiddingFilterConfig>(defaultConditions);
    const [isEditBidding, setIsEditBidding] = useState<boolean>(false);
    const [openFormAddOrEditBidding, setOpenFormAddOrEditBidding] = useState<boolean>(false);
    const { biddingPermission } = PERMISSIONS.sale.salePipeline;
    const [exchangeRateUSDpercentVND, setExchangeRateUSDpercentVND] = useState<number>(0);
    const [currencyAndExchangeRateDefault, setCurrencyAndExchangeRateDefault] = useState<ICurrencyBidding | null>(null);
    const [monthlyBillableDay, setMonthlyBillableDay] = useState<any>();
    const [commentItem, setCommentItem] = useState<any>(null);
    const [isEditComment, setIsEditComment] = useState<boolean>(false);
    const [anchorElComment, setAnchorElComment] = useState<
        Element | (() => Element) | PopoverVirtualElement | (() => PopoverVirtualElement) | null | undefined
    >(null);
    const [isRefresh, setIsRefresh] = useState<boolean>(false);

    const [isEditedTotalList, setIsEditedTotalList] = useState(false);

    // ================= Functions =================
    // Get Bidding
    const getDataTable = async () => {
        setLoading(true);
        const response: IResponseList<IBiddingResponse> = await sendRequest(Api.sale_pipe_line_bidding.getBidding, {
            ...conditions,
            page: conditions.page + 1
        });

        if (response) {
            const { status, result } = response;

            if (status) {
                const { content, pagination } = result;
                if (!isEmpty(content)) {
                    setSaleBiddings(content.dataList);
                    setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                    setLoading(false);
                } else {
                    setDataEmpty();
                }
            }
        } else {
            setDataEmpty();
        }
    };

    const getTotalBidding = async () => {
        setLoading(true);
        const response: IResponseList<ITotalBiddingResponse> = await sendRequest(Api.sale_pipe_line_bidding.getTotal, {
            ...conditions
        });

        if (response) {
            const { status, result } = response;

            if (status) {
                const { content } = result;
                if (!isEmpty(content)) {
                    setTotalBidding(content.total.filter((item) => item.show));
                    setLoading(false);
                } else {
                    setTotalBidding([]);
                    setLoading(false);
                }
            }
        } else {
            setTotalBidding([]);
            setLoading(false);
        }
    };

    // Get monthly billable day
    const getMonthlyBillableDay = async () => {
        const params = { year: conditions.year };
        const response = await sendRequest(Api.sale_pipe_line_bidding.getMonthlyBillable, params);
        if (response?.status) {
            const { result } = response;
            if (result) {
                setMonthlyBillableDay(result.content);
            }
        } else return;
    };

    // Get Exchange rate USD / VND
    const getCurrencyAndExchangeRateDefault = async () => {
        const response: IResponseList<ICurrencyBiddingList> = await sendRequest(Api.sale_productivity.getExchangeRate, {
            year: conditions.year,
            convert: 'Yes'
        });
        if (response) {
            if (response.status) {
                const { content } = response.result;
                if (!isEmpty(content)) {
                    setCurrencyAndExchangeRateDefault(content[0]);
                } else {
                    setCurrencyAndExchangeRateDefault(null);
                }
            }
        } else return;
    };

    // Get exchange rate usd / vnd
    const getExchangeRateUSDpercentVND = async () => {
        const response: IResponseList<ICurrencyBiddingList> = await sendRequest(Api.sale_productivity.getExchangeRate, {
            year: conditions.year,
            convert: 'Yes',
            currency: 'USD'
        });
        if (response) {
            if (response.status) {
                const { content } = response.result;
                if (!isEmpty(content)) {
                    setExchangeRateUSDpercentVND(content[0].exchangeRate);
                } else return;
            }
        } else return;
    };

    // Set data empty
    const setDataEmpty = () => {
        setSaleBiddings([]);
        setLoading(false);
    };

    // postAddOrEditBidding
    const postAddOrEditBidding = async (payload: any) => {
        setLoadingPost(true);
        const response = await sendRequest(Api.sale_pipe_line_bidding.postAddOrEditBidding, payload);
        if (response) {
            if (response?.status) {
                setLoadingPost(false);
                setOpenFormAddOrEditBidding(false);
                setIsRefresh(!isRefresh);
                dispatch(closeConfirm());
                getDataTable();
                dispatch(
                    openSnackbar({
                        open: true,
                        message: isEditBidding ? 'update-success' : 'add-success',
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
            } else {
                setLoadingPost(false);
                dispatch(
                    openSnackbar({
                        open: true,
                        message: response.result.content,
                        variant: 'alert',
                        alert: { color: 'warning' }
                    })
                );
            }
        }
        setLoadingPost(false);
    };

    // Get detail bidding
    const getDetailBidding = async (idHexString: string) => {
        const response: IResponseList<IDetailBiddingResponse> = await sendRequest(Api.sale_pipe_line_bidding.getDetailBidding, {
            year: conditions.year,
            idHexString
        });
        if (response?.status) {
            setOpenFormAddOrEditBidding(true);
            const { result } = response;
            setDetailBidding(result.content);
        } else return;
    };

    // delete bidding
    const deleteProjectBidding = async (idHexString: string) => {
        const response = await sendRequest(Api.sale_pipe_line_bidding.deleteBidding, { idHexString });
        if (response.status) {
            dispatch(closeConfirm());
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'delete-success',
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            getDataTable();
        }
    };

    // post edit comment bidding
    const postEditComment = async (payload?: any) => {
        const response = await sendRequest(Api.sale_pipe_line_bidding.comment, { ...payload, year: conditions.year });
        if (response?.status) {
            dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));
            setIsEditComment(false);
            const { content } = response.result;
            const biddingListUpdate = saleBiddings.map((prv) => {
                if (prv.idHexString === content.idHexString) {
                    return content;
                }
                return prv;
            });
            setSaleBiddings(biddingListUpdate);
        } else return;
    };

    // ================= Event =================
    // Handle delete bidding
    const handleOpenDeleteProjectBidding = (idHexString: string) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: (
                    <>
                        <FormattedMessage id="messege-delete" />
                    </>
                ),
                handleConfirm: () => deleteProjectBidding(idHexString)
            })
        );
    };

    // Handle open add or edit modal bidding
    const handleOpenFormAddOrUpdateBidding = (idHexString?: string) => {
        if (idHexString) {
            setIsEditBidding(true);
            getDetailBidding(idHexString);
            return;
        }
        setOpenFormAddOrEditBidding(true);
        setDetailBidding(null);
    };

    // Handle close form add or update bidding
    const handleCloseFormAddOrUpdateBidding = () => {
        setOpenFormAddOrEditBidding(false);
        setIsEditBidding(false);
        setDetailBidding(null);
    };

    // Handle change page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    // Handle change rows per page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    // Handle comment bidding
    const handleOpenCommentBidding = (event: React.MouseEvent<Element>, item: any) => {
        setAnchorElComment(event.currentTarget);
        const { comment, ...cloneItem } = item;
        setCommentItem({ ...cloneItem, content: comment });
    };

    // Handle close comment bidding
    const handleCloseCommentBidding = () => {
        setAnchorElComment(null);
        setIsEditComment(false);
    };

    // ================= Handle submit =================
    const handleSearch = (values: any) => {
        transformObject(values);
        setSearchParams(values);
        setConditions(values);
    };

    const hanldeConfirmEditList = async (list: ITotalBidding[]) => {
        const res = await sendRequest(Api.flexible_report.editArrangement, list);
        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? 'update-success' : 'update-fail',
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );
        if (res.status) {
            setTotalBidding(list);
            setIsEditedTotalList(false);
        }
    };
    // ================= Effect =================
    useEffect(() => {
        getDataTable();
        getMonthlyBillableDay();
        getExchangeRateUSDpercentVND();
        getCurrencyAndExchangeRateDefault();
        getTotalBidding();
    }, [conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <BiddingSearch formReset={formReset} handleSearch={handleSearch} isRefresh={isRefresh} />
            </FilterCollapse>
            {/* Bidding Total  */}
            {totalBidding.length > 0 && (
                <BiddingTotal
                    totalBidding={totalBidding}
                    loading={loading}
                    isEdited={isEditedTotalList}
                    setIsEdited={setIsEditedTotalList}
                    handleConFirmEdit={hanldeConfirmEditList}
                />
            )}
            {/* Bidding List */}
            <MainCard>
                <TableToolbar
                    handleOpen={checkAllowedPermission(biddingPermission.add) ? handleOpenFormAddOrUpdateBidding : undefined}
                    handleRefreshData={getDataTable}
                />
                <Table heads={<BiddingThead />} isLoading={loading} data={saleBiddings}>
                    <BiddingTBody
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        handleOpen={handleOpenFormAddOrUpdateBidding}
                        saleBiddings={saleBiddings}
                        handleDelete={handleOpenDeleteProjectBidding}
                        handleOpenComment={handleOpenCommentBidding}
                    />
                </Table>
            </MainCard>
            {/* Pagination  */}
            {!loading && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
            {/* Form Add or Edit Bidding */}
            {openFormAddOrEditBidding && (
                <AddOrEditBidding
                    monthlyBillableDay={monthlyBillableDay}
                    year={conditions.year}
                    exchangeRateUSDpercentVND={exchangeRateUSDpercentVND}
                    currencyAndExchangeRateDefault={currencyAndExchangeRateDefault!}
                    open={openFormAddOrEditBidding}
                    handleClose={handleCloseFormAddOrUpdateBidding}
                    isEdit={isEditBidding}
                    postAddOrEditBidding={postAddOrEditBidding}
                    detailBidding={detailBidding}
                    loading={loadingPost}
                />
            )}
            {/* Comment */}
            <CommentPopover
                isSalesPipeLine
                item={commentItem!}
                anchorEl={anchorElComment}
                handleClose={handleCloseCommentBidding}
                isEdit={isEditComment}
                setIsEdit={setIsEditComment}
                editComment={postEditComment}
            />
        </>
    );
};

export default Bidding;
