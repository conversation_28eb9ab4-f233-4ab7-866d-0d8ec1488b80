/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect, useState } from 'react';

// third party
import { useSearchParams } from 'react-router-dom';

// material-ui
import { PopoverVirtualElement, SelectChangeEvent } from '@mui/material';

// project imports
import { store } from 'app/store';
import MainCard from 'components/cards/MainCard';
import { Table } from 'components/extended/Table';
import Api from 'constants/Api';
import { CONTRACT_TYPE_SALE_REPORT, DEPARTMENTS, SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, UNIT_SALE_REPORT } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import {
    AddOrEditProductionPerformance,
    CommentPopover,
    EditHeadCount,
    MonthlyProductionPerformanceSearch,
    MonthlyProductionPerformanceTBody,
    MonthlyProductionPerformanceThead
} from 'containers/sales';
import { FilterCollapse } from 'containers/search';
import sendRequest from 'services/ApiService';
import { openSnackbar } from 'store/slice/snackbarSlice';
import {
    IAddOrEditProductivityResponse,
    ICommentForm,
    ICommentItem,
    IDataHCByMonth,
    IDepartmentPerformance,
    IDepartmentPerformanceData,
    IEditCommentResponse,
    IHeadCountValueByMonthDetailResponse,
    ILastYearProductivity,
    IMonthlyProductionPerformanceAddOrEditForm,
    IMonthlyProductionPerformanceInfo,
    IMonthlyProductionPerformanceResponse,
    IOption,
    IProductivity,
    IProductivityHeadCountEditForm,
    IResponseList,
    ISaleTotal
} from 'types';
import { checkAllowedPermission } from 'utils/authorization';
import { exportDocument, getDataProductivityByMonth, getSearchParam, isEmpty, transformObject } from 'utils/common';
import { authSelector } from 'store/slice/authSlice';
import { convertMonthFromToDate, getMonthsOfYear } from 'utils/date';
import {
    IMonthlyProductionPerformanceFilterConfig,
    monthlyProductionPerformanceFilterConfig,
    monthlyProductionPerformanceInfoDefault,
    productionPerformanceAddOrEditFormDefault,
    productivityHeadCountEditFormDefault
} from './Config';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import useConfig from 'hooks/useConfig';

// ==============================|| Monthly Production Performance ||============================== //

const MonthlyProductionPerformance = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [SEARCH_PARAM_KEY.year];
    const keyParamsArray = [SEARCH_PARAM_KEY.month];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams, keyParamsArray);
    transformObject(params);
    const { locale } = useConfig();

    // Hooks, State, Variable
    const defaultConditions = { ...monthlyProductionPerformanceFilterConfig, ...params, language: locale };
    const { userInfo } = useAppSelector(authSelector);
    const [loading, setLoading] = useState<boolean>(false);
    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);
    const [open, setOpen] = useState<boolean>(false);
    const [openHC, setOpenHC] = useState<boolean>(false);
    const [monthlyProductionPerformanceInfo, setMonthlyProductionPerformanceInfo] = useState<IMonthlyProductionPerformanceInfo>(
        monthlyProductionPerformanceInfoDefault
    );

    const [conditions, setConditions] = useState<IMonthlyProductionPerformanceFilterConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IMonthlyProductionPerformanceFilterConfig>(defaultConditions);
    const [months, setMonths] = useState<IOption[]>([]);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const [productivity, setProductivity] = useState<IMonthlyProductionPerformanceAddOrEditForm>(productionPerformanceAddOrEditFormDefault);
    const [productivityHeadCount, setProductivityHeadCount] = useState<IProductivityHeadCountEditForm>(
        productivityHeadCountEditFormDefault
    );
    const [anchorEl, setAnchorEl] = useState<
        Element | (() => Element) | PopoverVirtualElement | (() => PopoverVirtualElement) | null | undefined
    >(null);
    const [commentItem, setCommentItem] = useState<ICommentItem | null>(null);
    const [isEditComment, setIsEditComment] = useState<boolean>(false);
    const { monthlyProductionPerformancePermission } = PERMISSIONS.sale;
    const [loadingData, setLoadingData] = useState<boolean>(false);
    const [isEdited, setIsEdited] = useState(false);

    // Functions
    const getDataTable = async () => {
        setLoading(true);
        const response: IResponseList<IMonthlyProductionPerformanceResponse> = await sendRequest(Api.sale_productivity.getAll, {
            ...conditions
        });
        if (response) {
            const { status, result } = response;
            if (status) {
                setMonthlyProductionPerformanceInfo(result.content as IMonthlyProductionPerformanceInfo);
            } else {
                setDataEmpty();
            }
            setLoading(false);
        } else {
            setDataEmpty();
        }
    };

    const getDetailMonths = async (idHexString: string, month?: any, standardWorkingDayOfMonth?: string) => {
        setLoadingData(true);
        const params = { idHexString, year: conditions.year };
        const response = await sendRequest(Api.sale_productivity.getDetail, params);
        const { status, result } = response;
        if (response && status) {
            setLoadingData(false);
            const data = result.content;
            const getMonth = months.filter((month) => month.value === data.productivity[0].month);

            let foundObject = {
                month: 1,
                delivered: {
                    value: 0,
                    comment: ''
                },
                receivable: {
                    value: 0,
                    comment: ''
                },
                received: {
                    value: 0,
                    comment: ''
                },
                financial: {
                    value: 0,
                    comment: ''
                },
                hcInfo: [],
                year: year
            };

            data.productivity.forEach((item: ILastYearProductivity, index: number) => {
                // eslint-disable-next-line eqeqeq
                if (item.month == month) {
                    foundObject = data.productivity[index];
                }
            });
            const { fromDate, toDate } = convertMonthFromToDate(getMonth[0].label);
            const standardWorkingDay = await getStandardWorkingDay(fromDate, toDate);
            const payload = {
                idHexString: data.idHexString,
                year: conditions.year,
                month: month ? foundObject.month : data.productivity[0].month,
                standardWorkingDay: month ? standardWorkingDayOfMonth : standardWorkingDay.toString() || '1',
                projectId: { value: data.project?.projectId, label: data.project?.projectName },
                departmentId: data.departmentId,
                projectName: data.project.projectName,
                contractSize: data.contractSize.value,
                serviceType: data.serviceType,
                contractType: data.contractType,
                originalContractSize: data.originalContractSize,
                unit: data.unit || UNIT_SALE_REPORT.MAN_MONTH,
                contractAllocation: data.contractAllocation,
                duration: data.duration,
                paymentTerm: data.paymentTerm || 1,
                currency: data.exchangeRate.label,
                exchangeRate: data.exchangeRate.value,
                delivered: month ? foundObject.delivered.value : data?.productivity[0].delivered.value,
                receivable: month ? foundObject.receivable.value : data?.productivity[0].receivable.value,
                received: month ? foundObject.received.value : data?.productivity[0].received.value,
                financial: month ? foundObject.financial.value : data?.productivity[0].financial.value,
                deliveredCurrency: month
                    ? foundObject.delivered.value * data.exchangeRate.value
                    : data?.productivity[0].delivered.value * data.exchangeRate.value,
                receivableCurrency: month
                    ? foundObject.receivable.value * data.exchangeRate.value
                    : data?.productivity[0].receivable.value * data.exchangeRate.value,
                receivedCurrency: month
                    ? foundObject.received.value * data.exchangeRate.value
                    : data?.productivity[0].received.value * data.exchangeRate.value,
                financialCurrency: month
                    ? foundObject.financial.value * data.exchangeRate.value
                    : data?.productivity[0].financial.value * data.exchangeRate.value,
                hcInfo: month ? foundObject.hcInfo : data?.productivity[0]?.hcInfo || [],
                productivity: data?.productivity || [],
                lastYearProductivity: data.lastYearProductivity
            };
            setProductivity(payload);
        }
    };

    const getStandardWorkingDay = async (fromDate: string, toDate: string) => {
        let standardWorkingDay = 0;
        const params = { fromDate, toDate };
        const response = await sendRequest(Api.sale_productivity.getStandardWorkingDay, params);
        const { status, result } = response;
        if (response && status) {
            const { value } = result.content;
            standardWorkingDay = value;
        }
        return standardWorkingDay;
    };

    const onChangeYearGetStandardWorkingDay = async (
        payload: IMonthlyProductionPerformanceAddOrEditForm,
        fromDate: string,
        toDate: string,
        month?: number,
        paymentTerm?: string
    ) => {
        const standardWorkingDayValue = await getStandardWorkingDay(fromDate, toDate);
        const productivityBase = productivity.productivity ? productivity.productivity : [];
        const productivityLastYear = productivity.lastYearProductivity ? productivity.lastYearProductivity : [];
        const exchangeRate = payload.exchangeRate || 1;

        let payloadBase = {
            ...payload,
            month: month ? month : 1,
            standardWorkingDay: String(standardWorkingDayValue),
            paymentTerm: paymentTerm ? paymentTerm : payload.paymentTerm
        };
        let productivityArray =
            productivity.productivity &&
            productivity.productivity.filter((el: IProductivity) => {
                return el.month === month;
            });

        const productivityDefaultValue = {
            ...payloadBase,
            delivered: 0,
            receivable: 0,
            received: 0,
            financial: 0,
            deliveredCurrency: 0,
            receivableCurrency: 0,
            receivedCurrency: 0,
            financialCurrency: 0,
            hcInfo: []
        };
        const createProductivityObject = (item: any) => {
            return {
                ...payloadBase,
                delivered: item.delivered?.value || 0,
                receivable: item.receivable?.value || 0,
                received: item.received?.value || 0,
                financial: item.financial?.value || 0,
                deliveredCurrency: item.delivered?.value * exchangeRate,
                receivableCurrency: item.receivable?.value * exchangeRate,
                receivedCurrency: item.received?.value * exchangeRate,
                financialCurrency: item.financial?.value * exchangeRate,
                hcInfo: item.hcInfo || []
            };
        };

        // TH productivityArray trả về mảng rỗng
        if (payload.contractType === CONTRACT_TYPE_SALE_REPORT.TM && payload.departmentId === DEPARTMENTS.SCS) {
            if (
                Array.isArray(productivityArray) &&
                (productivityArray.length === 0 ||
                    (productivityArray[0].delivered?.value === 0 &&
                        productivityArray[0].receivable?.value === 0 &&
                        productivityArray[0].received?.value === 0 &&
                        productivityArray[0].financial?.value === 0))
            ) {
                // Month > Payment Term
                if (month && +month > +(paymentTerm ? paymentTerm : payload.paymentTerm)) {
                    let arr1 = getDataProductivityByMonth(productivityBase, +month - +(paymentTerm ? paymentTerm : payload.paymentTerm));

                    setProductivity(
                        arr1 && arr1.length > 0
                            ? {
                                  ...payloadBase,
                                  delivered: arr1[0].delivered?.value,
                                  receivable: arr1[0].delivered?.value,
                                  received: arr1[0].delivered?.value,
                                  financial: arr1[0].delivered?.value
                              }
                            : productivityDefaultValue
                    );
                }

                // Month = Payment Term
                if (month && +month === +(paymentTerm ? paymentTerm : payload.paymentTerm)) {
                    let arr2 = getDataProductivityByMonth(productivityLastYear, 12);

                    setProductivity(
                        arr2 && arr2.length > 0
                            ? {
                                  ...payloadBase,
                                  delivered: arr2[0].delivered?.value,
                                  receivable: arr2[0].delivered?.value,
                                  received: arr2[0].delivered?.value,
                                  financial: arr2[0].delivered?.value,
                                  hcInfo: []
                              }
                            : productivityDefaultValue
                    );
                }

                // Month < Payment Term
                if (month && +month < +(paymentTerm ? paymentTerm : payload.paymentTerm)) {
                    const count = +(paymentTerm ? paymentTerm : payload.paymentTerm) - +month;
                    const countFinal = 12 - count;
                    let arr3 = getDataProductivityByMonth(productivityLastYear, countFinal);

                    setProductivity(
                        arr3 && arr3.length > 0
                            ? {
                                  ...payloadBase,
                                  delivered: arr3[0].delivered?.value,
                                  receivable: arr3[0].delivered?.value,
                                  received: arr3[0].delivered?.value,
                                  financial: arr3[0].delivered?.value,
                                  hcInfo: []
                              }
                            : productivityDefaultValue
                    );
                }
            } else {
                productivityArray && productivityArray.length > 0 && setProductivity(createProductivityObject(productivityArray[0]));
            }
        } else {
            let arr4 = getDataProductivityByMonth(productivityBase, month!);
            setProductivity(arr4 && arr4.length > 0 ? createProductivityObject(arr4[0]) : productivityDefaultValue);
        }
    };

    const getHeadCountValueByMonth = async (month: string) => {
        const params = { month, year: conditions.year };
        const response: IResponseList<IHeadCountValueByMonthDetailResponse> = await sendRequest(
            Api.sale_productivity.getDetailHeadCountByMonth,
            params
        );
        if (response) {
            const { status, result } = response;

            if (status) {
                const data = result.content as IDataHCByMonth;
                const isEmptyData = isEmpty(data);
                setProductivityHeadCount({
                    ...productivityHeadCount,
                    month,
                    value: !isEmptyData ? data.value : ''
                });
            }
        }
    };

    const setDataEmpty = () => {
        setMonthlyProductionPerformanceInfo(monthlyProductionPerformanceInfoDefault);
        setLoading(false);
    };

    const getMonthInYears = useCallback(async (y: number) => {
        const monthInYears = await getMonthsOfYear(y);
        return monthInYears;
    }, []);

    // Event
    const handleChangeYear = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value;
        setYear(value as number);
        setIsChangeYear(true);
    };

    const handleExportDocument = () => {
        exportDocument(Api.sale_productivity.getDownload.url, { year: conditions.year, userName: userInfo?.userName, language: locale });
    };

    const handleOpenDialog = async (idHexString: string) => {
        getDetailMonths(idHexString);
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const handleOpenEditHeadCountDialog = (item: IDataHCByMonth[]) => {
        setProductivityHeadCount({
            year: conditions.year,
            month: conditions.month ? conditions.month[0] : '1',
            value: item[0].value
        });
        setOpenHC(true);
    };

    const handleCloseEditHeadCountDialog = () => {
        setOpenHC(false);
    };

    const postAddOrEditProductivity = async (payload: IMonthlyProductionPerformanceAddOrEditForm) => {
        setAddOrEditLoading(true);
        const response: IResponseList<IAddOrEditProductivityResponse> = await sendRequest(
            Api.sale_productivity.postCreateOrUpdate,
            payload
        );
        if (response) {
            const { status } = response;
            setAddOrEditLoading(false);
            store.dispatch(
                openSnackbar({
                    open: true,
                    message: status ? 'update-success' : 'exist-project-name',
                    variant: 'alert',
                    alert: { color: status ? 'success' : 'warning' }
                })
            );
            getDetailMonths(response.result?.content?.message?.idHexString, payload.month, payload.standardWorkingDay);
            getDataTable();
        } else {
            setAddOrEditLoading(false);
        }
    };

    const postEditHeadCount = async (payload: IProductivityHeadCountEditForm) => {
        setAddOrEditLoading(true);
        const response: IResponseList<IAddOrEditProductivityResponse> = await sendRequest(
            Api.sale_productivity.postUpdateHeadCount,
            payload
        );
        if (response) {
            const { status } = response;
            if (status) {
                setAddOrEditLoading(false);
                handleCloseEditHeadCountDialog();
                store.dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));
                getDataTable();
            } else {
                setAddOrEditLoading(false);
            }
        } else {
            setAddOrEditLoading(false);
        }
    };

    const postEditComment = async (payload?: ICommentForm) => {
        const response: IResponseList<IEditCommentResponse> = await sendRequest(Api.sale_productivity.postComment, {
            ...payload,
            months: conditions.month,
            year: conditions.year
        });

        if (response) {
            const result = response.result.content;
            const departmentNews = monthlyProductionPerformanceInfo.departments.map((department: IDepartmentPerformance) => ({
                ...department,
                data: department.data.map((el: IDepartmentPerformanceData) => (el.idHexString === result.idHexString ? result : el))
            }));
            setMonthlyProductionPerformanceInfo({ ...monthlyProductionPerformanceInfo, departments: departmentNews });
            store.dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));
            setIsEditComment(false);
        }
    };

    const handleOpenComment = (event: React.MouseEvent<Element>, item: ICommentItem) => {
        setAnchorEl(event.currentTarget);
        setCommentItem(item);
    };

    const handleClose = () => {
        setAnchorEl(null);
        setIsEditComment(false);
    };

    // Handle submit
    const handleSearch = (value: IMonthlyProductionPerformanceFilterConfig) => {
        transformObject(value);
        setSearchParams(value as any);
        setConditions(value);
    };

    const handleEditProductivity = (productivityEdit: IMonthlyProductionPerformanceAddOrEditForm) => {
        postAddOrEditProductivity(productivityEdit);
    };

    const handleEditHeadCount = (headCountEdit: IProductivityHeadCountEditForm) => {
        postEditHeadCount(headCountEdit);
    };

    const handleEditComment = (comment?: ICommentForm) => {
        postEditComment(comment);
    };
    const dispatch = useAppDispatch();
    const handleConFirmEditRows = async (orderList: ISaleTotal[]) => {
        const res = await sendRequest(Api.flexible_report.editArrangement, orderList);
        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? 'update-success' : 'update-fail',
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );
        if (res.status) {
            setIsEdited(false);
            getDataTable();
        }
    };
    // Effect
    useEffect(() => {
        getDataTable();
    }, [conditions]);

    useEffect(() => {
        getMonthInYears(year).then((items: IOption[]) => {
            setMonths(items);
            if (items.length > 0 && isChangeYear) setFormReset({ ...formReset, year, month: [] });
        });
    }, [year]);

    return (
        <>
            {/* Filter */}
            <FilterCollapse
                handleExport={checkAllowedPermission(monthlyProductionPerformancePermission.download) ? handleExportDocument : undefined}
                downloadLabel={TEXT_CONFIG_SCREEN.salesReport.monthlyProductionPerformance + '-download-report'}
            >
                <MonthlyProductionPerformanceSearch
                    conditions={formReset}
                    months={months}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            {/* Table */}
            <MainCard>
                <Table
                    heads={
                        <MonthlyProductionPerformanceThead
                            months={monthlyProductionPerformanceInfo.months}
                            count={monthlyProductionPerformanceInfo.departments.length}
                        />
                    }
                    isLoading={loading}
                    data={monthlyProductionPerformanceInfo.departments}
                >
                    <MonthlyProductionPerformanceTBody
                        info={monthlyProductionPerformanceInfo}
                        handleOpen={handleOpenDialog}
                        handleOpenComment={handleOpenComment}
                        handleOpenEditHeadCount={handleOpenEditHeadCountDialog}
                        editComment={handleEditComment}
                        setIsEdited={setIsEdited}
                        isEdited={isEdited}
                        handleConFirmEdit={handleConFirmEditRows}
                    />
                </Table>
            </MainCard>

            {/* Edit Production Performance Dialog */}
            {open && (
                <AddOrEditProductionPerformance
                    open={open}
                    loading={addOrEditLoading}
                    months={months}
                    handleClose={handleCloseDialog}
                    productivity={productivity}
                    editProductivity={handleEditProductivity}
                    onChangeYearGetStandardWorkingDay={onChangeYearGetStandardWorkingDay}
                    loadingData={loadingData}
                />
            )}

            {/* Edit HeadCount Dialog */}
            {openHC && (
                <EditHeadCount
                    open={openHC}
                    loading={addOrEditLoading}
                    headCount={productivityHeadCount}
                    months={months}
                    handleClose={handleCloseEditHeadCountDialog}
                    getDetailByMonth={getHeadCountValueByMonth}
                    editHeadCount={handleEditHeadCount}
                />
            )}

            {/* Comment */}
            <CommentPopover
                item={commentItem!}
                anchorEl={anchorEl}
                handleClose={handleClose}
                isEdit={isEditComment}
                setIsEdit={setIsEditComment}
                editComment={handleEditComment}
            />
        </>
    );
};

export default MonthlyProductionPerformance;
