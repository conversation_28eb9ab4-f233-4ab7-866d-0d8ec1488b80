// React
import React, { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';

// project imports
import { IMonthlyProductionPerformanceFilterConfig, monthlyProductionPerformanceFilterConfig } from './Config';
import { exportDocument, getSearchParam, transformObject } from 'utils/common';
import { SummaryTBody, SummaryThead, SummarySearch } from 'containers/sales';
import { IListTable, IOption, ISalePipelineSummary } from 'types';
import { checkAllowedPermission } from 'utils/authorization';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { FilterCollapse } from 'containers/search';
import { Table } from 'components/extended/Table';
import sendRequest from 'services/ApiService';
import { getMonthsOfYear } from 'utils/date';
import useConfig from 'hooks/useConfig';
import Api from 'constants/Api';
import { useAppDispatch } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';

const Summary = () => {
    const [summary, setSummary] = useState<ISalePipelineSummary>();
    const [loading, setLoading] = useState<boolean>(false);
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [SEARCH_PARAM_KEY.year];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);

    const { locale } = useConfig();

    const defaultConditions = { ...monthlyProductionPerformanceFilterConfig, ...params, language: locale };
    const [conditions, setConditions] = useState<IMonthlyProductionPerformanceFilterConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IMonthlyProductionPerformanceFilterConfig>(defaultConditions);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const [totalSummary, setTotalSummary] = useState<IListTable[]>([]);

    const dispatch = useAppDispatch();

    const [isEditedTotalList, setIsEditedTotalList] = useState(false);

    const { summaryPermission } = PERMISSIONS.sale.salePipeline;

    const getDataTable = async () => {
        setLoading(true);
        const response = await sendRequest(Api.sale_list.getSaleSummary, {
            ...conditions
        });

        if (response.status) {
            const { result } = response;
            setSummary(result.content as ISalePipelineSummary);
        } else {
            setSummary(undefined);
        }

        const res = await sendRequest(Api.sale_list.getTotalSummary, {
            ...conditions
        });

        if (res.status) {
            const { result } = res;
            setTotalSummary((result.content.tableSmall?.listTable as IListTable[]).filter((item) => item.show));
        } else {
            setTotalSummary([]);
        }

        setLoading(false);
    };

    // Event
    const handleChangeYear = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value;
        setYear(value as number);
        setIsChangeYear(true);
    };

    // Handle submit
    const handleSearch = (value: IMonthlyProductionPerformanceFilterConfig) => {
        transformObject(value);
        setSearchParams(value as any);
        setConditions({ ...value, language: locale });
    };

    const getMonthInYears = useCallback(async (y: number) => {
        const monthInYears = await getMonthsOfYear(y);
        return monthInYears;
    }, []);

    const handleExportDocument = () => {
        exportDocument(Api.sale_list.getDownloadSalePineLine.url, { year, language: locale });
    };

    const hanldeConfirmEditList = async (list: IListTable[]) => {
        const res = await sendRequest(Api.flexible_report.editArrangement, list);
        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? 'update-success' : 'update-fail',
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );
        if (res.status) {
            setTotalSummary(list);
            setIsEditedTotalList(false);
        }
    };

    useEffect(() => {
        getMonthInYears(year).then((items: IOption[]) => {
            if (items.length > 0 && isChangeYear) setFormReset({ ...formReset, year });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [year]);

    // Effect
    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <div className="summary-container">
            <FilterCollapse
                handleExport={checkAllowedPermission(summaryPermission.download) ? handleExportDocument : undefined}
                downloadLabel={TEXT_CONFIG_SCREEN.salesReport.summary + '-download-report'}
            >
                <SummarySearch conditions={formReset} handleChangeYear={handleChangeYear} handleSearch={handleSearch} />
                <Table
                    sx={{
                        '& table': {
                            borderCollapse: 'collapse'
                        },
                        '& td, th': {
                            border: '1px solid #ccc',
                            color: '#000000'
                        },
                        marginTop: '10px'
                    }}
                    maxHeight="auto"
                    heads={<SummaryThead summaryLength={[summary!].length} />}
                    isLoading={loading}
                    data={summary?.tableLarge || summary?.tableSmall ? [summary!] : []}
                >
                    <SummaryTBody
                        summary={summary}
                        totalSummarry={totalSummary.map((item, index) => ({ ...item, index: index + 1 }))}
                        isEdited={isEditedTotalList}
                        setIsEdited={setIsEditedTotalList}
                        handleConFirmEdit={hanldeConfirmEditList}
                    />
                </Table>
            </FilterCollapse>
        </div>
    );
};

export default Summary;
