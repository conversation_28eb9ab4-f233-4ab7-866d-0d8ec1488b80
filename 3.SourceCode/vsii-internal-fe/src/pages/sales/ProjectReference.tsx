import { useEffect, useState } from 'react';

// third party
import { useSearchParams } from 'react-router-dom';

// project imports
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { exportDocument, getSearchParam, isEmpty, transformObject } from 'utils/common';
import { IPaginationResponse, IProject, IResponseList } from 'types';
import { FilterCollapse } from 'containers/search';
import { ProjectReferenceSearch, ProjectReferenceTBody, ProjectReferenceTHead } from 'containers/sales';
import MainCard from 'components/cards/MainCard';
import { Table, TableFooter } from 'components/extended/Table';
import { IProjectReferenceSearchDefaultValue, projectReferenceSearchDefaultValue } from './Config';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// ==============================|| Project Reference ||============================== //
/**
 *  URL Params
 *  page
 *  size
 *  projectId
 *  projectName
 *  projectType
 */

const ProjectReference = () => {
    const { projectReference } = TEXT_CONFIG_SCREEN.salesReport;
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.projectName,
        SEARCH_PARAM_KEY.projectId,
        SEARCH_PARAM_KEY.projectType,
        SEARCH_PARAM_KEY.domain,
        SEARCH_PARAM_KEY.technology
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { projectName, ...cloneParams } = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...projectReferenceSearchDefaultValue,
        ...cloneParams,
        projectId: params.projectId ? { value: params.projectId, label: params.projectName } : null
    };
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [projects, setProjects] = useState<IProject[]>([]);
    const [conditions, setConditions] = useState<IProjectReferenceSearchDefaultValue>(defaultConditions);
    const [formReset] = useState<IProjectReferenceSearchDefaultValue>(defaultConditions);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const { projectReferencePermission } = PERMISSIONS.sale;

    // Function
    const setDataEmpty = () => {
        setProjects([]);
        setIsLoading(false);
    };

    const getAllProject = async () => {
        setIsLoading(true);
        const response: IResponseList<any> = await sendRequest(Api.project.getAll, {
            ...conditions,
            page: conditions.page + 1,
            projectId: conditions.projectId ? conditions.projectId.value : null,
            isTypeSelect: true
        });

        if (response) {
            const { status, result } = response;

            if (status) {
                const { content, pagination } = result;
                if (!isEmpty(content)) {
                    setProjects(content);
                    setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                    setIsLoading(false);
                } else {
                    setDataEmpty();
                    setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                }
            }
        } else {
            setDataEmpty();
        }
    };

    const exportProjectReference = () => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { page, size, ...cloneConditions } = conditions;
        transformObject(cloneConditions);
        exportDocument(Api.project.getDownload.url, { ...cloneConditions, projectId: conditions.projectId?.value, isScreenPRCE: true });
    };

    // Event
    // Handle change page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    // Handle change rows per page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    // Submit
    const handleSearch = (values: IProjectReferenceSearchDefaultValue) => {
        transformObject(values);
        const params: any = values.projectId
            ? { ...values, projectId: values.projectId.value, projectName: values.projectId.label }
            : values;
        setSearchParams(params);
        setConditions({ ...values, page: paginationParamDefault.page, size: conditions.size });
    };

    // Effect
    useEffect(() => {
        getAllProject();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse
                handleExport={checkAllowedPermission(projectReferencePermission.download) ? exportProjectReference : undefined}
                downloadLabel={projectReference + 'download'}
            >
                <ProjectReferenceSearch handleSearch={handleSearch} formReset={formReset} />
            </FilterCollapse>

            {/* Table */}
            <MainCard>
                <Table heads={<ProjectReferenceTHead />} data={projects} isLoading={isLoading}>
                    <ProjectReferenceTBody pageNumber={conditions.page} pageSize={conditions.size} projects={projects} />
                </Table>
            </MainCard>

            {/* Pagination */}
            {!isLoading && (
                <TableFooter
                    pagination={{ total: paginationResponse?.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
        </>
    );
};

export default ProjectReference;
