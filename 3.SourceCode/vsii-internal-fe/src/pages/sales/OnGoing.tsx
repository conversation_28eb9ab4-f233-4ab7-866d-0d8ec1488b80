/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { ChangeEvent, useEffect, useState } from 'react';
import { PopoverVirtualElement, SelectChangeEvent } from '@mui/material';
import { useSearchParams } from 'react-router-dom';

// project imports
import { IPaginationResponse, IResponseList, ISaleOnGoingItem, ISaleOnGoingList, ISaleOnGoingTotals, ISalesTotal } from 'types';
import { CommentPopover, EditOnGoing, OnGoingSearch, OnGoingTBody, OnGoingThead, OnGoingTotal } from 'containers/sales';
import { SEARCH_PARAM_KEY, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { Table, TableFooter } from 'components/extended/Table';
import { getSearchParam, transformObject } from 'utils/common';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { IOnGoingConfig, onGoingConfig } from './Config';
import { FilterCollapse } from 'containers/search';
import MainCard from 'components/cards/MainCard';
import sendRequest from 'services/ApiService';
import { useAppDispatch } from 'app/hooks';
import { TableToolbar } from 'containers';
import useConfig from 'hooks/useConfig';
import Api from 'constants/Api';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// third party

// ==============================|| On Going ||============================== //
/**
 *  URL Params
 *  page
 *  size
 *  type
 *  year
 *  projectName
 *  productionPerformanceIdHexString
 *  status
 */
const OnGoing = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.type,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.projectName,
        SEARCH_PARAM_KEY.productionPerformanceIdHexString,
        SEARCH_PARAM_KEY.status
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { projectName, ...cloneParams }: any = params;

    const { locale } = useConfig();
    // Hooks, State, Variable

    const defaultConditions = {
        ...onGoingConfig,
        ...cloneParams,
        productionPerformanceIdHexString: params.productionPerformanceIdHexString
            ? { value: params.productionPerformanceIdHexString, label: params.projectName }
            : null,
        language: locale
    };

    const dispatch = useAppDispatch();
    const [loading, setLoading] = useState<boolean>(false);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [projects, setProjects] = useState<ISaleOnGoingItem[]>([]);
    const [project, setProject] = useState<any>(null);
    const [editLoading, setEditLoading] = useState<boolean>(false);
    const [totalOnGoing, setTotalOnGoing] = useState<ISalesTotal[]>([]);
    const [conditions, setConditions] = useState<IOnGoingConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IOnGoingConfig>(defaultConditions);
    const [open, setOpen] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [commentItem, setCommentItem] = useState<any>(null);
    const [isEditComment, setIsEditComment] = useState<boolean>(false);
    const [anchorElComment, setAnchorElComment] = useState<
        Element | (() => Element) | PopoverVirtualElement | (() => PopoverVirtualElement) | null | undefined
    >(null);

    const { onGoingPermission } = PERMISSIONS.sale.salePipeline;

    const [isEditedTotalList, setIsEditedTotalList] = useState(false);
    // Functions
    const setDataEmpty = () => {
        setProjects([]);
        setLoading(false);
    };

    // get ongoing
    const getDataTable = async () => {
        setLoading(true);
        const response: IResponseList<ISaleOnGoingList> = await sendRequest(Api.sale_pipeline_on_going.getAll, {
            ...conditions,
            productionPerformanceIdHexString: conditions?.productionPerformanceIdHexString
                ? conditions?.productionPerformanceIdHexString.value
                : null,
            page: conditions.page + 1
        });

        if (response) {
            const { status, result } = response;
            if (status) {
                const { content, pagination } = result;
                setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                setProjects(content.data);
                setLoading(false);
            } else {
                setDataEmpty();
            }
        } else {
            setDataEmpty();
        }
    };

    const getTotal = async () => {
        setLoading(true);
        const response: IResponseList<ISaleOnGoingTotals> = await sendRequest(Api.sale_pipeline_on_going.getTotal, {
            ...conditions,
            productionPerformanceIdHexString: conditions?.productionPerformanceIdHexString
                ? conditions?.productionPerformanceIdHexString.value
                : null
        });

        if (response) {
            const { status, result } = response;
            if (status) {
                const { content } = result;
                setTotalOnGoing(content.totals?.filter((item) => item.show));
                setLoading(false);
            } else {
                setTotalOnGoing([]);
                setLoading(false);
            }
        } else {
            setTotalOnGoing([]);
            setLoading(false);
        }
    };

    // edit ongoing
    const postEditOnGoing = async (payload: any) => {
        setEditLoading(true);
        const response = await sendRequest(Api.sale_pipeline_on_going.saveOrUpdate, payload);
        if (response) {
            if (response.status) {
                setEditLoading(false);
                setOpen(false);
                getDataTable();
                dispatch(
                    openSnackbar({
                        open: true,
                        message: 'update-success',
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
            } else {
                setEditLoading(false);
                dispatch(
                    openSnackbar({
                        open: true,
                        message: response.message,
                        variant: 'alert',
                        alert: { color: 'warning' }
                    })
                );
            }
        } else {
            setEditLoading(false);
        }
    };

    // post edit comment ongoing
    const postEditComment = async (payload?: any) => {
        const response = await sendRequest(Api.sale_pipeline_on_going.comment, { ...payload, year: conditions.year });
        if (response?.status) {
            dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));
            setIsEditComment(false);
            const { content } = response.result;
            const onGoingListUpdate = projects.map((prv) => {
                if (prv.idHexString === content.idHexString) {
                    return content;
                }
                return prv;
            });
            setProjects(onGoingListUpdate);
        } else return;
    };

    // Events
    // Handle change page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    // Handle change rows / page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    // Handle search
    const handleSearch = (values: any) => {
        const { productionPerformanceIdHexString } = values;
        transformObject(values);
        const paramsSearchOnGoing = productionPerformanceIdHexString
            ? {
                  ...values,
                  productionPerformanceIdHexString: productionPerformanceIdHexString.value,
                  projectName: productionPerformanceIdHexString.label
              }
            : { ...values };
        setSearchParams(paramsSearchOnGoing);
        setConditions({ ...values });
    };

    // Handle open form edit ongoing
    const handleOpenDialog = (item?: ISaleOnGoingItem) => {
        setIsEdit(item ? true : false);
        setProject(item);
        setOpen(true);
    };

    // Handle close form edit ongoing
    const handleCloseDialog = () => {
        setOpen(false);
    };

    // Handle change year
    const handleChangeYear = (e: ChangeEvent<HTMLInputElement>) => {
        setFormReset((prev) => ({
            ...prev,
            productionPerformanceIdHexString: null,
            year: +e.target.value as number
        }));
    };

    const handleChangeProject = (data: any) => {
        if (data)
            setFormReset((prev) => ({
                ...prev,
                productionPerformanceIdHexString: {
                    value: data.idHexString,
                    label: data.project.projectName
                },
                status: data.status,
                type: data.type
            }));
    };

    const handleChangeSalePipelineType = (e: SelectChangeEvent<unknown>) => {
        setFormReset((prev) => ({
            ...prev,
            productionPerformanceIdHexString: null,
            type: e.target.value as string
        }));
    };

    const handleChangeStatus = (e: SelectChangeEvent<unknown>) => {
        setFormReset((prev) => ({
            ...prev,
            productionPerformanceIdHexString: null,
            status: e.target.value as string
        }));
    };

    // Handle comment Ongoing
    const handleOpenCommentOngoing = (event: React.MouseEvent<Element>, item: any) => {
        setAnchorElComment(event.currentTarget);
        const { comment, ...cloneItem } = item;
        setCommentItem({ ...cloneItem, content: comment });
    };

    // Handle close comment Ongoing
    const handleCloseCommentOngoing = () => {
        setAnchorElComment(null);
        setIsEditComment(false);
    };

    const hanldeConfirmEditList = async (list: ISalesTotal[]) => {
        const res = await sendRequest(Api.flexible_report.editArrangement, list);

        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? 'update-success' : 'update-fail',
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );
        if (res.status) {
            setTotalOnGoing(list);
            setIsEditedTotalList(false);
        }
    };
    // Effects
    useEffect(() => {
        getDataTable();
        getTotal();
    }, [conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse>
                <OnGoingSearch
                    formReset={formReset}
                    handleSearch={handleSearch}
                    handleChangeYear={handleChangeYear}
                    handleChangeProject={handleChangeProject}
                    handleChangeSalePipelineType={handleChangeSalePipelineType}
                    handleChangeStatus={handleChangeStatus}
                />
            </FilterCollapse>

            {/* Total */}
            {totalOnGoing?.length > 0 && (
                <OnGoingTotal
                    loading={loading}
                    total={totalOnGoing}
                    isEdited={isEditedTotalList}
                    setIsEdited={setIsEditedTotalList}
                    handleConFirmEdit={checkAllowedPermission(onGoingPermission.editRows) ? hanldeConfirmEditList : undefined}
                />
            )}

            {/* Table and Toolbar */}
            <MainCard>
                <TableToolbar />
                <Table heads={<OnGoingThead />} isLoading={loading} data={projects}>
                    <OnGoingTBody
                        page={conditions.page}
                        size={conditions.size}
                        items={projects}
                        handleOpen={handleOpenDialog}
                        handleOpenComment={handleOpenCommentOngoing}
                    />
                </Table>
            </MainCard>
            {/* Pagination */}
            {!loading && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
            {/* Add or Edit User Dialog */}
            {open && (
                <EditOnGoing
                    year={conditions.year}
                    open={open}
                    project={project}
                    handleClose={handleCloseDialog}
                    isEdit={isEdit}
                    loading={editLoading}
                    postEditOnGoing={postEditOnGoing}
                />
            )}
            {/* Comment */}
            <CommentPopover
                isSalesPipeLine
                item={commentItem!}
                anchorEl={anchorElComment}
                handleClose={handleCloseCommentOngoing}
                isEdit={isEditComment}
                setIsEdit={setIsEditComment}
                editComment={postEditComment}
            />
        </>
    );
};

export default OnGoing;
