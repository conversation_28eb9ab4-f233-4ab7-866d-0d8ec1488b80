import React, { SyntheticEvent, memo, useEffect, useState } from 'react';

// project imports
import MainCard from 'components/cards/MainCard';
import { Table, TableFooter } from 'components/extended/Table';
import { TabPanel } from 'components/extended/Tabs';
import Api from 'constants/Api';
import {
    SEARCH_PARAM_KEY,
    TEXT_CONFIG_SCREEN,
    paginationParamDefault,
    paginationResponseDefault,
    saleMonitorBiddingPackageTabs
} from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { TabCustom, TableToolbar } from 'containers';
import { useAppDispatch } from 'app/hooks';
import {
    AddOrEditBiddingReport,
    BiddingReportSearch,
    BiddingReportTBody,
    BiddingReportThead,
    BiddingTrackingSearch,
    BiddingTrackingTBody,
    BiddingTrackingThead,
    Synchronize
} from 'containers/sales';
import { FilterCollapse } from 'containers/search';
import { useSearchParams } from 'react-router-dom';
import sendRequest from 'services/ApiService';
import { openSnackbar } from 'store/slice/snackbarSlice';
import {
    IBiddingDownloadParams,
    IBiddingReport,
    IBiddingReportResponse,
    IBiddingTracking,
    IBiddingTrackingResponse,
    IPaginationResponse,
    ICreateBiddingReportResponse,
    IResponseList
} from 'types';
import { checkAllowedPermission, checkAllowedTab } from 'utils/authorization';
import { downloadDocument, getSearchParam, transformObject } from 'utils/common';
import { IMonitorBiddingPackagesFilterConfig, addOrEditBiddingReportFormDefault, monitorBiddingPackagesFilterConfig } from './Config';
import pick from 'lodash/pick';
import { Box } from '@mui/material';
// ==============================|| Monitor Bidding Packages ||============================== //

const MonitorBiddingPackages = memo(() => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.tab,
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        // ====== tab 0 - Bidding tracking ======
        SEARCH_PARAM_KEY.type,
        SEARCH_PARAM_KEY.packageName,
        SEARCH_PARAM_KEY.address,
        // ====== tab 1 - Bidding Report ======
        SEARCH_PARAM_KEY.type,
        SEARCH_PARAM_KEY.biddingPackagesName,
        SEARCH_PARAM_KEY.status,
        SEARCH_PARAM_KEY.address
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);

    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...monitorBiddingPackagesFilterConfig,
        ...cloneParams
    };

    const { monitorBiddingPackages } = TEXT_CONFIG_SCREEN.salesReport;

    const dispatch = useAppDispatch();
    const [open, setOpen] = useState<boolean>(false);
    const [openSynchronize, setOpenSynchronize] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [synchronizeLoading, setSynchronizeLoading] = useState<boolean>(false);
    const [biddingTracking, setBiddingTracking] = useState<IBiddingTracking[]>([]);
    const [biddingReport, setBiddingReport] = useState<IBiddingReport[]>([]);
    const [detailBiddingReport, setDetailBiddingReport] = useState<IBiddingReport>(addOrEditBiddingReportFormDefault);
    const [conditions, setConditions] = useState<IMonitorBiddingPackagesFilterConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IMonitorBiddingPackagesFilterConfig>(defaultConditions);
    const [tabValue, setTabValue] = useState(checkAllowedTab(saleMonitorBiddingPackageTabs, params.tab)[0]);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);
    const { monitorBiddingPackage } = PERMISSIONS.sale;

    // Function
    const getDataTable = async (tabNumber?: number | string) => {
        setLoading(true);
        const response: IResponseList<IBiddingTrackingResponse | IBiddingReportResponse> = await sendRequest(
            tabValue === 0 ? Api.monitor_bidding.getBiddingReport : Api.monitor_bidding.getBiddingTracking,
            {
                ...conditions,
                page: conditions.page + 1
            }
        );
        if (response) {
            const { status, result } = response;

            if (status) {
                const { content, pagination } = result;
                tabValue === 0 ? setBiddingReport(content as IBiddingReport[]) : setBiddingTracking(content as IBiddingTracking[]);
                setPaginationResponse({ ...paginationResponse, totalElement: pagination.totalElement });
                setLoading(false);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    const setDataEmpty = () => {
        setBiddingTracking([]);
        setBiddingReport([]);
        setLoading(false);
    };

    // Event
    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {
        setTabValue(newTabValue);
        setConditions({ ...monitorBiddingPackagesFilterConfig });
        setFormReset({ ...monitorBiddingPackagesFilterConfig });
        setSearchParams({ tab: newTabValue } as any);
    };

    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    const handleOpenDialog = (item?: any) => {
        setIsEdit(item ? true : false);
        setDetailBiddingReport(
            item
                ? {
                      ...item
                  }
                : addOrEditBiddingReportFormDefault
        );
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    //Call API Bidding Report
    // TODO
    const postAddOrEditBiddingReport = async (value: IBiddingReport, idHexString?: string) => {
        try {
            setAddOrEditLoading(true);
            const response: IResponseList<ICreateBiddingReportResponse> = await sendRequest(
                isEdit ? Api.monitor_bidding.updateBiddingReportpost(idHexString!) : Api.monitor_bidding.createOrUpdateBiddingReportpost,
                value
            );

            if (response.status) {
                const message = isEdit ? 'update-success' : 'add-success';
                dispatch(
                    openSnackbar({
                        open: true,
                        message,
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
                getDataTable(tabValue);
                setOpen(false);
            } else {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: response.result.content,
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
            }
        } finally {
            setAddOrEditLoading(false);
        }
    };

    const postSynchronize = async (token: string) => {
        try {
            setSynchronizeLoading(true);

            const response = await sendRequest(Api.monitor_bidding.postSynchronize(token));

            if (response?.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: 'Synchronize successfully!',
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
                setOpenSynchronize(false);
                getDataTable();
            } else {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: response?.result?.content?.message || 'Synchronize failed!',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
            }
        } finally {
            setSynchronizeLoading(false);
        }
    };

    const hanldeAddBiddingReport = (value: IBiddingReport) => {
        postAddOrEditBiddingReport(value);
    };
    const hanldeEditBiddingReport = (value: IBiddingReport, idHexString: string) => {
        postAddOrEditBiddingReport(value, idHexString);
    };

    const handleExportDocument = async (params: IBiddingDownloadParams) => {
        const response = await sendRequest(Api.monitor_bidding.getDownload, params);
        if (response) {
            downloadDocument('MONITOR_BIDDING_REPORT.xlsx', response);
        }
    };

    const handleSynchronize = () => {
        setOpenSynchronize(true);
    };

    const handlePostSynchronize = (token: string) => {
        postSynchronize(token);
    };

    const handleCloseSynchronize = () => {
        setOpenSynchronize(false);
    };

    // Handle submit
    const handleBiddingTrackingSearch = (value: any) => {
        const biddingTrackingSearch = { ...value, tab: tabValue };

        transformObject(biddingTrackingSearch);
        setSearchParams(biddingTrackingSearch);
        setConditions({ ...conditions, ...value, page: paginationParamDefault.page });
    };

    const handleBiddingReportSearch = (value: any) => {
        const biddingReportSearch = { ...value, tab: tabValue };

        transformObject(biddingReportSearch);
        setSearchParams(biddingReportSearch);
        setConditions({ ...conditions, ...value, page: paginationParamDefault.page });
    };

    // Effect
    useEffect(() => {
        getDataTable(tabValue);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tabValue, conditions]);

    return (
        <>
            {/* Tabs  */}
            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={saleMonitorBiddingPackageTabs} />

            {/* Search form  */}
            <FilterCollapse
                handleSynchronize={
                    checkAllowedPermission(monitorBiddingPackage.synchronizePackagesBiddingTracking) && tabValue === 0
                        ? undefined
                        : handleSynchronize
                }
                handleExport={
                    checkAllowedPermission(monitorBiddingPackage.downloadPackagesBiddingtracking)
                        ? () => handleExportDocument(pick(conditions, ['address', 'biddingPackagesName', 'status', 'type']))
                        : undefined
                }
                syncLabel={monitorBiddingPackages + 'tracking-synchronize'}
                downloadLabel={monitorBiddingPackages + 'tracking-download'}
            >
                <TabPanel value={tabValue} index={0}>
                    <BiddingReportSearch formReset={formReset} handleSearch={handleBiddingReportSearch} />
                </TabPanel>
                <TabPanel value={tabValue} index={1}>
                    <BiddingTrackingSearch formReset={formReset} handleSearch={handleBiddingTrackingSearch} />
                </TabPanel>
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                <TabPanel value={tabValue} index={0}>
                    {checkAllowedPermission(monitorBiddingPackage.add) && (
                        <TableToolbar handleOpen={handleOpenDialog} handleRefreshData={getDataTable} />
                    )}
                    <Box
                        sx={{
                            width: '100%',
                            overflowX: 'auto'
                        }}
                    >
                        <Table
                            heads={<BiddingReportThead />}
                            isLoading={loading}
                            data={biddingReport}
                            sx={{
                                minWidth: '1310px'
                            }}
                        >
                            <BiddingReportTBody
                                pageNumber={conditions.page}
                                pageSize={conditions.size}
                                biddingReport={biddingReport}
                                handleOpen={handleOpenDialog}
                            />
                        </Table>
                    </Box>
                </TabPanel>

                <TabPanel value={tabValue} index={1}>
                    <Box
                        sx={{
                            width: '100%',
                            overflowX: 'auto'
                        }}
                    >
                        <Table
                            heads={<BiddingTrackingThead />}
                            isLoading={loading}
                            data={biddingTracking}
                            sx={{
                                minWidth: '1310px'
                            }}
                        >
                            <BiddingTrackingTBody
                                pageNumber={conditions.page}
                                pageSize={conditions.size}
                                biddingTracking={biddingTracking}
                            />
                        </Table>
                    </Box>
                </TabPanel>
            </MainCard>

            {/* Pagination  */}
            {!loading && tabValue < 2 && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {/* Add Or Edit Bidding Report */}
            <AddOrEditBiddingReport
                open={open}
                handleClose={handleCloseDialog}
                isEdit={isEdit}
                loading={addOrEditLoading}
                data={detailBiddingReport}
                hanldeAdd={hanldeAddBiddingReport}
                hanldeEdit={hanldeEditBiddingReport}
            />

            {/* Synchronize confirm */}
            <Synchronize
                open={openSynchronize}
                loading={synchronizeLoading}
                handleClose={handleCloseSynchronize}
                handleSynchronize={handlePostSynchronize}
            />
        </>
    );
});

export default MonitorBiddingPackages;
