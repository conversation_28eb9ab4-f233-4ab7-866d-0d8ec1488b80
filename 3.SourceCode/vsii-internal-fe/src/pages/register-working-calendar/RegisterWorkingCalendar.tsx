import { useCallback, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useFieldArray } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

// material-ui
import { IconButton, SelectChangeEvent, TableBody, Typography } from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

// project imports
import { exportDocument, getSearchParam, transformObject } from 'utils/common';
import { REGISTER_WORKING_CALENDAR_TYPE, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { FilterCollapse } from 'containers/search';
import sendRequest from 'services/ApiService';
import { getMonthsOfYear } from 'utils/date';
import Api from 'constants/Api';

// third party
import { IWorkingCalendarSearch, useWorkingCalendarForm, workingCalenderSearchConfig } from './Config';
import RegisterWorkingCalenderSearch from 'containers/working-calendar/RegisterWorkingCalenderSearch';
import RegisterWorkingCalendarThead from 'containers/working-calendar/RegisterWorkingCalendarThead';
import MessageWorkingCalendarModal from 'containers/working-calendar/MessageWorkingCalendarModal';
import RegisterWorkingCalendarType from 'containers/working-calendar/RegisterWorkingCalendarType';
import ClosingDateWorkingCalendar from 'containers/working-calendar/ClosingDateWorkingCalendar';
import CommentCalendarModal from 'containers/working-calendar/CommentCalendarModal';
import { IClosingDate, ITypeList, IWorkingCalendar } from 'types/working-calendar';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import ReadOnly from 'containers/working-calendar/ReadOnly';
import EditRow from 'containers/working-calendar/EditRow';
import { authSelector } from 'store/slice/authSlice';
import { Table } from 'components/extended/Table';
import MainCard from 'components/cards/MainCard';
import { IOption } from 'types';

// third party

// ==============================|| Register Working Calendar ||============================== //

const RegisterWorkingCalendar = () => {
    const { register_working_calendar } = TEXT_CONFIG_SCREEN.workingCalendar;
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.month,
        SEARCH_PARAM_KEY.idHexString,
        SEARCH_PARAM_KEY.fullname,
        SEARCH_PARAM_KEY.departmentId
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, idHexString, ...cloneParams }: any = params;
    // Hooks, State, Variable

    const defaultConditions = {
        ...workingCalenderSearchConfig,
        ...cloneParams,
        idHexString: params.idHexString ? { value: params.idHexString, label: params.fullname } : null
    };

    const dispatch = useAppDispatch();
    const [loading, setLoading] = useState<boolean>(false);
    const [workingCalendars, setWorkingCalendars] = useState<IWorkingCalendar[]>([]);
    const [typeList, setTypeList] = useState<ITypeList[]>([]);
    const [conditions, setConditions] = useState<IWorkingCalendarSearch>(defaultConditions);
    const [formReset, setFormReset] = useState<IWorkingCalendarSearch>(defaultConditions);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [months, setMonths] = useState<IOption[]>(getMonthsOfYear(workingCalenderSearchConfig.year));
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);
    const { registerWorkingCalendar } = PERMISSIONS.workingCalendar;
    const [openMess, setOpenMess] = useState<boolean>(false);
    const [openComment, setOpenComment] = useState<boolean>(false);
    const [isEdit, setIsEdit] = useState<boolean>(true);
    const [toggle, setToggle] = useState<string>('');
    const [editData, setEditData] = useState<IWorkingCalendar>();
    const [openClosingDate, setOpenClosingDate] = useState<boolean>(false);
    const [closingDate, setClosingDate] = useState<IClosingDate>();
    const [openEditClosingDate, setEditClosingDate] = useState<boolean>(false);
    const [isCheckEdit, setIsCheckEdit] = useState<boolean>(false);
    const [totalUser, setTotalUser] = useState<string>('');
    const [isMonth, setIsMonth] = useState<number | string>(conditions.month);
    const [isCheckDetail, setIsCheckDetail] = useState<boolean>(false);
    const [selectedMember, setSelectedMember] = useState<IWorkingCalendar[]>([]);
    const [optionTypeList, setOptionTypeList] = useState<IOption[]>(REGISTER_WORKING_CALENDAR_TYPE);

    const { userInfo } = useAppSelector(authSelector);

    const form = useWorkingCalendarForm();
    const { handleSubmit, reset, control } = form;

    // API get
    const getDataTable = async () => {
        setLoading(true);
        const idHexString = conditions.idHexString ? conditions.idHexString.value : '';
        const response = await sendRequest(Api.working_calendar.getAll, {
            ...conditions,
            idHexString: idHexString
        });

        if (response) {
            const { status, result } = response;
            const { content } = result;
            const workingCalendarLength = content.workingCalendarResponses ? content.workingCalendarResponses.length : 0;
            if (status && result && (content.length > 0 || workingCalendarLength > 0)) {
                const workingCalendarResponses = content.workingCalendarResponses;
                setWorkingCalendars(workingCalendarResponses);
                setTypeList(content.typeList);
                setOptionTypeList(content.typeList.map((type: IOption) => ({ value: type.key, label: type.value, color: type.color })));
                setTotalUser(content.totalUser);
                setLoading(false);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    // Call API saveOrUpdate
    const postEditWorkingCalendar = async (value: any) => {
        setAddOrEditLoading(true);
        const response = await sendRequest(Api.working_calendar.postSaveOrUpdate, value);
        if (response) {
            const workingCalendarBk = [...workingCalendars];
            const { result, status } = response;
            if (status) {
                setOpenMess(false);
                setAddOrEditLoading(false);
                workingCalendarBk.forEach((element, index) => {
                    if (element.idHexString === result.idHexString) workingCalendarBk[index] = result;
                });
                setWorkingCalendars(workingCalendarBk);
                setToggle('');
                dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));
            } else {
                setOpenMess(true);
            }
        } else {
            setAddOrEditLoading(false);
        }
    };

    // Call API Verified of Member
    const postUpdateStatus = async (item: IWorkingCalendar) => {
        const response = await sendRequest(Api.working_calendar.postUpdateStatus, item);
        if (response) {
            const { status, result } = response;
            if (status) {
                setOpenMess(false);
                const newWorkingCalendar = [...workingCalendars];

                newWorkingCalendar.forEach((element, index) => {
                    if (element.idHexString === result.idHexString) newWorkingCalendar[index] = result;
                });
                setWorkingCalendars(newWorkingCalendar);
                setToggle('');
                dispatch(openSnackbar({ open: true, message: 'verified-success', variant: 'alert', alert: { color: 'success' } }));
            } else {
                dispatch(openSnackbar({ open: true, message: 'update-status-error', variant: 'alert', alert: { color: 'error' } }));
            }
        }
    };

    // Call API Verify All
    const postVerifyClosingDate = async (value: any, month: number | string) => {
        const response = await sendRequest(Api.working_calendar.postVerifyClosingDate, { userIdList: value, month: month });
        if (response) {
            const { status } = response;
            if (status) {
                dispatch(openSnackbar({ open: true, message: 'update-success', variant: 'alert', alert: { color: 'success' } }));
            } else {
                setOpenMess(true);
            }
        }
        dispatch(closeConfirm());
    };

    // Call Api Get Closing Date
    const getDataClosingdate = async (year?: number) => {
        const response = await sendRequest(Api.working_calendar.getClosingDate, {
            year
        });

        if (response) {
            const { status, result } = response;
            const { content } = result;
            if (status) {
                setClosingDate(content);
                setIsCheckEdit(true);
                const groups = userInfo?.groups;
                if (content.locked === 'Yes' && groups) {
                    if (!groups.includes(registerWorkingCalendar.verifyWorkingCalendar)) {
                        setIsCheckEdit(false);
                    }
                }
            }
            return;
        } else {
            setDataClosingDate();
        }
    };
    // Call API Update Closing Date
    const postEditClosingDate = async (closingDateValue: IClosingDate) => {
        const response = await sendRequest(Api.working_calendar.postUpdateClosingDate, closingDateValue);
        if (response) {
            const status = response.status;

            if (status) {
                setEditClosingDate(false);
                getDataClosingdate(closingDateValue.year as number);
            } else {
                setEditClosingDate(true);
            }
            dispatch(
                openSnackbar({
                    open: true,
                    message: status ? 'update-closing-date-success' : 'error-closing-date',
                    variant: 'alert',
                    alert: { color: status ? 'success' : 'error' }
                })
            );
        }
    };
    // putUpdateCommentWorkingCalendar
    const putUpdateCommentWorkingCalendar = async (id: string, comment: string) => {
        const response = await sendRequest(Api.working_calendar.updateComment, { id, comment });
        if (response) {
            setOpenComment(false);
            dispatch(
                openSnackbar({
                    open: true,
                    message: response.result.content,
                    variant: 'alert',
                    alert: { color: response.status ? 'success' : 'error' }
                })
            );
            if (response.status) getDataTable();
        }
    };

    // funtion
    const setDataEmpty = () => {
        setWorkingCalendars([]);
        setLoading(false);
    };

    const setDataClosingDate = () => {
        setClosingDate(undefined);
    };

    const getMonthsInYear = useCallback(async (p: number) => {
        const monthInYear = await getMonthsOfYear(p);
        return monthInYear;
    }, []);

    const onEdit = (item: IWorkingCalendar) => {
        setIsEdit(true);
        setAddOrEditLoading(true);
        setToggle(item.idHexString);
        setEditData(item);
    };

    const onVerify = (item: IWorkingCalendar) => {
        setOpenMess(true);
        setIsEdit(false);
        setEditData(item);
    };
    const onComment = (item: IWorkingCalendar) => {
        setOpenComment(true);
        setEditData(item);
    };

    const onCancel = (item: IWorkingCalendar) => {
        setAddOrEditLoading(false);
        reset({ workdays: item?.workdays });
        setToggle('');
    };

    const handleCloseDialog = () => {
        setOpenMess(false);
        setOpenComment(false);
        setOpenClosingDate(false);
    };
    const onSubmit = (values: any) => {
        const editPayload = {
            idHexString: editData?.idHexString,
            userIdHexString: editData?.idHexStringUser,
            year: editData?.year,
            month: editData?.month,
            userId: editData?.userId,
            userName: editData?.userName,
            lastName: editData?.lastName,
            firstName: editData?.firstName,
            departmentId: editData?.departmentId,
            memberCode: editData?.memberCode,
            rank: editData?.rank,
            workdays: values?.workdays,
            workTime: values?.workTime
        };
        postEditWorkingCalendar(editPayload);
        setEditData(editPayload as any);
        setAddOrEditLoading(false);
    };

    const onSubmitMessage = (message: string) => {
        const payLoadNew = { ...editData, message: message };
        if (isEdit) {
            postEditWorkingCalendar(payLoadNew);
        }
    };
    // comment
    const onSubmitComment = (comment: string) => {
        if (editData?.idHexString) {
            putUpdateCommentWorkingCalendar(editData.idHexString, comment);
        }
    };

    const handleChangeYear = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const { value } = e.target;
        setYear(value as number);
        setIsChangeYear(true);
    };

    const handleExportDocument = () => {
        exportDocument(Api.working_calendar.getDownload.url, {
            year: conditions.year,
            month: conditions.month
        });
    };

    const handleSearch = (value: IWorkingCalendarSearch) => {
        const { idHexString } = value;
        transformObject(value);
        const payload = { ...value, idHexString: idHexString?.value, fullname: idHexString?.label };
        transformObject(payload);
        setSearchParams(payload as any);
        setConditions({ ...value });
        setToggle('');
        setIsCheckDetail(false);
    };

    const { fields } = useFieldArray({ control, name: 'workdays' });

    const handleOpenClosingDialog = () => {
        setOpenClosingDate(true);
    };

    const handleOpenVerifyDialog = () => {
        const allApproved = workingCalendars.map((element) => element.status);
        const authorizedIds = workingCalendars.map((element) => element.userId);

        if (allApproved.every((status) => status === 'approve') && closingDate && closingDate.locked === 'Yes') {
            dispatch(
                openConfirm({
                    open: true,
                    title: <FormattedMessage id="warning" />,
                    content: <FormattedMessage id="verify-working-calendar" />,
                    handleConfirm: () => postVerifyClosingDate(authorizedIds, conditions.month)
                })
            );
        } else {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'status-not-approve',
                    variant: 'alert',
                    alert: { color: 'warning' }
                })
            );
        }
    };

    // Thông tin 1 member
    const handleFilterMember = (memberCode: string) => {
        setIsCheckDetail(true);

        const foundMember = workingCalendars.find((item) => item.memberCode === memberCode);

        if (foundMember) {
            setSelectedMember([foundMember]);
        }
    };

    const handleBack = () => {
        setIsCheckDetail(false);
    };

    // Effect
    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    useEffect(() => {
        reset({ workdays: editData?.workdays });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [editData]);

    useEffect(() => {
        getMonthsInYear(year).then((items: IOption[]) => {
            setMonths(items);
            if (items.length > 0 && isChangeYear) {
                setFormReset({ ...formReset, year: year, month: isMonth, departmentId: conditions.departmentId });
            }
        });
        getDataClosingdate(year);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [year]);

    useEffect(() => {
        if (fields.length > 0 && toggle) setAddOrEditLoading(false);
    }, [fields, toggle]);

    useEffect(() => {
        setFormReset((prevState) => ({ ...prevState, month: isMonth, idHexString: null }));
    }, [isMonth]);
    return (
        <>
            {/* Search form  */}
            <FilterCollapse
                handleClosingDate={
                    checkAllowedPermission(registerWorkingCalendar.closingWorkingCalendar) ? handleOpenClosingDialog : undefined
                }
                handleVerifiedWorkingCalendar={
                    checkAllowedPermission(registerWorkingCalendar.verifyWorkingCalendar) ? handleOpenVerifyDialog : undefined
                }
                handleExport={checkAllowedPermission(registerWorkingCalendar.exportWorkingCalendar) ? handleExportDocument : undefined}
                downloadLabel={register_working_calendar + 'download'}
                hrClosingDateLabel={register_working_calendar + 'closing-date'}
                hrVerifyLabel={register_working_calendar + 'verify'}
            >
                <RegisterWorkingCalenderSearch
                    formReset={formReset}
                    months={months}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                    year={year}
                    month={isMonth}
                    setIsMonth={setIsMonth}
                />
            </FilterCollapse>

            {/* Note */}
            {typeList && <RegisterWorkingCalendarType typeList={typeList} month={isMonth} year={year} />}
            {/* Table  */}
            <MainCard>
                {isCheckDetail ? (
                    <IconButton type="button" size="small" onClick={() => handleBack()}>
                        <ArrowBackIcon fontSize="small" />
                    </IconButton>
                ) : (
                    <Typography sx={{ fontWeight: '500', color: '#3162D2', marginBottom: '20px' }}>
                        <FormattedMessage id={register_working_calendar + 'total-number-of-employees'} /> {totalUser}
                    </Typography>
                )}

                <form onSubmit={handleSubmit(onSubmit)}>
                    <Table
                        heads={<RegisterWorkingCalendarThead conditions={conditions} data={workingCalendars} />}
                        isLoading={loading}
                        data={workingCalendars}
                    >
                        <TableBody>
                            {workingCalendars
                                ? (isCheckDetail ? selectedMember : workingCalendars).map((item: IWorkingCalendar, index: number) =>
                                      !addOrEditLoading && toggle === item.idHexString ? (
                                          <EditRow
                                              key={index}
                                              idx={index}
                                              item={item}
                                              dbSelected={optionTypeList}
                                              handleCancel={onCancel}
                                              form={form}
                                              dataLength={workingCalendars.length}
                                          />
                                      ) : (
                                          <ReadOnly
                                              key={index}
                                              dataLength={workingCalendars.length}
                                              idx={index}
                                              item={item}
                                              dbSelected={optionTypeList}
                                              handleEdit={onEdit}
                                              loading={addOrEditLoading}
                                              toggle={toggle}
                                              handleVerify={onVerify}
                                              handleComment={onComment}
                                              isCheckEdit={isCheckEdit}
                                              handleFilterMember={handleFilterMember}
                                          />
                                      )
                                  )
                                : null}
                        </TableBody>
                    </Table>
                </form>

                {/* Message working calendar */}
                <MessageWorkingCalendarModal
                    isEdit={isEdit}
                    open={openMess}
                    handleClose={handleCloseDialog}
                    item={editData}
                    onSubmitMessage={onSubmitMessage}
                    postUpdateStatus={postUpdateStatus}
                />
                {/* comment calendar */}
                <CommentCalendarModal
                    open={openComment}
                    handleClose={handleCloseDialog}
                    item={editData}
                    onSubmitComment={onSubmitComment}
                />

                {/* Closing Date */}
                {openClosingDate && closingDate && (
                    <ClosingDateWorkingCalendar
                        closingDate={closingDate}
                        getDataTable={getDataClosingdate}
                        postEditClosingDate={postEditClosingDate}
                        openClosingDate={openClosingDate}
                        handleClose={handleCloseDialog}
                        openEditClosingDate={openEditClosingDate}
                        setEditClosingDate={setEditClosingDate}
                    />
                )}
            </MainCard>
        </>
    );
};

export default RegisterWorkingCalendar;
