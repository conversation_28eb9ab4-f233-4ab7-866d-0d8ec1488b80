import { useEffect } from 'react';

import { AuthForgot } from 'containers/authentication';
import { authSelector } from 'store/slice/authSlice';
import { authLayoutRef } from 'layout/AuthLayout';
import { useAppSelector } from 'app/hooks';

const ForgotPassword = () => {
    const { forgotPWSuccessfully } = useAppSelector(authSelector);

    useEffect(() => {
        authLayoutRef.current?.setState({ title: forgotPWSuccessfully ? 'Email confirmation successful' : 'Recover Password' });
    }, [forgotPWSuccessfully]);

    return <AuthForgot />;
};
export default ForgotPassword;
