import { useEffect } from 'react';

// project imports
import { AuthRegister } from 'containers/authentication';
import { authSelector } from 'store/slice/authSlice';
import { authLayoutRef } from 'layout/AuthLayout';
import { useAppSelector } from 'app/hooks';

// ================================|| AUTH - REGISTER ||================================ //

const Register = () => {
    const { registerSuccessfully } = useAppSelector(authSelector);

    useEffect(() => {
        authLayoutRef.current?.setState({
            title: registerSuccessfully ? 'Successful account registration' : 'Register',
            maxWidth: registerSuccessfully ? 400 : 600
        });
    }, [registerSuccessfully]);

    return <AuthRegister />;
};
export default Register;
