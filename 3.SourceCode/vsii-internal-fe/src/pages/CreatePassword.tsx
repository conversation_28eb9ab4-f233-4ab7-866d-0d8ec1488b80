import { useEffect } from 'react';

import { AuthCreatePassword } from 'containers/authentication';
import { authSelector } from 'store/slice/authSlice';
import { authLayoutRef } from 'layout/AuthLayout';
import { useAppSelector } from 'app/hooks';

const CreatePassword = () => {
    const { createPasswordSuccessfully } = useAppSelector(authSelector);

    useEffect(() => {
        authLayoutRef.current?.setState({
            title: createPasswordSuccessfully ? 'Create password successful' : 'Create Password'
        });
    }, [createPasswordSuccessfully]);

    return <AuthCreatePassword />;
};

export default CreatePassword;
