import { useEffect, useState } from 'react';
import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';

import { convertWeekFromToDate, getNumberOfWeek, getWeeksPeriodsInYear } from 'utils/date';
import { EstimateCard, WeeklyCostMonitoringSearch } from 'containers/cost-monitoring';
import { ICostMonitoringFilterConfig, costMonitoringFilterConfig } from './Config';
import { getSearchParam, transformObject } from 'utils/common';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import { IOption } from 'types';
import {
    costAndEffortMonitoringSelector,
    getWeeklyCostMonitoring,
    getCostMonitoringProjectOption
} from 'store/slice/costAndEffortMonitoringSlice';

// ==============================|| Weekly Cost Monitoring ||============================== //
/**
 *  URL Params
 *  year
 *  week
 *  projectId
 *  projectName
 */

const keyParams = [SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.week, SEARCH_PARAM_KEY.projectId, SEARCH_PARAM_KEY.projectName];

const WeeklyCostMonitoring = () => {
    const [weeks, setWeeks] = useState<IOption[]>([]);

    const [searchParams, setSearchParams] = useSearchParams();

    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);

    transformObject(params);

    const { projectName, ...cloneParams } = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...costMonitoringFilterConfig,
        ...cloneParams,
        projectId: params.projectId ? { value: params.projectId, label: projectName } : costMonitoringFilterConfig.projectId
    };

    const dispatch = useAppDispatch();

    const [formReset, setFormReset] = useState<ICostMonitoringFilterConfig>(defaultConditions);
    const [week, setWeek] = useState<string | number>(defaultConditions.week);

    const { weeklyCostMonitoring, loading } = useAppSelector(costAndEffortMonitoringSelector);

    const handleGetTableData = (data: ICostMonitoringFilterConfig) => {
        const weekSelected = convertWeekFromToDate(data.week);

        const weeklyEffortProjects = data.projectId ? { ...data, projectId: data.projectId.value } : data;

        dispatch(
            getWeeklyCostMonitoring({
                ...transformObject({ ...weeklyEffortProjects }, ['month', 'week']),
                ...weekSelected,
                weekNumber: getNumberOfWeek(data.week || '')
            })
        );
    };

    const setWeeksFunc = async (year: number, week?: string | number) => {
        const items = getWeeksPeriodsInYear(year);

        setWeeks(items);
        if (items.length > 0) {
            const resultAction = await dispatch(getCostMonitoringProjectOption({ type: 'week', value: items[0].value }));

            let projectId: IOption | null = null;
            if (getCostMonitoringProjectOption.fulfilled.match(resultAction) && resultAction.payload.status) {
                projectId = resultAction.payload.result?.content.length
                    ? {
                          value: resultAction.payload.result.content[0].projectId,
                          label: resultAction.payload.result.content[0].projectName,
                          typeCode: resultAction.payload.result.content[0].typeCode
                      }
                    : null;

                if (week) {
                    const weeklyEffortProjects = {
                        ...defaultConditions,
                        year,
                        projectId
                    };
                    handleGetTableData(weeklyEffortProjects);
                }
            }
            setFormReset((prev) => ({ ...prev, year, week: week || items[0].value, projectId }));
        }
    };

    const handleChangeYear = (e: SelectChangeEvent<unknown>) => {
        const { value } = e.target;

        setWeeksFunc(Number(value));
    };

    const handleSearch = (value: ICostMonitoringFilterConfig) => {
        const { projectId } = value;
        const newParam = projectId ? { ...value, projectId: projectId.value, projectName: projectId.label } : value;

        transformObject(value);
        setSearchParams(newParam as unknown as URLSearchParamsInit);
        setWeek(value.week);
        handleGetTableData(value);
    };

    useEffect(() => {
        if (Number.isInteger(defaultConditions.year)) {
            setWeeksFunc(defaultConditions.year, defaultConditions.week);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <WeeklyCostMonitoringSearch
                    weeks={weeks}
                    formReset={formReset}
                    handleSearch={handleSearch}
                    handleChangeYear={handleChangeYear}
                />
            </FilterCollapse>

            {/* Estimate Cost Card  */}
            <EstimateCard
                isLoading={loading[getWeeklyCostMonitoring.typePrefix]}
                data={weeklyCostMonitoring?.content || { weeklyCostMonitoringList: [] }}
                week={week}
            />
        </>
    );
};

export default WeeklyCostMonitoring;
