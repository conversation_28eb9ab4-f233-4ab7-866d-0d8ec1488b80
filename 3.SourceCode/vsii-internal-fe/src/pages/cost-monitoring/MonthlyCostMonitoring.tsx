import { SyntheticEvent, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';

import { CostStatisticCard, EffortStatisticCard, MonthlyCostMonitoringSearch } from 'containers/cost-monitoring';
import { REPORT_TYPE, SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, listMonthlyMonitoringCostTabs } from 'constants/Common';
import { ICostMonitoringFilterConfig, costMonitoringFilterConfig } from './Config';
import { exportDocument, getSearchParam, transformObject } from 'utils/common';
import { getMonthsOfYear, getCurrentMonth, getCurrentYear } from 'utils/date';
import { openCommentDialog } from 'store/slice/commentSlice';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { authSelector } from 'store/slice/authSlice';
import { PERMISSIONS } from 'constants/Permission';
import { FilterCollapse } from 'containers/search';
import { TabCustom } from 'containers';
import { IOption } from 'types';
import Api from 'constants/Api';
import {
    costAndEffortMonitoringSelector,
    getCostMonitoringProjectOptionByFixCost,
    getMonthlyCostMonitoring
} from 'store/slice/costAndEffortMonitoringSlice';

// ==============================|| Monthly Cost Monitoring ||============================== //
/**
 *  URL Params
 *  year
 *  month
 *  projectId
 *  projectName
 */

const MonthlyCostMonitoring = () => {
    const { monthlyMonitoring } = TEXT_CONFIG_SCREEN.costAndEffortMonitoring;
    const [months, setMonths] = useState<IOption[]>([]);

    const [searchParams, setSearchParams] = useSearchParams();

    const keyParams = [
        SEARCH_PARAM_KEY.tab,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.month,
        SEARCH_PARAM_KEY.projectId,
        SEARCH_PARAM_KEY.projectName
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);

    transformObject(params);

    const { projectName, ...cloneParams } = params;

    const defaultConditions = {
        ...costMonitoringFilterConfig,
        ...cloneParams,
        projectId: params.projectId ? { value: params.projectId, label: projectName } : costMonitoringFilterConfig.projectId
    };

    const dispatch = useAppDispatch();

    const [tabValue, setTabValue] = useState(params.tab || 0);
    const [formReset, setFormReset] = useState<ICostMonitoringFilterConfig>(defaultConditions);
    const [conditions, setConditions] = useState<ICostMonitoringFilterConfig>(defaultConditions);
    const [monthSearch, setMonthSearch] = useState<number | string>(params.month || getCurrentMonth());
    const [yearSearch, setYearSearch] = useState<number | string>(params.year || getCurrentYear());

    const { monthlyCostMonitoring, loading } = useAppSelector(costAndEffortMonitoringSelector);
    const { userInfo } = useAppSelector(authSelector);

    const { costMonitoring } = PERMISSIONS.report;

    const handleGetTableData = (data: ICostMonitoringFilterConfig) => {
        const monthlyEffortProjects = data.projectId ? { ...data, projectId: data.projectId.value } : data;

        dispatch(getMonthlyCostMonitoring(transformObject({ ...monthlyEffortProjects }, ['week'])));
    };

    const setMonthsFunc = async (year: number, tabValue: number, month?: string | number) => {
        const items = getMonthsOfYear(year);

        setMonths(items);

        if (items.length > 0) {
            const resultAction = await dispatch(
                getCostMonitoringProjectOptionByFixCost({
                    type: 'month',
                    value: month ? items.filter((item) => item.value === month)[0].label : items[0].label,
                    fixCost: !tabValue
                })
            );

            let projectId: IOption | null = null;
            if (getCostMonitoringProjectOptionByFixCost.fulfilled.match(resultAction) && resultAction.payload.status) {
                projectId = resultAction.payload.result?.content.length
                    ? {
                          value: resultAction.payload.result.content[0].projectId,
                          label: resultAction.payload.result.content[0].projectName,
                          typeCode: resultAction.payload.result.content[0].typeCode
                      }
                    : null;

                if (month) {
                    const monthlyEffortProjects = {
                        ...defaultConditions,
                        year,
                        projectId
                    };
                    handleGetTableData(monthlyEffortProjects);
                }
            }
            setFormReset((prev) => ({ ...prev, year, month: month || items[0].value, projectId }));
        }
    };

    // Event
    const handleOpenCommentDialog = () => {
        const newConditions = { ...formReset, week: '' };
        const updatedConditions = { ...newConditions, reportType: REPORT_TYPE.RRP_MONTHLY_COST_MONITORING };
        const titleDetail = `${formReset.month} - ${formReset.year}`;
        dispatch(
            openCommentDialog({
                conditions: updatedConditions,
                titleDetail: titleDetail
            })
        );
    };

    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {
        setTabValue(newTabValue);
        setSearchParams({ tab: newTabValue, month: monthSearch, year: yearSearch } as any);
    };

    const handleChangeYear = (e: SelectChangeEvent<unknown>) => {
        const { value } = e.target;

        setMonthsFunc(Number(value), tabValue);
    };

    const handleExportDocument = () => {
        exportDocument(Api.cost_monitoring.getDownload.url, {
            year: conditions.year,
            month: conditions.month,
            userName: userInfo?.userName
        });
    };

    const handleSearch = (value: any) => {
        const { projectId } = value;
        transformObject(value);
        setSearchParams(projectId ? { ...value, projectId: projectId.value, projectName: projectId.label } : value);
        setConditions((prev) => ({ ...prev, ...value }));
        handleGetTableData(value);
        setMonthSearch(value.month);
        setYearSearch(value.year);
    };

    useEffect(() => {
        if (Number.isInteger(defaultConditions.year)) {
            setMonthsFunc(defaultConditions.year, tabValue, defaultConditions.month);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tabValue]);

    return (
        <>
            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={listMonthlyMonitoringCostTabs} />

            {/* Search form  */}
            <FilterCollapse
                handleExport={checkAllowedPermission(costMonitoring.download) ? handleExportDocument : undefined}
                handleOpenCommentDialog={checkAllowedPermission(costMonitoring.comment) ? handleOpenCommentDialog : undefined}
                commentLabel={monthlyMonitoring + 'comments'}
                downloadLabel={monthlyMonitoring + 'download'}
            >
                <MonthlyCostMonitoringSearch
                    formReset={formReset}
                    months={months}
                    fixCost={!tabValue}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            {/* Cost Statistic */}
            <CostStatisticCard
                isLoading={loading[getMonthlyCostMonitoring.typePrefix]}
                data={monthlyCostMonitoring.costStatistics}
                year={conditions.year}
                fixCost={!tabValue}
            />
            {/* Effort Statistic */}
            {conditions.month !== '13' && (
                <EffortStatisticCard
                    isLoading={loading[getMonthlyCostMonitoring.typePrefix]}
                    data={monthlyCostMonitoring.effortStatistics}
                    year={conditions.year}
                />
            )}
        </>
    );
};

export default MonthlyCostMonitoring;
