/* eslint-disable prettier/prettier */
import React, { useEffect, useState } from 'react';

// project imports
import { CVDownloadOrViewPDFOption, SkillsUpdateSearch, SkillsUpdateTBody, SkillsUpdateTHead } from 'containers/skills-manage';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { exportDocument, getSearchParam, showPdfInNewTab, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { IPaginationResponse, ITitleCode } from 'types';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import MainCard from 'components/cards/MainCard';
import sendRequest from 'services/ApiService';
import { ROUTER } from 'constants/Routers';
import { TableToolbar } from 'containers';
import {
    CVDownloadOrViewPDFDefaultValues,
    ICVDownloadOrViewPDFDefaultValues,
    ISkillsUpdateSearchConfig,
    skillsUpdateSearchConfig
} from './Config';
import Api from 'constants/Api';

// third party
import { createSearchParams, useNavigate, useSearchParams } from 'react-router-dom';

// react-hook-form
import { useForm } from 'react-hook-form';

// ==============================|| Skills Update ||============================== //
/**
 *  URL Params
 *  page
 *  size
 *  memberCode
 *  fullname
 *  userName
 *  titleCode
 *  departmentId
 */

const SkillsUpdate = () => {
    const { salesReport } = TEXT_CONFIG_SCREEN;
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.fullname,
        SEARCH_PARAM_KEY.userName,
        SEARCH_PARAM_KEY.titleCode,
        SEARCH_PARAM_KEY.titleName,
        SEARCH_PARAM_KEY.departmentId,
        SEARCH_PARAM_KEY.status
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, titleName, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const navigate = useNavigate();
    const defaultConditions = {
        ...skillsUpdateSearchConfig,
        ...cloneParams,
        userName: params.userName ? { value: params.userName, label: params.fullname } : null,
        titleCode: params.titleCode ? { value: params.titleCode, label: params.titleName } : null
    };
    const [loading, setLoading] = useState<boolean>(false);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [members, setMembers] = useState([]);
    const [conditions, setConditions] = useState<ISkillsUpdateSearchConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<ISkillsUpdateSearchConfig>(defaultConditions);
    const [isOpenDownloadOrViewPDF, setIsOpenDownloadOrViewPDF] = useState<boolean>(false);
    const [isViewPDF, setIsViewPDF] = useState<boolean>(false);
    const CVRouter = `/${ROUTER.reports.skills_manage.index}/${ROUTER.reports.skills_manage.skills_update}/${ROUTER.reports.skills_manage.cv}`;
    const { skillsUpdate } = PERMISSIONS.report.skillManage;

    // Functions
    const setDataEmpty = () => {
        setLoading(false);
        setMembers([]);
    };

    const getDataTable = async () => {
        setLoading(true);
        const response = await sendRequest(Api.skills_manage.getSkillsUpdate, {
            ...conditions,
            userName: conditions.userName ? conditions.userName.value : null,
            titleCode: conditions.titleCode ? conditions.titleCode.value! : null,
            page: conditions.page + 1
        });
        if (response?.status) {
            const { content, pagination } = response.result;
            setMembers(content);
            setLoading(false);
            setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
            return;
        }
        setDataEmpty();
    };

    const getCVDownloadOrViewPDF = async (params: ICVDownloadOrViewPDFDefaultValues) => {
        if (isViewPDF) {
            const response = await sendRequest(Api.skills_manage.viewPDF, params);
            if (response?.status) {
                const { content } = response.result;
                showPdfInNewTab(content.pdf);
            } else {
                return;
            }
        } else {
            exportDocument(Api.skills_manage.downloadCV.url, { ...params, type: 'doc' });
        }
        handleCloseDownloadOrViewCV();
    };

    // form download or view PDF
    const methodsCVDownloadOrViewPDF = useForm({
        mode: 'all',
        defaultValues: CVDownloadOrViewPDFDefaultValues
    });

    // Events
    const handleSearch = (values: any) => {
        const { userName, titleCode } = values;
        const member = userName
            ? {
                  userName: userName.value,
                  fullname: userName.label
              }
            : null;
        const title = titleCode
            ? {
                  titleCode: titleCode.value,
                  titleName: titleCode.label
              }
            : null;
        const cloneValues = { ...values, ...member, ...title };
        transformObject(cloneValues);
        setSearchParams(cloneValues);
        setConditions({ ...values });
    };

    const handleOpenCVForm = () => {
        navigate(CVRouter);
    };

    const handleShowDetail = (params: { memberCode: string; userName: string }) => {
        transformObject(params);
        navigate({
            pathname: CVRouter,
            search: `?${createSearchParams({ ...params })}`
        });
    };

    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    const handleOpenDownloadCV = (memberCode: string) => {
        setIsOpenDownloadOrViewPDF(true);
        setIsViewPDF(false);
        methodsCVDownloadOrViewPDF.setValue('memberCode', memberCode);
    };

    const handleOpenViewPDF = (memberCode: string) => {
        setIsOpenDownloadOrViewPDF(true);
        setIsViewPDF(true);
        methodsCVDownloadOrViewPDF.setValue('memberCode', memberCode);
    };

    const handleCloseDownloadOrViewCV = () => {
        setIsOpenDownloadOrViewPDF(false);
    };
    // --------------------- auto filter--------------
    const handleChangeTitleCode = (values: ITitleCode) => {
        setFormReset({
            ...formReset,
            titleCode: values !== null ? { value: values.titleCode, label: `[${values.titleCode}] - ${values.titleName}` } : null,
            userName: null
        });
    };
    const handleChangeDepartmentId = (values: string) => {
        setFormReset({
            ...formReset,
            departmentId: values,
            userName: null
        });
    };
    const handleChangeStatus = (values: string) => {
        setFormReset({
            ...formReset,
            status: values,
            userName: null
        });
    };
    const handleChangeMember = (values: ISkillsUpdateSearchConfig) => {
        if (values) {
            setFormReset({
                ...formReset,
                titleCode: values.title ? { value: `${values.title}`, label: `[${values.title}] - ${values.titleName}` } : null,
                departmentId: `${values.departmentId}`,
                status: values.status,
                userName: { value: `${values.userName}`, label: `${values.firstName} ${values.lastName}` }
            });
        } else {
            setFormReset({
                ...formReset,
                userName: null
            });
        }
    };
    // ---------------------end auto filter--------------

    // Effects
    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <SkillsUpdateSearch
                    handleSubmit={handleSearch}
                    formReset={formReset}
                    handleChangeTitleCode={handleChangeTitleCode}
                    handleChangeDepartmentId={handleChangeDepartmentId}
                    handleChangeStatus={handleChangeStatus}
                    handleChangeMember={handleChangeMember}
                />
            </FilterCollapse>

            {/* Table and Toolbar */}
            <MainCard>
                <TableToolbar
                    handleOpen={checkAllowedPermission(skillsUpdate.add) ? handleOpenCVForm : undefined}
                    addLabel={salesReport.skillsUpdate + 'add-new'}
                />
                <Table heads={<SkillsUpdateTHead />} data={members} isLoading={loading}>
                    <SkillsUpdateTBody
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        members={members}
                        handleShowDetail={handleShowDetail}
                        handleOpenViewPDF={handleOpenViewPDF}
                        handleOpenDownloadCV={handleOpenDownloadCV}
                    />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!loading && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {/* Download */}
            <CVDownloadOrViewPDFOption
                title={isViewPDF ? 'view-cv' : 'download-cv'}
                open={isOpenDownloadOrViewPDF}
                formReturn={methodsCVDownloadOrViewPDF}
                handleDownloadOrViewPDF={getCVDownloadOrViewPDF}
                handleClose={handleCloseDownloadOrViewCV}
            />
        </>
    );
};

export default SkillsUpdate;
