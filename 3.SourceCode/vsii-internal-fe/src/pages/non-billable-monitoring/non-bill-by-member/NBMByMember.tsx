import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';
import { useCallback, useEffect, useState } from 'react';
import { SelectChangeEvent } from '@mui/material';
import { useIntl } from 'react-intl';

import { getNBMByMember, nonBillableMonitoringSelector } from 'store/slice/nonBillableMonitoringSlice';
import { openCommentDialog, isCommentedSelector, changeCommented } from 'store/slice/commentSlice';
import { convertWeekFromToDate, getNumberOfWeek } from 'utils/date';
import { INonBillConfig, nonBillByMemberDefault } from '../Config';
import { exportDocument, transformObject } from 'utils/common';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import { Table } from 'components/extended/Table';
import MainCard from 'components/cards/MainCard';
import { REPORT_TYPE, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { IOption } from 'types';
import Api from 'constants/Api';
import {
    NonBillByMemberNote,
    NonBillByMemberSearch,
    NonBillByMemberThead,
    NonBillByMemberTotal,
    NonBillByMemberTBody
} from 'containers/non-billable-monitoring';

interface IProps {
    weeks: IOption[];
    formReset: INonBillConfig;
    defaultConditions: INonBillConfig;
    params: {
        [key: string]: any;
    };
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    getWeekandYearWhenSearch?: (week: string, year: string | number) => void;
}

const NonBillByMemberTab = ({ weeks, defaultConditions, formReset, params, handleChangeYear, getWeekandYearWhenSearch }: IProps) => {
    const [conditions, setConditions] = useState<INonBillConfig>(defaultConditions);

    const { NBMByMember, loading } = useAppSelector(nonBillableMonitoringSelector);

    const { nBMByMember } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;

    const dispatch = useAppDispatch();

    const intl = useIntl();

    const [, setSearchParams] = useSearchParams();

    const { nonBillable } = PERMISSIONS.report;
    const isCommented = useAppSelector(isCommentedSelector);

    const handleOpenCommentDialog = (userId?: string, subTitle?: string) => {
        const updatedConditions = { ...conditions, userId, reportType: REPORT_TYPE.RP_NON_BILLABLE_MONITORING };
        const titleDetail = conditions?.week ? `${getNumberOfWeek(conditions.week)} - ${conditions.year}` : '';

        dispatch(
            openCommentDialog({
                conditions: updatedConditions,
                titleDetail: userId ? subTitle : intl.formatMessage({ id: 'week' }) + ' ' + titleDetail
            })
        );
    };

    const handleExportDocument = () => {
        exportDocument(Api.non_billable_monitoring.getDownload.url, {
            ...convertWeekFromToDate(conditions.week),
            weekNumber: getNumberOfWeek(conditions.week),
            year: conditions.year
        });
    };

    // Handle submit
    const handleSearch = (value: INonBillConfig) => {
        transformObject(value);

        setSearchParams({ ...params, ...value } as unknown as URLSearchParamsInit);
        setConditions(value);
        getWeekandYearWhenSearch?.(value.week as string, value.year);
    };

    const getTableData = useCallback(() => {
        const weekSelected = convertWeekFromToDate(conditions.week);

        dispatch(
            getNBMByMember({
                ...weekSelected,
                ...transformObject({ ...conditions }, ['tab', 'week']),
                reportType: REPORT_TYPE.RP_NON_BILLABLE_MONITORING
            })
        );
    }, [conditions, dispatch]);

    useEffect(getTableData, [getTableData]);

    useEffect(() => {
        if (isCommented) {
            getTableData();
            dispatch(changeCommented(false));
        }
    }, [isCommented, dispatch, getTableData]);

    return (
        <>
            <FilterCollapse
                downloadLabel={nBMByMember + 'download'}
                commentLabel={nBMByMember + 'commnents'}
                handleExport={checkAllowedPermission(nonBillable.download) ? handleExportDocument : undefined}
                handleOpenCommentDialog={checkAllowedPermission(nonBillable.comment) ? handleOpenCommentDialog : undefined}
            >
                <NonBillByMemberSearch
                    conditions={formReset}
                    weeks={weeks}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            <NonBillByMemberNote />

            <NonBillByMemberTotal nonBillByMember={NBMByMember} isLoading={loading[getNBMByMember.typePrefix]} />

            <MainCard>
                <Table
                    heads={<NonBillByMemberThead />}
                    isLoading={loading[getNBMByMember.typePrefix]}
                    data={NBMByMember ? NBMByMember.data : nonBillByMemberDefault.data}
                >
                    <NonBillByMemberTBody
                        handleOpenCommentDialog={handleOpenCommentDialog}
                        data={NBMByMember?.data ? NBMByMember.data : nonBillByMemberDefault.data}
                    />
                </Table>
            </MainCard>
        </>
    );
};

export default NonBillByMemberTab;
