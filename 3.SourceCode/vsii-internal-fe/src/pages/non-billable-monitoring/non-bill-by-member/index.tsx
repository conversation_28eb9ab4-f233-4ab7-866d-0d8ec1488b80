import { SyntheticEvent, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';

import { getSearchParam, setLocalStorageSearchTime, transformObject } from 'utils/common';
import { checkAllowedPermission, checkAllowedTab } from 'utils/authorization';
import { SEARCH_PARAM_KEY, nonBillMonitoringTabs } from 'constants/Common';
import { openDeniedPermission } from 'store/slice/deniedPermissionSlice';
import { INonBillConfig, nonBillConfig } from '../Config';
import WarningNonBillByMember from './WarningNBMMember';
import { TabPanel } from 'components/extended/Tabs';
import { getWeeksPeriodsInYear } from 'utils/date';
import NonBillByMemberTab from './NBMByMember';
import { useAppDispatch } from 'app/hooks';
import { TabCustom } from 'containers';
import { IOption } from 'types';

// ==============================|| NonBill By Member ||============================== //
/**
 *  URL Params
 *  tab
 *  year
 *  week
 *  departmentId
 *  ====== tab 0 - By Member ======
 *  timeStatus
 */

const NonBillByMember = () => {
    const [weeks, setWeeks] = useState<IOption[]>([]);

    const [searchParams, setSearchParams] = useSearchParams();

    const keyParams = [
        SEARCH_PARAM_KEY.tab,
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.week,
        SEARCH_PARAM_KEY.departmentId,
        SEARCH_PARAM_KEY.timeStatus
    ];

    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);

    transformObject(params);

    const defaultConditions = {
        ...nonBillConfig,
        ...params
    };

    const dispatch = useAppDispatch();

    const [formReset, setFormReset] = useState<INonBillConfig>(defaultConditions);
    const [tabValue, setTabValue] = useState(0);

    const [weekYearforSearch, setWeekYearForSearch] = useState({
        week: defaultConditions.week,
        year: defaultConditions.year
    });
    const getWeekandYearWhenSearch = (weekSearch: string, yearSearch: string | number) => {
        setWeekYearForSearch({ week: weekSearch, year: yearSearch as number });
        // lưu thời gian vào localStorage
        setLocalStorageSearchTime({ week: weekSearch, year: yearSearch });
    };

    const handleChangeTab = (event: SyntheticEvent, newTabValue: number) => {
        setTabValue(newTabValue);
        setFormReset({ ...nonBillConfig });
        setSearchParams({ tab: newTabValue, ...weekYearforSearch } as any);
    };

    const setWeeksFunc = async (year: number, week?: string | number) => {
        const items = getWeeksPeriodsInYear(year);

        setWeeks(items);
        if (items.length > 0) {
            setFormReset((prev) => ({ ...prev, year, week: week || items[0].value }));
        }
    };

    const handleChangeYear = (e: SelectChangeEvent<unknown>) => {
        const { value } = e.target;

        setWeeksFunc(Number(value));
    };

    useEffect(() => {
        let allowedTabs = checkAllowedTab(nonBillMonitoringTabs, params.tab);
        const deniedTabs = allowedTabs.filter((item) => {
            const permission_key = nonBillMonitoringTabs.find((tab) => tab.value === item)!.permission_key;
            return permission_key && !checkAllowedPermission(permission_key) && item === params.tab;
        });

        if (deniedTabs.length) {
            dispatch(openDeniedPermission(true));
        }

        setTabValue(allowedTabs[0]);
        if (Number.isInteger(allowedTabs[0])) {
            setSearchParams((prev) => ({
                ...transformObject(getSearchParam(keyParams, prev)),
                tab: allowedTabs[0].toString()
            }));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch]);

    useEffect(() => {
        if (Number.isInteger(defaultConditions.year)) {
            setWeeksFunc(defaultConditions.year, defaultConditions.week);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tabValue]);

    return (
        <>
            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={nonBillMonitoringTabs} />

            <TabPanel value={tabValue} index={0}>
                <NonBillByMemberTab
                    weeks={weeks}
                    params={params}
                    formReset={formReset}
                    handleChangeYear={handleChangeYear}
                    defaultConditions={defaultConditions}
                    getWeekandYearWhenSearch={getWeekandYearWhenSearch}
                />
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
                <WarningNonBillByMember
                    weeks={weeks}
                    params={params}
                    formReset={formReset}
                    handleChangeYear={handleChangeYear}
                    defaultConditions={defaultConditions}
                    getWeekandYearWhenSearch={getWeekandYearWhenSearch}
                />
            </TabPanel>
        </>
    );
};

export default NonBillByMember;
