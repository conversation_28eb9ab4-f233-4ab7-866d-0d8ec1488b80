// project imports
import { EApproveStatus, paginationParamDefault } from 'constants/Common';
import { VALIDATE_MESSAGES } from 'constants/Message';
import { IOption, IPaginationParam } from 'types';

// third party
import * as yup from 'yup';

// ============== Manage leaves ============== //

// Filter
export interface IManageLeavesDefaultValues extends IPaginationParam {
    memberId: IOption | null;
    fromDate: Date | null;
    toDate: Date | null;
    type: string;
    dept: string;
    status: string;
}

export const manageLeavesDefaultValues: IManageLeavesDefaultValues = {
    ...paginationParamDefault,
    memberId: null,
    fromDate: null,
    toDate: null,
    type: '',
    dept: '',
    status: ''
};

export const manageLeavesSearchSchema = yup.object().shape({
    fromDate: yup.date().nullable(),
    toDate: yup.date().nullable().min(yup.ref('fromDate'), VALIDATE_MESSAGES.ENDDATE),
    type: yup.string().nullable(),
    status: yup.string().nullable()
});

// Add or edit
export interface IAddOrEditLeaves {
    id?: string;
    member: IOption | null;
    directApproverIdHexString?: string;
    nextApproverIdHexString?: string;

    // Các trường hiển thị
    codeMember?: string;
    titleMember?: string;
    groupMember?: string;
    department?: string;
    contractType?: string;
    totalLeaveDays?: number;
    remainingLeaveDays?: string | number;

    requestDay: Date | null;
    fromDate: Date | null;
    toDate: Date | null;
    leaveDetails?: Array<{
        leaveType: string;
        numberOfDays: number;
        fromDate: Date | null;
        toDate: Date | null;
        note: string;
        hasError?: boolean;
    }>;
    status?: string;
}

export const addOrEditLeavesDefaultValues: IAddOrEditLeaves = {
    id: '',
    member: null,
    directApproverIdHexString: '',
    nextApproverIdHexString: '',
    requestDay: null,
    fromDate: null,
    toDate: null,
    leaveDetails: [
        {
            leaveType: '',
            numberOfDays: 0,
            fromDate: null,
            toDate: null,
            note: '',
            hasError: false
        }
    ]
};

export const addOrEditLeavesSchema = yup.object().shape({
    member: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
        .required(VALIDATE_MESSAGES.REQUIRED),
    indirectManagement: yup.string().nullable(),
    directApproverIdHexString: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    nextApproverIdHexString: yup.string().test('nextManagerApprove-validation', VALIDATE_MESSAGES.REQUIRED, function (value) {
        const { status } = this.parent;
        const { currentUserId } = this.options.context || {};
        const isCreator = this.parent.member.value === currentUserId;
        const shouldValidate = status === EApproveStatus.AWAITING_QLTT && !isCreator;

        return shouldValidate ? !!value : true;
    }),
    requestDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),
    leaveDetails: yup
        .array()
        .of(
            yup.object().shape({
                leaveType: yup.string().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                fromDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED),
                toDate: yup.date().nullable().required(VALIDATE_MESSAGES.REQUIRED).min(yup.ref('fromDate'), VALIDATE_MESSAGES.AFTER_DAY),
                numberOfDays: yup.number(),
                note: yup.string().nullable(),
                hasError: yup.boolean().test('no-validation-error', VALIDATE_MESSAGES.REQUIRED, function (value) {
                    return value === false;
                })
            })
        )
        .min(1, VALIDATE_MESSAGES.REQUIRED)
        .test('date-sequence', VALIDATE_MESSAGES.LEAVE_SEQUENCE, function (leaveDetails) {
            if (!Array.isArray(leaveDetails)) return true;

            for (let i = 1; i < leaveDetails.length; i++) {
                const prev = leaveDetails[i - 1];
                const current = leaveDetails[i];

                if (prev?.toDate && current?.fromDate) {
                    const prevTo = new Date(prev.toDate);
                    const currFrom = new Date(current.fromDate);

                    if (currFrom <= prevTo) {
                        return this.createError({
                            path: `leaveDetails[${i}].fromDate`,
                            message: VALIDATE_MESSAGES.LEAVE_SEQUENCE
                        });
                    }
                }
            }

            return true;
        })
});

export interface IUserDetail {
    idHexString: string;
    fullName: string;
    memberCode: string;
    titleMember: string;
    department: string;
    group: string;
    typeContract: string;
    position: string;
}
// Reject Leave Request
export interface IRejectLeaveRequest {
    reason: string;
    fromDate: Date;
    toDate: Date;
    fullName: string;
    totalDays: number | string;
}

export const rejectLeaveRequestConfig: IRejectLeaveRequest = {
    reason: '',
    fromDate: new Date(),
    toDate: new Date(),
    fullName: '',
    totalDays: ''
};

export const rejectLeaveRequestSchema = yup.object().shape({
    reason: yup.string().required(VALIDATE_MESSAGES.REQUIRED),
    fullName: yup.string().required(VALIDATE_MESSAGES.REQUIRED)
});

export interface ILeaveRequestData {
    fullName: string;
    totalDays: number | string;
    fromDate: Date;
    toDate: Date;
}
