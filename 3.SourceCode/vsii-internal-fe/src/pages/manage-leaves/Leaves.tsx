/* eslint-disable prettier/prettier */
// react
import { useEffect, useState } from 'react';

// third party
import { yupResolver } from '@hookform/resolvers/yup';
import { useSearchParams } from 'react-router-dom';
import { useForm, useWatch } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

// project imports
import { AddOrEditLeaves, ManageLeavesSearch, ManageLeavesTBody, ManageLeavesTHead } from 'containers/working-calendar';
import { getSearchParam, isEmpty, transformObject } from 'utils/common';
import { ILeavesItem, ILeavesList } from 'types/working-calendar';
import { IPaginationResponse, IResponseList } from 'types';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { FilterCollapse } from 'containers/search';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { PERMISSIONS } from 'constants/Permission';
import { useAppDispatch } from 'app/hooks';
import MainCard from 'components/cards/MainCard';
import Api from 'constants/Api';
import { EApproveStatus, SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import { TableToolbar } from 'containers';
import sendRequest from 'services/ApiService';
import { dateFormat } from 'utils/date';
import {
    IAddOrEditLeaves,
    ILeaveRequestData,
    IManageLeavesDefaultValues,
    IUserDetail,
    addOrEditLeavesDefaultValues,
    addOrEditLeavesSchema,
    manageLeavesDefaultValues,
    rejectLeaveRequestConfig,
    rejectLeaveRequestSchema
} from './Config';
import { openSnackbar } from 'store/slice/snackbarSlice';
import RejectLeaveModal, { IRejectLeaveFormValues } from 'containers/working-calendar/RejectLeaveRequest';
import { useSelector } from 'react-redux';
import { authSelector } from 'store/slice/authSlice';

// ==============================|| Manage Leaves ||============================== //
/**
 * URL Params
 * page
 * size
 * year
 * fullname
 * idHexstring
 * type
 * status
 */
const ManageLeaves = () => {
    const { manage_leaves } = TEXT_CONFIG_SCREEN.workingCalendar;
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size,
        SEARCH_PARAM_KEY.idHexString,
        SEARCH_PARAM_KEY.fullname,
        SEARCH_PARAM_KEY.type,
        SEARCH_PARAM_KEY.fromDate,
        SEARCH_PARAM_KEY.toDate,
        SEARCH_PARAM_KEY.dept,
        SEARCH_PARAM_KEY.status
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, idHexString, ...cloneParams }: any = params;
    const { userInfo } = useSelector(authSelector);

    // Hooks, State, Variable
    const defaultConditions = {
        ...manageLeavesDefaultValues,
        ...cloneParams,
        memberId: params.idHexString ? { value: params.idHexString, label: params.fullname } : null
    };
    const dispatch = useAppDispatch();
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [paginationResponse, setPaginationResponse] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [leaves, setLeaves] = useState<ILeavesItem[]>([]);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [userDetail, setUserDetail] = useState<IUserDetail | null>(null);
    const [conditions, setConditions] = useState<IManageLeavesDefaultValues>(defaultConditions);
    const [isRejectModalOpen, setIsRejectModalOpen] = useState<boolean>(false);
    const [rejectData, setRejectData] = useState<ILeaveRequestData | null>(null);
    const [formReset] = useState<IManageLeavesDefaultValues>(defaultConditions);
    const [userItem, setUserItem] = useState<ILeavesItem>();
    const [leaveInfo, setLeaveInfo] = useState<IAddOrEditLeaves>();
    const { manageLeaves } = PERMISSIONS.workingCalendar;

    // ================= Use form =================
    const methodsAddOrEdit = useForm({
        defaultValues: addOrEditLeavesDefaultValues,
        mode: 'all',
        resolver: yupResolver(addOrEditLeavesSchema),
        context: {
            currentUserId: userInfo?.idHexString // Thêm currentUserId vào context
        }
    });
    // Reject leave
    const rejectLeaveFormReturn = useForm<IRejectLeaveFormValues>({
        defaultValues: rejectLeaveRequestConfig,
        resolver: yupResolver(rejectLeaveRequestSchema)
    });

    // ================= Functions =================
    const setDataEmpty = () => {
        setLeaves([]);
        setIsLoading(false);
    };

    // Get leaves
    const getLeaves = async () => {
        setIsLoading(true);
        const response: IResponseList<ILeavesList> = await sendRequest(Api.manage_leaves.getAll, {
            ...conditions,
            fromDate: conditions.fromDate ? dateFormat(conditions.fromDate) : null,
            toDate: conditions.toDate ? dateFormat(conditions.toDate) : null,
            page: conditions.page + 1,
            memberId: conditions.memberId ? conditions.memberId.value : null
        });

        if (response) {
            const { status, result } = response;

            if (status) {
                const { content, pagination } = result;
                if (!isEmpty(content)) {
                    setLeaves(content);
                    setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                    setIsLoading(false);
                } else {
                    setDataEmpty();
                }
            }
        } else {
            setDataEmpty();
        }
    };

    // ================= Event =================
    // Handle change page
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    // Handle change rows per page
    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };
    const getUserDetail = async () => {
        const response = await sendRequest(Api.manage_leaves.getDetailUser);
        if (response?.status) {
            const content = response?.result?.content;
            if (!isEmpty(content)) {
                setUserDetail(content);
            }
        } else {
            setDataEmpty();
        }
    };
    const getLeaveDetail = async (idHexString: string) => {
        const response = await sendRequest(Api.manage_leaves.getLeaveDetail(idHexString));
        if (response?.status) {
            const content = response?.result?.content;
            setLeaveInfo({
                ...content,
                member: { value: content.memberIdHexString, label: content.memberName },
                directApproverIdHexString: content.directApproverId,
                nextApproverIdHexString: content.indirectApproverId
            });
            if (!isEmpty(content)) {
                return content;
            }
        }
        return null;
    };

    // Handle add or edit leaves
    const handleOpenFormAddOrEdit = async (item?: ILeavesItem) => {
        if (item) {
            setUserItem(item);
            setIsEdit(true);
            const leaveDetail = await getLeaveDetail(item.id);
            if (leaveDetail) {
                const { leaveType, memberIdHexString, ...leaveData } = leaveDetail;
                methodsAddOrEdit.reset({
                    ...leaveData,
                    member: { value: memberIdHexString, label: leaveDetail.memberName },
                    directApproverIdHexString: leaveDetail.directApproverId || '',
                    nextApproverIdHexString: leaveDetail.indirectApproverId || '',
                    department: leaveDetail.dept,
                    contractType: leaveDetail.typeContract,
                    requestDay: new Date(leaveData.createDate),
                    fromDate: new Date(leaveData.fromDate),
                    toDate: new Date(leaveData.toDate),
                    leaveDetails: leaveType?.map((leave: any) => {
                        const fromDateValue = leave.fromDate ? new Date(leave.fromDate) : null;
                        const toDateValue = leave.toDate ? new Date(leave.toDate) : null;

                        return {
                            ...leave,
                            fromDate: fromDateValue,
                            toDate: toDateValue,
                            numberOfDays: leave.numberOfDaysOff,
                            note: leave.note
                        };
                    })
                });
            }
            setIsOpen(true);
        } else {
            methodsAddOrEdit.reset({
                ...addOrEditLeavesDefaultValues,
                member: { value: userDetail?.idHexString, label: userDetail?.fullName },
                codeMember: userDetail?.memberCode,
                titleMember: userDetail?.position,
                groupMember: userDetail?.group,
                department: userDetail?.department,
                contractType: userDetail?.typeContract
            });
            setIsOpen(true);
        }
    };
    const nextApproverIdHexString = useWatch({
        control: methodsAddOrEdit.control,
        name: 'nextApproverIdHexString'
    });

    const handleApprove = async () => {
        if (userItem) {
            try {
                const response = await sendRequest(Api.manage_leaves.putApproved(userItem.id), {
                    status: userItem.status,
                    nextApproverIdHexString: userItem.status === EApproveStatus.AWAITING_QLTT ? nextApproverIdHexString : ''
                });
                if (response) {
                    const { status, result } = response;
                    if (status) {
                        const { content, pagination } = result;
                        if (!isEmpty(content)) {
                            getLeaves();
                            handleCloseFormAddOrEdit();
                            setPaginationResponse({ ...paginationResponse, totalElement: pagination?.totalElement });
                            setIsLoading(false);
                        } else {
                            setDataEmpty();
                        }
                    }
                }
            } catch {
                setDataEmpty();
            } finally {
                dispatch(closeConfirm());
            }
        }
    };

    const handleConfirmApprove = async () => {
        const isValid = await methodsAddOrEdit.trigger();
        if (!isValid) {
            return;
        }
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: (
                    <>
                        <FormattedMessage id="confirm-approve-leaves" />
                    </>
                ),
                handleConfirm: () => handleApprove()
            })
        );
    };

    // Handle close form add or edit leaves
    const handleCloseFormAddOrEdit = () => {
        setIsEdit(false);
        setIsOpen(false);
    };

    // Handle delete
    const handleDelete = (idHexString: string) => {
        dispatch(
            openConfirm({
                title: <FormattedMessage id="delete" />,
                content: <FormattedMessage id="delete-leave-request" />,
                handleConfirm: async () => {
                    const response = await sendRequest(Api.manage_leaves.deleteLeave(idHexString));
                    if (response?.status) {
                        dispatch(
                            openSnackbar({
                                open: true,
                                message: response.result.content,
                                variant: 'alert',
                                alert: { color: 'success' }
                            })
                        );
                        await getLeaves();
                    } else {
                        dispatch(
                            openSnackbar({
                                open: true,
                                message: response?.result?.content || 'Error',
                                variant: 'alert',
                                alert: { color: 'error' }
                            })
                        );
                    }
                    dispatch(closeConfirm());
                },
                width: 400
            })
        );
    };
    // Handle download
    const handleDownload = async (idHexString: string) => {
        const response = await sendRequest(Api.manage_leaves.downloadLeave(idHexString));
        if (response?.status) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: response.result.content,
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
        } else {
            dispatch(
                openSnackbar({
                    open: true,
                    message: response?.result?.content || 'Error',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
        }
    };

    // Handle open reject modal
    const handleOpenRejectModal = (data: ILeaveRequestData) => {
        setRejectData(data);
        setIsRejectModalOpen(true);
    };

    // Handle close reject modal
    const handleCloseRejectModal = () => {
        setIsRejectModalOpen(false);
        setRejectData(null);
    };

    // ================= Handle search =================
    const handleSearch = (values: any) => {
        transformObject(values);
        const { memberId, ...cloneValues } = values;
        const parameters = memberId
            ? {
                  ...cloneValues,
                  idHexString: memberId.value,
                  fullname: memberId.label,
                  fromDate: dateFormat(values.fromDate),
                  toDate: dateFormat(values.toDate)
              }
            : {
                  ...cloneValues,
                  fromDate: dateFormat(values.fromDate),
                  toDate: dateFormat(values.toDate)
              };
        setSearchParams(parameters);
        setConditions({ ...values });
    };

    // ================= Effect =================
    useEffect(() => {
        getUserDetail();
    }, []);
    useEffect(() => {
        getLeaves();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form */}
            <FilterCollapse title={manage_leaves + 'filters'}>
                <ManageLeavesSearch formReset={formReset} handleSearch={handleSearch} />
            </FilterCollapse>

            {/* Leaves list */}
            <MainCard>
                <TableToolbar
                    handleOpen={checkAllowedPermission(manageLeaves.add) ? handleOpenFormAddOrEdit : undefined}
                    addLabel={manage_leaves + 'add'}
                />
                <Table heads={<ManageLeavesTHead />} isLoading={isLoading} data={leaves}>
                    <ManageLeavesTBody
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        leaves={leaves}
                        handleEdit={handleOpenFormAddOrEdit}
                        handleDelete={handleDelete}
                        handleDownload={handleDownload}
                    />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!isLoading && (
                <TableFooter
                    pagination={{ total: paginationResponse.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {/* Form Add or Edit Leaves */}
            {isOpen && (
                <AddOrEditLeaves
                    open={isOpen}
                    isEdit={isEdit}
                    handleClose={handleCloseFormAddOrEdit}
                    formReturn={methodsAddOrEdit}
                    onSuccess={getLeaves}
                    handleApprove={handleConfirmApprove}
                    onReject={handleOpenRejectModal}
                    leaveInfo={leaveInfo}
                />
            )}

            {isRejectModalOpen && rejectData && (
                <RejectLeaveModal
                    isOpen={isRejectModalOpen}
                    onClose={handleCloseRejectModal}
                    data={rejectData}
                    formReturn={rejectLeaveFormReturn}
                    onSuccess={getLeaves}
                    leaveIdHexString={userItem?.id}
                />
            )}
        </>
    );
};

export default ManageLeaves;
