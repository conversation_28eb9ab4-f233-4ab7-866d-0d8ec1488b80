/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import moment from 'moment';

// project imports
import { MonthlyEffortProjectSearch, MonthlyEffortProjectTBody, MonthlyEffortProjectThead } from 'containers/monthly-effort';
import { transformObject, exportDocument, getSearchParam, isEmpty, setLocalStorageSearchTime } from 'utils/common';
import { IMonthlyEffortProjectConfig, monthEffortProjectDefault, monthlyEffortProjectConfig } from './Config';
import { convertMonthFromToDate, getCurrentMonth, getCurrentYear, getMonthsOfYear } from 'utils/date';
import { IMonthlyEffortProject, IMonthlyEffortProjectList, IOption, IResponseList } from 'types';
import { SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { FilterCollapse } from 'containers/search';
import { Table } from 'components/extended/Table';
import MainCard from 'components/cards/MainCard';
import sendRequest from 'services/ApiService';
import { useAppDispatch } from 'app/hooks';
import Api from 'constants/Api';

// third party
import { useSearchParams } from 'react-router-dom';
import { SelectChangeEvent } from '@mui/material';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { checkAllowedPermission } from 'utils/authorization';

// ==============================|| Monthy Effort - Project ||============================== //
/**
 *  URL Params
 *  year
 *  month
 *  departmentId
 *  projectType
 *  projectId
 *  projectName
 */
const MonthlyEffortProject = () => {
    const dispatch = useAppDispatch();
    const intl = useIntl();

    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.month,
        SEARCH_PARAM_KEY.departmentId,
        SEARCH_PARAM_KEY.projectType,
        SEARCH_PARAM_KEY.projectId,
        SEARCH_PARAM_KEY.projectName
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { projectName, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...monthlyEffortProjectConfig,
        ...cloneParams,
        projectId: params.projectId ? { value: params.projectId, label: params.projectName } : null
    };
    const [loading, setLoading] = useState<boolean>(false);
    const [project, setProject] = useState<IMonthlyEffortProject>(monthEffortProjectDefault);
    const [conditions, setConditions] = useState<IMonthlyEffortProjectConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IMonthlyEffortProjectConfig>(defaultConditions);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [months, setMonths] = useState<IOption[]>([]);
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const [month, setMonth] = useState({ fromDate: '', toDate: '' });
    const { monthlyEffort } = PERMISSIONS.report;

    const { monthlyEffortProject } = TEXT_CONFIG_SCREEN.monthlyEffort;

    // Function
    const getDataTable = async () => {
        setLoading(true);
        const response: IResponseList<IMonthlyEffortProjectList> = await sendRequest(Api.monthly_efford.getProject, {
            ...conditions,
            projectId: conditions.projectId ? conditions.projectId.value : null
        });

        if (response.status) {
            const { result } = response;
            setProject(isEmpty(result.content) ? monthEffortProjectDefault : result.content);
            setLoading(false);

            return;
        } else {
            setDataEmpty();
        }
    };

    const setDataEmpty = () => {
        setProject(monthEffortProjectDefault);
        setLoading(false);
    };

    const getMonthInYears = useCallback(async (y: number) => {
        const monthInYears = await getMonthsOfYear(y);
        return monthInYears;
    }, []);

    // Event
    const handleChangeYear = (e: any) => {
        const { value } = e.target;
        setYear(value);
        setIsChangeYear(true);
        setFormReset((prev) => ({
            ...prev,
            projectId: null,
            year: value
        }));
    };

    const handleExportDocument = () => {
        exportDocument(Api.monthly_efford.getDownload.url, { year: conditions.year, month: conditions.month });
    };
    const handleChangeProject = (data: any) => {
        if (data)
            setFormReset((prev) => ({
                ...prev,
                departmentId: data?.dept,
                projectType: data?.typeCode,
                billable: data?.billable,
                projectId: data ? data : null
            }));
    };

    const monthOnload = months.find((item) => item.value === formReset.month);
    const handleChangeMonth = (value: string) => {
        const getMonth = months.find((month) => month.value === value);
        if (getMonth) {
            setMonth(convertMonthFromToDate(getMonth.label));
        }
        setFormReset((prev) => ({
            ...prev,
            projectId: null,
            month: value
        }));
    };

    const handleChangeProjectType = (e: SelectChangeEvent<unknown>) => {
        setFormReset((prev) => ({
            ...prev,
            projectId: null,
            projectType: e.target.value as string
        }));
    };

    const handleChangeBillable = (e: SelectChangeEvent<unknown>) => {
        setFormReset((prev) => ({
            ...prev,
            projectId: null,
            billable: e.target.value as string
        }));
    };

    const handleChangeDept = (value: string) => {
        setFormReset((prev) => ({
            ...prev,
            projectId: null,
            departmentId: value
        }));
    };

    // Handle submit
    const handleSearch = (value: any) => {
        const { projectId } = value;
        transformObject(value);
        setSearchParams(projectId ? { ...value, projectId: projectId.value, projectName: projectId.label } : value);
        setConditions(value);
        // lưu thời gian vào localStorage
        setLocalStorageSearchTime({ month: value.month, year: value.year });
    };

    useEffect(() => {
        if (monthOnload) {
            setMonth(convertMonthFromToDate(monthOnload.label as string));
        }
    }, [monthOnload]);
    // Effect
    useEffect(() => {
        getDataTable();
    }, [conditions]);

    useEffect(() => {
        getMonthInYears(year).then((items: IOption[]) => {
            if (getCurrentYear() === year) {
                let currentMonth = items.slice(0, getCurrentMonth());
                setMonths(currentMonth);
            } else {
                setMonths(items);
            }
            if (items.length > 0 && isChangeYear) {
                setFormReset({ ...formReset, year, month: items[0].value });
            }
        });
    }, [year]);
    useEffect(() => {
        if (conditions.projectId) {
            project.users.forEach((user) => {
                let monthsWithNoEffort: Array<number> = [];
                Object.keys(user.months).forEach((key) => {
                    if ((user.months as any)[key] === 0 && key !== 'thirteenthSalary') {
                        monthsWithNoEffort.push(Number(moment().month(key).format('M')));
                    }
                });
                const filteredMonthsWithNoEffort = monthsWithNoEffort
                    .filter((e) => e <= (conditions.month as number))
                    .sort((a, b) => a - b);
                if (filteredMonthsWithNoEffort.length > 0) {
                    dispatch(
                        openSnackbar({
                            open: true,
                            message: intl.formatMessage(
                                { id: 'no_effort_data' },
                                { projectName, months: filteredMonthsWithNoEffort.join(',') }
                            ),
                            variant: 'alert',
                            alert: { color: 'info' }
                        })
                    );
                }
            });
        }
    }, [project]);
    return (
        <>
            {/* Search form  */}
            <FilterCollapse
                handleExport={checkAllowedPermission(monthlyEffort.projectDownload) ? handleExportDocument : undefined}
                downloadLabel={monthlyEffortProject + 'download-report'}
            >
                <MonthlyEffortProjectSearch
                    formReset={formReset}
                    months={months}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                    handleChangeProject={handleChangeProject}
                    handleChangeMonth={handleChangeMonth}
                    handleChangeProjectType={handleChangeProjectType}
                    handleChangeBillable={handleChangeBillable}
                    handleChangeDept={handleChangeDept}
                    month={month}
                />
            </FilterCollapse>

            {/* Table */}
            <MainCard>
                <Table
                    heads={<MonthlyEffortProjectThead projectLength={project?.users.length} currentYear={conditions.year} />}
                    isLoading={loading}
                    data={project?.users}
                >
                    <MonthlyEffortProjectTBody projects={project?.users} total={project?.total} />
                </Table>
            </MainCard>
        </>
    );
};

export default MonthlyEffortProject;
