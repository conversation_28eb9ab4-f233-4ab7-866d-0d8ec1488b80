import { useCallback, useEffect, useState } from 'react';

// material-ui
import { Grid } from '@mui/material';

// project imports
import { monthEffortSummaryInfoDefault, monthlyEffortSummaryConfig, IMonthlyEffortSummaryConfig } from './Config';
import { exportDocument, getSearchParam, setLocalStorageSearchTime, transformObject } from 'utils/common';
import { IMonthEffortSummaryInfo, IMonthlyEffortSummaryResponse, IOption, IResponseList } from 'types';
import { MONTHS, REPORT_TYPE, SEARCH_PARAM_KEY, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { checkAllowedPermission } from 'utils/authorization';
import { openCommentDialog } from 'store/slice/commentSlice';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import sendRequest from 'services/ApiService';
import { gridSpacing } from 'store/constant';
import { getMonthsOfYear } from 'utils/date';
import { useAppDispatch } from 'app/hooks';
import Api from 'constants/Api';
import {
    ActualEffortAllocationCard,
    EffortPlanUpdateStatusCard,
    LogTimesheetOnRedmineCard,
    MonthlyEffortSummarySearch,
    NumberOfProjectCard
} from 'containers/monthly-effort';

// third party
import { useSearchParams } from 'react-router-dom';
import { useIntl } from 'react-intl';

// ==============================|| Monthly Effort - Summary ||============================== //
/**
 *  URL Params
 *  year
 *  month
 */
const MonthlyEffortSummary = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.month];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);

    // Hooks, State, Variable
    const defaultConditions = { ...monthlyEffortSummaryConfig, ...params };
    const dispatch = useAppDispatch();
    const intl = useIntl();
    const [loading, setLoading] = useState<boolean>(false);
    const [monthlyEffortSummaryInfo, setMonthlyEffortSummaryInfo] = useState<IMonthEffortSummaryInfo>(monthEffortSummaryInfoDefault);
    const [conditions, setConditions] = useState<IMonthlyEffortSummaryConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IMonthlyEffortSummaryConfig>(defaultConditions);
    const [months, setMonths] = useState<IOption[]>([]);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const { monthlyEffort } = PERMISSIONS.report;

    const { Summary } = TEXT_CONFIG_SCREEN.monthlyEffort;

    const getDataChart = async () => {
        setLoading(true);
        const response: IResponseList<IMonthlyEffortSummaryResponse> = await sendRequest(Api.monthly_efford.getSummary, conditions);

        if (response) {
            const { status, result } = response;

            if (status && !Array.isArray(result.content)) {
                setMonthlyEffortSummaryInfo(result.content as IMonthEffortSummaryInfo);
            } else {
                setDataEmpty();
            }
            setLoading(false);
        } else {
            setDataEmpty();
        }
    };

    const setDataEmpty = () => {
        setMonthlyEffortSummaryInfo(monthEffortSummaryInfoDefault);
        setLoading(false);
    };

    const getMonthInYears = useCallback(async (y: number) => {
        const monthInYears = await getMonthsOfYear(y);
        return monthInYears;
    }, []);

    // Event
    const handleOpenCommentDialog = () => {
        const updatedConditions = { ...conditions, reportType: REPORT_TYPE.RP_MONTHLY };
        const month = conditions?.month && typeof conditions.month === 'number' ? MONTHS[conditions.month - 1].toLowerCase() : '';
        const titleDetail = `${intl.formatMessage({ id: month })} - ${conditions.year}`;
        dispatch(
            openCommentDialog({
                conditions: updatedConditions,
                titleDetail: titleDetail
            })
        );
    };

    const handleChangeYear = (e: any) => {
        const { value } = e.target;
        setYear(value);
        setIsChangeYear(true);
    };

    const handleExportDocument = () => {
        exportDocument(Api.monthly_efford.getDownload.url, { year: conditions.year, month: conditions.month });
    };

    // Handle submit
    const handleSearch = (value: IMonthlyEffortSummaryConfig) => {
        setSearchParams(value as any);
        setConditions({ ...conditions, ...value });
        // lưu thời gian vào localStorage
        setLocalStorageSearchTime({ month: value.month, year: value.year });
    };

    useEffect(() => {
        getDataChart();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    useEffect(() => {
        getMonthInYears(year).then((items: IOption[]) => {
            setMonths(items);
            if (items.length > 0 && isChangeYear) setFormReset({ ...formReset, year, month: items[0].value });
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [year]);

    return (
        <>
            <FilterCollapse
                downloadLabel={Summary + 'download-report'}
                commentLabel={Summary + 'commnents'}
                handleExport={checkAllowedPermission(monthlyEffort.summaryDownload) ? handleExportDocument : undefined}
                handleOpenCommentDialog={checkAllowedPermission(monthlyEffort.comment) ? handleOpenCommentDialog : undefined}
            >
                <MonthlyEffortSummarySearch
                    conditions={formReset}
                    months={months}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            {/* Number of project card */}
            <NumberOfProjectCard isLoading={loading} data={monthlyEffortSummaryInfo.projectTypeByDepartment} />

            {/* Update effort plan and log timesheet on redmine card */}
            <Grid
                container
                spacing={gridSpacing}
                sx={{
                    marginBottom: '16px',
                    '& .MuiPaper-root': {
                        marginBottom: '0 !important',
                        height: '100%'
                    }
                }}
                alignItems="center"
            >
                <Grid item xs={12}>
                    <Grid container spacing={gridSpacing}>
                        <Grid item xs={12} md={4}>
                            <EffortPlanUpdateStatusCard
                                isLoading={loading}
                                data={monthlyEffortSummaryInfo.effortPlan}
                                conditions={conditions}
                                fetchData={getDataChart}
                            />
                        </Grid>
                        <Grid item xs={12} md={8}>
                            <LogTimesheetOnRedmineCard isLoading={loading} data={monthlyEffortSummaryInfo.logTimesheetOnRedmine} />
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>

            {/* Actual effort allocation card */}
            <ActualEffortAllocationCard isLoading={loading} conditions={conditions} data={monthlyEffortSummaryInfo.effortByProjectType} />
        </>
    );
};

export default MonthlyEffortSummary;
