// apexcharts
import { Props } from 'react-apexcharts';

// yup
import * as yup from 'yup';

// project imports
import { IMonthEffortSummaryInfo, IMonthlyEffortProjectReport, IOption, IPaginationParam } from 'types';
import { getCurrentMonth, getCurrentYear } from 'utils/date';
import { paginationParamDefault } from 'constants/Common';
import { VALIDATE_MESSAGES } from 'constants/Message';

// ============== Department member ============== //
export interface IMonthlyEffortConfig extends IPaginationParam {
    year: number;
    month: number | string;
    departmentId?: string;
    userId?: IOption | null;
    timeStatus?: string;
    projectId?: IOption | null;
    fromDate?: string;
    toDate?: string;
}
export interface IAutoFilterMemberConfig {
    fromDate: string;
    toDate: string;
    departmentId?: string;
    timeStatus?: string;
}

export const monthlyEffortConfig: IMonthlyEffortConfig = {
    ...paginationParamDefault,
    year: getCurrentYear(),
    month: getCurrentMonth(),
    departmentId: '',
    userId: null,
    timeStatus: '',
    projectId: null
};

export const monthlyEffortDepartmentMemberSchema = yup.object().shape({
    year: yup.string(),
    month: yup.string(),
    userId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable(),
    departmentId: yup.string(),
    timeStatus: yup.string()
});

export const monthlyEffortDepartmentProjectSchema = yup.object().shape({
    year: yup.string(),
    month: yup.string(),
    projectId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
});

// ============== Project ============== //

export const monthEffortProjectReportDefault: IMonthlyEffortProjectReport[] = [
    {
        endDate: '',
        firstNamePM: '',
        id: '',
        lastNamePM: '',
        lastUpdated: '',
        projectName: '',
        projectPhase: '',
        projectProgressAssessment: '',
        startDate: '',
        status: '',
        userUpdated: '',
        projectType: '',
        department: ''
    }
];

export const monthEffortProjectDefault = {
    users: [],
    total: {
        totalQuota: 0,
        implementQuota: 0,
        maintainanceQuota: 0,
        previousQuota: 0,
        year: 0,
        months: {
            january: 0,
            february: 0,
            march: 0,
            april: 0,
            may: 0,
            june: 0,
            july: 0,
            august: 0,
            september: 0,
            october: 0,
            november: 0,
            december: 0
        },
        totalUsedEffort: 0,
        remainingEffort: 0
    }
};

export const ormReportDefault: IOrmReport = {
    createdAt: '',
    department: '',
    id: '',
    month: '',
    reportName: '',
    uploadUser: '',
    year: getCurrentYear(),
    docCode: ''
};

export interface IMonthlyEffortProjectConfig {
    year: number;
    month: string | number;
    departmentId?: string;
    projectType?: string;
    projectId?: IOption | null;
    billable?: string;
}
export const monthlyEffortProjectConfig: IMonthlyEffortProjectConfig = {
    year: getCurrentYear(),
    month: getCurrentMonth(),
    departmentId: '',
    projectType: '',
    projectId: null,
    billable: ''
};

export interface IMonthlyEffortOrmReportConfig {
    year: number;
    month: string | number;
    department?: string;
    reportName?: string | null;
    pageNumber: number;
    pageSize: number;
}
export interface IOrmReport {
    id: string;
    createdAt: string;
    department: string;
    month: string;
    reportName: string;
    uploadUser: string;
    year: number;
    docCode: string;
}
export interface OrmReportList {
    content: IOrmReport[];
}

export const monthlyEffortOrmReportConfig: IMonthlyEffortOrmReportConfig = {
    year: getCurrentYear(),
    month: getCurrentMonth(),
    department: '',
    reportName: '',
    pageNumber: 1,
    pageSize: 10
};

export const monthlyEffortOmReportSchema = yup.object().shape({
    year: yup.string(),
    month: yup.string(),
    projectType: yup.string(),
    department: yup.string(),
    reportName: yup.string()
});

export const monthlyEffortProjectSchema = yup.object().shape({
    year: yup.string(),
    month: yup.string(),
    projectType: yup.string(),
    projectId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable(),
    departmentId: yup.string(),
    billable: yup.string()
});

export interface IMonthlyEffortProjectReportDefault {
    year: number;
    month: string | number;
    projectManagerId?: string;
    projectManager?: string;
    projectId?: string;
    projectName?: string;
}

export interface IMonthlyEffortProjectReportConfig {
    year: number;
    month: string | number;
    projectManager?: { value: string; label: string } | string | null;
    usernamePM?: string | null;
    project?: { value: number; label: string; typeCode: string } | string | null;
    projectId?: number | null;
    page: number;
    size: number;
}

export const monthlyEffortProjectReportDefault: IMonthlyEffortProjectReportConfig = {
    year: getCurrentYear(),
    month: 1,
    usernamePM: null,
    projectId: null,
    project: null,
    projectManager: null,
    page: 1,
    size: 10
};

export const monthlyEffortProjectReportSchema = yup.object().shape({
    year: yup.string(),
    month: yup.string(),
    projectId: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable(),
    projectManager: yup
        .object()
        .shape({
            value: yup.string(),
            label: yup.string()
        })
        .nullable()
});

// ============== Summary ============== //
export interface IMonthlyEffortSummaryConfig {
    year: number;
    month: string | number;
}

export const monthlyEffortSummaryConfig: IMonthlyEffortSummaryConfig = {
    year: getCurrentYear(),
    month: getCurrentMonth()
};

export const monthlyEffortSummarySchema = yup.object().shape({
    year: yup.number(),
    month: yup.number()
});

export const monthEffortSummaryInfoDefault: IMonthEffortSummaryInfo = {
    projectTypeByDepartment: [],
    effortPlan: [],
    effortByProjectType: [],
    logTimesheetOnRedmine: { logTime: [] }
};

export const effortPlanUpdateStatusColors = [
    { color: '#4272DF', name: 'send-report' },
    { color: '#D74642', name: 'no-send-report' }
];

export const logTimesheetOnRedmineColorsChart = [
    { color: '#4272DF', name: 'standard-time' },
    { color: '#D74642', name: 'less-standard-time' },
    { color: '#DDCD38', name: 'more-standard-time' }
];

export const logTimesheetOnRedmineChartOptionBase: Props = {
    colors: logTimesheetOnRedmineColorsChart.map((x) => x.color),
    responsive: [
        {
            breakpoint: 1920,
            options: {
                chart: { width: 335 }
            }
        }
    ],
    legend: { show: false }
};

export const logTimesheetOnRedmineChartOptions = (labels: string[]) => {
    const options: Props = {
        ...logTimesheetOnRedmineChartOptionBase,
        labels
    };
    return options;
};

// Update effort plan dialog
export const updateEffortPlanFormSchema = yup.object().shape({
    effortPlanDepts: yup.array().of(
        yup.object().shape({
            sentReport: yup.number().typeError(VALIDATE_MESSAGES.REQUIRED).min(0, VALIDATE_MESSAGES.INVALID_NUMBER),
            notSentReport: yup.number().typeError(VALIDATE_MESSAGES.REQUIRED).min(0, VALIDATE_MESSAGES.INVALID_NUMBER),
            show: yup.boolean()
        })
    )
});
