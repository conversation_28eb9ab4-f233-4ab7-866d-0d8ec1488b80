import { useCallback, useEffect, useState } from 'react';
import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';

import { IMonthlyEffortProjectReportConfig, monthEffortProjectReportDefault, monthlyEffortProjectReportDefault } from './Config';
import {
    IMonthlyEffortAddProjectReport,
    IMonthlyEffortProjectReport,
    IMonthlyEffortProjectReportList,
    IOption,
    IPaginationResponse,
    IResponseList
} from 'types';
import MonthlyEffortProjectReportThead from 'containers/monthly-effort/MonthlyEffortProjectReportThead';
import MonthlyEffortProjectReportTBody from 'containers/monthly-effort/MonthlyEffortProjectReportTBody';
import AddorEditProjectReport from 'containers/administration/AddorEditProjectReport';
import ProjectReportSearch from 'containers/monthly-effort/ProjectReportSearch';
import { getSearchParam, isEmpty, transformObject } from 'utils/common';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { FilterCollapse } from 'containers/search';
import { PERMISSIONS } from 'constants/Permission';
import MainCard from 'components/cards/MainCard';
import sendRequest from 'services/ApiService';
import { getMonthsOfYear } from 'utils/date';
import { TableToolbar } from 'containers';
import Api from 'constants/Api';

const MonthlyEffortProjectReport = () => {
    const [reportToEdit, setReportsToEdit] = useState<IMonthlyEffortAddProjectReport>();
    const [searchParams, setSearchParams] = useSearchParams();

    const keyParams = [
        SEARCH_PARAM_KEY.year,
        SEARCH_PARAM_KEY.month,
        SEARCH_PARAM_KEY.projectLabel,
        SEARCH_PARAM_KEY.projectValue,
        SEARCH_PARAM_KEY.projectTypeCode,
        SEARCH_PARAM_KEY.projectManagerLabel,
        SEARCH_PARAM_KEY.projectManagerValue,
        SEARCH_PARAM_KEY.page,
        SEARCH_PARAM_KEY.size
    ];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    const defaultConditions = {
        year: params.year ? params.year : new Date().getFullYear(),
        month: params.month ? params.month : (new Date().getMonth() + 1).toString(),
        size: params.size ? params.size : 10,
        page: params.page ? params.page : 1,
        project:
            params.projectValue && params.projectValue !== 'null'
                ? {
                      value: params.projectValue,
                      label: params.projectLabel,
                      typeCode: params.projectTypeCode
                  }
                : null,
        projectId: params.projectValue !== 'null' ? params.projectValue : null,
        projectManager:
            params.projectManagerValue && params.projectManagerValue !== 'null'
                ? {
                      value: params.projectManagerValue,
                      label: params.projectManagerLabel
                  }
                : null,
        usernamePM: params.projectManagerValue !== 'null' ? params.projectManagerValue : null
    };

    const [project, setProject] = useState<IMonthlyEffortProjectReport[]>(monthEffortProjectReportDefault);
    const [months, setMonths] = useState<IOption[]>(getMonthsOfYear(monthlyEffortProjectReportDefault.year));
    const [loading, setLoading] = useState<boolean>(false);
    const [open, setOpen] = useState<boolean>(false);
    const [pagination, setPagination] = useState<IPaginationResponse>();
    const [conditions, setConditions] = useState<IMonthlyEffortProjectReportConfig>(defaultConditions);
    const [formReset] = useState<IMonthlyEffortProjectReportConfig>(defaultConditions);
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage + 1 });
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, size: parseInt(event.target.value, 10) });
    };

    // Function;
    const getDataTable = async () => {
        setLoading(true);
        const response: IResponseList<IMonthlyEffortProjectReportList> = await sendRequest(Api.monthly_efford.getProjectReports, {
            ...conditions
        });
        if (response?.status) {
            const { result } = response;
            setProject(isEmpty(result.content) ? [] : result.content);
            setPagination(result.pagination);
            setLoading(false);
            return;
        } else {
            setDataEmpty();
        }
    };

    const setDataEmpty = () => {
        setProject([]);
        setLoading(false);
    };

    const getMonthInYears = useCallback(async (y: number) => {
        const monthInYears = await getMonthsOfYear(y);
        return monthInYears;
    }, []);

    const handleChangeYear = (e: any) => {
        const { value: year } = e.target;

        getMonthInYears(year).then((items: IOption[]) => {
            setMonths(items);
        });
    };

    // Handle submit
    const handleSearch = async (value: IMonthlyEffortProjectReportConfig) => {
        transformObject(value);
        let dbSearch = {
            ...value,
            projectId: typeof value.project == 'object' && value.project ? value.project.value : null,
            usernamePM: typeof value.projectManager == 'object' && value.projectManager ? value.projectManager.value : null
        };
        await setConditions(dbSearch);
        let setParam = {
            ...value,
            projectLabel: typeof value.project == 'object' && value.project ? value.project.label : null,
            projectValue: typeof value.project == 'object' && value.project ? value.project.value : null,
            projectTypeCode: typeof value.project == 'object' && value.project ? value.project.typeCode : null,
            projectManagerLabel: typeof value.projectManager == 'object' && value.projectManager ? value.projectManager.label : null,
            projectManagerValue: typeof value.projectManager == 'object' && value.projectManager ? value.projectManager.value : null
        };

        setSearchParams(setParam as unknown as URLSearchParamsInit);
    };
    const handleOpenDialog = () => {
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
        setReportsToEdit(undefined);
    };

    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    const handleSetFormToEdit = async (singleReport: IMonthlyEffortProjectReport) => {
        if (singleReport) {
            const response = await sendRequest(Api.monthly_efford.getReportById(singleReport.id));
            if (response.status) {
                handleOpenDialog();
                const { result } = response;
                const { content } = result;
                setReportsToEdit(content);
            }
        }
    };

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <ProjectReportSearch
                    formReset={formReset}
                    months={months}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            {/* Table */}
            <MainCard>
                {checkAllowedPermission(PERMISSIONS.report.generalReport.projectReport.add) && (
                    <TableToolbar handleOpen={handleOpenDialog} />
                )}
                <Table
                    heads={<MonthlyEffortProjectReportThead projectLength={project.length} currentYear={conditions.year} />}
                    isLoading={loading}
                    data={project}
                >
                    <MonthlyEffortProjectReportTBody projects={project} resetDataTable={getDataTable} sendataReport={handleSetFormToEdit} />
                </Table>
            </MainCard>
            {!loading && project?.length > 0 && (
                <TableFooter
                    pagination={{
                        total: pagination?.totalElement as number,
                        page: (pagination?.pageNumber as number) ? (pagination?.pageNumber as number) - 1 : conditions.page,
                        size: (pagination?.pageSize as number) ? (pagination?.pageSize as number) : conditions.size
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
            {open && (
                <AddorEditProjectReport
                    open={open}
                    projectReport={reportToEdit}
                    handleClose={handleCloseDialog}
                    updateTable={getDataTable}
                />
            )}
        </>
    );
};

export default MonthlyEffortProjectReport;
