/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useState } from 'react';

// project imports
import { IMonthlyEffortOrmReportConfig, IOrmReport, OrmReportList, monthlyEffortOrmReportConfig, ormReportDefault } from './Config';
import MonthlyEffortORMReportSearch from 'containers/monthly-effort/MonthlyEffortORMReportSearch';
import MonthlyEffortORMReportTBody from 'containers/monthly-effort/MonthlyEffortORMReportTBody';
import MonthlyEffortORMReportThead from 'containers/monthly-effort/MonthlyEffortORMReportThead';
import { transformObject, getSearchParam, isEmpty, setLocalStorageSearchTime } from 'utils/common';
import UploadORMReport from 'containers/administration/UploadORMReport';
import { IOption, IPaginationResponse, IResponseList } from 'types';
import { Table, TableFooter } from 'components/extended/Table';
import { checkAllowedPermission } from 'utils/authorization';
import { SEARCH_PARAM_KEY } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { FilterCollapse } from 'containers/search';
import MainCard from 'components/cards/MainCard';
import sendRequest from 'services/ApiService';
import { getMonthsOfYear } from 'utils/date';
import Api from 'constants/Api';

// third party
import { useSearchParams } from 'react-router-dom';
import { Box } from '@mui/system';

// ==============================|| ORM Effort - report ||============================== //
/**
 *  URL Params
 *  year
 *  month
 *  department
 *  upload user
 * last update
 *
 */

const ORMReport = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.month, SEARCH_PARAM_KEY.ormReportName];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // delete unnecessary key value
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { projectName, ...cloneParams }: any = params;

    // Hooks, State, Variable
    const defaultConditions = {
        ...monthlyEffortOrmReportConfig,
        ...cloneParams
    };
    const [loading, setLoading] = useState<boolean>(false);
    const [ormReport, setOrmReport] = useState<IOrmReport[]>([ormReportDefault]);
    const [conditions, setConditions] = useState<IMonthlyEffortOrmReportConfig>(defaultConditions);
    const [formReset, setFormReset] = useState<IMonthlyEffortOrmReportConfig>(defaultConditions);
    const [year, setYear] = useState<number>(defaultConditions.year);
    const [months, setMonths] = useState<IOption[]>([]);
    const [isChangeYear, setIsChangeYear] = useState<boolean>(false);
    const [pagination, setPagination] = useState<IPaginationResponse>();
    const { generalReport } = PERMISSIONS.report;

    // Function
    const getDataTable = async () => {
        setLoading(true);
        const response: IResponseList<OrmReportList> = await sendRequest(Api.monthly_efford.getOMReport, {
            ...conditions
        });

        if (response?.status) {
            const { result } = response;
            setOrmReport(isEmpty(result.content.length) ? [ormReportDefault] : result.content);
            setPagination(result.pagination);
            setLoading(false);
            return;
        } else {
            setDataEmpty();
        }
    };

    const setDataEmpty = () => {
        setOrmReport([]);
        setLoading(false);
    };

    const getMonthInYears = useCallback(async (y: number) => {
        const monthInYears = await getMonthsOfYear(y);
        return monthInYears;
    }, []);

    // Event
    const handleChangeYear = (e: any) => {
        const { value } = e.target;
        setYear(value);
        setIsChangeYear(true);
    };
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, pageNumber: newPage + 1 });
        setSearchParams({ ...params, pageNumber: newPage + 1 } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, pageNumber: conditions.pageNumber, pageSize: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, pageNumber: conditions.pageNumber, pageSize: parseInt(event.target.value, 10) } as any);
    };
    // Handle submit
    const handleSearch = (value: any) => {
        transformObject(value);
        const searchParams: any = {
            month: value.month,
            year: value.year
        };
        const conditions: any = {
            month: value.month,
            year: value.year
        };
        if (value.department) {
            searchParams.department = value.department;
            conditions.department = value.department;
        }
        if (value.reportName) {
            searchParams.reportName = value.reportName;
            conditions.reportName = value.reportName;
        }
        setSearchParams({ ...searchParams });
        setConditions(conditions);
        // lưu thời gian vào localStorage
        setLocalStorageSearchTime({ month: value.month, year: value.year });
    };

    // Effect
    useEffect(() => {
        getDataTable();
    }, [conditions]);

    useEffect(() => {
        getMonthInYears(year).then((items: IOption[]) => {
            setMonths(items);
            if (items.length > 0 && isChangeYear) {
                setFormReset({ ...formReset, year, month: items[0].value });
            }
        });
    }, [year]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <MonthlyEffortORMReportSearch
                    formReset={formReset}
                    months={months}
                    handleChangeYear={handleChangeYear}
                    handleSearch={handleSearch}
                />
            </FilterCollapse>

            {/* Table */}
            <MainCard>
                {checkAllowedPermission(generalReport.uploadORMReport) && (
                    <Box sx={{ mb: 2 }}>
                        <UploadORMReport months={months} handleChangeYear={handleChangeYear} updateAfterUpload={() => getDataTable()} />
                    </Box>
                )}

                <Table
                    heads={<MonthlyEffortORMReportThead projectLength={ormReport.length} currentYear={conditions.year} />}
                    isLoading={loading}
                    data={ormReport}
                >
                    <MonthlyEffortORMReportTBody ormReports={ormReport} updateTable={() => getDataTable()} />
                </Table>
            </MainCard>

            {!loading && (
                <TableFooter
                    pagination={{
                        total: pagination?.totalElement || 0,
                        page: pagination?.pageNumber ? pagination?.pageNumber - 1 : conditions?.pageNumber - 1,
                        size: pagination?.pageSize ? pagination?.pageSize : conditions.pageSize
                    }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}
        </>
    );
};

export default ORMReport;
