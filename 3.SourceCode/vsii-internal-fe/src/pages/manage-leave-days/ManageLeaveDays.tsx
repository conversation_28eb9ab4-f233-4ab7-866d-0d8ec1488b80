import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

// project imports
import { useAppDispatch } from 'app/hooks';
import MainCard from 'components/cards/MainCard';
import { Table, TableFooter } from 'components/extended/Table';
import Api from 'constants/Api';
import { SEARCH_PARAM_KEY, paginationParamDefault, paginationResponseDefault } from 'constants/Common';
import LeaveDaySearch from 'containers/administration/LeaveDaySearch';
import { FilterCollapse } from 'containers/search';
import AddOrEditLeaveDay from 'containers/working-calendar/AddOrEditLeaveDay';
import LeaveDaysTBody from 'containers/working-calendar/LeaveDaysTBody';
import LeaveDaysThead from 'containers/working-calendar/LeaveDaysThead';
import { ILeaveDaySearchConfig, leaveDaySearchConfig } from 'pages/administration/Config';
import sendRequest from 'services/ApiService';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { IPaginationResponse } from 'types';
import { ILeaveDays } from 'types/working-calendar';
import { getSearchParam, transformObject } from 'utils/common';
import { saveOrUpdateLeaveDayConfig } from './config';

const ManageLeaveDays = () => {
    // URL Params
    const [searchParams, setSearchParams] = useSearchParams();
    const keyParams = [SEARCH_PARAM_KEY.year, SEARCH_PARAM_KEY.month, SEARCH_PARAM_KEY.idHexString, SEARCH_PARAM_KEY.fullname];
    const params: { [key: string]: any } = getSearchParam(keyParams, searchParams);
    transformObject(params);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fullname, idHexString, ...cloneParams }: any = params;
    // Hooks, State, Variable
    const defaultConditions = {
        ...leaveDaySearchConfig,
        ...cloneParams,
        idHexString: params.idHexString ? { value: params.idHexString, label: params.fullname } : null
    };
    // Hooks, State, Variable
    const dispatch = useAppDispatch();
    const [open, setOpen] = useState<boolean>(false);
    const [formReset] = useState<ILeaveDaySearchConfig>(defaultConditions);
    const [leaveDayPagination, setLeaveDayPagination] = useState<IPaginationResponse>({
        ...paginationResponseDefault,
        pageNumber: params.page ? params.page : paginationResponseDefault.pageNumber,
        pageSize: params.size ? params.size : paginationResponseDefault.pageSize
    });
    const [loading, setLoading] = useState<boolean>(false);
    const [leaveDays, setLeaveDays] = useState<ILeaveDays[]>([]);
    const [leaveDay, setLeaveDay] = useState<any>(saveOrUpdateLeaveDayConfig);
    const [conditions, setConditions] = useState<ILeaveDaySearchConfig>(defaultConditions);
    const [addOrEditLoading, setAddOrEditLoading] = useState<boolean>(false);
    // Funtion get API
    const getDataTable = async () => {
        setLoading(true);
        const idHexString = conditions.idHexString ? conditions.idHexString.value : '';
        const response = await sendRequest(Api.leave_day.getAll, {
            ...conditions,
            idHexString: idHexString,
            page: conditions.page + 1
        });
        if (response) {
            const { status, result } = response;
            if (status) {
                const { content, pagination } = result;
                setLeaveDayPagination({ ...leaveDayPagination, totalElement: pagination?.totalElement });
                setLeaveDays(content);
                setLoading(false);
            } else {
                setDataEmpty();
            }
            return;
        } else {
            setDataEmpty();
        }
    };

    const postEditLeaveDays = async (valueLeaveDays: ILeaveDays) => {
        setAddOrEditLoading(true);
        const response = await sendRequest(Api.leave_day.postSaveOrUpdate, {
            userIdHexString: valueLeaveDays.idHexString,
            totalLeaveDayThisYear: +valueLeaveDays.leaveDaysInformation.totalLeaveDayThisYear,
            totalLeaveDaysUsed: +valueLeaveDays.leaveDaysInformation.totalLeaveDaysUsed,
            totalLeaveDaysLastYear: +valueLeaveDays.leaveDaysInformation.totalLeaveDaysLastYear
        });
        if (response) {
            const status = response.status;
            dispatch(
                openSnackbar({
                    open: true,
                    message: status ? 'edit-leave-days-success' : response.result.messages[0].message,
                    variant: 'alert',
                    alert: { color: status ? 'success' : 'error' }
                })
            );
            setAddOrEditLoading(false);
            if (status) {
                getDataTable();
                setOpen(false);
            }
        } else {
            setAddOrEditLoading(false);
        }
    };

    const setDataEmpty = () => {
        setLeaveDays([]);
        setLoading(false);
    };

    // Event
    const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
        setConditions({ ...conditions, page: newPage });
        setSearchParams({ ...params, page: newPage } as any);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setConditions({ ...conditions, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) });
        setSearchParams({ ...params, page: paginationParamDefault.page, size: parseInt(event.target.value, 10) } as any);
    };

    const handleSearch = (values: any) => {
        transformObject(values);
        const { idHexString, ...cloneValues } = values;
        setSearchParams(idHexString ? { ...cloneValues, idHexString: idHexString.value, fullname: idHexString.label } : cloneValues);
        setConditions(values);
    };

    const handleOpenDialog = (item: any) => {
        setLeaveDay(
            item
                ? {
                      ...item
                  }
                : saveOrUpdateLeaveDayConfig
        );
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const handleEditLeaveDay = (leaveDayEdit: ILeaveDays) => {
        postEditLeaveDays(leaveDayEdit);
    };

    // Effect
    useEffect(() => {
        getDataTable();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [conditions]);

    return (
        <>
            {/* Search form  */}
            <FilterCollapse>
                <LeaveDaySearch formReset={formReset} handleSearch={handleSearch} />
            </FilterCollapse>

            {/* Table */}
            <MainCard>
                <Table heads={<LeaveDaysThead />} isLoading={loading} data={leaveDays}>
                    <LeaveDaysTBody
                        pageNumber={conditions.page}
                        pageSize={conditions.size}
                        leaveDays={leaveDays}
                        handleOpen={handleOpenDialog}
                    />
                </Table>
            </MainCard>

            {/* Pagination  */}
            {!loading && (
                <TableFooter
                    pagination={{ total: leaveDayPagination?.totalElement, page: conditions.page, size: conditions.size }}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                />
            )}

            {/* Add or edit leave day */}
            {open && (
                <AddOrEditLeaveDay
                    open={open}
                    loading={addOrEditLoading}
                    handleClose={handleCloseDialog}
                    leaveDay={leaveDay}
                    setLeaveDay={setLeaveDay}
                    editLeaveDay={handleEditLeaveDay}
                />
            )}
        </>
    );
};

export default ManageLeaveDays;
