import { VALIDATE_MESSAGES } from 'constants/Message';
import { IOption } from 'types';
import { ILeaveDaysInfo } from 'types/working-calendar';
import * as yup from 'yup';

export interface ILeaveDaysSearchConfig {
    idHexString: IOption | null;
}

export const leaveDaysSearchConfig: ILeaveDaysSearchConfig = {
    idHexString: null
};

export interface ILeaveDayConfig {
    idHexString?: string;
    firstName: string;
    lastName: string;
    memberCode: string;
    titleCode: string;
    departmentId: string;
    leaveDaysInformation: ILeaveDaysInfo;
}

export const saveOrUpdateLeaveDayConfig: ILeaveDayConfig = {
    idHexString: '',
    firstName: '',
    lastName: '',
    memberCode: '',
    titleCode: '',
    departmentId: '',
    leaveDaysInformation: {
        totalLeaveDayThisYear: 0,
        totalLeaveDaysUsed: 0,
        totalLeaveDaysLastYear: 0,
        totalLeaveDaysRemain: 0,
        totalLeaveDays: 0,
        memberCode: ''
    }
};

export const saveOrUpdateLeaveDaySchema = yup.object().shape({
    idHexString: yup.string().nullable(),
    firstName: yup.string().nullable(),
    lastName: yup.string().nullable(),
    memberCode: yup.string().nullable(),
    titleCode: yup.string().nullable(),
    departmentId: yup.string().nullable(),
    leaveDaysInformation: yup.object().shape({
        totalLeaveDayThisYear: yup.string().nullable(),
        totalLeaveDaysLastYear: yup.string().nullable(),
        totalLeaveDaysUsed: yup.string().test('totalLeaveDaysUsed', VALIDATE_MESSAGES.NUMBER_LEAVE_DAYS_USED, function (value) {
            const totalLeaveDayThisYear = parseFloat(this.parent.totalLeaveDayThisYear);
            const totalLeaveDaysLastYear = parseFloat(this.parent.totalLeaveDaysLastYear);
            if (
                typeof value === 'string' && // Ensure value is a positive integer
                !isNaN(totalLeaveDayThisYear) &&
                !isNaN(totalLeaveDaysLastYear)
            ) {
                const used = parseFloat(value);

                return used <= totalLeaveDayThisYear + totalLeaveDaysLastYear;
            }
            return false;
        })
    })
});
