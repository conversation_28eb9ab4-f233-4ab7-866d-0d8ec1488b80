// redux
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from 'app/store';

// project imports
import { IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import { IGetAllLanguage, ILanguageConfig } from 'types/languageConfig';

// interface
interface IRankInitialState {
    laguageConfigList: ILanguageConfig[];
    loading: {
        [key: string]: boolean;
    };
}

// initialState
const initialState: IRankInitialState = {
    laguageConfigList: [],
    loading: {}
};

// Call API
export const getLanguage = createAsyncThunk<IResponseList<IGetAllLanguage>>(Api.flexible_textConfig.getLanguage.url, async () => {
    const response = await sendRequest(Api.flexible_textConfig.getLanguage);
    return response;
});

// Slice & Actions
const languageConfigSlice = createSlice({
    name: 'language-config',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        // getAll
        builder.addCase(getLanguage.pending, (state) => {
            state.laguageConfigList = [];
            state.loading[getLanguage.typePrefix] = true;
        });
        builder.addCase(getLanguage.fulfilled, (state, action) => {
            if (action.payload?.status) {
                const { content } = action.payload.result;
                state.laguageConfigList = content;
            }
            state.loading[getLanguage.typePrefix] = false;
        });
        builder.addCase(getLanguage.rejected, (state) => {
            state.loading[getLanguage.typePrefix] = false;
        });
    }
});

// Reducer & export
export default languageConfigSlice.reducer;

// export const {} = rankSlice.actions;

// Selector & export
export const languageConfigSelector = (state: RootState) => state.languageConfig;
