import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import omit from 'lodash/omit';

import { ICreateTitleRequest, IEditTitleRequest, IGetTitleResponse } from 'types/titleConfig';
import { ITitleFilterConfig } from 'pages/administration/Config';
import { IResponseList, Response } from 'types';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';

export const getSearchTitle = createAsyncThunk<IResponseList<IGetTitleResponse>, ITitleFilterConfig>(
    Api.title.search.url,
    async (params) => {
        const response = await sendRequest(Api.title.search, params);

        return response;
    }
);

export const createTitle = createAsyncThunk<Response<{ content: string }>, ICreateTitleRequest>(Api.title.create.url, async (params) => {
    const response = await sendRequest(Api.title.create, params);

    return response;
});

export const editTitle = createAsyncThunk<Response<{ content: string }>, IEditTitleRequest>('Api.title.edit.url', async (params) => {
    const response = await sendRequest(Api.title.edit(params.id), omit(params, ['id']));

    return response;
});

export const deleteTitle = createAsyncThunk<Response<{ content: string }>, string>('Api.title.delete.url', async (params) => {
    const response = await sendRequest(Api.title.delete(params));

    return response;
});

interface ITitleConfigState {
    titles?: IResponseList<IGetTitleResponse>['result'];
    loading: { [key: string]: boolean };
}

const initialState: ITitleConfigState = {
    loading: {}
};

const titleSlice = createSlice({
    name: 'title',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(getSearchTitle.pending, (state) => {
            state.loading[getSearchTitle.typePrefix] = true;
        });
        builder.addCase(getSearchTitle.fulfilled, (state, action) => {
            if (action.payload.status && Array.isArray(action.payload.result.content)) {
                state.titles = action.payload.result;
            }
            state.loading[getSearchTitle.typePrefix] = false;
        });
        builder.addCase(getSearchTitle.rejected, (state) => {
            state.loading[getSearchTitle.typePrefix] = false;
        });
        builder.addCase(createTitle.pending, (state) => {
            state.loading[createTitle.typePrefix] = true;
        });
        builder.addCase(createTitle.fulfilled, (state) => {
            state.loading[createTitle.typePrefix] = false;
        });
        builder.addCase(createTitle.rejected, (state) => {
            state.loading[createTitle.typePrefix] = false;
        });
        builder.addCase(editTitle.pending, (state) => {
            state.loading[editTitle.typePrefix] = true;
        });
        builder.addCase(editTitle.fulfilled, (state) => {
            state.loading[editTitle.typePrefix] = false;
        });
        builder.addCase(editTitle.rejected, (state) => {
            state.loading[editTitle.typePrefix] = false;
        });
        builder.addCase(deleteTitle.pending, (state) => {
            state.loading[deleteTitle.typePrefix] = true;
        });
        builder.addCase(deleteTitle.fulfilled, (state) => {
            state.loading[deleteTitle.typePrefix] = false;
        });
        builder.addCase(deleteTitle.rejected, (state) => {
            state.loading[deleteTitle.typePrefix] = false;
        });
    }
});

export default titleSlice.reducer;

export const titleConfigSelector = (state: RootState) => state.title;
