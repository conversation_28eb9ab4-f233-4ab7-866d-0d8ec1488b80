import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { IOption, IProjectList, IResponseList } from 'types';
import { convertWeekFromToDate } from 'utils/date';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';
import {
    GetWeeklyEffortRequest,
    IWeeklyEffortMemberResponse,
    IWeeklyEffortProjectDetailResponse,
    IWeeklyEffortProjectsResponse
} from 'types/weekly-effort';

// interface
interface IWeeklyEffortState {
    loading: { [key: string]: boolean };
    weeklyEffortMember?: IResponseList<IWeeklyEffortMemberResponse>['result'];
    weeklyEffortProjects?: IResponseList<IWeeklyEffortProjectsResponse>['result'];
    weeklyEffortProjectDetail?: IResponseList<IWeeklyEffortProjectDetailResponse>['result'];
    projectOptions: IOption[];
}

// initial state
const initialState: IWeeklyEffortState = {
    loading: {},
    projectOptions: []
};

export const getWeeklyEffortMember = createAsyncThunk<IResponseList<IWeeklyEffortMemberResponse>, GetWeeklyEffortRequest>(
    Api.weekly_efford.getMember.url,
    async (params) => {
        const response = await sendRequest(Api.weekly_efford.getMember, params);

        return response;
    }
);

export const getWeeklyEffortProjects = createAsyncThunk<IResponseList<IWeeklyEffortProjectsResponse>, GetWeeklyEffortRequest>(
    Api.weekly_efford.getProject.url,
    async (params) => {
        const response = await sendRequest(Api.weekly_efford.getProject, params);

        return response;
    }
);

export const getWeeklyEffortProjectDetail = createAsyncThunk<IResponseList<IWeeklyEffortProjectDetailResponse>, GetWeeklyEffortRequest>(
    Api.weekly_efford.getProjectDetail.url,
    async (params) => {
        const response = await sendRequest(Api.weekly_efford.getProjectDetail, params);

        return response;
    }
);

export const getWeeklyEffortProjectOption = createAsyncThunk<IResponseList<IProjectList>, { week: string | number; color?: boolean }>(
    'getWeeklyEffortProjectOption',
    async (params) => {
        const response = await sendRequest(Api.weekly_efford.getWeeklyEffortProject, {
            ...convertWeekFromToDate(params.week),
            projectAuthorization: true,
            size: 1000
        });

        return response;
    }
);

const weeklyEffortSlice = createSlice({
    name: 'weekly-effort',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        // getWeeklyEffortMember
        builder.addCase(getWeeklyEffortMember.pending, (state) => {
            state.weeklyEffortMember = undefined;
            state.loading[getWeeklyEffortMember.typePrefix] = true;
        });
        builder.addCase(getWeeklyEffortMember.fulfilled, (state, action) => {
            state.loading[getWeeklyEffortMember.typePrefix] = false;
            if (action.payload.status) {
                state.weeklyEffortMember = action.payload.result;
            }
        });
        builder.addCase(getWeeklyEffortMember.rejected, (state) => {
            state.loading[getWeeklyEffortMember.typePrefix] = false;
        });

        // getWeeklyEffortProjects
        builder.addCase(getWeeklyEffortProjects.pending, (state) => {
            state.weeklyEffortProjects = undefined;
            state.loading[getWeeklyEffortProjects.typePrefix] = true;
        });
        builder.addCase(getWeeklyEffortProjects.fulfilled, (state, action) => {
            state.loading[getWeeklyEffortProjects.typePrefix] = false;
            if (action.payload.status) {
                state.weeklyEffortProjects = action.payload.result;
            }
        });
        builder.addCase(getWeeklyEffortProjects.rejected, (state) => {
            state.loading[getWeeklyEffortProjects.typePrefix] = false;
        });

        // getWeeklyEffortProjectDetail
        builder.addCase(getWeeklyEffortProjectDetail.pending, (state) => {
            state.weeklyEffortProjectDetail = undefined;
            state.loading[getWeeklyEffortProjectDetail.typePrefix] = true;
        });
        builder.addCase(getWeeklyEffortProjectDetail.fulfilled, (state, action) => {
            state.loading[getWeeklyEffortProjectDetail.typePrefix] = false;
            if (action.payload.status) {
                state.weeklyEffortProjectDetail = action.payload.result;
            }
        });
        builder.addCase(getWeeklyEffortProjectDetail.rejected, (state) => {
            state.loading[getWeeklyEffortProjectDetail.typePrefix] = false;
        });

        // getWeeklyEffortProjectOption
        builder.addCase(getWeeklyEffortProjectOption.pending, (state) => {
            state.projectOptions = [];
            state.loading[getWeeklyEffortProjectOption.typePrefix] = true;
        });
        builder.addCase(getWeeklyEffortProjectOption.fulfilled, (state, action) => {
            state.loading[getWeeklyEffortProjectOption.typePrefix] = false;
            if (action.payload?.status) {
                state.projectOptions = action.payload.result.content.map((prj) => ({
                    value: prj.projectId,
                    label: prj.projectName,
                    typeCode: prj.typeCode,
                    color: action.meta.arg.color ? prj.color : undefined
                }));
            }
        });
        builder.addCase(getWeeklyEffortProjectOption.rejected, (state) => {
            state.loading[getWeeklyEffortProjectOption.typePrefix] = false;
        });
    }
});

// export const {} = weeklyEffortSlice.actions;

// selectors
export const weeklyEffortSelector = (state: RootState) => state.weeklyEffort;

// reducers
export default weeklyEffortSlice.reducer;
