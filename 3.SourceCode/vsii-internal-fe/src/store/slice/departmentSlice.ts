import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import omit from 'lodash/omit';

import { ICreateDepartmentRequest, IEditDepartmentRequest, IGetDepartmentResponse } from 'types/department';
import { IDepartmentFilterConfig } from 'pages/administration/Config';
import { IResponseList, Response } from 'types';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';

export const getSearchDepartment = createAsyncThunk<IResponseList<IGetDepartmentResponse>, IDepartmentFilterConfig>(
    Api.department.search.url,
    async (params) => {
        const response = await sendRequest(Api.department.search, params);

        return response;
    }
);

export const createDepartment = createAsyncThunk<Response<{ content: string }>, ICreateDepartmentRequest>(
    Api.department.create.url,
    async (params) => {
        const response = await sendRequest(Api.department.create, params);

        return response;
    }
);

export const editDepartment = createAsyncThunk<Response<{ content: string }>, IEditDepartmentRequest>(
    'Api.department.edit.url',
    async (params) => {
        const response = await sendRequest(Api.department.edit(params.id), omit(params, ['id']));

        return response;
    }
);

export const deleteDepartment = createAsyncThunk<Response<{ content: string }>, string>('Api.department.delete.url', async (params) => {
    const response = await sendRequest(Api.department.delete(params));

    return response;
});

interface IDepartmentState {
    departments?: IResponseList<IGetDepartmentResponse>['result'];
    loading: { [key: string]: boolean };
}

const initialState: IDepartmentState = {
    loading: {}
};

const departmnetSlice = createSlice({
    name: 'department',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(getSearchDepartment.pending, (state) => {
            state.loading[getSearchDepartment.typePrefix] = true;
        });
        builder.addCase(getSearchDepartment.fulfilled, (state, action) => {
            if (action.payload.status && Array.isArray(action.payload.result.content)) {
                state.departments = action.payload.result;
            }
            state.loading[getSearchDepartment.typePrefix] = false;
        });
        builder.addCase(getSearchDepartment.rejected, (state) => {
            state.loading[getSearchDepartment.typePrefix] = false;
        });
        builder.addCase(createDepartment.pending, (state) => {
            state.loading[createDepartment.typePrefix] = true;
        });
        builder.addCase(createDepartment.fulfilled, (state) => {
            state.loading[createDepartment.typePrefix] = false;
        });
        builder.addCase(createDepartment.rejected, (state) => {
            state.loading[createDepartment.typePrefix] = false;
        });
        builder.addCase(editDepartment.pending, (state) => {
            state.loading[editDepartment.typePrefix] = true;
        });
        builder.addCase(editDepartment.fulfilled, (state) => {
            state.loading[editDepartment.typePrefix] = false;
        });
        builder.addCase(editDepartment.rejected, (state) => {
            state.loading[editDepartment.typePrefix] = false;
        });
        builder.addCase(deleteDepartment.pending, (state) => {
            state.loading[deleteDepartment.typePrefix] = true;
        });
        builder.addCase(deleteDepartment.fulfilled, (state) => {
            state.loading[deleteDepartment.typePrefix] = false;
        });
        builder.addCase(deleteDepartment.rejected, (state) => {
            state.loading[deleteDepartment.typePrefix] = false;
        });
    }
});

export default departmnetSlice.reducer;

export const departmentSelector = (state: RootState) => state.department;
