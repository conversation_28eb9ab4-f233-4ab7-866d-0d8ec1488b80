// redux
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { RootState } from 'app/store';

// project imports
import Api from 'constants/Api';
import { paginationResponseDefault } from 'constants/Common';
import sendRequest from 'services/ApiService';
import manualSyncAPI, { IResponseManualSync } from 'services/others/manualSyncAPI';
import {
    IPaginationResponse,
    IProductReportDetailResponse,
    IProductReport,
    IResponseList,
    IProductReportResponse,
    IProductReportRequest,
    IOption,
    IProject,
    IProjectList
} from 'types';

// interface
interface IProjectInitialState {
    productReport: IProductReport[];
    projectDetail?: IProductReportDetailResponse['content'];
    sprints: IProductReportDetailResponse['content']['sprints'];
    pagination: IPaginationResponse;
    sprintSelectDefaut?: string;
    productReportOption: IOption[];
    loading: {
        [key: string]: boolean;
    };
}

// initialState
const initialState: IProjectInitialState = {
    productReport: [],
    sprints: [],
    productReportOption: [],
    pagination: paginationResponseDefault,
    loading: {}
};

// Call API
export const getAllProductReport = createAsyncThunk<IResponseList<IProductReportResponse>, IProductReportRequest>(
    Api.product.getReport.url,
    async (parameters) => {
        const response = await sendRequest(Api.product.getReport, parameters);

        return response;
    }
);
export const getReportByProjectId = createAsyncThunk<IResponseList<IProductReportDetailResponse>, { projectId: number }>(
    'Api.product.getReportByProjectId.url',
    async (parameters) => {
        const response = await sendRequest(Api.product.getReportByProjectId(parameters.projectId));

        return response;
    }
);
export const syncDataProjectReport = createAsyncThunk<IResponseManualSync>('manualSyncAPI.getSyncProjectReport', async () => {
    const response = await manualSyncAPI.getSyncProjectReport();

    return response;
});

export const getProjectReportOption = createAsyncThunk<IOption[]>('Api.product.getProductReportOptions', async () => {
    const response: IResponseList<IProjectList> = await sendRequest(Api.product.getProductReportOptions);
    const { status, result } = response;
    if (!response || !status) return [];

    const arrOption: IOption[] = result.content.map((pro: IProject) => ({
        value: pro.projectId,
        label: pro.projectName,
        typeCode: pro.typeCode
    }));

    return arrOption;
});

// Slice & Actions
const productReportSlice = createSlice({
    name: 'productReport',
    initialState: initialState,
    reducers: {
        filterSprint: (state, action) => {
            state.sprints =
                state.projectDetail?.sprints.filter((sprint) =>
                    action.payload === 'all' ? true : sprint.sprintId.toString() === action.payload
                ) || [];
        },
        setSprintSelectDefault: (state, action) => {
            state.sprintSelectDefaut = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder.addCase(getAllProductReport.pending, (state) => {
            state.loading[getAllProductReport.typePrefix] = true;
        });
        builder.addCase(getAllProductReport.fulfilled, (state, action) => {
            state.productReport = action.payload?.result.content;
            state.pagination = action.payload?.result.pagination || paginationResponseDefault;
            state.loading[getAllProductReport.typePrefix] = false;
        });
        builder.addCase(getAllProductReport.rejected, (state) => {
            state.loading[getAllProductReport.typePrefix] = false;
        });
        builder.addCase(getReportByProjectId.pending, (state) => {
            state.loading[getReportByProjectId.typePrefix] = true;
        });
        builder.addCase(getReportByProjectId.fulfilled, (state, action) => {
            state.projectDetail = action.payload?.result.content;
            state.sprints =
                action.payload?.result.content?.sprints?.filter((sprint) => sprint.sprintId.toString() === state.sprintSelectDefaut) ||
                action.payload?.result.content?.sprints?.[0] ||
                [];
            state.loading[getReportByProjectId.typePrefix] = false;
        });
        builder.addCase(getReportByProjectId.rejected, (state) => {
            state.loading[getReportByProjectId.typePrefix] = false;
        });
        builder.addCase(syncDataProjectReport.pending, (state) => {
            state.loading[syncDataProjectReport.typePrefix] = true;
        });
        builder.addCase(syncDataProjectReport.fulfilled, (state) => {
            state.loading[syncDataProjectReport.typePrefix] = false;
        });
        builder.addCase(syncDataProjectReport.rejected, (state) => {
            state.loading[syncDataProjectReport.typePrefix] = false;
        });
        builder.addCase(getProjectReportOption.pending, (state) => {
            state.loading[getProjectReportOption.typePrefix] = true;
        });
        builder.addCase(getProjectReportOption.fulfilled, (state, action) => {
            state.productReportOption = action.payload;
            state.loading[getProjectReportOption.typePrefix] = false;
        });
        builder.addCase(getProjectReportOption.rejected, (state) => {
            state.loading[getProjectReportOption.typePrefix] = false;
        });
    }
});

// Reducer & export

export default productReportSlice.reducer;

export const { filterSprint, setSprintSelectDefault } = productReportSlice.actions;

// Selector & export
export const productReportSelector = (state: RootState) => state.productReport;
