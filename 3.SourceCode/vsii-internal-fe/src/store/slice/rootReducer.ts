import authReducer from './authSlice';
import menuReducer from './menuSlice';
import snackbarReducer from './snackbarSlice';
import departmentReducer from './departmentSlice';
import projectReducer from './projectSlice';
import confirmReducer from './confirmSlice';
import syncReducer from './syncSlice';
import commentReducer from './commentSlice';
import productReportReducer from './productReportSlice';
import projectTypeReducer from './projectTypeSlice';
import titleReducer from './titleSlice';
import memberReducer from './memberSlice';
import deniedPermissionReducer from './deniedPermissionSlice';
import weeklyEffortReducer from './weeklyEffortSlice';
import costAndEffortMonitoringReducer from './costAndEffortMonitoringSlice';
import nonBillableMonitoringReducer from './nonBillableMonitoringSlice';
import rankReducer from './rankSlice';
import monthlyEffortReducer from './monthlyEffortSlice';
import nonBillableConfigReducer from './nonBillableConfigSlice';
import flexiableReportReducer from './flexiableReportSlice';
import languageConfigReducer from './languageConfigSlice';

const rootReducer = {
    auth: authReducer,
    menu: menuReducer,
    snackbar: snackbarReducer,
    department: departmentReducer,
    project: projectReducer,
    confirm: confirmReducer,
    sync: syncReducer,
    comment: commentReducer,
    productReport: productReportReducer,
    projectType: projectTypeReducer,
    title: titleReducer,
    config: nonBillableConfigReducer,
    member: memberReducer,
    deniedPermission: deniedPermissionReducer,
    weeklyEffort: weeklyEffortReducer,
    costAndEffortMonitoring: costAndEffortMonitoringReducer,
    nonBillableMonitoring: nonBillableMonitoringReducer,
    rank: rankReducer,
    monthlyEffort: monthlyEffortReducer,
    nonBillableConfig: nonBillableConfigReducer,
    flexiableReport: flexiableReportReducer,
    languageConfig: languageConfigReducer
};

export default rootReducer;
