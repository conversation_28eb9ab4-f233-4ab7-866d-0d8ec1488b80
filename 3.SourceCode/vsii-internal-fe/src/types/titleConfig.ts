export interface ITitleConfig {
    id: string;
    titleCode: string;
    titleName: string;
    created: Date;
    creator?: string;
    lastUpdate?: Date;
    userUpdate?: string;
}

export interface IGetTitleResponse {
    content: ITitleConfig[];
}

export interface ICreateTitleRequest {
    titleCode: string;
    titleName: string;
}

export interface IEditTitleRequest {
    id: string;
    titleCode: string;
    titleName: string;
}
