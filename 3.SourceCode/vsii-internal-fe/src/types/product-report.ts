import { IPaginationParam } from './common';

export interface IProductReport {
    id: {
        timestamp: number;
        date: Date;
    };
    name: string;
    description: string;
    sprintStartDate: Date;
    sprintEndDate: Date;
    userId: number;
    projectId: number;
    createdOn: Date;
    updatedOn: Date;
    status: string;
    shared: boolean;
    sprintId: number;
    completed: number;
    requireMents: IRequirement[];
    totalEffors: number;
    effortBug: number;
    effortTask: number;
    currentSprintCost: number;
    projectName: string;
    projectCost: number;
    assignName: string;
    productBacklog: boolean;
}

export interface IProductReportResponse {
    content: IProductReport[];
}

export interface IProductReportRequest extends IPaginationParam {
    projectId: number | null;
}

export interface IProductReportDetailResponse {
    content: {
        projectCost: number;
        projectId: number;
        projectName: string;
        sprints: IProductReport[];
        viewBackLogs: {
            content: IProductReport[];
            totalEffort: number;
        };
    };
}

export interface IRequirement {
    issueId: string;
    sprintId: number;
    requireName: string;
    status: string;
    sprintName: string;
    desc: string;
    assignName: string | null;
    effort: number;
    requirementCost: number;
    tasks: {
        taskName: string;
        assigneeName: string;
        status: string;
        effort: number;
    }[];
}
