export interface IWorkingCalendar {
    idHexString: string;
    idHexStringUser?: string;
    year: number;
    month: number;
    userId: string;
    userName: string;
    lastName: string;
    firstName: string;
    departmentId: string;
    memberCode: string;
    workdays: IWorkdays[];
    rank: IRankWC;
    workingDaySum: IWorkingDaySum;
    status: string;
    verify: string;
    message?: string;
    comment?: string;
    workTime: IWorkTime;
}
export interface IWorkTime {
    late: number;
    early: number;
}

export interface IWorkdays {
    day: number;
    type: string;
    dayOfWeek: string;
    verified?: string | null;
    sessionCalendar: ISessionCalendar;
}
export interface ISessionCalendar {
    morning: string;
    afternoon: string;
}

export interface IRankWC {
    fromDate: string;
    toDate?: string;
    rank: string;
    title: string;
}

export interface IWorkingDaySum {
    wao: number;
    wfh: number;
    onSite: number;
    leave: number;
    halfDayLeave: number;
    sickLeave: number;
    maternityLeave: number;
    holiday: number;
    wedding: number;
    compensatoryLeave: number;
    unpaidLeave: number;
    overTime: number;
}

export interface IAddOrEditResponse {
    content: string;
}

export interface ITypeList {
    key: string;
    value: string;
    total: string;
    color: string;
}

export interface TUpdateStatus {
    idHexString: string;
    status: string;
}

// Closing Date
export interface IClosingDate {
    year: number | string;
    idHexString: string;
    locked: string;
    closingDates: IClosingDateType[];
    userUpdate: string | undefined;
    lastUpdate: string | Date;
}

export interface IClosingDateType {
    month: number;
    closingDate: string;
}

// Leave Day
export interface ILeaveDaysInfo {
    totalLeaveDayThisYear: number;
    totalLeaveDaysUsed: number;
    totalLeaveDaysRemain: number;
    totalLeaveDaysLastYear: number;
    totalLeaveDays: number;
    memberCode: string;
}
export interface ILeaveDays {
    idHexString?: string;
    firstName: string;
    lastName: string;
    memberCode: string;
    titleCode: string;
    departmentId: string;
    leaveDaysInformation: ILeaveDaysInfo;
    member?: string;
}

// Manage leaves
export interface ILeavesItem {
    id: string;
    memberName: string;
    approveName: string;
    dept: string;
    leaveType: string;
    fromDate: string;
    toDate: string;
    status: string;
    approvedDate: string;
}

export interface ILeavesList {
    content: ILeavesItem[];
}

//Manage OT
export interface IOTItem {
    id: string;
    memberName: string;
    approveName: string;
    dept: string;
    overTimeType: string;
    fromDate: string;
    toDate: string;
    status: string;
    approvedDate: string;
}

export interface IOTList {
    content: IOTItem[];
}
// Manage resignation
export interface IResignationItem {
    idHexString: string;
    member: {
        idHexString: string;
        fullName: string;
    };
    approver: {
        idHexString: string;
        fullName: string;
    };
    title: string;
    dept: string;
    fromDate: string;
    status: string;
    approvedDate: string;
}

export interface IResignationList {
    content: IResignationItem[];
}

export interface IWorkingOnsite {
    pagination: null;
    content: {
        idHexString: string;
        userIdHexString: string;
        userId: string;
        firstName: string;
        lastName: string;
        onsiteDays: number;
        onsiteLastMonth: number;
        onsiteThisMonth: number;
        team: string;
        projectList: string[] | null;
    }[];
}
