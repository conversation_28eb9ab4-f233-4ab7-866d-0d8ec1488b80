import { IOption, IPaginationParam } from './common';

// Column Config
export interface ISearchColumnConfigParams extends IPaginationParam {
    flexibleReportId: string;
    flexibleColumnName?: IOption | null | string;
    flexibleColumnId?: string;
}

export interface IColumnConfig {
    id: string | IOption;
    flexibleReportId: string;
    code: string;
    columnName: string;
    inputType: string;
    isCalculate: boolean;
    isPercentage: boolean;
    note: string;
    flexibleColumnName: string | null;
    reportName: string;
}

export interface IGetReportNameResponse {
    content: {
        id: string;
        reportName: string;
        isConditionVisible?: boolean;
        isCalculateVisible?: boolean;
    }[];
}

export interface IGetColumnNameRequest {
    flexibleReportId: string;
}

export interface IGetColumnNameResponse {
    content: IColumnConfig[];
}

// Text Config
export interface ISearchTextConfigParams extends IPaginationParam {
    flexibleReportId: string;
    textName: string;
    code: string;
}

export interface ITextConfig {
    id: string;
    flexibleReportId: string;
    screenName: string;
    defaultTextNameVN: string;
    defaultTextNameENG: string;
    textNameVN: string;
    textNameENG: string;
    note: string;
    key: string;
    languageConfigs: ILanguageConfigInfor[];
}
export interface ILanguageConfigInfor {
    languageCode: string;
    newText: string;
}
export interface IGetTextConfigResponse {
    content: ITextConfig[];
}

// Flexible reporting config
export interface IConditionTypesList {
    content: {
        dtos: IConditionTypes[];
        totalReport: IOtherTotalReport[];
    };
}

export interface IOtherTotalReport {
    id: string;
    text: string;
    total: string;
    reportName: string;
}
export interface IConditionTypes {
    id: string;
    columnName: string;
    flexibleReportId: string;
    inputType: string;
    isCalculate: boolean;
    isPercentage: boolean;
    note: string;
    code: string;
    key: string;
    reportName?: string;
}

export interface FlexibleReportSearchConfig {
    page: number;
    size: number;
    reportId?: string | null;
    textName?: string;
}

export interface IFlexibleReports {
    id: string;
    reportId: IOption | string;
    defaultTextNameVN: string;
    reportName: string;
    defaultTextNameENG: string;
    textNameVN: string;
    textNameENG: string;
    layout: string;
    note: string;
    conditions: IConditionGroup[];
    calculationInputs: ICalculationInput[] | string;
    isCalculation: boolean;
    calculationInputNames: string;
    selectMultipleReport: boolean;
    otherDataSource: string[] | string;
    style: {
        fontWeight: string;
        textDecoration: string;
        fontStyle: string;
        color: string;
        backgroundColor: string;
    };
    show?: boolean;
    newText: string[];
    languageConfigs: IlanguageConfigs[];
}

export interface IlanguageConfigs {
    languageCode?: string;
    newText?: string;
}

export interface ICalculationInput {
    sign?: string;
    code?: IOption;
}
export type IConditionGroup = ICondition[];

export interface ICondition {
    key: string;
    compare: string;
    value: string | number | Date | string[] | undefined;
    type: 'text' | 'select' | 'number' | 'date';
    code: string;
    conditionSelecteted: boolean;
    minValue: string | number | Date | undefined;
    maxValue: string | number | Date | undefined;
    name: string;
    isPercentage: boolean;
}
