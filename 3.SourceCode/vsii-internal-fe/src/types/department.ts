export interface IDepartment {
    id: string;
    deptId: string;
    deptName: string;
    userCreate?: string;
    createdDate?: string;
    userUpdate?: string;
    lastUpdate?: string;
}

export interface IGetDepartmentResponse {
    content: IDepartment[];
}

export interface ICreateDepartmentRequest {
    deptId: string;
    deptName: string;
}

export interface IEditDepartmentRequest {
    id: string;
    deptId: string;
    deptName: string;
}
