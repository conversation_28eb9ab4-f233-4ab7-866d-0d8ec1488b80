export interface ILeaveDaysInformation {
    memberCode: string;
    totalLeaveDayThisYear: number;
    totalLeaveDaysUsed: number;
    totalLeaveDaysMonthly: number;
    totalLeaveDaysLastYear: number;
    totalLeaveDays: number;
    totalLeaveDaysSick: number;
    totalLeaveDaysCompansation: number;
    totalLeaveDaysWedding: number;
    totalLeaveDaysSeniority: number;
    totalLeaveDaysFuneral: number;
    totalLeaveDaysUnpaid: number;
    totalLeaveDaysMaternity: number;
    note: string;
}

export interface ILeaveOption {
    label: string;
    value: keyof ILeaveDaysInformation;
    days: number;
}