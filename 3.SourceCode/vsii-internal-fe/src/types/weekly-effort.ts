import { IPaginationParam } from './common';

export interface GetWeeklyEffortRequest extends IPaginationParam {
    year: number;
    week: string | number;
    userId?: string;
    fullname?: string;
    timeStatus?: string[];
    projectId?: string;
    projectName?: string;
}

// Member
export interface IWeeklyEffortMember {
    id: string;
    member: string;
    memberCode: string;
    firstName: string;
    lastName: string;
    rank: string;
    rankCost: string;
    allocationCost: string;
    department: string;
    effortInWeek: string;
    differenceHours: string;
    project: string;
    projectList: string[];
    standardHour: string;
    onboardDate: string;
    outboardDate: string;
}

export interface IWeeklyEffortMemberResponse {
    content: IWeeklyEffortMember[];
}

// Project
export interface IWeeklyEffortProject {
    projectId: string;
    projectName: string;
    department: string;
    spentOn: Date;
    type: string;
    rank: {
        level: string;
        cost: number;
    }[];
    totalEffort: string;
    totalCost: string;
}

export interface IWeeklyEffortProjectsResponse {
    content: IWeeklyEffortProject[];
}

// Project detail
export interface IListLogTime {
    issueId: string;
    taskName: string;
    spentTime: number;
    date: string;
}

export interface IWeeklyEffortProjectDetail {
    effortInWeek?: number;
    effortPMVerified?: number | null;
    firstName: string;
    lastName: string;
    memberCode?: string;
    nonPayAbleOT?: number;
    nonPayAbleOTVerified?: number | null;
    payAbleOT?: number;
    payAbleOTVerified?: number | null;
    pmVerified?: number | null;
    pmVerifiedDate?: string | null;
    projectId?: number;
    projectName?: string;
    qaVerified?: string | null;
    qaVerifiedDate?: string | null;
    userId: string;
    userName?: string;
    listLogtime: IListLogTime[];
}

export interface IWeeklyEffortProjectDetailResponse {
    content: IWeeklyEffortProjectDetail[];
}

export interface IUserVerify {
    userId: string;
    pmVerifiedDate?: string | null;
    qaVerifiedDate?: string | null;
}

export interface IRankCostHistory {
    fromDate: string | null;
    toDate: string | null;
    amount: number | null;
    length?: number;
}

export interface IRank {
    rankId?: string;
    rankName: string;
    description: string;
    created?: string;
    creator?: string;
    lastUpdate?: string;
    userUpdate?: string;
    rankCost?: number;
    rankCostHistoryList: IRankCostHistory;
    idHexString: string;
}
export interface IGetAllRankResponse {
    content: IRank[];
}
