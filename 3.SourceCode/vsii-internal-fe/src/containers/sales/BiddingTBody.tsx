// materia-ui
import { <PERSON><PERSON><PERSON><PERSON>on, <PERSON>ack, TableBody, TableCell, TableRow, Tooltip, useMediaQuery, useTheme } from '@mui/material';

// third party
import { FormattedMessage } from 'react-intl';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { ISaleBiddingItem } from 'types';
import { checkAllowedPermission } from 'utils/authorization';
import { formatPrice } from 'utils/common';
import { dateFormat } from 'utils/date';
import { E_BIDDING_STATUS, E_COMMENT_TYPE_SALES_PIPELINE } from 'constants/Common';

// assets
import ClearSharpIcon from '@mui/icons-material/ClearSharp';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

interface BiddingTBodyProps {
    pageNumber: number;
    pageSize: number;
    saleBiddings: ISaleBiddingItem[];
    handleOpen: (idHexString?: string) => void;
    handleDelete: (idHexString: string) => void;
    handleOpenComment: (event: React.MouseEvent<Element>, item: any) => void;
}

const BiddingTBody = (props: BiddingTBodyProps) => {
    const { saleBiddings, handleOpen, pageNumber, pageSize, handleDelete, handleOpenComment } = props;
    const { biddingPermission } = PERMISSIONS.sale.salePipeline;
    const theme = useTheme();
    const matches = useMediaQuery(theme.breakpoints.up('md'));

    return (
        <TableBody>
            {saleBiddings?.map((saleBidding: ISaleBiddingItem, key: number) => (
                <TableRow key={key}>
                    {checkAllowedPermission(biddingPermission.edit) || checkAllowedPermission(biddingPermission.delete) ? (
                        <TableCell sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: 'white' }}>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                {checkAllowedPermission(biddingPermission.edit) && (
                                    <Tooltip
                                        placement="top"
                                        title={<FormattedMessage id="edit" />}
                                        onClick={() => handleOpen(saleBidding.idHexString)}
                                    >
                                        <IconButton aria-label="edit" size="small">
                                            <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                        </IconButton>
                                    </Tooltip>
                                )}
                                {checkAllowedPermission(biddingPermission.delete) && saleBidding.status === E_BIDDING_STATUS.BIDDING ? (
                                    <Tooltip
                                        placement="top"
                                        title={<FormattedMessage id="delete" />}
                                        onClick={() => handleDelete(saleBidding.idHexString)}
                                    >
                                        <IconButton aria-label="edit" size="small">
                                            <ClearSharpIcon sx={{ fontSize: '1.1rem' }} />
                                        </IconButton>
                                    </Tooltip>
                                ) : (
                                    <></>
                                )}
                            </Stack>
                        </TableCell>
                    ) : (
                        <></>
                    )}
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{saleBidding.contractType}</TableCell>
                    <TableCell>{saleBidding.serviceType}</TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: saleBidding.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.PROJECT_NAME,
                                comment: saleBidding.projectNameComment
                            });
                        }}
                    >
                        {saleBidding.projectName}
                    </TableCell>
                    <TableCell>{saleBidding.customer}</TableCell>
                    <TableCell>{saleBidding.probability === null ? '' : saleBidding.probability + '%'}</TableCell>
                    <TableCell>{saleBidding.revenuePercent === null ? '' : saleBidding.revenuePercent + '%'}</TableCell>
                    <TableCell>{saleBidding.status}</TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: saleBidding.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.SIZE_VND,
                                comment: saleBidding.sizeVNDComment
                            });
                        }}
                    >
                        {formatPrice(Math.round(Number(saleBidding.sizeVND)))}
                    </TableCell>
                    <TableCell>{formatPrice(saleBidding.sizeUSD)}</TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: saleBidding.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.MANAGEMENT_REVENUE,
                                comment: saleBidding.managementRevenueComment
                            });
                        }}
                    >
                        {formatPrice(Math.round(Number(saleBidding.managementRevenueAllocated)))}
                    </TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: saleBidding.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.ACCOUNTANT_REVENUE,
                                comment: saleBidding.accountantRevenueComment
                            });
                        }}
                    >
                        {formatPrice(Math.round(Number(saleBidding.accountantRevenueAllocated)))}
                    </TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: saleBidding.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.LICENSE_FEE,
                                comment: saleBidding.licenseFeeComment
                            });
                        }}
                    >
                        {formatPrice(saleBidding.licenseFee)}
                    </TableCell>
                    <TableCell sx={{ whiteSpace: 'nowrap', position: 'sticky', right: !!matches ? 0 : 'unset', background: '#fff' }}>
                        {`${dateFormat(saleBidding.contractFromDate)} - ${dateFormat(saleBidding.contractToDate)}`}
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default BiddingTBody;
