/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/exhaustive-deps */
import { SyntheticEvent, useEffect, useState } from 'react';

// react-hoook-form
import { useFieldArray, useForm } from 'react-hook-form';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid, Stack } from '@mui/material';

// third party
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';

// project imports
import { useAppDispatch } from 'app/hooks';
import { FormProvider } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { TabPanel } from 'components/extended/Tabs';
import {
    CONTRACT_TYPE_SALE_REPORT,
    E_BIDDING_STATUS,
    E_SALES_BIDDING_TYPE_SET_HCINFO,
    FIELD_BY_TAB_BIDDING,
    SERVICE_TYPE_STATUS,
    addOrEditBiddingTabs
} from 'constants/Common';
import { TabCustom } from 'containers';
import { addOrEditBiddingConfig, addOrEditBiddingSchema } from 'pages/sales/Config';
import { openConfirm } from 'store/slice/confirmSlice';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { ICurrencyBidding, IDetailBidding, IHCMonthlyList } from 'types';
import { getTabValueByFieldError, isEmpty } from 'utils/common';
import { dateFormat } from 'utils/date';
import AddOrEditBiddingFinancialInfo from './AddOrEditBiddingFinancialInfo';
import AddOrEditBiddingHcInfo from './AddOrEditBiddingHcInfo';
import AddOrEditBiddingOtherInfo from './AddOrEditBiddingOtherInfo';
import AddOrEditBiddingProjectInfo from './AddOrEditBiddingProjectInfo';
import TMOrFCBidding from './TMOrFCBidding';
import { IMember } from 'types/member';

interface IAddOrEditBiddingProps {
    open: boolean;
    year?: number;
    isEdit: boolean;
    handleClose: () => void;
    loading: boolean;
    detailBidding?: IDetailBidding | null;
    currencyAndExchangeRateDefault: ICurrencyBidding;
    exchangeRateUSDpercentVND: number;
    // TODO:
    postAddOrEditBidding: (payload: any) => void;
    monthlyBillableDay: any;
}

const AddOrEditBidding = (props: IAddOrEditBiddingProps) => {
    // Hooks, State, Variable, Props
    const dispatch = useAppDispatch();
    const {
        open,
        handleClose,
        isEdit,
        currencyAndExchangeRateDefault,
        monthlyBillableDay,
        loading,
        postAddOrEditBidding,
        detailBidding,
        year,
        exchangeRateUSDpercentVND
    } = props;
    const [tabValue, setTabValue] = useState(0);
    const [openNestedModal, setOpenNestedModal] = useState<boolean>(false);
    const [contact, setContact] = useState<{ idHexString: string; firstName: string; lastName: string } | null>(null);
    // TODO: DELETE ANY
    const [hcInfoMonth, setHcInfoMonth] = useState<any>(null);
    const [fixCostHcInfo, setFixCostHcInfo] = useState<any>(null);

    const monthlyHCList = monthlyBillableDay.map((item: any) => {
        return {
            year: item.year,
            month: item.month,
            billableDay: item.workingDays,
            hcMonthly: 0,
            billable: 0
        };
    });
    const defaultValues = {
        ...addOrEditBiddingConfig,
        hcInfo: {
            ...addOrEditBiddingConfig.hcInfo,
            monthlyHCList
        },
        financialInfo: {
            ...addOrEditBiddingConfig.financialInfo,
            currency: currencyAndExchangeRateDefault?.currency || '',
            exchangeRate: currencyAndExchangeRateDefault?.exchangeRate
        }
    };

    // useForm
    const methods = useForm({
        defaultValues,
        mode: 'all',
        resolver: yupResolver(addOrEditBiddingSchema)
    });
    const { errors } = methods.formState;

    // useFieldArray
    const { fields: fieldsMonthlyHCList } = useFieldArray({
        name: 'hcInfo.monthlyHCList',
        control: methods.control
    } as any);

    const handleSetHCInfo = (hcListResponse: IHCMonthlyList[], type?: string) => {
        const monthlyHCListDefaultValue = methods.watch('hcInfo.monthlyHCList');
        const monthlyHCListConvert = hcListResponse.map((x: IHCMonthlyList) => {
            const monthlyHCDefaultValue = monthlyHCListDefaultValue.find((y: IHCMonthlyList) => x.year === y.year && x.month === y.month);
            return {
                ...x,
                billable:
                    type === E_SALES_BIDDING_TYPE_SET_HCINFO.ESTIMATE
                        ? +x.billable
                        : monthlyHCDefaultValue
                        ? +monthlyHCDefaultValue?.billable
                        : 0,
                hcMonthly: monthlyHCDefaultValue ? +monthlyHCDefaultValue.hcMonthly : x.hcMonthly
            };
        });
        methods.setValue('hcInfo.monthlyHCList', monthlyHCListConvert);
    };

    const focusErrors = () => {
        const tabNumber = getTabValueByFieldError(errors, FIELD_BY_TAB_BIDDING);
        setTabValue(tabNumber);
    };

    // ================= Event =================
    // Handle change tab
    const handleChangeTab = (event: SyntheticEvent, value: number) => {
        setTabValue(value);
    };

    const handleChangeUserContact = (userSelected: IMember) => {
        setContact(
            userSelected
                ? {
                      idHexString: userSelected.idHexString!,
                      firstName: userSelected.firstName,
                      lastName: userSelected.lastName
                  }
                : null
        );
    };

    const handleOpenTMOrFCHCInfo = () => {
        setOpenNestedModal(true);
    };

    const handleCloseTMOrFCHCInfo = () => {
        setOpenNestedModal(false);
    };

    // ================= Submit =================
    const handleSubmit = (values: any) => {
        // project Redmine
        const projectRedmine = values.project.projectRedmineId;
        // contractDurationFrom
        const contractDurationFrom = values.project.contractDurationFrom;
        // contract type
        const contractType = values.project.contractType;
        // hcInfo base contractType
        const hcInfoMonthPayload = contractType === CONTRACT_TYPE_SALE_REPORT.TM ? { hcInfoMonth } : { fixCostHcInfo };
        // payload
        const payload = {
            ...values,
            project: {
                ...values.project,
                contractDueDate: dateFormat(values.project.contractDueDate),
                contractDurationFrom: dateFormat(contractDurationFrom),
                contractDurationTo: dateFormat(values.project.contractDurationTo),
                projectRedmineId: projectRedmine?.value,
                projectRedmineName: projectRedmine?.label
            },
            financialInfo: {
                ...values.financialInfo,
                year: isEdit ? year : new Date(contractDurationFrom).getFullYear()
            },
            hcInfo: { ...values.hcInfo, ...hcInfoMonthPayload },
            otherInfo: {
                ...values.otherInfo,
                contact
            }
        };

        if (isEdit) {
            // check contract type and service type
            if (
                values.project.serviceType === SERVICE_TYPE_STATUS.PRODUCT &&
                values.project.contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST
            ) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: 'edit-contract-type-service-type',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            }
            // confirm submit if isEdit && status = Contract
            const status = values.project.status;
            if (status === E_BIDDING_STATUS.CONTRACT && detailBidding?.project.status !== E_BIDDING_STATUS.CONTRACT) {
                dispatch(
                    openConfirm({
                        open: true,
                        title: <FormattedMessage id="warning" />,
                        content: <FormattedMessage id="bidding-confirm-edit-contract" />,
                        handleConfirm: () => postAddOrEditBidding(payload)
                    })
                );
                return;
            }
            postAddOrEditBidding(payload);
            return;
        }
        postAddOrEditBidding(payload);
    };

    // ================= Effect =================
    useEffect(() => {
        !isEmpty(errors) && focusErrors();
    }, [errors]);

    useEffect(() => {
        if (isEdit && detailBidding) {
            const { project, otherInfo, hcInfo } = detailBidding;
            setContact({
                idHexString: otherInfo.contact.idHexString,
                firstName: otherInfo.contact.firstName,
                lastName: otherInfo.contact.lastName
            });
            methods.reset({
                ...detailBidding,
                project: {
                    ...project,
                    projectRedmineId: project?.projectRedmineId
                        ? {
                              value: project?.projectRedmineId,
                              label: project?.projectRedmineName
                          }
                        : null
                },
                otherInfo: {
                    ...otherInfo,
                    contact: otherInfo?.contact.idHexString
                        ? {
                              value: otherInfo.contact.idHexString,
                              label: `${otherInfo.contact.firstName} ${otherInfo.contact.lastName}`
                          }
                        : null
                }
            } as any);
            hcInfo?.hcInfoMonth && setHcInfoMonth(hcInfo.hcInfoMonth);
            hcInfo?.fixCostHcInfo && setFixCostHcInfo(hcInfo.fixCostHcInfo);
        }
    }, [detailBidding]);

    return (
        <Modal
            isOpen={open}
            title={isEdit ? 'all-sale-pipeline-edit-bidding-packages' : 'all-sale-pipeline-add-bidding-packages'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="lg"
        >
            <TabCustom value={tabValue} handleChange={handleChangeTab} tabs={addOrEditBiddingTabs} />
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                {/* Project Info */}
                <TabPanel value={tabValue} index={0}>
                    <AddOrEditBiddingProjectInfo isEdit={isEdit} status={detailBidding?.project.status} handleSetHCInfo={handleSetHCInfo} />
                </TabPanel>
                {/* Financial Info */}
                <TabPanel value={tabValue} index={1}>
                    <AddOrEditBiddingFinancialInfo
                        year={year}
                        exchangeRateUSDpercentVND={exchangeRateUSDpercentVND}
                        status={detailBidding?.project.status}
                    />
                </TabPanel>
                {/* HC Info */}
                <TabPanel value={tabValue} index={2}>
                    <AddOrEditBiddingHcInfo
                        fieldsMonthlyHCList={fieldsMonthlyHCList}
                        handleOpenTMOrFCHCInfo={handleOpenTMOrFCHCInfo}
                        status={detailBidding?.project.status}
                    />
                </TabPanel>
                {/* Other Info */}
                <TabPanel value={tabValue} index={3}>
                    <AddOrEditBiddingOtherInfo handleChangeUserContact={handleChangeUserContact} status={detailBidding?.project.status} />
                </TabPanel>
                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose} disabled={loading}>
                                <FormattedMessage id="cancel" />
                            </Button>
                            <LoadingButton
                                loading={loading}
                                variant="contained"
                                type="submit"
                                disabled={detailBidding?.project.status === E_BIDDING_STATUS.CONTRACT}
                            >
                                {isEdit ? <FormattedMessage id="submit" /> : <FormattedMessage id="add" />}
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>

            {/* TM or Fix Cost Modal HC Info */}
            {openNestedModal && (
                <TMOrFCBidding
                    open={openNestedModal}
                    handleClose={handleCloseTMOrFCHCInfo}
                    handleSetHCInfo={handleSetHCInfo}
                    hcInfoMonth={hcInfoMonth}
                    setHcInfoMonth={setHcInfoMonth}
                    fixCostHcInfo={fixCostHcInfo}
                    setFixCostHcInfo={setFixCostHcInfo}
                    exchangeRate={methods.getValues('financialInfo.exchangeRate')}
                    contractType={methods.getValues('project.contractType')}
                    sizeVND={methods.getValues('financialInfo.sizeVND')}
                    from={methods.getValues('project.contractDurationFrom')}
                    to={methods.getValues('project.contractDurationTo')}
                    status={detailBidding?.project.status}
                />
            )}
        </Modal>
    );
};

export default AddOrEditBidding;
