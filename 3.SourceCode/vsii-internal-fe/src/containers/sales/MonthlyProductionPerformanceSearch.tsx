import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid, SelectChangeEvent } from '@mui/material';

// project imports
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { E_SCREEN_SALES_YEAR } from 'constants/Common';
import {
    IMonthlyProductionPerformanceFilterConfig,
    monthlyProductionPerformanceFilterConfig,
    monthlyProductionPerformanceFilterSchema
} from 'pages/sales/Config';
import { IOption } from 'types';
import { Months, SalesYear, SearchForm } from '../search';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IMonthlyProductionPerformanceSearchProps {
    conditions: IMonthlyProductionPerformanceFilterConfig;
    months: IOption[];
    handleChangeYear: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
    handleSearch: (value: IMonthlyProductionPerformanceFilterConfig) => void;
}

const MonthlyProductionPerformanceSearch = (props: IMonthlyProductionPerformanceSearchProps) => {
    const { conditions, months, handleChangeYear, handleSearch } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={monthlyProductionPerformanceFilterConfig}
            formSchema={monthlyProductionPerformanceFilterSchema}
            handleSubmit={handleSearch}
            formReset={conditions}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={4}>
                    <SalesYear
                        handleChangeYear={handleChangeYear}
                        screen={E_SCREEN_SALES_YEAR.PRODUCTION_PERFORMANCE}
                        label={salesReport.monthlyProductionPerformance + '-year'}
                    />
                </Grid>
                <Grid item xs={12} lg={4}>
                    <Months months={months} isMultiple label={salesReport.monthlyProductionPerformance + '-month'} />
                </Grid>
                <Grid item xs={12} lg={4}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyProductionPerformanceSearch;
