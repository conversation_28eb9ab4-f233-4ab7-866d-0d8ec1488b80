import { useEffect } from 'react';

// react-hook-form
import { useFormContext } from 'react-hook-form';

// material-ui
import { Grid, Table, TableContainer } from '@mui/material';

// project imports
import { Input, NumericFormatCustom } from 'components/extended/Form';
import BiddingHCTBody from './BiddingHCTBody';
import BiddingHCThead from './BiddingHCThead';

// third party
import { FormattedMessage } from 'react-intl';
import { E_BIDDING_STATUS } from 'constants/Common';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditBiddingHcInfoProps {
    fieldsMonthlyHCList: any;
    handleOpenTMOrFCHCInfo: () => void;
    status?: string;
}

const AddOrEditBiddingHcInfo = (props: IAddOrEditBiddingHcInfoProps) => {
    // ================= Hooks, State, Variable =================
    const { fieldsMonthlyHCList, handleOpenTMOrFCHCInfo, status } = props;
    const { setValue, watch } = useFormContext();

    const { salesReport } = TEXT_CONFIG_SCREEN;

    // ================= Effects =================
    useEffect(() => {
        const { unsubscribe } = watch((value, info) => {
            if (info.name?.startsWith('hcInfo')) {
                if (
                    info.name.endsWith('teamLeadHcs') ||
                    info.name.endsWith('seniorHcs') ||
                    info.name.endsWith('middleHcs') ||
                    info.name.endsWith('juniorHcs')
                ) {
                    const teamLeadHcs = value.hcInfo.teamLeadHcs;
                    const seniorHcs = value.hcInfo.seniorHcs;
                    const middleHcs = value.hcInfo.middleHcs;
                    const juniorHcs = value.hcInfo.juniorHcs;
                    setValue('hcInfo.hcs', +teamLeadHcs + +seniorHcs + +middleHcs + +juniorHcs);
                }
            }
        });
        return () => unsubscribe();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [watch]);

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-billable-hcs'} />}
                    name="hcInfo.billableHcs"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    disabled
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-1st'} />}
                    name="hcInfo.quarter1st"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    disabled
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-hcs'} />}
                    name="hcInfo.hcs"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    disabled
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-2nd'} />}
                    name="hcInfo.quarter2nd"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-team-lead-hcs'} />}
                    name="hcInfo.teamLeadHcs"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    disabled
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-3rd'} />}
                    name="hcInfo.quarter3rd"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-senior-hcs'} />}
                    name="hcInfo.seniorHcs"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    disabled
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-4th'} />}
                    name="hcInfo.quarter4th"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-middle-hcs'} />}
                    name="hcInfo.middleHcs"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    disabled
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-total-new-sale'} />}
                    name="hcInfo.totalNewSale"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-junior-hcs'} />}
                    name="hcInfo.juniorHcs"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    disabled
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-total-billable'} />}
                    name="hcInfo.totalBillable"
                    textFieldProps={{
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>

            {/* Array HC Info */}
            <TableContainer>
                <Table>
                    <BiddingHCThead monthlyHCList={fieldsMonthlyHCList} />
                    <BiddingHCTBody monthlyHCList={fieldsMonthlyHCList} handleOpen={handleOpenTMOrFCHCInfo} status={status} />
                </Table>
            </TableContainer>
        </Grid>
    );
};

export default AddOrEditBiddingHcInfo;
