/* eslint-disable import/no-extraneous-dependencies */
import { Fragment, useCallback, useState } from 'react';
import { FormattedMessage } from 'react-intl';

// materia-ui
import { Box, ButtonBase, IconButton, Stack, TableBody, TableCell, TableRow, Tooltip, useMediaQuery, useTheme } from '@mui/material';

// project imports
import { GROUP_COLOR_MONTH } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import {
    ICommentForm,
    ICommentItem,
    IDataHCByMonth,
    IDepartmentPerformance,
    IDepartmentPerformanceData,
    IDepartmentPerformanceDataByMonth,
    IDepartmentPerformanceDataByTotal,
    IMonthlyProductionPerformanceInfo,
    ISaleTotal
} from 'types';
import { calculationSum, formatPrice } from 'utils/common';
import { checkAllowedPermission } from 'utils/authorization';

// assets
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import ReorderTableRow from 'components/extended/Table/ReorderTableRow';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import DoneIcon from '@mui/icons-material/Done';

import PinchIcon from '@mui/icons-material/Pinch';

// ==============================|| Monthly Production Performance TBODY ||============================== //

// style
const style_group_even_total = {
    '& .MuiTableCell-root:nth-of-type(8n+3),.MuiTableCell-root:nth-of-type(8n+5),.MuiTableCell-root:nth-of-type(8n+7),.MuiTableCell-root:nth-of-type(8n+9)':
        {
            backgroundColor: GROUP_COLOR_MONTH
        }
};

const style_group_even_item = {
    '& .MuiTableCell-root:nth-of-type(8n+6),.MuiTableCell-root:nth-of-type(8n+7), .MuiTableCell-root:nth-of-type(8n+8),.MuiTableCell-root:nth-of-type(8n+9)':
        {
            backgroundColor: GROUP_COLOR_MONTH
        }
};

const style_group_even_total_dept = {
    '& .MuiTableCell-root:nth-of-type(8n+3),.MuiTableCell-root:nth-of-type(8n+4),.MuiTableCell-root:nth-of-type(8n+5),.MuiTableCell-root:nth-of-type(8n+6)':
        {
            backgroundColor: GROUP_COLOR_MONTH
        }
};

// Render total headcount of months
const TotalHeadCountByMonth = ({ headCounts }: { headCounts: IDataHCByMonth[] }) => {
    return (
        <>
            {headCounts?.map((headCount: IDataHCByMonth, index: number) => (
                <Fragment key={index}>
                    <TableCell colSpan={4}>{headCount.value}</TableCell>
                </Fragment>
            ))}
        </>
    );
};

// Render total Productivity of months
const TotalProductivityByMonth = ({ productivities }: { productivities: number[] }) => {
    return (
        <>
            {productivities?.map((productivity: number, index: number) => (
                <Fragment key={index}>
                    <TableCell colSpan={4}>{formatPrice(productivity)}</TableCell>
                </Fragment>
            ))}
        </>
    );
};

// Render total money of status by months
export const TotalMoneyOfStatusByMonth = ({ months }: { months: IDepartmentPerformanceDataByTotal[] }) => {
    return (
        <>
            {months?.map((month: IDepartmentPerformanceDataByTotal, index: number) => (
                <Fragment key={index}>
                    <TableCell>{formatPrice(month.delivered)}</TableCell>
                    <TableCell>{formatPrice(month.receivable)}</TableCell>
                    <TableCell>{formatPrice(month.received)}</TableCell>
                    <TableCell>{formatPrice(month.financial)}</TableCell>
                </Fragment>
            ))}
        </>
    );
};

// Render button add or edit row
const AddOrEditButton = ({ handleOpen }: { handleOpen: (item?: IDepartmentPerformanceData) => void }) => {
    return (
        <Tooltip placement="top" title={<FormattedMessage id="edit" />}>
            <IconButton aria-label={'Edit'} size="small" onClick={() => handleOpen()}>
                <EditOutlinedIcon sx={{ fontSize: '1rem' }} />
            </IconButton>
        </Tooltip>
    );
};

interface IMonthlyProductionPerformanceTBodyProps {
    info: IMonthlyProductionPerformanceInfo;
    handleOpen: (idHexString: string) => void;
    handleOpenEditHeadCount: (item: IDataHCByMonth[]) => void;
    editComment: (payload?: ICommentForm) => void;
    handleOpenComment: (event: React.MouseEvent<Element>, item: ICommentItem) => void;
    handleConFirmEdit?: (orderList: ISaleTotal[]) => void;
    setIsEdited: React.Dispatch<React.SetStateAction<boolean>>;
    isEdited: boolean;
}

const MonthlyProductionPerformanceTBody = (props: IMonthlyProductionPerformanceTBodyProps) => {
    const { handleOpen, handleOpenEditHeadCount, handleOpenComment, handleConFirmEdit, setIsEdited, isEdited } = props;
    const monthlyProductionPerformanceInfo = props.info;
    const theme = useTheme();
    const [actionEl, setActionEl] = useState<any>(null);
    const [projectNameEl, setProjectNameEl] = useState<any>(null);
    const [serviceTypeEl, setServiceTypeEl] = useState<any>(null);
    const [contractTypeEl, setContractTypeEl] = useState<any>(null);
    const { monthlyProductionPerformancePermission } = PERMISSIONS.sale;
    const { salesReport } = TEXT_CONFIG_SCREEN;

    // action
    const actionElRef = useCallback((domNode: any) => {
        if (domNode) setActionEl(domNode.getBoundingClientRect());
    }, []);
    // project name
    const projectNameElRef = useCallback((domNode: any) => {
        if (domNode) setProjectNameEl(domNode.getBoundingClientRect());
    }, []);
    // service type
    const serviceTypeElRef = useCallback((domNode: any) => {
        if (domNode) setServiceTypeEl(domNode.getBoundingClientRect());
    }, []);
    // contract type
    const contractTypeElRef = useCallback((domNode: any) => {
        if (domNode) setContractTypeEl(domNode.getBoundingClientRect());
    }, []);

    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const freezeDefault = { position: !!matches ? 'sticky' : 'unset', left: 0, background: '#fff' };
    const freezeActionsEl = { position: !!matches ? 'sticky' : 'unset', left: actionEl?.width, background: '#fff' };

    const sortList = (list: ISaleTotal[]) => list.slice().sort((first: any, second: any) => first.index - second.index);

    const [rows, setRows] = useState(sortList(monthlyProductionPerformanceInfo.companyTotals.saleTotals.filter((item) => item.id)));

    const [showButton, setShowButton] = useState(false);

    const reorderList = useCallback(
        (sourceIndex: number, destinationIndex: number) => {
            if (destinationIndex === sourceIndex) {
                return;
            }

            // Copy the current sorted list
            const updatedList = [...rows];

            // Remove the item being dragged and insert it at the destination index
            const [movedItem] = updatedList.splice(sourceIndex, 1);
            updatedList.splice(destinationIndex, 0, movedItem);

            // Update the index of all items to ensure they are consecutive starting from 0
            const reindexedList = updatedList.map((item, index) => ({
                ...item,
                index: index + 1 // Set the index to the current position in the array
            }));

            // Update the state with the re-sorted and re-indexed list
            setRows(sortList(reindexedList));
        },
        [rows]
    );

    const handleMouseEnter = () => setShowButton(true);
    const handleMouseLeave = () => setShowButton(false);

    return (
        <TableBody>
            {/* Department list */}
            {monthlyProductionPerformanceInfo.departments.map((dept: IDepartmentPerformance, index: number) => (
                <Fragment key={index}>
                    <TableRow sx={{ backgroundColor: '#fdd1b7' }}>
                        <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', position: 'sticky', left: 0 }} align="left">
                            {dept.name}
                        </TableCell>
                        <TableCell colSpan={13 * 4 + 4}></TableCell>
                    </TableRow>
                    <TableRow sx={{ '& .MuiTableCell-root': { fontWeight: 'bold' }, ...style_group_even_total_dept }}>
                        <TableCell sx={freezeDefault}>&nbsp;</TableCell>
                        <TableCell sx={freezeActionsEl} colSpan={4}>
                            {dept.name} <FormattedMessage id="total" />
                        </TableCell>
                        <TotalMoneyOfStatusByMonth months={dept.total} />
                    </TableRow>
                    {dept.data.map((item: IDepartmentPerformanceData, itemIndex: number) => (
                        <TableRow
                            sx={{
                                '&:last-child td, &:last-child th': { border: 0 },
                                ...style_group_even_item
                            }}
                            key={itemIndex}
                        >
                            <TableCell ref={actionElRef} sx={freezeDefault}>
                                <Stack direction="row" justifyContent="center" alignItems="center">
                                    {checkAllowedPermission(monthlyProductionPerformancePermission.edit) && (
                                        <AddOrEditButton handleOpen={() => handleOpen(item.idHexString)} />
                                    )}
                                </Stack>
                            </TableCell>
                            <TableCell
                                ref={projectNameElRef}
                                sx={{ cursor: 'pointer', ...freezeActionsEl, background: '#fff' }}
                                onClick={(e) =>
                                    handleOpenComment(e, {
                                        type: 'projectName',
                                        idHexString: item.idHexString,
                                        label: item.project.value,
                                        content: item.project.comment
                                    })
                                }
                            >
                                <span className="w150px">{item.project.value}</span>
                            </TableCell>
                            <TableCell
                                ref={serviceTypeElRef}
                                sx={{
                                    position: !!matches ? 'sticky' : 'unset',
                                    background: '#fff',
                                    left: projectNameEl && calculationSum(actionEl?.width, projectNameEl?.width)
                                }}
                            >
                                {item.serviceType}
                            </TableCell>
                            <TableCell
                                ref={contractTypeElRef}
                                sx={{
                                    position: !!matches ? 'sticky' : 'unset',
                                    background: '#fff',
                                    left: serviceTypeEl && calculationSum(actionEl?.width, projectNameEl?.width, serviceTypeEl?.width)
                                }}
                            >
                                {item.contractType}
                            </TableCell>
                            <TableCell
                                sx={{
                                    cursor: 'pointer',
                                    position: !!matches ? 'sticky' : 'unset',
                                    background: '#fff',
                                    left:
                                        contractTypeEl &&
                                        calculationSum(actionEl?.width, projectNameEl?.width, serviceTypeEl?.width, contractTypeEl?.width)
                                }}
                                onClick={(e) =>
                                    handleOpenComment(e, {
                                        type: 'contractSize',
                                        idHexString: item.idHexString,
                                        label: item.contractSize.value,
                                        content: item.contractSize.comment
                                    })
                                }
                            >
                                <span>{formatPrice(item.contractSize.value)}</span>
                            </TableCell>
                            {item.months.map((month: IDepartmentPerformanceDataByMonth, mIndex: number) => {
                                return month.month !== 'Y-T-D' ? (
                                    <Fragment key={mIndex}>
                                        <TableCell
                                            sx={{ cursor: 'pointer' }}
                                            onClick={(e) =>
                                                handleOpenComment(e, {
                                                    type: 'delivered',
                                                    idHexString: item.idHexString,
                                                    label: month.delivered.value,
                                                    month: month.month,
                                                    content: month.delivered.comment
                                                })
                                            }
                                        >
                                            <span>{formatPrice(month.delivered.value)}</span>
                                        </TableCell>
                                        <TableCell
                                            sx={{ cursor: 'pointer' }}
                                            onClick={(e) =>
                                                handleOpenComment(e, {
                                                    type: 'receivable',
                                                    idHexString: item.idHexString,
                                                    label: month.receivable.value,
                                                    month: month.month,
                                                    content: month.receivable.comment
                                                })
                                            }
                                        >
                                            <span>{formatPrice(month.receivable.value)}</span>
                                        </TableCell>
                                        <TableCell
                                            sx={{ cursor: 'pointer' }}
                                            onClick={(e) =>
                                                handleOpenComment(e, {
                                                    type: 'received',
                                                    idHexString: item.idHexString,
                                                    label: month.received.value,
                                                    month: month.month,
                                                    content: month.received.comment
                                                })
                                            }
                                        >
                                            <span>{formatPrice(month.received.value)}</span>
                                        </TableCell>
                                        <TableCell
                                            sx={{ cursor: 'pointer' }}
                                            onClick={(e) =>
                                                handleOpenComment(e, {
                                                    type: 'financial',
                                                    idHexString: item.idHexString,
                                                    label: month.financial.value,
                                                    month: month.month,
                                                    content: month.financial.comment
                                                })
                                            }
                                        >
                                            <span>{formatPrice(month.financial.value)}</span>
                                        </TableCell>
                                    </Fragment>
                                ) : (
                                    <Fragment key={mIndex}>
                                        <TableCell>
                                            <span>{formatPrice(month.delivered.value)}</span>
                                        </TableCell>
                                        <TableCell>
                                            <span>{formatPrice(month.receivable.value)}</span>
                                        </TableCell>
                                        <TableCell>
                                            <span>{formatPrice(month.received.value)}</span>
                                        </TableCell>
                                        <TableCell>
                                            <span>{formatPrice(month.financial.value)}</span>
                                        </TableCell>
                                    </Fragment>
                                );
                            })}
                        </TableRow>
                    ))}
                </Fragment>
            ))}

            {/* Company Total */}
            <TableRow sx={{ backgroundColor: '#e6fdcf' }}>
                <TableCell sx={{ fontWeight: 'bold', position: 'sticky', whiteSpace: 'nowrap', left: 0 }} align="left">
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-total'} />
                </TableCell>
                <TableCell colSpan={13 * 4 + 4}></TableCell>
            </TableRow>
            <TableRow sx={{ ...style_group_even_total, '& > td': { fontWeight: 'bold' } }}>
                <TableCell align="center" sx={freezeDefault}>
                    {checkAllowedPermission(monthlyProductionPerformancePermission.edit) && (
                        <AddOrEditButton
                            handleOpen={() => handleOpenEditHeadCount(monthlyProductionPerformanceInfo.companyTotals.totalHCs)}
                        />
                    )}
                </TableCell>
                <TableCell sx={{ ...freezeActionsEl }} colSpan={4}>
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-total-hc'} />
                </TableCell>
                <TotalHeadCountByMonth headCounts={monthlyProductionPerformanceInfo.companyTotals.totalHCs} />
            </TableRow>
            <TableRow sx={{ ...style_group_even_total, '& > td': { fontWeight: 'bold' } }}>
                <TableCell sx={freezeDefault}>&nbsp;</TableCell>
                <TableCell sx={freezeActionsEl} colSpan={4}>
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-productivity'} />
                </TableCell>
                <TotalProductivityByMonth productivities={monthlyProductionPerformanceInfo.companyTotals.productivity} />
            </TableRow>

            {!isEdited ? (
                <>
                    {monthlyProductionPerformanceInfo.companyTotals.saleTotals?.length !== 0 &&
                        monthlyProductionPerformanceInfo.companyTotals.saleTotals?.map((item, index) => (
                            <TableRow
                                sx={{
                                    '& > td': {
                                        ...item.flexibleStyle
                                    },
                                    '& .MuiTableCell-root:nth-of-type(8n+3),.MuiTableCell-root:nth-of-type(8n+4),.MuiTableCell-root:nth-of-type(8n+5),.MuiTableCell-root:nth-of-type(8n+6)':
                                        {
                                            backgroundColor:
                                                item.flexibleStyle?.backgroundColor === '#ffffff'
                                                    ? GROUP_COLOR_MONTH
                                                    : item.flexibleStyle?.backgroundColor
                                        }
                                }}
                                onMouseLeave={handleMouseLeave}
                                onMouseEnter={handleMouseEnter}
                            >
                                <TableCell sx={freezeDefault} align="center">
                                    {index === 0 &&
                                    showButton &&
                                    checkAllowedPermission(monthlyProductionPerformancePermission.editRows) ? (
                                        <Tooltip title={<FormattedMessage id="edit-arrangement"></FormattedMessage>} sx={{ mb: 1 }}>
                                            <ButtonBase
                                                onClick={() => {
                                                    setIsEdited(true);
                                                    setRows(
                                                        monthlyProductionPerformanceInfo.companyTotals.saleTotals.filter((item) => item.id)
                                                    );
                                                }}
                                            >
                                                <PinchIcon sx={{ fontSize: 17 }} />
                                            </ButtonBase>
                                        </Tooltip>
                                    ) : (
                                        ''
                                    )}
                                </TableCell>
                                <TableCell sx={freezeActionsEl} colSpan={4}>
                                    {item.text}
                                </TableCell>
                                <TotalMoneyOfStatusByMonth months={item.total} />
                            </TableRow>
                        ))}
                </>
            ) : (
                <DndProvider backend={HTML5Backend}>
                    {rows?.map((item, index) => (
                        <ReorderTableRow
                            key={item.id}
                            index={index}
                            id={item.id as string}
                            moveRow={reorderList}
                            sx={{
                                '& > td': {
                                    ...item.flexibleStyle
                                },
                                '& .MuiTableCell-root:nth-of-type(8n+3),.MuiTableCell-root:nth-of-type(8n+4),.MuiTableCell-root:nth-of-type(8n+5),.MuiTableCell-root:nth-of-type(8n+6)':
                                    {
                                        backgroundColor:
                                            item.flexibleStyle?.backgroundColor === '#ffffff'
                                                ? GROUP_COLOR_MONTH
                                                : item.flexibleStyle?.backgroundColor
                                    }
                            }}
                        >
                            <TableCell sx={freezeDefault} align="center">
                                {index === 0 && (
                                    <Box display="flex" gap={1} justifyContent="center">
                                        <Tooltip title={<FormattedMessage id="cancel"></FormattedMessage>}>
                                            <ButtonBase
                                                onClick={() => {
                                                    setIsEdited(false);
                                                }}
                                            >
                                                <HighlightOffIcon sx={{ fontSize: 20 }} />
                                            </ButtonBase>
                                        </Tooltip>
                                        <Tooltip title={<FormattedMessage id="confirm"></FormattedMessage>}>
                                            <ButtonBase
                                                onClick={() => {
                                                    handleConFirmEdit?.(rows);
                                                }}
                                            >
                                                <DoneIcon sx={{ fontSize: 18 }} />
                                            </ButtonBase>
                                        </Tooltip>
                                    </Box>
                                )}
                            </TableCell>
                            <TableCell sx={freezeActionsEl} colSpan={4}>
                                {item.text}
                            </TableCell>
                            <TotalMoneyOfStatusByMonth months={item.total} />
                        </ReorderTableRow>
                    ))}
                </DndProvider>
            )}
        </TableBody>
    );
};

export default MonthlyProductionPerformanceTBody;
