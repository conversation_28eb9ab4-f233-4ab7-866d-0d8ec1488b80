// material-ui
import { TableCell, TableHead, TableRow, useMediaQuery, useTheme } from '@mui/material';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// third party
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const OnGoingThead = () => {
    const { onGoingPermission } = PERMISSIONS.sale.salePipeline;
    const theme = useTheme();
    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                {checkAllowedPermission(onGoingPermission.delete) || checkAllowedPermission(onGoingPermission.edit) ? (
                    <TableCell align="center" sx={{ position: 'sticky', left: 0, zIndex: 3, backgroundColor: 'white' }}>
                        <FormattedMessage id={salesReport.salesOnGoing + '-action'} />
                    </TableCell>
                ) : (
                    <></>
                )}
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-contract-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-service-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-project-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-probability'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-sale-pipeline-status'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="revenue" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-size-vnd'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-size-usd'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-management-revenue-allocated'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-accountant-revenue-allocatedVND'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesOnGoing + '-license-fee'} />
                </TableCell>
                <TableCell sx={{ position: 'sticky', right: !!matches ? 0 : 'unset' }}>
                    <FormattedMessage id={salesReport.salesOnGoing + '-time-duration'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default OnGoingThead;
