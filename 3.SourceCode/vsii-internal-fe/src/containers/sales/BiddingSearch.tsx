// material-ui
import { Box, Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { FormProvider, Input, Label } from 'components/extended/Form';
import { FormattedMessage, useIntl } from 'react-intl';
import { SalesYear, SalePipelineType, BiddingStatus } from '../search';
import { IBiddingFilterConfig, biddingFilterConfig, biddingFilterShemcha } from 'pages/sales/Config';
import { E_SCREEN_SALES_YEAR, SEARCH_MODE } from 'constants/Common';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

interface IBiddingSearchProps {
    formReset: IBiddingFilterConfig;
    handleSearch: (value: any) => void;
    isRefresh?: boolean;
}

const BiddingSearch = (props: IBiddingSearchProps) => {
    const { formReset, handleSearch, isRefresh } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    const intl = useIntl();
    const methods = useForm({
        defaultValues: biddingFilterConfig,
        resolver: yupResolver(biddingFilterShemcha)
    });

    return (
        <FormProvider formReturn={methods} onSubmit={handleSearch} formReset={formReset}>
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2}>
                    <SalePipelineType isShowAll label={salesReport.allSalesPineline + '-type'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <SalesYear screen={E_SCREEN_SALES_YEAR.BIDDING} isRefresh={isRefresh} label={salesReport.allSalesPineline + '-year'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <BiddingStatus label={salesReport.allSalesPineline + '-status'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Input
                        name="projectName"
                        label={<FormattedMessage id={salesReport.allSalesPineline + '-project-name'} />}
                        placeholder={intl.formatMessage({ id: salesReport.allSalesPineline + '-project-name' })}
                        textFieldProps={{
                            InputProps: {
                                startAdornment: (
                                    <Box sx={{ mr: 1 }}>
                                        <select id="cars" {...methods.register('searchModeOfProject')}>
                                            {SEARCH_MODE.map((item) => (
                                                <option value={item.value}>{item.label}</option>
                                            ))}
                                        </select>
                                    </Box>
                                )
                            }
                        }}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Input
                        name="customer"
                        label={<FormattedMessage id={salesReport.allSalesPineline + '-customer'} />}
                        placeholder={intl.formatMessage({ id: salesReport.allSalesPineline + '-customer' })}
                        textFieldProps={{
                            InputProps: {
                                startAdornment: (
                                    <Box sx={{ mr: 1 }}>
                                        <select id="customerSearchMode" {...methods.register('searchModeOfCustomer')}>
                                            {SEARCH_MODE.map((item) => (
                                                <option value={item.value}>{item.label}</option>
                                            ))}
                                        </select>
                                    </Box>
                                )
                            }
                        }}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.allSalesPineline + '-search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </FormProvider>
    );
};

export default BiddingSearch;
