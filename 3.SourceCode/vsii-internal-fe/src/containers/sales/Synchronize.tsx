// Third party
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';

// material-ui
import { Button, DialogActions, Grid, Stack } from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project import
import { synchronizeFormDefault, synchronizeSchema } from 'pages/sales/Config';
import { FormProvider, Input } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';

interface ISynchronizeProps {
    open: boolean;
    handleClose?: () => void;
    loading?: boolean;
    handleSynchronize: (token: string) => void;
}

const Synchronize = (props: ISynchronizeProps) => {
    const { open, handleClose, loading, handleSynchronize } = props;

    const handleSubmit = (value: any) => {
        handleSynchronize(value.token);
    };

    return (
        <Modal isOpen={open} title="synchronize" onClose={handleClose} keepMounted={false} maxWidth="xs">
            <FormProvider
                form={{ defaultValues: synchronizeFormDefault, resolver: yupResolver(synchronizeSchema) }}
                onSubmit={handleSubmit}
            >
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12}>
                        <Input name="token" label="Token" textFieldProps={{ multiline: true, rows: 5 }} required />
                    </Grid>
                    <Grid item xs={12}>
                        <DialogActions>
                            <Stack direction="row" spacing={1} justifyContent="flex-end">
                                <Button color="error" onClick={handleClose}>
                                    <FormattedMessage id="cancel" />
                                </Button>
                                <LoadingButton loading={loading} variant="contained" type="submit">
                                    <FormattedMessage id="submit" />
                                </LoadingButton>
                            </Stack>
                        </DialogActions>
                    </Grid>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default Synchronize;
