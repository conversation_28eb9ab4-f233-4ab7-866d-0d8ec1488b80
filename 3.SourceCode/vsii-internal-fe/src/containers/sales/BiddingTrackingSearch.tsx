import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid } from '@mui/material';

// project imports
import { IMonitorBiddingPackagesFilterConfig, monitorBiddingPackagesFilterConfig, biddingPackageShemcha } from 'pages/sales/Config';
import { Address, BiddingPackageName, SearchForm, Type } from '../search';
import { Label } from 'components/extended/Form';
import { Button } from 'components';

interface IBiddingTrackingSearchProps {
    formReset: IMonitorBiddingPackagesFilterConfig;
    handleSearch: (value: any) => void;
}

const BiddingTrackingSearch = (props: IBiddingTrackingSearchProps) => {
    const { formReset, handleSearch } = props;

    return (
        <SearchForm
            defaultValues={monitorBiddingPackagesFilterConfig}
            formSchema={biddingPackageShemcha}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Type />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <BiddingPackageName />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Address />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id="search" />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default BiddingTrackingSearch;
