import { Fragment } from 'react';

// third party
import { FormattedMessage, useIntl } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow, Box } from '@mui/material';

// project imports
import { HC_ON_GOING_MONTHS_OF_YEAR } from 'constants/Common';
import { IMonthlyHCList } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IOnGoingTheadProps {
    hcInfo?: IMonthlyHCList[];
}

const OnGoingHCThead = (props: IOnGoingTheadProps) => {
    const { hcInfo } = props;
    const intl = useIntl();

    return (
        <TableHead>
            <TableRow
                sx={{
                    '& th': {
                        whiteSpace: 'nowrap'
                    }
                }}
            >
                {hcInfo?.map((item: IMonthlyHCList, key) => (
                    <Fragment key={key}>
                        <Box>
                            <TableCell>{intl.formatMessage({ id: HC_ON_GOING_MONTHS_OF_YEAR[item?.month] })}</TableCell>
                        </Box>
                        <TableCell>
                            <FormattedMessage id={TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-billable-day'} />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage id={TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-billable'} />
                        </TableCell>
                    </Fragment>
                ))}
            </TableRow>
        </TableHead>
    );
};

export default OnGoingHCThead;
