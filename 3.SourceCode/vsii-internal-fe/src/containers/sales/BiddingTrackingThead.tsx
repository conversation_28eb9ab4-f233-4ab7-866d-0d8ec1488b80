import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const BiddingTrackingThead = () => {
    const { salesReport } = TEXT_CONFIG_SCREEN;
    return (
        <TableHead>
            <TableRow>
                <TableCell sx={{ width: '3%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-no'} />
                </TableCell>
                <TableCell sx={{ width: '6%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-type'} />
                </TableCell>
                <TableCell sx={{ width: '7%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-KHLCNT-number'} />
                </TableCell>
                <TableCell sx={{ width: '20%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-bidding-package-name'} />
                </TableCell>
                <TableCell sx={{ width: '7%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-date-posting'} />
                </TableCell>
                <TableCell sx={{ width: '7%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-time-bidding-closing'} />
                </TableCell>
                <TableCell sx={{ width: '6%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-form-bidding-participation'} />
                </TableCell>
                <TableCell sx={{ width: '7%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-TBMT-number'} />
                </TableCell>
                <TableCell sx={{ width: '6%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-group'} />
                </TableCell>
                <TableCell sx={{ width: '11%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-company'} />
                </TableCell>
                <TableCell sx={{ width: '8%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-address'} />
                </TableCell>
                <TableCell sx={{ width: '7%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-keyword'} />
                </TableCell>
                <TableCell sx={{ width: '5%', px: '6px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'tracking-link'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default BiddingTrackingThead;
