// react
import React from 'react';
import { FormattedMessage } from 'react-intl';
import { useFormContext } from 'react-hook-form';

// material-ui
import { TableCell, TableBody, Stack, Tooltip, IconButton, TableRow } from '@mui/material';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

// project imports
import { Input, NumericFormatCustom } from 'components/extended/Form';
import { formatPrice } from 'utils/common';
import { E_BIDDING_STATUS } from 'constants/Common';

interface IBiddingHCTBodyProps {
    monthlyHCList?: any;
    handleOpen: () => void;
    status?: string;
}

const BiddingHCTBody = (props: IBiddingHCTBodyProps) => {
    const { monthlyHCList, handleOpen, status } = props;
    const { watch } = useFormContext();
    const contractDurationFrom = watch('project.contractDurationFrom');
    const contractDurationTo = watch('project.contractDurationTo');

    return (
        <TableBody>
            <TableRow>
                {contractDurationFrom && contractDurationTo ? (
                    <TableCell sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: 'white' }}>
                        <Stack direction="row" justifyContent="center" alignItems="center">
                            <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleOpen()}>
                                <IconButton aria-label="edit" size="small">
                                    <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                </IconButton>
                            </Tooltip>
                        </Stack>
                    </TableCell>
                ) : (
                    <></>
                )}

                {monthlyHCList?.map((field: any, index: number) => (
                    <React.Fragment key={field.id}>
                        <TableCell
                            sx={{
                                '& .MuiFormControl-root': {
                                    width: '100px'
                                }
                            }}
                        >
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    },
                                    defaultValue: field.hcMonthly
                                }}
                                name={`hcInfo.monthlyHCList.${index}.hcMonthly`}
                                disabled={status === E_BIDDING_STATUS.CONTRACT}
                            />
                        </TableCell>
                        <TableCell>{formatPrice(monthlyHCList[index].billableDay)}</TableCell>
                        <TableCell>{formatPrice(monthlyHCList[index].billable)}</TableCell>
                    </React.Fragment>
                ))}
            </TableRow>
        </TableBody>
    );
};

export default BiddingHCTBody;
