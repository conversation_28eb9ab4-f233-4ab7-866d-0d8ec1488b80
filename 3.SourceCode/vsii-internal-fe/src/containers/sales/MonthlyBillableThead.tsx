import { FormattedMessage } from 'react-intl';
import { TableCell, TableHead, TableRow, styled } from '@mui/material';

interface IMonthlyBillableTheadProps {
    lenght: number;
}

const RedAsterisk = styled('span')({
    color: 'red',
    paddingLeft: '4px'
});
const MonthlyBillableThead = (props: IMonthlyBillableTheadProps) => {
    const { lenght } = props;

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center">
                    <FormattedMessage id="no" />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id="role" />
                    {lenght > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id="rate" />
                    {lenght > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id="rate-vnd" />
                    {lenght > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id="quantity" />
                    {lenght > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id="action" />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default MonthlyBillableThead;
