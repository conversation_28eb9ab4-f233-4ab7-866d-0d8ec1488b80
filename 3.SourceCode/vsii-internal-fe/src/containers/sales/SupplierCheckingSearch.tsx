// materia-ui
import { Grid } from '@mui/material';

// third-party
import { FormattedMessage } from 'react-intl';

// project imports
import { Button } from 'components';
import { DatePicker, Label } from 'components/extended/Form';
import { Name, SearchForm, Member } from '../search';
import { ISalesLeadFilterConfig, salesLeadFilterConfig, salesLeadFilterShemcha } from 'pages/sales/Config';
import { searchFormConfig } from 'containers/search/Config';
import { E_IS_LOGTIME, TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ISupplierCheckingSearchProps {
    handleSearch: (value: any) => void;
    formReset: ISalesLeadFilterConfig;
}

const SupplierCheckingSearch = (props: ISupplierCheckingSearchProps) => {
    const { handleSearch, formReset } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;
    return (
        <SearchForm
            formReset={formReset}
            defaultValues={salesLeadFilterConfig}
            formSchema={salesLeadFilterShemcha}
            handleSubmit={handleSearch}
        >
            <Grid container alignItems="center" justifyContent="space-beetween" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Name name={searchFormConfig.supplierName.name} label={salesReport.salesLead + 'supplier-supplier-name'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <DatePicker name="fromDate" label={<FormattedMessage id={salesReport.salesLead + 'supplier-from-date'} />} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <DatePicker name="toDate" label={<FormattedMessage id={salesReport.salesLead + 'supplier-to-date'} />} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Member
                        isLogTime={E_IS_LOGTIME.YES}
                        name="picUserName"
                        label={<FormattedMessage id={salesReport.salesLead + 'supplier-pic-user-name'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.salesLead + 'supplier-search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default SupplierCheckingSearch;
