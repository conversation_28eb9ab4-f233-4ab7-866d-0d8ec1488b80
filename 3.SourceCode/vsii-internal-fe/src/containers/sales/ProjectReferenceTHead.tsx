// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

const ProjectReferenceTHead = () => {
    const { salesReport } = TEXT_CONFIG_SCREEN;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'project-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'client'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'technology'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'domain'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'project-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'effort'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'project-manager'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'project-description'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.projectReference + 'to-date'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default ProjectReferenceTHead;
