import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow, Typography } from '@mui/material';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const BudgetingPlanThead = () => {
    const { budgetingPermission } = PERMISSIONS.sale.salePipeline;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <Typography sx={{ width: '50px' }}>
                        <FormattedMessage id={salesReport.budgetingPlan + '-type'} />
                    </Typography>
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-project-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-service-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-of-months'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-contracted-value'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-license-fee-budgeting'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-effort-limit-man-hours'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-cost-limit-VND'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-project-set-revenue-VND'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-estimated-KPI-project-saving-cost'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-estimated-share-company-profit'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.budgetingPlan + '-estimated-total-KPI-bonus'} />
                </TableCell>
                {checkAllowedPermission(budgetingPermission.edit) && (
                    <TableCell>
                        <FormattedMessage id={salesReport.budgetingPlan + '-action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default BudgetingPlanThead;
