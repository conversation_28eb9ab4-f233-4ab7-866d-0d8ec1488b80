import React, { useState } from 'react';
import { Box, ButtonBase, Grid, ListItem, ListItemText, Tooltip, Typography, useTheme } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import PinchIcon from '@mui/icons-material/Pinch';
import DoneIcon from '@mui/icons-material/Done';
import { FormattedMessage } from 'react-intl';

import SkeletonSummaryCard from 'components/cards/Skeleton/SummaryCard';
// eslint-disable-next-line import/no-extraneous-dependencies
import MainCard from 'components/cards/MainCard';
import { gridSpacing } from 'store/constant';
import { formatPrice } from 'utils/common';
import { ISalesTotal } from 'types';

// eslint-disable-next-line import/no-extraneous-dependencies
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

interface IOnGoingTotalProps {
    loading: boolean;
    totalBidding: ISalesTotal[];
    handleConFirmEdit?: (list: ISalesTotal[]) => void;
    setIsEdited: React.Dispatch<React.SetStateAction<boolean>>;
    isEdited: boolean;
}

const OnGoingTotal = (props: IOnGoingTotalProps) => {
    const { loading, totalBidding, handleConFirmEdit, setIsEdited, isEdited } = props;
    const theme = useTheme();
    const { biddingPermission } = PERMISSIONS.sale.salePipeline;

    const [showButton, setShowButton] = useState(false);

    const handleMouseEnter = () => setShowButton(true);
    const handleMouseLeave = () => setShowButton(false);

    const taskStatus = {
        col1: {
            items: totalBidding.filter((item) => item.index % 4 === 1)
        },
        col2: {
            items: totalBidding.filter((item) => item.index % 4 === 2)
        },
        col3: {
            items: totalBidding.filter((item) => item.index % 4 === 3)
        },
        col4: {
            items: totalBidding.filter((item) => item.index % 4 === 0)
        }
    };

    const onDragEnd = (result: any, columns: any, setColumns: any) => {
        if (!result.destination) return;
        const { source, destination } = result;

        if (source.droppableId !== destination.droppableId) {
            const sourceColumn = columns[source.droppableId];
            const destColumn = columns[destination.droppableId];
            const sourceItems = [...sourceColumn.items];
            const destItems = [...destColumn.items];
            const [removed] = sourceItems.splice(source.index, 1);
            destItems.splice(destination.index, 0, removed);
            setColumns({
                ...columns,
                [source.droppableId]: {
                    ...sourceColumn,
                    items: sourceItems
                },
                [destination.droppableId]: {
                    ...destColumn,
                    items: destItems
                }
            });
        } else {
            const column = columns[source.droppableId];
            const copiedItems = [...column.items];
            const [removed] = copiedItems.splice(source.index, 1);
            copiedItems.splice(destination.index, 0, removed);
            setColumns({
                ...columns,
                [source.droppableId]: {
                    ...column,
                    items: copiedItems
                }
            });
        }
    };

    const [columns, setColumns] = useState(taskStatus);

    return loading ? (
        <SkeletonSummaryCard />
    ) : (
        <MainCard sx={{ marginBottom: theme.spacing(gridSpacing) }} contentSX={{ paddingTop: 1 }}>
            {isEdited ? (
                <Box>
                    <Box display="flex" gap={2} py={0.8}>
                        <Tooltip title={<FormattedMessage id="cancel"></FormattedMessage>}>
                            <ButtonBase
                                onClick={() => {
                                    setIsEdited(false);
                                }}
                            >
                                <HighlightOffIcon sx={{ fontSize: 18 }} />
                            </ButtonBase>
                        </Tooltip>
                        <Tooltip title={<FormattedMessage id="confirm"></FormattedMessage>}>
                            <ButtonBase
                                onClick={() => {
                                    handleConFirmEdit?.([
                                        ...columns.col1.items.map((item, key) => ({ ...item, index: key * 4 + 1 })),
                                        ...columns.col2.items.map((item, key) => ({ ...item, index: key * 4 + 2 })),
                                        ...columns.col3.items.map((item, key) => ({ ...item, index: key * 4 + 3 })),
                                        ...columns.col4.items.map((item, key) => ({ ...item, index: key * 4 + 4 }))
                                    ]);
                                }}
                            >
                                <DoneIcon sx={{ fontSize: 18 }} />
                            </ButtonBase>
                        </Tooltip>
                    </Box>
                    <Grid container spacing={1} sx={{ display: 'flex', justifyContent: 'center', height: '100%' }}>
                        <DragDropContext onDragEnd={(result) => onDragEnd(result, columns, setColumns)}>
                            {Object.entries(columns).map(([columnId, column], index) => {
                                return (
                                    <Grid
                                        item
                                        xs={3}
                                        sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center'
                                        }}
                                        key={columnId}
                                    >
                                        <Box sx={{ width: '100%' }}>
                                            <Droppable droppableId={columnId} key={columnId}>
                                                {(provided, snapshot) => {
                                                    return (
                                                        <Box
                                                            {...provided.droppableProps}
                                                            ref={provided.innerRef}
                                                            style={{
                                                                background: snapshot.isDraggingOver ? 'lightblue' : '#ffffff'
                                                            }}
                                                        >
                                                            {column.items.map((item, index) => {
                                                                return (
                                                                    <Draggable key={item.id} draggableId={item.id as string} index={index}>
                                                                        {(provided, snapshot) => {
                                                                            return (
                                                                                <ListItem
                                                                                    ref={provided.innerRef}
                                                                                    {...provided.draggableProps}
                                                                                    {...provided.dragHandleProps}
                                                                                    sx={{
                                                                                        ...item.style,
                                                                                        mb: 1,
                                                                                        borderRadius: 2,
                                                                                        border: 0.1
                                                                                    }}
                                                                                    secondaryAction={
                                                                                        <Typography>
                                                                                            {typeof item?.total === 'number'
                                                                                                ? formatPrice(Math.round(item?.total))
                                                                                                : ''}
                                                                                        </Typography>
                                                                                    }
                                                                                >
                                                                                    <ListItemText disableTypography sx={item.style}>
                                                                                        {item.text}
                                                                                    </ListItemText>
                                                                                </ListItem>
                                                                            );
                                                                        }}
                                                                    </Draggable>
                                                                );
                                                            })}
                                                            {provided.placeholder}
                                                        </Box>
                                                    );
                                                }}
                                            </Droppable>
                                        </Box>
                                    </Grid>
                                );
                            })}
                        </DragDropContext>
                    </Grid>
                </Box>
            ) : (
                <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
                    <Box height={20}>
                        {showButton && checkAllowedPermission(biddingPermission.editRows) && (
                            <Tooltip title={<FormattedMessage id="edit arrangement"></FormattedMessage>}>
                                <ButtonBase
                                    onClick={() => {
                                        setIsEdited(true);
                                        setColumns(taskStatus);
                                    }}
                                >
                                    <PinchIcon sx={{ fontSize: 20 }} />
                                </ButtonBase>
                            </Tooltip>
                        )}
                    </Box>

                    <Grid
                        container
                        sx={{
                            '& .MuiListItem-root': {
                                paddingTop: '1px !important',
                                paddingBottom: '1px !important',
                                paddingRight: '16px',
                                '& .MuiListItemSecondaryAction-root': {
                                    position: 'unset',
                                    transform: 'unset'
                                }
                            },
                            '& .MuiListItemSecondaryAction-root .MuiTypography-root': {
                                fontWeight: '700 !important'
                            }
                        }}
                    >
                        <Grid
                            item
                            xs={12}
                            lg={3}
                            direction="row"
                            wrap="wrap"
                            sx={{
                                borderBottom: { xs: '1px solid #D5D5D5', lg: 'none' },
                                borderRight: { xs: 'none', lg: '1px solid #D5D5D5' },
                                padding: 0.25
                            }}
                        >
                            {totalBidding.map((item, index) => (
                                <>
                                    {item.index % 4 === 1 && (
                                        <Tooltip
                                            title={
                                                <Typography sx={{ whiteSpace: 'pre-line', fontSize: 11 }}>{item.titleRecipe}</Typography>
                                            }
                                        >
                                            <ListItem
                                                sx={{
                                                    ...item.style,
                                                    borderRadius: 2,
                                                    mb: 1
                                                }}
                                                secondaryAction={
                                                    <Typography>
                                                        {typeof item?.total === 'number' ? formatPrice(Math.round(item?.total)) : ''}
                                                    </Typography>
                                                }
                                            >
                                                <ListItemText disableTypography sx={item.style}>
                                                    {item.text}
                                                </ListItemText>
                                            </ListItem>
                                        </Tooltip>
                                    )}
                                </>
                            ))}
                        </Grid>

                        <Grid
                            item
                            xs={12}
                            lg={3}
                            direction="row"
                            wrap="wrap"
                            sx={{
                                borderBottom: { xs: '1px solid #D5D5D5', lg: 'none' },
                                borderRight: { xs: 'none', lg: '1px solid #D5D5D5' },
                                padding: 0.25
                            }}
                        >
                            {totalBidding.map((item, index) => (
                                <>
                                    {item.index % 4 === 2 && (
                                        <Tooltip
                                            title={
                                                <Typography sx={{ whiteSpace: 'pre-line', fontSize: 11 }}>{item.titleRecipe}</Typography>
                                            }
                                        >
                                            <ListItem
                                                sx={{
                                                    ...item.style,
                                                    borderRadius: 2,
                                                    mb: 1
                                                }}
                                                secondaryAction={
                                                    <Typography>
                                                        {typeof item?.total === 'number' ? formatPrice(Math.round(item?.total)) : ''}
                                                    </Typography>
                                                }
                                            >
                                                <ListItemText disableTypography sx={item.style}>
                                                    {item.text}
                                                </ListItemText>
                                            </ListItem>
                                        </Tooltip>
                                    )}
                                </>
                            ))}
                        </Grid>

                        <Grid
                            item
                            xs={12}
                            lg={3}
                            direction="row"
                            wrap="wrap"
                            sx={{
                                borderBottom: { xs: '1px solid #D5D5D5', lg: 'none' },
                                borderRight: { xs: 'none', lg: '1px solid #D5D5D5' },
                                padding: 0.25
                            }}
                        >
                            {totalBidding.map((item, index) => (
                                <>
                                    {item.index % 4 === 3 && (
                                        <Tooltip
                                            title={
                                                <Typography sx={{ whiteSpace: 'pre-line', fontSize: 11 }}>{item.titleRecipe}</Typography>
                                            }
                                        >
                                            <ListItem
                                                sx={{
                                                    ...item.style,
                                                    borderRadius: 2,
                                                    mb: 1
                                                }}
                                                secondaryAction={
                                                    <Typography>
                                                        {typeof item?.total === 'number' ? formatPrice(Math.round(item?.total)) : ''}
                                                    </Typography>
                                                }
                                            >
                                                <ListItemText disableTypography sx={item.style}>
                                                    {item.text}
                                                </ListItemText>
                                            </ListItem>
                                        </Tooltip>
                                    )}
                                </>
                            ))}
                        </Grid>

                        <Grid
                            item
                            xs={12}
                            lg={3}
                            direction="row"
                            wrap="wrap"
                            sx={{
                                borderBottom: { xs: '1px solid #D5D5D5', lg: 'none' },
                                borderRight: { xs: 'none', lg: '1px solid #D5D5D5' },
                                padding: 0.25
                            }}
                        >
                            {totalBidding.map((item, index) => (
                                <>
                                    {item.index % 4 === 0 && (
                                        <Tooltip
                                            title={
                                                <Typography sx={{ whiteSpace: 'pre-line', fontSize: 11 }}>{item.titleRecipe}</Typography>
                                            }
                                        >
                                            <ListItem
                                                sx={{
                                                    ...item.style,
                                                    borderRadius: 2,
                                                    mb: 1
                                                }}
                                                secondaryAction={
                                                    <Typography>
                                                        {typeof item?.total === 'number' ? formatPrice(Math.round(item?.total)) : ''}
                                                    </Typography>
                                                }
                                            >
                                                <ListItemText disableTypography sx={item.style}>
                                                    {item.text}
                                                </ListItemText>
                                            </ListItem>
                                        </Tooltip>
                                    )}
                                </>
                            ))}
                        </Grid>
                    </Grid>
                </div>
            )}
        </MainCard>
    );
};

export default OnGoingTotal;
