// material-ui
import { Grid } from '@mui/material';

// project imports
import { Input } from 'components/extended/Form';
import { E_IS_LOGTIME, E_BIDDING_STATUS } from 'constants/Common';
import { Member } from 'containers/search';

// third party
import { FormattedMessage } from 'react-intl';
import { IMember } from 'types/member';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditBiddingOtherInfo {
    handleChangeUserContact: (userSelected: IMember) => void;
    status?: string;
}

const AddOrEditBiddingOtherInfo = (props: IAddOrEditBiddingOtherInfo) => {
    const { handleChangeUserContact, status } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} lg={6}>
                <Member
                    isLogTime={E_IS_LOGTIME.YES}
                    isDefaultAll
                    name="otherInfo.contact"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contact'} />}
                    isIdHexString
                    handleChange={handleChangeUserContact}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-phone-number'} />}
                    name="otherInfo.phoneNumber"
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-presale-folder'} />}
                    name="otherInfo.presaleFolder"
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-email-address'} />}
                    name="otherInfo.emailAddress"
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-customer-contact'} />} // thieu
                    name="otherInfo.customerContact"
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
        </Grid>
    );
};

export default AddOrEditBiddingOtherInfo;
