// material-ui
import { Grid, SelectChangeEvent } from '@mui/material';

// project imports
import {
    IMonthlyProductionPerformanceFilterConfig,
    monthlyProductionPerformanceFilterConfig,
    monthlyProductionPerformanceFilterSchema
} from 'pages/sales/Config';
import { SearchForm, SalesYear } from '../search';
import { E_SCREEN_SALES_YEAR } from 'constants/Common';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ISummarySearchProps {
    conditions: IMonthlyProductionPerformanceFilterConfig;
    handleChangeYear: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
    handleSearch: (value: IMonthlyProductionPerformanceFilterConfig) => void;
}

const SummarySearch = (props: ISummarySearchProps) => {
    const { conditions, handleChangeYear, handleSearch } = props;

    const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = { year: Number(e.target.value) };
        handleChangeYear(e);
        handleSearch(value);
    };

    return (
        <SearchForm
            defaultValues={monthlyProductionPerformanceFilterConfig}
            formSchema={monthlyProductionPerformanceFilterSchema}
            handleSubmit={handleSearch}
            formReset={conditions}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2}>
                    <SalesYear
                        handleChangeYear={handleYearChange}
                        screen={E_SCREEN_SALES_YEAR.SALES_PIPELINE_SUMMARY}
                        label={TEXT_CONFIG_SCREEN.salesReport.summary + '-year'}
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default SummarySearch;
