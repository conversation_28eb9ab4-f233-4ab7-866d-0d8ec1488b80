import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { BudgetingPlanServiceType, BudgetingPlanType, SalesYear, SearchForm } from 'containers/search';
import { IBudgetingPlanSearch, budgetingPlanSearchConfig, budgetingPlanSearchSchema } from 'pages/sales/Config';
import { E_SCREEN_SALES_YEAR } from 'constants/Common';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// third party
interface BudgetingPlanSearchProps {
    formReset: IBudgetingPlanSearch;
    handleSearch: (value: IBudgetingPlanSearch) => void;
}

const BudgetingPlanSearch = (props: BudgetingPlanSearchProps) => {
    const { formReset, handleSearch } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={budgetingPlanSearchConfig}
            formSchema={budgetingPlanSearchSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <SalesYear screen={E_SCREEN_SALES_YEAR.BUDGETING_PLAN} label={salesReport.budgetingPlan + '-year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <BudgetingPlanServiceType isShowAll label={salesReport.budgetingPlan + '-service-type'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <BudgetingPlanType isShowAll label={salesReport.budgetingPlan + '-type'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.budgetingPlan + '-search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default BudgetingPlanSearch;
