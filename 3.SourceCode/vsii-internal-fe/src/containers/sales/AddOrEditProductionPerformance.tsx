/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';

// third-party
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
// material-ui
import {
    Button,
    CircularProgress,
    DialogActions,
    Grid,
    IconButton,
    SelectChangeEvent,
    Stack,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Tooltip
} from '@mui/material';

// project imports
import { LoadingButton } from '@mui/lab';
import { useAppDispatch } from 'app/hooks';
import { AddCircleOutlineIcon } from 'assets/images/icons';
import { DatePicker, FormProvider, Input, Label, NumericFormatCustom } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { Table } from 'components/extended/Table';
import { CONTRACT_TYPE_SALE_REPORT, DEPARTMENTS, UNIT_SALE_REPORT } from 'constants/Common';
import { ContractType, Currency, Months, PaymentTerm, ProductivityType } from 'containers/search';
import Unit from 'containers/search/Unit';
import moment from 'moment';
import { HcInfoDefault, productionPerformanceAddOrEditFormSchema } from 'pages/sales/Config';
import { useFieldArray, useForm } from 'react-hook-form';
import { gridSpacing } from 'store/constant';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { IHcInfos, IMonthlyProductionPerformanceAddOrEditForm, IOption } from 'types';
import { customFormatPrice, getReceivableByMonth, isEmpty } from 'utils/common';
import { calculateMonthsDifference, convertMonthFromToDate, dateFormat } from 'utils/date';
import SaleTableTBody from './SaleTableTBody';
import SaleTableTHead from './SaleTableTHead';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// ==============================|| ADD OR EDIT PRODUCTION PERFORMANCE ||============================== //
interface IAddOrEditProductionPerformanceProps {
    months: IOption[];
    loading: boolean;
    open: boolean;
    handleClose: () => void;
    editProductivity: (payload: IMonthlyProductionPerformanceAddOrEditForm) => void;
    onChangeYearGetStandardWorkingDay: (
        payload: IMonthlyProductionPerformanceAddOrEditForm,
        startDate: string,
        endDate: string,
        month?: number,
        paymentTerm?: any
    ) => void;
    productivity: IMonthlyProductionPerformanceAddOrEditForm;
    loadingData: boolean;
}
const AddOrEditProductionPerformance = (props: IAddOrEditProductionPerformanceProps) => {
    // props, state, variables
    const { months, open, loading, handleClose, productivity, editProductivity, onChangeYearGetStandardWorkingDay, loadingData } = props;
    const dispatch = useAppDispatch();
    const [contractTypeValue, setContractTypeValue] = useState<string>('');
    const [totalAmount, setTotalAmount] = useState<number>(0);

    const { salesReport } = TEXT_CONFIG_SCREEN;

    // useForm
    const methods = useForm<any>({
        resolver: yupResolver(productionPerformanceAddOrEditFormSchema),
        mode: 'all'
    });
    const { watch, setValue, getValues } = methods;

    const {
        fields: hcInfoValues,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'hcInfo'
    });

    // add hc info
    const handleAddHcInfo = () => {
        append(HcInfoDefault);
        setTotalAmount(0);
    };

    const handleRemoveHcInfo = (index: number) => {
        let total = 0;
        const hcInfoDelete = getValues().hcInfo;
        hcInfoDelete.forEach((item: IHcInfos) => {
            total += +item.amount;
        });
        setTotalAmount(total);
        remove(index);
    };

    const calculateTotalAmount = (hcInfos: IHcInfos[]) => {
        let total = 0;
        hcInfos &&
            hcInfos.length > 0 &&
            hcInfos.forEach((item: IHcInfos) => {
                total += +item.amount;
            });
        return total;
    };

    const estimate = () => {
        const { contractSize, contractAllocation } = getValues();
        const totalAmount = calculateTotalAmount(getValues()?.hcInfo);
        const { month, paymentTerm } = getValues();
        let receivableValue: any = 0;

        if (month > paymentTerm) {
            receivableValue = getReceivableByMonth(productivity.productivity ? productivity.productivity : [], month - paymentTerm);
        } else if (month < paymentTerm) {
            const count = paymentTerm - month;
            receivableValue = getReceivableByMonth(productivity.lastYearProductivity ? productivity.lastYearProductivity : [], 12 - count);
        } else if (month === paymentTerm) {
            receivableValue = getReceivableByMonth(productivity.lastYearProductivity ? productivity.lastYearProductivity : [], 12);
        }

        if (contractTypeValue === CONTRACT_TYPE_SALE_REPORT.FIXED_COST) {
            setValue('delivered', contractAllocation === 0 ? 0 : +contractSize / +contractAllocation);
        } else {
            setValue('financial', totalAmount);
            setValue('delivered', totalAmount);
            setValue('receivable', receivableValue);
            setValue('received', receivableValue);
        }
    };

    // submit
    const handleSubmit = (values: IMonthlyProductionPerformanceAddOrEditForm) => {
        const projectId = values.projectId?.value;
        const payload = {
            ...values,
            projectId,
            duration: {
                fromDate: dateFormat(values.duration.fromDate),
                toDate: dateFormat(values.duration.toDate)
            },
            projectName: values.projectId ? values.projectId.label : values.projectName
        };
        const formattedFromDate = moment(dateFormat(values.duration.fromDate), 'DD/MM/YYYY');
        const formattedToDate = moment(dateFormat(values.duration.toDate), 'DD/MM/YYYY');
        const getMonth = months.filter((month) => {
            return month.value === +values.month;
        });
        const { toDate } = convertMonthFromToDate(getMonth[0].label);
        const toDateOfMonth = moment(toDate, 'DD/MM/YYYY');

        if (toDateOfMonth.isBetween(formattedFromDate, formattedToDate, 'month', '[]')) {
            editProductivity(payload);
        } else {
            dispatch(openSnackbar({ open: true, message: 'error-month-update', variant: 'alert', alert: { color: 'error' } }));
        }
    };

    // chọn tháng
    const handleChangeMonth = (value: string) => {
        const getMonth = months.filter((month) => {
            return month.value === value;
        });
        const { fromDate, toDate } = convertMonthFromToDate(getMonth[0].label);
        onChangeYearGetStandardWorkingDay(getValues(), fromDate, toDate, +value);
    };

    // chọn payment term
    const handleChangePaymentTerm = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const paymentTerm = e.target.value;
        const getMonth = months.filter((month) => {
            return month.value === getValues().month;
        });

        const { fromDate, toDate } = convertMonthFromToDate(getMonth[0].label);
        onChangeYearGetStandardWorkingDay(getValues(), fromDate, toDate, +getValues().month, paymentTerm);
    };

    // Change Unit
    const handleChangeUnit = () => {
        setValue('hcInfo', []);
    };

    // useEffect
    useEffect(() => {
        const totalAmount = calculateTotalAmount(productivity?.hcInfo);
        setTotalAmount(totalAmount);
        methods.reset({ ...productivity } as IMonthlyProductionPerformanceAddOrEditForm);
    }, [productivity]);

    useEffect(() => {
        const { unsubscribe } = watch((value, info) => {
            setContractTypeValue(value?.contractType!);
            const {
                standardWorkingDay,
                deliveredCurrency,
                receivableCurrency,
                receivedCurrency,
                financialCurrency,
                exchangeRate,
                receivable
            } = getValues();

            if (info.name?.endsWith('contractType')) {
                setValue('originalContractSize', '');
                setValue('contractSize', '');
                setValue('delivered', 0);
                setValue('receivable', 0);
                setValue('received', 0);
                setValue('financial', 0);
            }

            if (value.contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST) {
                if (info.name?.endsWith('currency') || info.name?.endsWith('originalContractSize')) {
                    setValue('contractSize', +value.originalContractSize / +value.exchangeRate);
                }
            }

            if ((info.name?.startsWith('hcInfo') && info.name?.endsWith('rate')) || info.name?.endsWith('currency')) {
                const index = parseInt(info.name.split('.')[1]);
                const rate = Number(value.hcInfo?.at(index)?.rate);
                if (rate) {
                    if (value.currency === 'USD') {
                        setValue(`hcInfo.${index}.rateUSD`, rate);
                    } else {
                        setValue(`hcInfo.${index}.rateUSD`, rate / +value.exchangeRate);
                    }
                } else {
                    setValue(`hcInfo.${index}.rateUSD`, 0);
                }
            }

            if (info.name?.endsWith('currency')) {
                setValue('hcInfo', []);
            }

            if (info.name?.startsWith('hcInfo') && (info.name?.endsWith('rateUSD') || info.name?.endsWith('quantity'))) {
                const index = parseInt(info.name.split('.')[1]);
                const rateUSD = Number(value.hcInfo?.at(index)?.rateUSD);
                const quantity = Number(value.hcInfo?.at(index)?.quantity);
                if (rateUSD && quantity) {
                    if (value.unit === UNIT_SALE_REPORT.MAN_MONTH) {
                        setValue(`hcInfo.${index}.amount`, rateUSD * quantity);
                    } else if (value.unit === UNIT_SALE_REPORT.MAN_DAY) {
                        setValue(`hcInfo.${index}.amount`, rateUSD * quantity * standardWorkingDay);
                    } else if (value.unit === UNIT_SALE_REPORT.MAN_HOUR) {
                        setValue(`hcInfo.${index}.amount`, rateUSD * quantity * standardWorkingDay * 8);
                    }
                }
            }

            if (info.name?.startsWith('hcInfo') && info.name?.endsWith('amount')) {
                const totalAmount = calculateTotalAmount(value?.hcInfo);
                setTotalAmount(totalAmount);
            }

            if (info.name?.endsWith('deliveredCurrency')) {
                setValue('delivered', deliveredCurrency / exchangeRate);
            }

            if (info.name?.endsWith('receivableCurrency')) {
                setValue('receivable', receivableCurrency / exchangeRate);
            }

            if (info.name?.endsWith('receivedCurrency')) {
                setValue('received', receivedCurrency / exchangeRate);
            }

            if (info.name?.endsWith('financialCurrency')) {
                setValue('financial', financialCurrency / exchangeRate);
            }

            if (value.contractType === CONTRACT_TYPE_SALE_REPORT.FIXED_COST && value.departmentId === DEPARTMENTS.SCS) {
                if (info.name?.endsWith('receivable')) {
                    setValue('received', receivable);
                }
            }

            if (info.name?.startsWith('duration')) {
                if (info.name?.endsWith('fromDate') || info.name?.endsWith('toDate')) {
                    const fromDate = value.duration.fromDate;
                    const toDate = value.duration.toDate;
                    if (!isEmpty(fromDate) && !isEmpty(toDate)) {
                        const allocationMonths = calculateMonthsDifference(fromDate, toDate);
                        setValue('contractAllocation', allocationMonths);
                    }
                }
            }
        });
        return () => unsubscribe();
    }, [watch]);

    return (
        <Modal
            isOpen={open}
            title="monthly-production-performance-edit-productivity"
            onClose={handleClose}
            keepMounted={false}
            maxWidth="md"
        >
            {loadingData ? (
                <div style={{ display: 'flex', justifyContent: 'center', height: '800px' }}>
                    <CircularProgress />
                </div>
            ) : (
                <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                    <Grid container spacing={gridSpacing}>
                        <Grid item xs={12} lg={3}>
                            <Input
                                name="departmentId"
                                label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-department'} />}
                                disabled
                                required
                            />
                        </Grid>
                        <Grid item xs={12} lg={3}>
                            <Input
                                name="year"
                                label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-year'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Months
                                months={months}
                                onChange={handleChangeMonth}
                                required
                                label={salesReport.monthlyProductionPerformance + '-month'}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                required
                                name="projectName"
                                label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-project-name'} />}
                                disabled
                            />
                        </Grid>
                        {productivity.departmentId === DEPARTMENTS.SCS && (
                            <Grid item xs={12} lg={6}>
                                <Input
                                    textFieldProps={{
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                    disabled={contractTypeValue === CONTRACT_TYPE_SALE_REPORT.TM}
                                    name="originalContractSize"
                                    label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-original-contract-size'} />}
                                />
                            </Grid>
                        )}
                        <Grid item xs={12} lg={6}>
                            <Currency
                                required
                                year={productivity.year}
                                name="currency"
                                convert="No"
                                disabled
                                label={salesReport.monthlyProductionPerformance + '-currency'}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                disabled={
                                    contractTypeValue === CONTRACT_TYPE_SALE_REPORT.TM && productivity.departmentId === DEPARTMENTS.SCS
                                }
                                name="contractSize"
                                label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-contract-size-usd'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <ProductivityType required label={salesReport.monthlyProductionPerformance + '-service-type'} />
                        </Grid>

                        <Grid item xs={12} lg={productivity.departmentId === DEPARTMENTS.SCS ? 3 : 6}>
                            <DatePicker
                                name="duration.fromDate"
                                label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-from-date'} />}
                                required
                            />
                        </Grid>
                        <Grid item xs={12} lg={productivity.departmentId === DEPARTMENTS.SCS ? 3 : 6}>
                            <DatePicker
                                name="duration.toDate"
                                label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-to-date'} />}
                                required
                            />
                        </Grid>
                        {productivity.departmentId === DEPARTMENTS.SCS && (
                            <>
                                <Grid item xs={12} lg={6}>
                                    <Input
                                        textFieldProps={{
                                            InputProps: {
                                                inputComponent: NumericFormatCustom as any
                                            }
                                        }}
                                        name="exchangeRate"
                                        label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-exchange-rate'} />}
                                        disabled
                                    />
                                </Grid>
                                <Grid item xs={12} lg={6}>
                                    <Input
                                        textFieldProps={{
                                            InputProps: {
                                                inputComponent: NumericFormatCustom as any
                                            }
                                        }}
                                        name="standardWorkingDay"
                                        label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-standard-working-day'} />}
                                        disabled
                                    />
                                </Grid>
                                <Grid item xs={12} lg={6}>
                                    <ContractType disabled required />
                                </Grid>
                                <Grid item xs={12} lg={6}>
                                    {contractTypeValue === CONTRACT_TYPE_SALE_REPORT.TM ? (
                                        <PaymentTerm
                                            handleChange={handleChangePaymentTerm}
                                            required={false}
                                            label={salesReport.monthlyProductionPerformance + '-payment-term'}
                                        />
                                    ) : (
                                        <Input
                                            textFieldProps={{
                                                InputProps: {
                                                    inputComponent: NumericFormatCustom as any
                                                }
                                            }}
                                            name="contractAllocation"
                                            label={
                                                <FormattedMessage
                                                    id={salesReport.monthlyProductionPerformance + '-contract-allocation-by-month'}
                                                />
                                            }
                                        />
                                    )}
                                </Grid>
                                <Grid item xs={12} lg={6}>
                                    {contractTypeValue === CONTRACT_TYPE_SALE_REPORT.TM ? (
                                        <Unit
                                            isShowAll={false}
                                            handleChange={handleChangeUnit}
                                            required
                                            label={salesReport.monthlyProductionPerformance + '-unit'}
                                        />
                                    ) : (
                                        ''
                                    )}
                                </Grid>
                                <Grid item xs={12} lg={12}>
                                    {productivity.departmentId === DEPARTMENTS.SCS &&
                                        contractTypeValue === CONTRACT_TYPE_SALE_REPORT.TM && (
                                            <TableContainer>
                                                <Stack
                                                    direction="row"
                                                    justifyContent="left"
                                                    alignItems="left"
                                                    paddingTop={'20px'}
                                                    paddingLeft={'10px'}
                                                    maxWidth="md"
                                                >
                                                    <Tooltip
                                                        placement="top"
                                                        title={<FormattedMessage id="add" />}
                                                        onClick={() => handleAddHcInfo()}
                                                    >
                                                        <IconButton aria-label="add" size="small">
                                                            <AddCircleOutlineIcon sx={{ fontSize: '1.1rem' }} />
                                                        </IconButton>
                                                    </Tooltip>
                                                </Stack>
                                                <Table
                                                    heads={<SaleTableTHead dataArray={hcInfoValues} />}
                                                    data={hcInfoValues}
                                                    heightTableEmpty="100px"
                                                >
                                                    <TableBody>
                                                        {hcInfoValues?.map((item: any, key: number) => (
                                                            <SaleTableTBody
                                                                key={item.id}
                                                                item={item}
                                                                index={key}
                                                                remove={handleRemoveHcInfo}
                                                            />
                                                        ))}
                                                        <TableRow>
                                                            <TableCell colSpan={4} />
                                                            <TableCell align="center">
                                                                <FormattedMessage id="total" />
                                                            </TableCell>
                                                            <TableCell>{customFormatPrice(totalAmount)}</TableCell>
                                                            <TableCell />
                                                        </TableRow>
                                                    </TableBody>
                                                </Table>
                                            </TableContainer>
                                        )}
                                </Grid>
                            </>
                        )}

                        {productivity.departmentId === DEPARTMENTS.SCS && (
                            <Grid item xs={12} lg={12}>
                                <Label label="&nbsp;" />
                                <Button variant="contained" onClick={estimate}>
                                    <FormattedMessage id="estimate" />
                                </Button>
                            </Grid>
                        )}
                        {productivity.departmentId !== DEPARTMENTS.SCS && (
                            <Grid item xs={12} lg={6}>
                                <Input
                                    textFieldProps={{
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                    name="deliveredCurrency"
                                    label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-delivered'} />}
                                />
                            </Grid>
                        )}
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                disabled={productivity.departmentId !== DEPARTMENTS.SCS}
                                name="delivered"
                                label={
                                    <FormattedMessage
                                        id={
                                            productivity.departmentId !== DEPARTMENTS.SCS
                                                ? salesReport.monthlyProductionPerformance + '-delivered-usd'
                                                : salesReport.monthlyProductionPerformance + '-delivered'
                                        }
                                    />
                                }
                            />
                        </Grid>

                        {productivity.departmentId !== DEPARTMENTS.SCS && (
                            <Grid item xs={12} lg={6}>
                                <Input
                                    textFieldProps={{
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                    name="receivableCurrency"
                                    label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-receivable'} />}
                                />
                            </Grid>
                        )}
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                disabled={productivity.departmentId !== DEPARTMENTS.SCS}
                                name="receivable"
                                label={
                                    <FormattedMessage
                                        id={
                                            productivity.departmentId !== DEPARTMENTS.SCS
                                                ? salesReport.monthlyProductionPerformance + '-receivable-usd'
                                                : salesReport.monthlyProductionPerformance + '-receivable'
                                        }
                                    />
                                }
                            />
                        </Grid>
                        {productivity.departmentId !== DEPARTMENTS.SCS && (
                            <Grid item xs={12} lg={6}>
                                <Input
                                    textFieldProps={{
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                    name="receivedCurrency"
                                    label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-received'} />}
                                />
                            </Grid>
                        )}
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                disabled={productivity.departmentId !== DEPARTMENTS.SCS}
                                name="received"
                                label={
                                    <FormattedMessage
                                        id={
                                            productivity.departmentId !== DEPARTMENTS.SCS
                                                ? salesReport.monthlyProductionPerformance + '-received-usd'
                                                : salesReport.monthlyProductionPerformance + '-received'
                                        }
                                    />
                                }
                            />
                        </Grid>

                        {productivity.departmentId !== DEPARTMENTS.SCS && (
                            <Grid item xs={12} lg={6}>
                                <Input
                                    textFieldProps={{
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                    name="financialCurrency"
                                    label={<FormattedMessage id={salesReport.monthlyProductionPerformance + '-financial'} />}
                                />
                            </Grid>
                        )}

                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                disabled={productivity.departmentId !== DEPARTMENTS.SCS}
                                name="financial"
                                label={
                                    <FormattedMessage
                                        id={
                                            productivity.departmentId !== DEPARTMENTS.SCS
                                                ? salesReport.monthlyProductionPerformance + '-financial-usd'
                                                : salesReport.monthlyProductionPerformance + '-financial'
                                        }
                                    />
                                }
                            />
                        </Grid>
                    </Grid>

                    {/* Sale */}
                    <DialogActions sx={{ mt: 2 }}>
                        <Button color="error" onClick={handleClose}>
                            <FormattedMessage id="cancel" />
                        </Button>
                        <LoadingButton loading={loading} variant="contained" type="submit">
                            <FormattedMessage id="submit" />
                        </LoadingButton>
                    </DialogActions>
                </FormProvider>
            )}
        </Modal>
    );
};

export default AddOrEditProductionPerformance;
