import { FormattedMessage } from 'react-intl';

// react-hook-form
import { useFormContext } from 'react-hook-form';

// material-ui
import { Grid } from '@mui/material';

// project imports
import { DatePicker, Input, PercentageFormat } from 'components/extended/Form';
import Api from 'constants/Api';
import { CONTRACT_TYPE_SALE_REPORT, E_BIDDING_STATUS, PERCENT_PLACEHOLDER, SERVICE_TYPE_STATUS } from 'constants/Common';
import { BiddingStatus, ContractType, DepartmentBidding, Project, SalePipelineType, ServiceType } from 'containers/search';
import sendRequest from 'services/ApiService';
import { dateFormat } from 'utils/date';
import { isEmpty } from 'utils/common';

// third party
import { useEffect } from 'react';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditBiddingProjectInfoProps {
    isEdit?: boolean;
    status?: string;
    handleSetHCInfo: (data: any) => void;
}

const AddOrEditBiddingProjectInfo = (props: IAddOrEditBiddingProjectInfoProps) => {
    // ================= Hooks, State, Variable, Props =================
    const { isEdit, status, handleSetHCInfo } = props;
    const { watch, setValue } = useFormContext();
    const statusBidding = watch('project.status');
    const contractType = watch('project.contractType');
    const serviceType = watch('project.serviceType');
    const probability = watch('project.probability');

    const { salesReport } = TEXT_CONFIG_SCREEN;

    // ================= Function =================
    const estimateHcInfoByFromToDate = async (fromDate: string, toDate: string) => {
        const payload = { contractType, from: dateFormat(fromDate), to: dateFormat(toDate) };
        const response = await sendRequest(Api.sale_pipe_line_bidding.estimateHCInfo, payload);
        if (response?.status) {
            const { result } = response;
            handleSetHCInfo(result.content);
        }
    };

    // ================= Effect =================
    useEffect(() => {
        const { unsubscribe } = watch((value, info) => {
            if (info.name?.startsWith('project')) {
                if (info.name?.endsWith('contractDurationFrom') || info.name?.endsWith('contractDurationTo')) {
                    const contractDurationFrom = value.project.contractDurationFrom;
                    const contractDurationTo = value.project.contractDurationTo;
                    if (!isEmpty(contractDurationFrom) && !isEmpty(contractDurationTo)) {
                        estimateHcInfoByFromToDate(contractDurationFrom, contractDurationTo);
                    }
                }
                if (info.name?.endsWith('serviceType')) {
                    value.project.serviceType === SERVICE_TYPE_STATUS.PRODUCT &&
                        setValue('project.contractType', CONTRACT_TYPE_SALE_REPORT.TM);
                }
                if (info.name?.endsWith('probability')) {
                    const probability = value.project.probability;
                    if (probability < 80) {
                        setValue('project.revenuePercent', '');
                    }
                }
            }
        });
        return () => unsubscribe();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [watch]);

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} lg={6}>
                <Input
                    name="project.customer"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-customer'} />}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <DepartmentBidding
                    name="project.department"
                    required
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                    label={salesReport.allSalesPineline + '-department'}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="project.projectName"
                    required
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-project-name'} />}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="project.contractNo"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contract-no'} />}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Project
                    name="project.projectRedmineId"
                    required={statusBidding === E_BIDDING_STATUS.CONTRACT}
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-project-redmine-name'} />}
                    isDefaultAll
                    projectAuthorization="false"
                    disabled={isEdit && status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="project.probability"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-probability'} />}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                    textFieldProps={{
                        placeholder: PERCENT_PLACEHOLDER,
                        InputProps: {
                            inputComponent: PercentageFormat as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <SalePipelineType
                    name="project.type"
                    required
                    isShowAll={false}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                    label={salesReport.allSalesPineline + '-type'}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="project.revenuePercent"
                    label={<FormattedMessage id="revenue" />}
                    disabled={status === E_BIDDING_STATUS.CONTRACT || probability < 80}
                    textFieldProps={{
                        placeholder: PERCENT_PLACEHOLDER,
                        InputProps: {
                            inputComponent: PercentageFormat as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <BiddingStatus
                    name="project.status"
                    required
                    disabled={
                        !isEdit ||
                        status === E_BIDDING_STATUS.CONTRACT ||
                        status === E_BIDDING_STATUS.FAILED ||
                        status === E_BIDDING_STATUS.LOSS
                    }
                    isShowAll={false}
                    label={salesReport.allSalesPineline + '-status'}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <ServiceType
                    required
                    name="project.serviceType"
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                    label={salesReport.allSalesPineline + '-service-type'}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <DatePicker
                    name="project.contractDueDate"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contract-date'} />} // thieu
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <ContractType
                    name="project.contractType"
                    required
                    disabled={isEdit || status === E_BIDDING_STATUS.CONTRACT || serviceType === SERVICE_TYPE_STATUS.PRODUCT}
                    label={salesReport.allSalesPineline + '-contract-type'}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <DatePicker
                    required
                    name="project.contractDurationFrom"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-contract-duration-from'} />}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <DatePicker
                    required
                    name="project.contractDurationTo"
                    label={<FormattedMessage id="contract-duration-to" />}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="project.note"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-note'} />}
                    textFieldProps={{ multiline: true, rows: 5 }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Grid item xs={12} lg={12}>
                    <Input
                        name="project.warrantyTime"
                        label={<FormattedMessage id={salesReport.allSalesPineline + '-warranty-time'} />}
                        disabled={status === E_BIDDING_STATUS.CONTRACT}
                    />
                </Grid>
            </Grid>
        </Grid>
    );
};

export default AddOrEditBiddingProjectInfo;
