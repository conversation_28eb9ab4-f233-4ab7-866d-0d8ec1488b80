// material-ui
import { <PERSON><PERSON><PERSON><PERSON>on, <PERSON>ack, TableBody, TableCell, TableRow, Tooltip, useMediaQuery, useTheme } from '@mui/material';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { ISaleOnGoingItem } from 'types';
import { checkAllowedPermission } from 'utils/authorization';
import { formatPrice } from 'utils/common';
import { dateFormat } from 'utils/date';
import { E_COMMENT_TYPE_SALES_PIPELINE } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

// assets
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

interface IOnGoingTBodyProps {
    page: number;
    size: number;
    items: ISaleOnGoingItem[];
    handleOpen: (items?: ISaleOnGoingItem) => void;
    handleOpenComment: (event: React.MouseEvent<Element>, item: any) => void;
}

const OnGoingTBody = (props: IOnGoingTBodyProps) => {
    const { page, size, items, handleOpen, handleOpenComment } = props;
    const { onGoingPermission } = PERMISSIONS.sale.salePipeline;
    const theme = useTheme();
    const matches = useMediaQuery(theme.breakpoints.up('md'));

    return (
        <TableBody>
            {items?.map((pro: ISaleOnGoingItem, key) => (
                <TableRow key={key}>
                    {checkAllowedPermission(onGoingPermission.edit) ? (
                        <TableCell align="center" sx={{ position: 'sticky', left: 0, zIndex: 1, backgroundColor: 'white' }}>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                {checkAllowedPermission(onGoingPermission.delete) && (
                                    <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleOpen(pro)}>
                                        <IconButton aria-label="delete" size="small">
                                            <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                        </IconButton>
                                    </Tooltip>
                                )}
                            </Stack>
                        </TableCell>
                    ) : (
                        <></>
                    )}

                    <TableCell>{size * page + key + 1}</TableCell>
                    <TableCell>{pro.projectInfo.contractType}</TableCell>
                    <TableCell>{pro.projectInfo.serviceType}</TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: pro.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.PROJECT_NAME,
                                comment: pro.projectInfo.projectNameComment
                            });
                        }}
                    >
                        {pro.projectInfo.projectName}
                    </TableCell>
                    <TableCell>{pro.projectInfo.probability ? pro.projectInfo.probability + '%' : ''}</TableCell>
                    <TableCell sx={{ whiteSpace: 'nowrap' }}>{pro.projectInfo.status}</TableCell>
                    <TableCell>{pro.projectInfo.revenuePercent ? pro.projectInfo.revenuePercent + '%' : ''}</TableCell>
                    <TableCell>{pro.projectInfo.revenuePercent ? pro.projectInfo.revenuePercent + '%' : ''}</TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: pro.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.SIZE_VND,
                                comment: pro.financialInfo.sizeVNDComment
                            });
                        }}
                    >
                        {formatPrice(Math.round(Number(pro.financialInfo.sizeVND)))}
                    </TableCell>
                    <TableCell>{formatPrice(pro.financialInfo.sizeUSD)}</TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: pro.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.MANAGEMENT_REVENUE,
                                comment: pro.financialInfo.managementRevenueComment
                            });
                        }}
                    >
                        {formatPrice(Math.round(Number(pro.financialInfo.managementRevenueAllocated)))}
                    </TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: pro.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.ACCOUNTANT_REVENUE,
                                comment: pro.financialInfo.accountantRevenueComment
                            });
                        }}
                    >
                        {formatPrice(Math.round(Number(pro.financialInfo.accountRevenueAllocatedVNDValue)))}
                    </TableCell>
                    <TableCell
                        sx={{ cursor: 'pointer' }}
                        onClick={(e) => {
                            handleOpenComment(e, {
                                idHexString: pro.idHexString,
                                commentType: E_COMMENT_TYPE_SALES_PIPELINE.LICENSE_FEE,
                                comment: pro.financialInfo.licenseFeeComment
                            });
                        }}
                    >
                        {formatPrice(pro.financialInfo.licenseFee)}
                    </TableCell>
                    <TableCell sx={{ whiteSpace: 'nowrap', position: 'sticky', right: !!matches ? 0 : 'unset', background: '#fff' }}>
                        {pro.projectInfo.contractDurationFrom &&
                            pro.projectInfo.contractDurationTo &&
                            `${dateFormat(pro.projectInfo.contractDurationFrom)} - ${dateFormat(pro.projectInfo.contractDurationTo)}`}
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default OnGoingTBody;
