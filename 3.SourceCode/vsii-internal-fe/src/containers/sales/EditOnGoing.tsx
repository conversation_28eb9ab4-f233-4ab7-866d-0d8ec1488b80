/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/exhaustive-deps */
import { SyntheticEvent, useEffect, useState } from 'react';

// react-hook-form
import { useFieldArray, useForm } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { yupResolver } from '@hookform/resolvers/yup';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid, Stack } from '@mui/material';

// project imports
import { DatePicker, FormProvider, Input, NumericFormatCustom, PercentageFormat } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { Table } from 'components/extended/Table';
import { TabPanel } from 'components/extended/Tabs';
import { E_IS_LOGTIME, FIELD_BY_TAB_ONGOING, MONEY_PLACEHOLDER, PERCENT_PLACEHOLDER, editOnGoingTabs } from 'constants/Common';
import TabCustom from 'containers/TabCustom';
import { Member, ProductionPerformance, SalePipelineStatus, SalePipelineType } from 'containers/search';
import { editOnGoingDefaultValue, editOnGoingSchema } from 'pages/sales/Config';
import { gridSpacing } from 'store/constant';
import { IMonthlyHCList, IProductionPerformanceItem, ISaleOnGoingItem } from 'types';
import { getTabValueByFieldError, isEmpty } from 'utils/common';
import { dateFormat } from 'utils/date';
import OnGoingHCTBody from './OnGoingHCTBody';
import OnGoingHCThead from './OnGoingHCThead';
import { IMember } from 'types/member';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// third party

interface IEditOnGoingProps {
    year?: number;
    open: boolean;
    project?: any;
    loading?: boolean;
    isEdit: boolean;
    handleClose: () => void;
    postEditOnGoing: (payload: ISaleOnGoingItem) => void;
}

const EditOnGoing = (props: IEditOnGoingProps) => {
    const { open, project, loading, isEdit, handleClose, year, postEditOnGoing } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    const [tabValue, setTabValue] = useState(0);
    const [projectOption, setProjectOption] = useState<{
        projectName: string;
        serviceType: string | null;
        contractType: string | null;
        contractDurationFrom: string | null;
        contractDurationTo: string | null;
        monthlyHCList: IMonthlyHCList[];
    } | null>(null);

    const [userContactOption, setUserContactOption] = useState<{ idHexString: string; firstName: string; lastName: string } | null>(null);

    const handleSubmit = (values: any) => {
        const filledHCInfo = values?.monthlyHCList?.map((item: IMonthlyHCList, index: number) => {
            return {
                year: values.projectInfo.year,
                month: +item?.month,
                hcMonthly: +item?.hcMonthly!
            };
        });

        const payload = {
            ...values,
            year,
            projectInfo: {
                ...values.projectInfo,
                productionPerformanceIdHexString: values.projectInfo.productionPerformanceIdHexString.value,
                contractDueDate: dateFormat(values.projectInfo?.contractDueDate),
                contractDurationFrom: dateFormat(values.projectInfo?.contractDurationFrom),
                contractDurationTo: dateFormat(values.projectInfo?.contractDurationTo)
            },

            otherInfo: {
                ...values.otherInfo,
                contact: userContactOption ? userContactOption : values.otherInfo.contact === null ? null : project.otherInfo.contact
            },
            hcInfo: { ...values.hcInfo, monthlyHCList: filledHCInfo }
        };
        delete payload.monthlyHCList;

        postEditOnGoing(payload);
    };

    const handleChangeTab = (event: SyntheticEvent, value: number) => {
        setTabValue(value);
    };

    const handleChangeUser = (userSelected: IMember) => {
        userSelected &&
            setUserContactOption({
                idHexString: userSelected?.idHexString!,
                firstName: userSelected?.firstName,
                lastName: userSelected?.lastName
            });
    };

    const handleChangeProject = (optionSelected: IProductionPerformanceItem) => {
        setProjectOption({
            projectName: optionSelected?.project.projectName,
            serviceType: optionSelected?.serviceType || null,
            contractType: optionSelected?.contractType || null,
            contractDurationFrom: optionSelected?.duration?.fromDate || null,
            contractDurationTo: optionSelected?.duration?.toDate || null,
            monthlyHCList: optionSelected.dataHcInfos || null
        });
    };

    const methods = useForm({
        defaultValues: {
            ...editOnGoingDefaultValue,
            projectInfo: {
                ...editOnGoingDefaultValue.projectInfo,
                year: year
            },
            monthlyHCList: [editOnGoingDefaultValue.hcInfo.monthlyHCList]
        },
        resolver: yupResolver(editOnGoingSchema),
        mode: 'all'
    });

    useEffect(() => {
        isEdit &&
            methods.reset({
                ...project,
                projectInfo: {
                    ...project.projectInfo,
                    productionPerformanceIdHexString: {
                        value: project.projectInfo.productionPerformanceIdHexString,
                        label: project.projectInfo.projectName
                    }
                },
                otherInfo: {
                    ...project.otherInfo,
                    contact: project.otherInfo.contact.idHexString
                        ? {
                            value: project.otherInfo.contact.idHexString,
                            label: `${project.otherInfo.contact.firstName} ${project.otherInfo.contact.lastName}`
                        }
                        : null
                },
                monthlyHCList: project?.hcInfo?.monthlyHCList
            });
    }, [isEdit]);

    useEffect(() => {
        projectOption &&
            methods.reset({
                ...methods.getValues(),
                projectInfo: {
                    ...methods.getValues().projectInfo,
                    serviceType: projectOption?.serviceType ? projectOption?.serviceType : '',
                    contractType: projectOption?.contractType ? projectOption?.contractType : '',
                    projectName: projectOption?.projectName,
                    contractDurationFrom: projectOption?.contractDurationFrom,
                    contractDurationTo: projectOption?.contractDurationTo
                }
            });
    }, [projectOption]);

    const { errors } = methods.formState;

    const { fields: monthlyHCListValues } = useFieldArray({
        control: methods.control,
        name: 'monthlyHCList'
    });

    const focusErrors = () => {
        const tabNumber = getTabValueByFieldError(errors, FIELD_BY_TAB_ONGOING);
        setTabValue(tabNumber);
    };

    useEffect(() => {
        !isEmpty(errors) && focusErrors();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [errors]);

    return (
        <Modal isOpen={open} title={'on-going-edit-on-going-detail'} onClose={handleClose} keepMounted={false} maxWidth="md">
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <TabCustom value={tabValue} tabs={editOnGoingTabs} handleChange={handleChangeTab} />
                {/* Project info */}
                <TabPanel value={tabValue} index={0}>
                    <Grid container spacing={gridSpacing}>
                        <Grid item xs={12} lg={6}>
                            <Input name="projectInfo.year" label={<FormattedMessage id={salesReport.salesOnGoing + '-year'} />} disabled />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.contractNo"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-contract-no'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.customer"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-customer'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.probability"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-probability'} />}
                                textFieldProps={{
                                    placeholder: PERCENT_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: PercentageFormat as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <ProductionPerformance
                                name="projectInfo.productionPerformanceIdHexString"
                                required
                                label={salesReport.salesOnGoing + '-project-name'}
                                disabled
                                handleChange={handleChangeProject}
                                requestParams={{ year: year }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <SalePipelineStatus
                                name="projectInfo.status"
                                required
                                label={salesReport.salesOnGoing + '-sale-pipeline-status'}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <SalePipelineType
                                name="projectInfo.type"
                                disabled
                                label={salesReport.salesOnGoing + '-sale-pipeline-type'}
                                required
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.revenuePercent"
                                label={<FormattedMessage id="revenue" />}
                                disabled
                                textFieldProps={{
                                    placeholder: PERCENT_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: PercentageFormat as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <DatePicker
                                name="projectInfo.contractDueDate"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-contract-date'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.serviceType"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-service-type'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <DatePicker
                                name="projectInfo.contractDurationFrom"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-contract-duration-from'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.contractType"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-contract-type'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <DatePicker
                                name="projectInfo.contractDurationTo"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-contract-duration-to'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.warrantyTime"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-warranty-time'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="projectInfo.note"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-note'} />}
                                textFieldProps={{ multiline: true, rows: 5 }}
                            />
                        </Grid>

                    </Grid>
                </TabPanel>
                {/* Financial info */}
                <TabPanel value={tabValue} index={1}>
                    <Grid container spacing={gridSpacing}>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.currency"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-currency'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.exchangeRate"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-exchange-rate'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.sizeVND"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-size-vnd'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.sizeUSD"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-size-usd'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.managementRevenueAllocated"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-management-revenue-allocated'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.newSaleUSD"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-new-sale-usd'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.accountRevenueAllocatedVNDValue"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-accountant-revenue-allocatedVND'} />}
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.acctReceivables"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-acct-receivables'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.licenseFee"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-license-fee'} />}
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>

                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.netEarn"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-net-earn'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.quarterLicense1st"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-license-1st'} />}
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.paid"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-paid'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.quarterLicense2nd"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-license-2nd'} />}
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>

                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.remain"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-remain'} />}
                                disabled
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>

                        <Grid item xs={12} lg={12}>
                            <Grid item xs={12} lg={5.9}>
                                <Input
                                    name="financialInfo.quarterLicense3rd"
                                    label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-license-3rd'} />}
                                    textFieldProps={{
                                        placeholder: MONEY_PLACEHOLDER,
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                />
                            </Grid>
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="financialInfo.quarterLicense4th"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-license-4th'} />}
                                textFieldProps={{
                                    placeholder: MONEY_PLACEHOLDER,
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                </TabPanel>
                {/* HC info */}
                <TabPanel value={tabValue} index={2}>
                    <Grid container spacing={gridSpacing} sx={{ mb: gridSpacing }}>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.billableHcs"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-billable-hcs'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.quarter1st"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-1st'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.hcs"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-hcs'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.quarter2nd"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-2nd'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.teamLeadHcs"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-team-lead-hcs'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.quarter3rd"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-3rd'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.seniorHcs"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-senior-hcs'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.quarter4th"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-quarter-4th'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.middleHcs"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-senior-hcs'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.totalNewSale"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-total-new-sale'} />}
                                disabled
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.juniorHcs"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-junior-hcs'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                textFieldProps={{
                                    InputProps: {
                                        inputComponent: NumericFormatCustom as any
                                    }
                                }}
                                name="hcInfo.totalBillable"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-total-billable'} />}
                                disabled
                            />
                        </Grid>
                    </Grid>
                    <Table
                        height="auto"
                        data={project?.hcInfo?.monthlyHCList}
                        heads={<OnGoingHCThead hcInfo={project?.hcInfo?.monthlyHCList} />}
                    >
                        <OnGoingHCTBody hcInfo={monthlyHCListValues as any} />
                    </Table>
                </TabPanel>
                {/* Other info */}
                <TabPanel value={tabValue} index={3}>
                    <Grid container spacing={gridSpacing}>
                        <Grid item xs={12} lg={6}>
                            <Member
                                name="otherInfo.contact"
                                isLogTime={E_IS_LOGTIME.YES}
                                isDefaultAll
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-contact'} />}
                                isIdHexString
                                handleChange={handleChangeUser}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="otherInfo.phoneNumber"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-phone-number'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="otherInfo.presaleFolder"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-presale-folder'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="otherInfo.emailAddress"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-email-address'} />}
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input
                                name="otherInfo.customerContact"
                                label={<FormattedMessage id={salesReport.salesOnGoing + '-customer-contact'} />}
                            />
                        </Grid>
                    </Grid>
                </TabPanel>
                <DialogActions>
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <Button color="error" onClick={handleClose}>
                            <FormattedMessage id="cancel" />
                        </Button>
                        <LoadingButton variant="contained" type="submit" loading={loading}>
                            <FormattedMessage id="submit" />
                        </LoadingButton>
                    </Stack>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default EditOnGoing;
