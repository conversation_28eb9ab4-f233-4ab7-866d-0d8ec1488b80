import { Link, TableBody, TableCell, TableRow } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { DATE_FORMAT } from 'constants/Common';
import { IBiddingTracking } from 'types';
import { dateFormat } from 'utils/date';

interface IBiddingTrackingTBodyProps {
    pageNumber: number;
    pageSize: number;
    biddingTracking: IBiddingTracking[];
}

const BiddingTrackingTBody = (props: IBiddingTrackingTBodyProps) => {
    const { pageNumber, pageSize, biddingTracking } = props;

    return (
        <TableBody>
            {biddingTracking?.map((bidding: IBiddingTracking, key: number) => (
                <TableRow key={key}>
                    <TableCell sx={{ width: '3%', px: '6px' }}>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell sx={{ width: '6%', px: '6px' }}>{bidding.type}</TableCell>
                    <TableCell sx={{ width: '7%', px: '6px' }}>{bidding.khlcntNumber}</TableCell>
                    <TableCell sx={{ width: '20%', px: '6px' }}>{bidding.packageName}</TableCell>
                    <TableCell sx={{ width: '7%', px: '6px' }}>{dateFormat(bidding.postingDate, undefined, false)}</TableCell>
                    <TableCell sx={{ width: '7%', px: '6px' }}>
                        {dateFormat(bidding.biddingClosingTime, DATE_FORMAT.HHmmssDDMMYYYY, false)}
                    </TableCell>
                    <TableCell sx={{ width: '6%', px: '6px' }}>
                        {bidding.biddingParticipationForm ? <FormattedMessage id={`${bidding.biddingParticipationForm}`} /> : ''}
                    </TableCell>
                    <TableCell sx={{ width: '7%', px: '6px' }}>{bidding.tbmtNumber}</TableCell>
                    <TableCell sx={{ width: '6%', px: '6px' }}>{bidding.group}</TableCell>
                    <TableCell sx={{ width: '11%', px: '6px' }}>{bidding.company}</TableCell>
                    <TableCell sx={{ width: '8%', px: '6px' }}>{bidding.address}</TableCell>
                    <TableCell sx={{ width: '7%', px: '6px' }}>{bidding.keyword}</TableCell>
                    <TableCell sx={{ width: '5%', px: '6px' }}>
                        {bidding.link ? (
                            <Link
                                sx={{
                                    color: 'black',
                                    ':hover': {
                                        color: '#1E88E5',
                                        cursor: 'pointer'
                                    },
                                    textDecoration: 'underline'
                                }}
                                href={bidding.link}
                                target="_blank"
                                underline="hover"
                            >
                                <FormattedMessage id="click-here" />
                            </Link>
                        ) : null}
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default BiddingTrackingTBody;
