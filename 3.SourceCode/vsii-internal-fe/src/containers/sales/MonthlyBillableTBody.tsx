// material-ui
import { <PERSON><PERSON><PERSON><PERSON>on, <PERSON>ack, TableBody, TableCell, TableRow } from '@mui/material';

// projects import
import { DeleteTwoToneIcon } from 'assets/images/icons';
import { Input, NumericFormatCustom } from 'components/extended/Form';
import { E_BIDDING_STATUS } from 'constants/Common';
import { Role } from 'containers/search';

interface IMonthlyBillableTBodyProps {
    fieldsRateByMonth: any;
    removeRateByMonth: any;
    status?: string;
}

const MonthlyBillableTBody = (props: IMonthlyBillableTBodyProps) => {
    const { fieldsRateByMonth, removeRateByMonth, status } = props;

    return (
        <TableBody>
            {fieldsRateByMonth.map((item: any, index: number) => (
                <TableRow
                    key={item.id}
                    sx={{
                        '& .MuiFormHelperText-root': {
                            whiteSpace: 'nowrap'
                        },
                        '& .MuiFormControl-root': { height: '50px' },
                        '&.MuiTableRow-root': {
                            '& td': {
                                borderColor: 'transparent',
                                '&.MuiTableCell-root:nth-of-type(2) .MuiFormHelperText-root': {
                                    position: 'absolute'
                                }
                            }
                        }
                    }}
                >
                    <TableCell>{index + 1}</TableCell>
                    <TableCell sx={{ '& .MuiInputBase-root': { width: '110px' } }}>
                        <Role isShowName={`rateByMonth.${index}.role`} isShowLabel={true} disabled={status === E_BIDDING_STATUS.CONTRACT} />
                    </TableCell>
                    <TableCell sx={{ '& .MuiFormControl-root': { marginTop: '10px' } }}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name={`rateByMonth.${index}.rate`}
                            disabled={status === E_BIDDING_STATUS.CONTRACT}
                        />
                    </TableCell>
                    <TableCell sx={{ '& .MuiFormControl-root': { marginTop: '10px' } }}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            disabled
                            name={`rateByMonth.${index}.rateVND`}
                        />
                    </TableCell>
                    <TableCell sx={{ '& .MuiFormControl-root': { marginTop: '10px' } }}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name={`rateByMonth.${index}.quantity`}
                            disabled={status === E_BIDDING_STATUS.CONTRACT}
                        />
                    </TableCell>
                    <TableCell sx={{ display: 'none', '& .MuiFormControl-root': { marginTop: '10px' } }}>
                        <Input
                            disabled
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name={`rateByMonth.${index}.amount`}
                        />
                    </TableCell>
                    <TableCell>
                        <Stack direction="row" justifyContent="center" alignItems="center">
                            <IconButton
                                aria-label="delete"
                                size="small"
                                onClick={() => removeRateByMonth(index)}
                                disabled={status === E_BIDDING_STATUS.CONTRACT}
                            >
                                <DeleteTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                            </IconButton>
                        </Stack>
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default MonthlyBillableTBody;
