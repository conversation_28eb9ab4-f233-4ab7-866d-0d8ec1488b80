import { FormattedMessage } from 'react-intl';
import { TableCell, TableHead, TableRow, styled } from '@mui/material';

import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ISaleTableTHeadProps {
    dataArray?: any;
}

const RedAsterisk = styled('span')({
    color: 'red',
    paddingLeft: '4px'
});

const SaleTableTHead = (props: ISaleTableTHeadProps) => {
    const { dataArray } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center">
                    <FormattedMessage id="no" />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-role'} />
                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-rate'} />
                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-rate-usd'} />
                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-quantity'} />
                    {dataArray?.length > 0 && <RedAsterisk>*</RedAsterisk>}
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-amount'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.monthlyProductionPerformance + '-action'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default SaleTableTHead;
