import { TableBody, TableCell, TableRow } from '@mui/material';
import { IProject } from 'types';
import { dateFormat } from 'utils/date';

interface IProjectReferenceTBodyProps {
    pageNumber: number;
    pageSize: number;
    projects: IProject[];
}

const ProjectReferenceTBody = (props: IProjectReferenceTBodyProps) => {
    const { pageNumber, pageSize, projects } = props;

    return (
        <TableBody>
            {projects?.map((pro: IProject, key: number) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{pro.projectName}</TableCell>
                    <TableCell>{pro.client}</TableCell>
                    <TableCell>{pro.technology}</TableCell>
                    <TableCell>{pro.domain}</TableCell>
                    <TableCell>{pro.type}</TableCell>
                    <TableCell>{pro.effort}</TableCell>
                    <TableCell>{pro.projectManager && `${pro.projectManager?.firstName} ${pro.projectManager?.lastName}`}</TableCell>
                    <TableCell>{pro.desc}</TableCell>
                    <TableCell>{pro.startDate && dateFormat(pro.startDate)}</TableCell>
                    <TableCell>{pro.endDate && dateFormat(pro.endDate)}</TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ProjectReferenceTBody;
