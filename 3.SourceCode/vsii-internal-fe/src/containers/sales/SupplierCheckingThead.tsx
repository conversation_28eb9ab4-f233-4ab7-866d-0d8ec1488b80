// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

const SupplierCheckingThead = () => {
    const { salesReport } = TEXT_CONFIG_SCREEN;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-supplier-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-technology'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-quantity'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-to-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-unit-price'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-pic-user-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-work-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'supplier-note'} />
                </TableCell>

                <TableCell align="center">
                    <FormattedMessage id={salesReport.salesLead + 'supplier-action'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default SupplierCheckingThead;
