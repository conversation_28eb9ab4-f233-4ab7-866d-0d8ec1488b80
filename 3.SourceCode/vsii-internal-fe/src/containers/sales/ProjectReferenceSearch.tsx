import { Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { Input, Label } from 'components/extended/Form';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { Project, ProjectType, SearchForm } from 'containers/search';
import { IProjectReferenceSearchDefaultValue, projectReferenceSearchDefaultValue, projectReferenceSearchSchema } from 'pages/sales/Config';

// third party
import { FormattedMessage } from 'react-intl';

interface IProjectReferenceSearchProps {
    formReset: IProjectReferenceSearchDefaultValue;
    handleSearch: (value: IProjectReferenceSearchDefaultValue) => void;
}

const ProjectReferenceSearch = (props: IProjectReferenceSearchProps) => {
    const { formReset, handleSearch } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={projectReferenceSearchDefaultValue}
            formSchema={projectReferenceSearchSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Project isNotStatus label={<FormattedMessage id={salesReport.projectReference + 'project-name'} />} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <ProjectType label={salesReport.projectReference + 'project-type'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Input name="technology" label={<FormattedMessage id={salesReport.projectReference + 'technology'} />} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Input name="domain" label={<FormattedMessage id={salesReport.projectReference + 'domain'} />} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    {/* not done */}
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.projectReference + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ProjectReferenceSearch;
