import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid } from '@mui/material';

// project import
import { IMonitorBiddingPackagesFilterConfig, biddingPackageShemcha, monitorBiddingPackagesFilterConfig } from 'pages/sales/Config';
import { Address, BiddingPackageName, SearchForm, StatusBiddingReport, Type } from 'containers/search';
import { Label } from 'components/extended/Form';
import { Button } from 'components';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IBiddingReportSearchProps {
    formReset: IMonitorBiddingPackagesFilterConfig;
    handleSearch: (value: any) => void;
}

const BiddingReportSearch = (props: IBiddingReportSearchProps) => {
    const { formReset, handleSearch } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={monitorBiddingPackagesFilterConfig}
            formSchema={biddingPackageShemcha}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.5}>
                    <Type label={salesReport.monitorBiddingPackages + 'report-type'} />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <BiddingPackageName label={salesReport.monitorBiddingPackages + 'report-bidding-package-name'} />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <Address label={salesReport.monitorBiddingPackages + 'report-address'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <StatusBiddingReport label={salesReport.monitorBiddingPackages + 'report-status'} />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    {/* not done */}
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default BiddingReportSearch;
