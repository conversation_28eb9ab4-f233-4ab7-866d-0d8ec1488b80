import { FormattedMessage } from 'react-intl';

// mui
import { IconButton, Link, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

// poject
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { DATE_FORMAT } from 'constants/Common';
import { formatPrice } from 'utils/common';
import { dateFormat } from 'utils/date';
import { IBiddingReport } from 'types';

interface IBiddingReportTBodyProps {
    pageNumber: number;
    pageSize: number;
    biddingReport: IBiddingReport[];
    handleOpen: (biddingReport?: IBiddingReport) => void;
}
const BiddingReportTBody = (props: IBiddingReportTBodyProps) => {
    const { pageNumber, pageSize, biddingReport, handleOpen } = props;
    const { monitorBiddingPackage } = PERMISSIONS.sale;

    return (
        <TableBody>
            {biddingReport?.map((bidding: IBiddingReport, key: number) => (
                <TableRow key={key}>
                    <TableCell sx={{ width: '2%', px: '3px' }}>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell sx={{ width: '3.5%', px: '3px' }}>{bidding.type}</TableCell>
                    <TableCell sx={{ width: '7.5%', px: '3px' }}>{bidding.numberKHLCNT}</TableCell>
                    <TableCell sx={{ width: '12.5%', px: '3px' }}>{bidding.biddingPackagesName}</TableCell>
                    <TableCell sx={{ width: '6%', px: '3px' }}>{formatPrice(bidding.estimatedCost)}</TableCell>
                    <TableCell sx={{ width: '5%', px: '3px' }}>{dateFormat(bidding.datePosting, undefined, false)}</TableCell>
                    <TableCell sx={{ width: '6.5%', px: '3px' }}>
                        {dateFormat(bidding.timeBiddingClosing, DATE_FORMAT.HHmmssDDMMYYYY, false)}
                    </TableCell>
                    <TableCell sx={{ width: '6.5%', px: '3px' }}>{formatPrice(bidding.bidPrice)}</TableCell>
                    <TableCell sx={{ width: '5%', px: '3px' }}>
                        {bidding.formBiddingParticipation ? <FormattedMessage id={`${bidding.formBiddingParticipation}`} /> : ''}
                    </TableCell>
                    <TableCell sx={{ width: '7%', px: '3px' }}>{bidding.numberTBMT}</TableCell>
                    <TableCell sx={{ width: '4%', px: '3px' }}>{bidding.group}</TableCell>
                    <TableCell sx={{ width: '9%', px: '3px' }}>{bidding.company}</TableCell>
                    <TableCell sx={{ width: '6%', px: '3px' }}>{bidding.address}</TableCell>
                    <TableCell sx={{ width: '4%', px: '3px' }}>{bidding.keyword}</TableCell>
                    <TableCell sx={{ width: '3.5%', px: '3px' }}>{<FormattedMessage id={`${bidding.status}`} />}</TableCell>
                    <TableCell sx={{ width: '5%', px: '3px' }}>{bidding.comment}</TableCell>
                    <TableCell sx={{ width: '4%', px: '3px' }}>
                        {bidding.link ? (
                            <Link
                                sx={{
                                    color: 'black',
                                    ':hover': {
                                        color: '#1E88E5',
                                        cursor: 'pointer'
                                    },
                                    textDecoration: 'underline'
                                }}
                                href={bidding.link}
                                target="_blank"
                                underline="hover"
                            >
                                <FormattedMessage id="click-here" />
                            </Link>
                        ) : (
                            <></>
                        )}
                    </TableCell>
                    <TableCell sx={{ width: '3%', px: '3px' }}>
                        {checkAllowedPermission(monitorBiddingPackage.edit) && (
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleOpen(bidding)}>
                                    <IconButton aria-label="delete" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        )}
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default BiddingReportTBody;
