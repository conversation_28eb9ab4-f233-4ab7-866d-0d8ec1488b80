/* eslint-disable react-hooks/exhaustive-deps */
import { startTransition, useEffect } from 'react';

// material-ui
import { Grid } from '@mui/material';

// react-hook-form
import { useFormContext } from 'react-hook-form';

// project imports
import { Input, NumericFormatCustom } from 'components/extended/Form';
import { E_BIDDING_STATUS, MONEY_PLACEHOLDER } from 'constants/Common';
import { Currency } from 'containers/search';
import { IOption } from 'types';

// third party
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditBiddingFinancialInfoProps {
    exchangeRateUSDpercentVND: number;
    status?: string;
    year?: number;
}
const AddOrEditBiddingFinancialInfo = (props: IAddOrEditBiddingFinancialInfoProps) => {
    // ================= Props, Hooks, Variables =================
    const { exchangeRateUSDpercentVND, status, year } = props;
    const { setValue, watch } = useFormContext();

    const { salesReport } = TEXT_CONFIG_SCREEN;

    // ================= Event =================
    const handleChangeCurrency = (currency: IOption) => {
        setValue('financialInfo.exchangeRate', currency.number);
    };

    const handleChangeAccountRevenueAllocatedVND = (e: any) => {
        const accountRevenueAllocatedVNDValue = e.target.value;
        startTransition(() => {
            setValue('financialInfo.acctReceivables', accountRevenueAllocatedVNDValue);
        });
    };

    // ================= Effect =================
    useEffect(() => {
        const { unsubscribe } = watch((value, info) => {
            if (info.name?.startsWith('financialInfo')) {
                if (info.name.endsWith('originalContractSize') || info.name?.endsWith('exchangeRate')) {
                    const contractSize = value.financialInfo.originalContractSize;
                    const exchangeRate = value.financialInfo.exchangeRate;
                    setValue('financialInfo.sizeVND', contractSize * exchangeRate);
                    const x = +exchangeRateUSDpercentVND / exchangeRate;
                    const sizeUSD = contractSize / +x.toFixed();
                    setValue('financialInfo.sizeUSD', sizeUSD.toFixed(2));
                }
                if (info.name.endsWith('paid') || info.name.endsWith('sizeVND')) {
                    const paid = value.financialInfo.paid;
                    const sizeVND = value.financialInfo.sizeVND;
                    setValue('financialInfo.remain', sizeVND - paid);
                }
                if (
                    info.name.endsWith('accountRevenueAllocatedVND') ||
                    info.name.endsWith('quarterLicense1st') ||
                    info.name.endsWith('quarterLicense2nd') ||
                    info.name.endsWith('quarterLicense3rd') ||
                    info.name.endsWith('quarterLicense4th') ||
                    info.name.endsWith('acctReceivables')
                ) {
                    const accountRevenueAllocatedVND = +value.financialInfo.accountRevenueAllocatedVND;
                    const quarterLicense1st = +value.financialInfo.quarterLicense1st;
                    const quarterLicense2nd = +value.financialInfo.quarterLicense2nd;
                    const quarterLicense3rd = +value.financialInfo.quarterLicense3rd;
                    const quarterLicense4th = +value.financialInfo.quarterLicense4th;
                    const acctReceivables = +value.financialInfo.acctReceivables;
                    const isChangeAccountRevenueAllocatedVND = info.name.endsWith('accountRevenueAllocatedVND');
                    const sumQuaterLicense = quarterLicense1st + quarterLicense2nd + quarterLicense3rd + quarterLicense4th;
                    setValue(
                        'financialInfo.netEarn',
                        isChangeAccountRevenueAllocatedVND
                            ? accountRevenueAllocatedVND - sumQuaterLicense
                            : acctReceivables - sumQuaterLicense
                    );
                }
            }
        });
        return () => unsubscribe();
    }, [watch]);

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} lg={6}>
                <Currency
                    required
                    name="financialInfo.currency"
                    handleChangeFullOption={handleChangeCurrency}
                    convert="Yes"
                    year={year}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                    label={salesReport.allSalesPineline + '-currency'}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.exchangeRate"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-exchange-rate'} />}
                    disabled
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={12}>
                <Grid item xs={12} lg={5.9}>
                    <Input
                        textFieldProps={{
                            placeholder: MONEY_PLACEHOLDER,
                            InputProps: {
                                inputComponent: NumericFormatCustom as any
                            }
                        }}
                        label={<FormattedMessage id={salesReport.allSalesPineline + '-original-contract-size'} />}
                        name="financialInfo.originalContractSize"
                        disabled={status === E_BIDDING_STATUS.CONTRACT}
                    />
                </Grid>
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.sizeVND"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-size-vnd'} />}
                    disabled
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.sizeUSD"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-size-usd'} />}
                    disabled
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.managementRevenueAllocated"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-management-revenue-allocated'} />}
                    disabled
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.newSaleUSD"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-new-sale-usd'} />}
                    disabled
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.accountRevenueAllocatedVND"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-accountant-revenue-allocatedVND'} />}
                    onChangeInput={handleChangeAccountRevenueAllocatedVND}
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.acctReceivables"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-acct-receivables'} />}
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.licenseFee"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-license-fee'} />}
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.netEarn"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-net-earn'} />}
                    disabled
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.quarterLicense1st"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-license-1st'} />}
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.paid"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-paid'} />}
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.quarterLicense2nd"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-license-2nd'} />}
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.remain"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-remain'} />}
                    disabled
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                />
            </Grid>
            <Grid item xs={12} lg={12}>
                <Grid item xs={12} lg={5.9}>
                    <Input
                        name="financialInfo.quarterLicense3rd"
                        label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-license-3rd'} />}
                        textFieldProps={{
                            placeholder: MONEY_PLACEHOLDER,
                            InputProps: {
                                inputComponent: NumericFormatCustom as any
                            }
                        }}
                        disabled={status === E_BIDDING_STATUS.CONTRACT}
                    />
                </Grid>
            </Grid>
            <Grid item xs={12} lg={6}>
                <Input
                    name="financialInfo.quarterLicense4th"
                    label={<FormattedMessage id={salesReport.allSalesPineline + '-quarter-license-4th'} />}
                    textFieldProps={{
                        placeholder: MONEY_PLACEHOLDER,
                        InputProps: {
                            inputComponent: NumericFormatCustom as any
                        }
                    }}
                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                />
            </Grid>
        </Grid>
    );
};

export default AddOrEditBiddingFinancialInfo;
