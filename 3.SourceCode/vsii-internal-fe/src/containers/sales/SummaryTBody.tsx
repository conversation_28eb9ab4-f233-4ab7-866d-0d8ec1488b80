import { TableBody, TableCell, TableRow, Box, useMediaQuery, useTheme, Tooltip, ButtonBase, Typography } from '@mui/material';
import { useCallback, useState } from 'react';
import { ListManager } from 'react-beautiful-dnd-grid';
import { FormattedMessage } from 'react-intl';

import { IListParent, IListTable, ISalePipelineSummary } from 'types';
import { formatPrice } from 'utils/common';

import DoneIcon from '@mui/icons-material/Done';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import PinchIcon from '@mui/icons-material/Pinch';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
interface ISummaryTBodyProps {
    summary: ISalePipelineSummary | undefined;
    totalSummarry?: IListTable[];
    handleConFirmEdit?: (list: IListTable[]) => void;
    setIsEdited: React.Dispatch<React.SetStateAction<boolean>>;
    isEdited: boolean;
}
const SummaryTBody = ({ summary, totalSummarry, handleConFirmEdit, setIsEdited, isEdited }: ISummaryTBodyProps) => {
    const theme = useTheme();

    const sortList = (list: IListTable[]) => list.slice().sort((first: any, second: any) => first.index - second.index);

    const [sortedList, setSortedList] = useState(sortList((totalSummarry as IListTable[]).filter((item) => item.id)));

    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

    const [showButton, setShowButton] = useState(false);

    const handleMouseEnter = () => setShowButton(true);
    const handleMouseLeave = () => setShowButton(false);

    const { summaryPermission } = PERMISSIONS.sale.salePipeline;

    const reorderList = useCallback(
        (sourceIndex: number, destinationIndex: number) => {
            if (destinationIndex === sourceIndex) {
                return;
            }

            // Copy the current sorted list
            const updatedList = [...sortedList];

            // Remove the item being dragged and insert it at the destination index
            const [movedItem] = updatedList.splice(sourceIndex, 1);
            updatedList.splice(destinationIndex, 0, movedItem);

            // Update the index of all items to ensure they are consecutive starting from 0
            const reindexedList = updatedList.map((item, index) => ({
                ...item,
                index: index + 1 // Set the index to the current position in the array
            }));

            // Update the state with the re-sorted and re-indexed list
            setSortedList(sortList(reindexedList));
        },
        [sortedList]
    );

    return (
        <>
            {summary && (
                <TableBody
                    sx={{
                        '& .tableCell-row-name': {
                            fontWeight: '900'
                        },
                        '& .tableCell-row': {
                            textAlign: 'right'
                        },
                        '& .highlighted-row': {
                            '& .MuiTableCell-root:first-of-type': {
                                paddingLeft: '40px'
                            }
                        },
                        '& .highlighted-row-childrens': {
                            '& .MuiTableCell-root:first-of-type': {
                                paddingLeft: '80px'
                            }
                        }
                    }}
                >
                    {summary.tableLarge.listParent.map((parent: IListParent, index: number) => (
                        <>
                            <TableRow key={index} sx={{ '& > td': { color: parent.color, backgroundColor: parent.backgroundColor } }}>
                                <TableCell className={'tableCell-row-name'}>{parent.name}</TableCell>
                                {parent.value && (
                                    <>
                                        <TableCell className={'tableCell-row'}>
                                            {parent.value.usd == null ? '' : formatPrice(Number(parent.value.usd))}
                                        </TableCell>
                                        <TableCell className={'tableCell-row'}>
                                            {parent.value.vnd == null ? '' : formatPrice(Number(parent.value.vnd))}
                                        </TableCell>
                                        <TableCell className={'tableCell-row'}>{formatPrice(Number(parent.value.quarter1))}</TableCell>
                                        <TableCell className={'tableCell-row'}>{formatPrice(Number(parent.value.quarter2))}</TableCell>
                                        <TableCell className={'tableCell-row'}>{formatPrice(Number(parent.value.quarter3))}</TableCell>
                                        <TableCell className={'tableCell-row'}>{formatPrice(Number(parent.value.quarter4))}</TableCell>
                                        <TableCell className={'tableCell-row'}>
                                            {formatPrice(Number(parent.value.totalRevenueEstimation))}
                                        </TableCell>
                                    </>
                                )}
                            </TableRow>
                            {parent?.childrens?.map((children: IListParent, index: number) => (
                                <>
                                    <TableRow
                                        key={index}
                                        className={'highlighted-row'}
                                        sx={{ '& > td': { color: children.color, backgroundColor: children.backgroundColor } }}
                                    >
                                        <TableCell className={'tableCell-row-name'}>{children.name}</TableCell>
                                        {children.value && (
                                            <>
                                                <TableCell className={'tableCell-row'}>
                                                    {formatPrice(Math.round(Number(children.value.usd)))}
                                                </TableCell>
                                                <TableCell className={'tableCell-row'}>
                                                    {formatPrice(Math.round(Number(children.value.vnd)))}
                                                </TableCell>
                                                <TableCell className={'tableCell-row'}>
                                                    {formatPrice(Math.round(Number(children.value.quarter1)))}
                                                </TableCell>
                                                <TableCell className={'tableCell-row'}>
                                                    {formatPrice(Math.round(Number(children.value.quarter2)))}
                                                </TableCell>
                                                <TableCell className={'tableCell-row'}>
                                                    {formatPrice(Math.round(Number(children.value.quarter3)))}
                                                </TableCell>
                                                <TableCell className={'tableCell-row'}>
                                                    {formatPrice(Math.round(Number(children.value.quarter4)))}
                                                </TableCell>
                                                <TableCell className={'tableCell-row'}>
                                                    {formatPrice(Math.round(Number(children.value.totalRevenueEstimation)))}
                                                </TableCell>
                                            </>
                                        )}
                                    </TableRow>
                                    {children?.childrens?.map((child: IListParent, index: number) => (
                                        <>
                                            <TableRow
                                                key={index}
                                                className={'highlighted-row-childrens'}
                                                sx={{ '& > td': { color: child.color, backgroundColor: child.backgroundColor } }}
                                            >
                                                <TableCell className={'tableCell-row-name'}>{child.name}</TableCell>
                                                {child.value && (
                                                    <>
                                                        <TableCell className={'tableCell-row'}>
                                                            {formatPrice(Math.round(Number(child.value.usd)))}
                                                        </TableCell>
                                                        <TableCell className={'tableCell-row'}>
                                                            {formatPrice(Math.round(Number(child.value.vnd)))}
                                                        </TableCell>
                                                        <TableCell className={'tableCell-row'}>
                                                            {formatPrice(Math.round(Number(child.value.quarter1)))}
                                                        </TableCell>
                                                        <TableCell className={'tableCell-row'}>
                                                            {formatPrice(Math.round(Number(child.value.quarter2)))}
                                                        </TableCell>
                                                        <TableCell className={'tableCell-row'}>
                                                            {formatPrice(Math.round(Number(child.value.quarter3)))}
                                                        </TableCell>
                                                        <TableCell className={'tableCell-row'}>
                                                            {formatPrice(Math.round(Number(child.value.quarter4)))}
                                                        </TableCell>
                                                        <TableCell className={'tableCell-row'}>
                                                            {formatPrice(Math.round(Number(child.value.totalRevenueEstimation)))}
                                                        </TableCell>
                                                    </>
                                                )}
                                            </TableRow>
                                        </>
                                    ))}
                                </>
                            ))}
                        </>
                    ))}
                </TableBody>
            )}

            <Box mt={3} />
            {isEdited ? (
                <>
                    <Box display="flex" gap={2} sx={{ mb: 1 }}>
                        <Tooltip title={<FormattedMessage id="cancel"></FormattedMessage>}>
                            <ButtonBase
                                onClick={() => {
                                    setIsEdited(false);
                                    setSortedList((totalSummarry as IListTable[]).filter((item) => item.id));
                                }}
                            >
                                <HighlightOffIcon sx={{ fontSize: 18 }} />
                            </ButtonBase>
                        </Tooltip>
                        <Tooltip title={<FormattedMessage id="confirm"></FormattedMessage>}>
                            <ButtonBase
                                onClick={() => {
                                    handleConFirmEdit?.(sortedList);
                                }}
                            >
                                <DoneIcon sx={{ fontSize: 18 }} />
                            </ButtonBase>
                        </Tooltip>
                    </Box>
                    <ListManager
                        items={sortedList}
                        direction={'vertical'}
                        maxItems={1}
                        render={(item: IListTable) => (
                            <Box
                                sx={{
                                    ...item.style,
                                    display: 'flex',
                                    g: 2,
                                    width: isMobile ? '90vw' : '30vw',
                                    border: 0.2
                                }}
                            >
                                <Box sx={{ ...item.style, p: 1, width: '40%', borderRight: 0.1 }}>{item.name}</Box>
                                <Box p={1} width="30%" sx={{ borderRight: 0.1 }}>
                                    {formatPrice(Math.round(Number(item.valueUsd)))}
                                </Box>
                                <Box width="30%" p={1}>
                                    {formatPrice(Math.round(Number(item.value)))}
                                </Box>
                            </Box>
                        )}
                        onDragEnd={reorderList}
                    />
                </>
            ) : (
                <>
                    {totalSummarry?.length !== 0 && (
                        <TableBody
                            sx={{
                                '& .tableCell-row-name': {
                                    fontWeight: '900'
                                },
                                '& .tableCell-row': {
                                    textAlign: 'right'
                                }
                            }}
                            onMouseLeave={handleMouseLeave}
                            onMouseEnter={handleMouseEnter}
                        >
                            <Box height={20}>
                                {showButton && checkAllowedPermission(summaryPermission.editRows) && (
                                    <Tooltip title={<FormattedMessage id="edit-arrangement"></FormattedMessage>} sx={{ mb: 1 }}>
                                        <ButtonBase
                                            onClick={() => {
                                                setIsEdited(true);
                                            }}
                                        >
                                            <PinchIcon sx={{ fontSize: 20 }} />
                                        </ButtonBase>
                                    </Tooltip>
                                )}
                            </Box>
                            {totalSummarry?.map((item: IListTable, index: number) => (
                                <TableRow key={index} sx={{ '& > td': { ...item.style } }}>
                                    <>
                                        <TableCell sx={item.style}>
                                            <Tooltip title={item.titleRecipe}>
                                                <Typography>{item.name}</Typography>
                                            </Tooltip>
                                        </TableCell>
                                        <TableCell className={'tableCell-row'}>{formatPrice(Math.round(Number(item.valueUsd)))}</TableCell>
                                        <TableCell className={'tableCell-row'}>{formatPrice(Math.round(Number(item.value)))}</TableCell>
                                    </>
                                </TableRow>
                            ))}
                        </TableBody>
                    )}
                </>
            )}
        </>
    );
};

export default SummaryTBody;
