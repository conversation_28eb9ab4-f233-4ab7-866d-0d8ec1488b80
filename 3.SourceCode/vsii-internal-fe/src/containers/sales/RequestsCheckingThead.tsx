// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

const RequestsCheckingThead = () => {
    const { salesReport } = TEXT_CONFIG_SCREEN;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-partner-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-received-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-request'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-technology'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-quantity'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-timeline'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-pic-user-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-status'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-possibility'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-domain'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.salesLead + 'requests-note'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.salesLead + 'requests-actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default RequestsCheckingThead;
