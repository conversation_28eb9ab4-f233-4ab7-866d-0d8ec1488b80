import { Fragment } from 'react';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// react-hook-form
import { useFormContext } from 'react-hook-form';

// project imports
import { HC_ASPL_MONTHS_OF_YEAR } from 'constants/Common';

// third party
import { FormattedMessage, useIntl } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IBiddingHCTheadProps {
    monthlyHCList?: any;
}

const BiddingHCThead = (props: IBiddingHCTheadProps) => {
    const { monthlyHCList } = props;
    const intl = useIntl();
    const { watch } = useFormContext();
    const contractDurationFrom = watch('project.contractDurationFrom');
    const contractDurationTo = watch('project.contractDurationTo');

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow
                sx={{
                    '& th': {
                        whiteSpace: 'nowrap'
                    }
                }}
            >
                {contractDurationFrom && contractDurationTo ? (
                    <TableCell sx={{ position: 'sticky', left: 0, backgroundColor: 'white' }} align="center">
                        <FormattedMessage id="action" />
                    </TableCell>
                ) : (
                    <></>
                )}
                {monthlyHCList?.map((item: any, key: number) => (
                    <Fragment key={key}>
                        <TableCell>{`${intl.formatMessage({ id: HC_ASPL_MONTHS_OF_YEAR[item?.month] })} - ${item.year}`}</TableCell>
                        <TableCell>
                            <FormattedMessage id={salesReport.allSalesPineline + '-billable-day'} />
                        </TableCell>
                        <TableCell>
                            <FormattedMessage id={salesReport.allSalesPineline + '-billable'} />
                        </TableCell>
                    </Fragment>
                ))}
            </TableRow>
        </TableHead>
    );
};

export default BiddingHCThead;
