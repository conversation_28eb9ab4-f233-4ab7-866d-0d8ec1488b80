// Third party
import { FormattedMessage } from 'react-intl';

// yup
import { yupResolver } from '@hookform/resolvers/yup';

// material-ui
import { Button, DialogActions, Grid, Stack } from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project import
import { addOrEditBiddingReportFormDefault, addOrEditBiddingReportSchema } from 'pages/sales/Config';
import { FormProvider, Input, NumericFormatCustom } from 'components/extended/Form';
import { StatusBiddingReport } from 'containers/search';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { IBiddingReport } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditBiddingReportProps {
    open: boolean;
    handleClose: () => void;
    loading?: boolean;
    isEdit: boolean;
    data: IBiddingReport;
    hanldeAdd: (value: IBiddingReport) => void;
    hanldeEdit: (value: IBiddingReport, idHexString: string) => void;
}

const AddOrEditBiddingReport = (props: IAddOrEditBiddingReportProps) => {
    const { open, handleClose, data, isEdit, loading, hanldeAdd, hanldeEdit } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    const handleSubmit = (values: IBiddingReport) => {
        const idHexString = data.idHexString;
        if (isEdit) {
            hanldeEdit(values, idHexString!);
        } else {
            hanldeAdd(values);
        }
    };

    return (
        <Modal
            isOpen={open}
            title={
                isEdit
                    ? salesReport.monitorBiddingPackages + 'report-edit-bidding-packages'
                    : salesReport.monitorBiddingPackages + 'report-add-bidding-packages'
            }
            onClose={handleClose}
            keepMounted={false}
        >
            <FormProvider
                form={{ defaultValues: addOrEditBiddingReportFormDefault, resolver: yupResolver(addOrEditBiddingReportSchema) }}
                onSubmit={handleSubmit}
                formReset={data}
            >
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={6}>
                        <Input required name="type" label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-type'} />} />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            disabled
                            name="numberKHLCNT"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-KHLCNT-number'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            required
                            name="biddingPackagesName"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-bidding-package-name'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            disabled
                            name="estimatedCost"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-budget'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            disabled
                            name="datePosting"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-date-posting'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            disabled
                            name="timeBiddingClosing"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-time-bidding-closing'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name="bidPrice"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-bid-price'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            disabled
                            name="formBiddingParticipation"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-form-bidding-participation'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            name="numberTBMT"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-TBMT-number'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            disabled
                            name="group"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-group'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            required
                            name="company"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-company'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            disabled
                            name="address"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-address'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            disabled
                            name="keyword"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-keyword'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <StatusBiddingReport select label={salesReport.monitorBiddingPackages + 'report-status'} />
                    </Grid>
                    <Grid item xs={12}>
                        <Input
                            textFieldProps={{ multiline: true, rows: 4 }}
                            name="comment"
                            label={<FormattedMessage id={salesReport.monitorBiddingPackages + 'report-comment'} />}
                        />
                    </Grid>
                    {/* not done */}
                    <Grid item xs={12}>
                        <DialogActions>
                            <Stack direction="row" spacing={1} justifyContent="flex-end">
                                <Button color="error" onClick={handleClose}>
                                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-cancel'} />
                                </Button>
                                <LoadingButton loading={loading} variant="contained" type="submit">
                                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-submit'} />
                                </LoadingButton>
                            </Stack>
                        </DialogActions>
                    </Grid>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditBiddingReport;
