import { ChangeEvent } from 'react';

// material-ui
import { Grid, SelectChangeEvent } from '@mui/material';

// project imports
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { ProductionPerformance, SalePipelineStatus, SalePipelineType, SearchForm, SalesYear } from 'containers/search';
import { IOnGoingConfig, onGoingConfig, onGoingSchema } from 'pages/sales/Config';
import { E_SCREEN_SALES_YEAR } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface OnGoingSearchProps {
    formReset: IOnGoingConfig;
    handleSearch: (value: any) => void;
    handleChangeYear?: (year: ChangeEvent<HTMLInputElement>) => void;
    handleChangeProject?: (data: any) => void;
    handleChangeStatus?: (e: SelectChangeEvent<unknown>) => void;
    handleChangeSalePipelineType?: (e: SelectChangeEvent<unknown>) => void;
}

const OnGoingSearch = (props: OnGoingSearchProps) => {
    const { formReset, handleSearch, handleChangeYear, handleChangeSalePipelineType, handleChangeStatus, handleChangeProject } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm defaultValues={onGoingConfig} formSchema={onGoingSchema} handleSubmit={handleSearch} formReset={formReset}>
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <SalePipelineType
                        isShowAll
                        handleChangeSalePipelineType={handleChangeSalePipelineType}
                        label={salesReport.salesOnGoing + '-sale-pipeline-type'}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <SalesYear
                        handleChangeYear={handleChangeYear}
                        screen={E_SCREEN_SALES_YEAR.ON_GOING}
                        label={salesReport.salesOnGoing + '-year'}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <ProductionPerformance
                        isDefaultAll={false}
                        handleChange={handleChangeProject}
                        requestParams={formReset}
                        label={salesReport.salesOnGoing + '-project'}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <SalePipelineStatus
                        isShowAll
                        handleChangeStatus={handleChangeStatus}
                        label={salesReport.salesOnGoing + '-sale-pipeline-status'}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.salesOnGoing + '-search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default OnGoingSearch;
