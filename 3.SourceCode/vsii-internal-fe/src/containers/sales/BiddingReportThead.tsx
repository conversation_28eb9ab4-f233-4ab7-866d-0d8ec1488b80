import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const BiddingReportThead = () => {
    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell sx={{ width: '2%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-no'} />
                </TableCell>
                <TableCell sx={{ width: '3.5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-type'} />
                </TableCell>
                <TableCell sx={{ width: '7.5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-KHLCNT-number'} />
                </TableCell>
                <TableCell sx={{ width: '12.5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-bidding-package-name'} />
                </TableCell>
                <TableCell sx={{ width: '6%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-budget'} />
                </TableCell>
                <TableCell sx={{ width: '5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-date-posting'} />
                </TableCell>
                <TableCell sx={{ width: '6.5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-time-bidding-closing'} />
                </TableCell>
                <TableCell sx={{ width: '6.5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-bid-price'} />
                </TableCell>
                <TableCell sx={{ width: '5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-form-bidding-participation'} />
                </TableCell>
                <TableCell sx={{ width: '7%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-TBMT-number'} />
                </TableCell>
                <TableCell sx={{ width: '4%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-group'} />
                </TableCell>
                <TableCell sx={{ width: '9%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-company'} />
                </TableCell>
                <TableCell sx={{ width: '6%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-address'} />
                </TableCell>
                <TableCell sx={{ width: '4%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-keyword'} />
                </TableCell>
                <TableCell sx={{ width: '3.5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-status'} />
                </TableCell>
                <TableCell sx={{ width: '5%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-comment'} />
                </TableCell>
                <TableCell sx={{ width: '4%', px: '3px' }}>
                    <FormattedMessage id={salesReport.monitorBiddingPackages + 'report-link'} />
                </TableCell>
                <TableCell sx={{ width: '3%', px: '3px' }} />
            </TableRow>
        </TableHead>
    );
};

export default BiddingReportThead;
