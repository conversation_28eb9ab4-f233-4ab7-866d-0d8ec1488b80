// material-ui
import { TableCell, TableHead, TableRow, useMediaQuery, useTheme } from '@mui/material';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// third party
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const BiddingThead = () => {
    const { biddingPermission } = PERMISSIONS.sale.salePipeline;
    const theme = useTheme();
    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                {checkAllowedPermission(biddingPermission.edit) ? (
                    <TableCell align="center" sx={{ position: 'sticky', left: 0, zIndex: 3, backgroundColor: 'white' }}>
                        <FormattedMessage id={salesReport.allSalesPineline + '-action'} />
                    </TableCell>
                ) : (
                    <></>
                )}
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-contract-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-service-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-project-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-customer'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-probability'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="revenue" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-status'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-size-vnd'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-size-usd'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-management-revenue-allocated'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-accountant-revenue-allocatedVND'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.allSalesPineline + '-license-fee'} />
                </TableCell>
                <TableCell sx={{ position: 'sticky', right: !!matches ? 0 : 'unset' }}>
                    <FormattedMessage id={salesReport.allSalesPineline + '-time-duration'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default BiddingThead;
