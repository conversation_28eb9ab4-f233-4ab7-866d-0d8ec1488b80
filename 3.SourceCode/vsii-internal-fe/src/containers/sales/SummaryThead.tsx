// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { useCallback, useState } from 'react';
import { FormattedMessage } from 'react-intl';

import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ISummaryTheadTheadProps {
    summaryLength: number;
}

const SummaryThead = (props: ISummaryTheadTheadProps) => {
    const { summaryLength } = props;

    const [dimensions, setDimensions] = useState<any>(null);

    const { salesReport } = TEXT_CONFIG_SCREEN;

    const callBackRef = useCallback(
        (domNode: any) => {
            if (summaryLength > 0 && domNode) {
                setDimensions(domNode.getBoundingClientRect());
            }
        },
        [summaryLength]
    );

    return (
        <TableHead>
            <TableRow
                sx={{
                    '& th': {
                        color: 'white !important',
                        textAlign: 'center',
                        background: '#3163D4',
                        whiteSpace: 'nowrap'
                    }
                }}
            >
                <TableCell rowSpan={2}>
                    <FormattedMessage id={salesReport.summary + '-indicator'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={salesReport.summary + '-usd'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={salesReport.summary + '-vnd'} />
                </TableCell>
                <TableCell rowSpan={1} colSpan={4}>
                    <FormattedMessage id={salesReport.summary + '-revenue-estimation'} />
                </TableCell>
                <TableCell ref={callBackRef} rowSpan={2}>
                    <FormattedMessage id={salesReport.summary + '-total-revenue-estimation'} />
                </TableCell>
            </TableRow>
            <TableRow
                sx={{
                    '& th': {
                        color: 'white !important',
                        textAlign: 'center',
                        background: '#3163D4',
                        borderBottom: 'none !important',
                        whiteSpace: 'nowrap'
                    },
                    '&': {
                        position: 'sticky',
                        top: dimensions && dimensions.height / 2
                    }
                }}
            >
                <TableCell>
                    <FormattedMessage id={salesReport.summary + '-1st-quarter'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.summary + '-2nd-quarter'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.summary + '-3rd-quarter'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.summary + '-4th-quarter'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default SummaryThead;
