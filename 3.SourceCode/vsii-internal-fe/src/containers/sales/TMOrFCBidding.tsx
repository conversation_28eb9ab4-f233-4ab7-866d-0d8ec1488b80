/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Stack } from '@mui/material';

// react-hook-form
import { useFieldArray, useForm } from 'react-hook-form';

// third party
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';

// project imports
import { useAppDispatch } from 'app/hooks';
import { FormProvider, Input, NumericFormatCustom } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { Table } from 'components/extended/Table';
import Api from 'constants/Api';
import { CONTRACT_TYPE_SALE_REPORT, E_BIDDING_STATUS, E_SALES_BIDDING_TYPE_SET_HCINFO } from 'constants/Common';
import { Unit } from 'containers/search';
import { FixCostBiddingFormDefaultValue, FixCostBiddingSchema, TMBiddingFormDefaultValue, TMBiddingFormSchema } from 'pages/sales/Config';
import sendRequest from 'services/ApiService';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { calculateMonthsDifference, dateFormat } from 'utils/date';
import MonthlyBillableTBody from './MonthlyBillableTBody';
import MonthlyBillableThead from './MonthlyBillableThead';

interface IAddOrEditBiddingProps {
    open?: boolean;
    contractType: string;
    sizeVND: string;
    exchangeRate: number;
    from: string;
    to: string;
    handleClose?: () => void;
    handleSetHCInfo: (data: any, type?: string) => void;
    hcInfoMonth: any;
    setHcInfoMonth: any;
    fixCostHcInfo: any;
    setFixCostHcInfo: any;
    status?: string;
}

const TMOrFCBidding = (props: IAddOrEditBiddingProps) => {
    // ================= Hooks, State, Variable =================
    const {
        open,
        handleClose,
        contractType,
        exchangeRate,
        sizeVND,
        from,
        to,
        handleSetHCInfo,
        hcInfoMonth,
        setHcInfoMonth,
        fixCostHcInfo,
        setFixCostHcInfo,
        status
    } = props;
    const dispatch = useAppDispatch();
    const isTMBidding = contractType === CONTRACT_TYPE_SALE_REPORT.TM ? true : false;
    const [totalAmount, setTotalAmount] = useState<number>(0);
    const [loadingEstimate, setLoadingEstimate] = useState<boolean>(false);

    const methodsTM = useForm({
        defaultValues: TMBiddingFormDefaultValue,
        mode: 'all',
        resolver: yupResolver(TMBiddingFormSchema)
    });

    const methodsFixCost = useForm({
        defaultValues: FixCostBiddingFormDefaultValue,
        mode: 'all',
        resolver: yupResolver(FixCostBiddingSchema)
    });

    const {
        fields: fieldsRateByMonth,
        append: appendRateByMonth,
        remove: removeRateByMonth
    } = useFieldArray({ control: methodsTM.control, name: 'rateByMonth' } as any);

    // ================= Function =================
    const calculateSum = (arr: any) => {
        const sum = arr.reduce((acc: any, curr: any) => +acc + +curr.amount, 0);
        setTotalAmount(sum);
    };

    // ================= Event =================
    const estimateBillableFixCost = () => {
        const contractAllocationByMonth = methodsFixCost.watch('contractAllocationByMonth');
        methodsFixCost.setValue('billable', +sizeVND / +contractAllocationByMonth);
    };

    // ================= Submit =================
    const handleSubmit = async (values: any) => {
        setLoadingEstimate(true);
        const estimateTMHCInfo = { contractType, from: dateFormat(from), to: dateFormat(to), unit: values?.unit, totalAmount };
        const estimateFixCostHCInfo = {
            contractType,
            from: dateFormat(from),
            to: dateFormat(to),
            totalAmount: sizeVND,
            contractAllocationByMonth: values.contractAllocationByMonth
        };
        const payloadEstimateHCInfo = isTMBidding ? estimateTMHCInfo : estimateFixCostHCInfo;
        const repsonseEstimate = await sendRequest(Api.sale_pipe_line_bidding.estimateHCInfo, payloadEstimateHCInfo);
        if (repsonseEstimate?.status) {
            const { result } = repsonseEstimate;
            handleSetHCInfo(result.content, E_SALES_BIDDING_TYPE_SET_HCINFO.ESTIMATE);
            isTMBidding ? setHcInfoMonth(values) : setFixCostHcInfo(values);
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'estimate-success',
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            setLoadingEstimate(false);
        } else {
            setLoadingEstimate(false);
        }
    };

    // ================= Effect =================
    useEffect(() => {
        if (isTMBidding) {
            methodsTM.reset(hcInfoMonth);
        } else {
            const allocationMonths = calculateMonthsDifference(from, to);
            methodsFixCost.reset({ ...fixCostHcInfo, contractAllocationByMonth: allocationMonths });
        }
    }, []);

    useEffect(() => {
        const { unsubscribe } = methodsTM.watch((value, info) => {
            if (info.name?.startsWith('rateByMonth')) {
                const index = info.name.split('.')[1];
                const rateByMonth: any = methodsTM.watch(`rateByMonth`);
                if (info.name.endsWith('rate') || info.name.endsWith('quantity')) {
                    const rateVND = +exchangeRate * +rateByMonth[index].rate;
                    methodsTM.setValue(`rateByMonth.${index}.rateVND` as any, rateVND);
                    methodsTM.setValue(`rateByMonth.${index}.amount` as any, +rateByMonth[index].quantity * rateVND);
                }
                calculateSum(rateByMonth);
            }
        });
        return () => unsubscribe();
    }, [methodsTM.watch, exchangeRate]);

    return (
        <Modal isOpen={open!} title={'monthly-billable'} onClose={handleClose} keepMounted={false} maxWidth={isTMBidding ? 'md' : 'sm'}>
            <FormProvider formReturn={(isTMBidding ? methodsTM : methodsFixCost) as any} onSubmit={handleSubmit}>
                {isTMBidding ? (
                    <Stack spacing={2}>
                        <div style={{ width: '25%' }}>
                            <Unit name="unit" disabled={status === E_BIDDING_STATUS.CONTRACT} />
                        </div>
                        <div>
                            <Button
                                onClick={() => appendRateByMonth({ role: '', rate: 0, rateVND: 0, quantity: 0, amount: 0 })}
                                variant="contained"
                                disabled={status === E_BIDDING_STATUS.CONTRACT}
                            >
                                <FormattedMessage id="add" />
                            </Button>
                        </div>
                        <Table
                            heads={<MonthlyBillableThead lenght={fieldsRateByMonth.length} />}
                            data={fieldsRateByMonth}
                            heightTableEmpty="100px"
                        >
                            <MonthlyBillableTBody
                                fieldsRateByMonth={fieldsRateByMonth}
                                removeRateByMonth={removeRateByMonth}
                                status={status}
                            />
                        </Table>
                    </Stack>
                ) : (
                    <>
                        <Stack spacing={2}>
                            <div>
                                <Input
                                    name="contractAllocationByMonth"
                                    required
                                    label={<FormattedMessage id="contract-allocation-by-month" />}
                                    disabled
                                    textFieldProps={{
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                />
                            </div>
                            <div>
                                <Button
                                    onClick={estimateBillableFixCost}
                                    variant="contained"
                                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                                >
                                    <FormattedMessage id="estimate" />
                                </Button>
                            </div>
                            <div>
                                <Input
                                    textFieldProps={{
                                        InputProps: {
                                            inputComponent: NumericFormatCustom as any
                                        }
                                    }}
                                    name="billable"
                                    label={<FormattedMessage id="billable" />}
                                    disabled={status === E_BIDDING_STATUS.CONTRACT}
                                />
                            </div>
                        </Stack>
                    </>
                )}
                <DialogActions>
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <Button color="error" onClick={handleClose} disabled={loadingEstimate}>
                            <FormattedMessage id="cancel" />
                        </Button>
                        <LoadingButton
                            variant="contained"
                            type="submit"
                            loading={loadingEstimate}
                            disabled={status === E_BIDDING_STATUS.CONTRACT}
                        >
                            <FormattedMessage id="submit" />
                        </LoadingButton>
                    </Stack>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default TMOrFCBidding;
