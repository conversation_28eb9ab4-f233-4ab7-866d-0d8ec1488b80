import { FormattedMessage } from 'react-intl';

// materia-ui
import { ListItem, ListItemIcon, ListItemText, Stack } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { nonBIllableConfigSelector } from 'store/slice/nonBillableConfigSlice';
import { useAppSelector } from 'app/hooks';
import MainCard from 'components/cards/MainCard';
import { gridSpacing } from 'store/constant';
import { IOption } from 'types';

// assets
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';

const NonBillByMemberNote = () => {
    const theme = useTheme();

    const { timesheetStatusOptions } = useAppSelector(nonBIllableConfigSelector);

    return (
        <MainCard
            sx={{
                '& div .MuiCardContent-root': {
                    padding: '0 24px !important'
                },
                marginBottom: theme.spacing(gridSpacing)
            }}
        >
            <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                spacing={2}
                sx={{
                    overflowX: 'auto'
                }}
            >
                {timesheetStatusOptions.map((item: IOption, index) => (
                    <ListItem key={index}>
                        <ListItemIcon>
                            <FiberManualRecordIcon sx={{ color: item.color }} />
                        </ListItemIcon>
                        <ListItemText primary={<FormattedMessage id={item.label} />} />
                    </ListItem>
                ))}
            </Stack>
        </MainCard>
    );
};

export default NonBillByMemberNote;
