import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

function NonBillByMemberThead() {
    const { nonBillable } = PERMISSIONS.report;

    const { nBMByMember } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'title'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'dept'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'total-efforts-manhours'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'projects'} />
                </TableCell>
                <TableCell sx={{ color: '#D9001B !important' }}>
                    <FormattedMessage id={nBMByMember + 'not-enough-timesheets-ratio'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'billables-projects'} />
                </TableCell>
                {checkAllowedPermission(nonBillable.commentDetail) && (
                    <TableCell>
                        <FormattedMessage id={nBMByMember + 'actions'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
}
export default NonBillByMemberThead;
