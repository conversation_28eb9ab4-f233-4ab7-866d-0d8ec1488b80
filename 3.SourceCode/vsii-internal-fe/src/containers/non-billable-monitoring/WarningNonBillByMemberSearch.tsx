import { Grid, SelectChangeEvent } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { INonBillConfig, nonBillConfig, nonBillSchema } from 'pages/non-billable-monitoring/Config';
import { Department, SearchForm, Weeks, Years } from '../search';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { Label } from 'components/extended/Form';
import { Button } from 'components';
import { IOption } from 'types';

interface IWarningNonBillByMemberSearchProps {
    conditions: INonBillConfig;
    weeks: IOption[];
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    handleSearch: (value: INonBillConfig) => void;
}

const WarningNonBillByMemberSearch = (props: IWarningNonBillByMemberSearchProps) => {
    const { conditions, weeks, handleChangeYear, handleSearch } = props;

    const { nBMByMember } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;

    return (
        <SearchForm defaultValues={nonBillConfig} formSchema={nonBillSchema} handleSubmit={handleSearch} formReset={conditions}>
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Years handleChangeYear={handleChangeYear} ignoreDefault label={nBMByMember + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Weeks weeks={weeks} label={nBMByMember + 'weeks'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Department label={nBMByMember + 'dept'} />
                </Grid>

                <Grid item xs={12} lg={2.4} />

                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={nBMByMember + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default WarningNonBillByMemberSearch;
