import { useEffect } from 'react';
// material-ui
import { FormattedMessage } from 'react-intl';
import { Grid } from '@mui/material';

// project imports
import { INonBillConfig, nonBillConfig, nonBillSchema } from 'pages/non-billable-monitoring/Config';
import { getTimesheetStatus, nonBIllableConfigSelector } from 'store/slice/nonBillableConfigSlice';
import { Department, SearchForm, Weeks, Years } from '../search';
import { searchFormConfig } from 'containers/search/Config';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { Label, Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { Button } from 'components';
import { IOption } from 'types';

interface INonBillByMemberSearchProps {
    conditions: INonBillConfig;
    weeks: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
}

const NonBillByMemberSearch = (props: INonBillByMemberSearchProps) => {
    const { conditions, weeks, handleChangeYear, handleSearch } = props;
    const { timesheetStatusOptions } = useAppSelector(nonBIllableConfigSelector);

    const { nBMByMember } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;

    const dispatch = useAppDispatch();
    useEffect(() => {
        dispatch(getTimesheetStatus({ color: true }));
    }, [dispatch]);

    return (
        <SearchForm defaultValues={nonBillConfig} formSchema={nonBillSchema} handleSubmit={handleSearch} formReset={conditions}>
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Years handleChangeYear={handleChangeYear} label={nBMByMember + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Weeks weeks={weeks} label={nBMByMember + 'weeks'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Department label={nBMByMember + 'dept'} />
                </Grid>

                <Grid item xs={12} lg={2.4}>
                    <Select
                        isMultipleLanguage
                        selects={[DEFAULT_VALUE_OPTION, ...timesheetStatusOptions]}
                        name={searchFormConfig.timeStatus.name}
                        label={<FormattedMessage id={nBMByMember + 'timesheet-status'} />}
                    />
                </Grid>

                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={nBMByMember + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default NonBillByMemberSearch;
