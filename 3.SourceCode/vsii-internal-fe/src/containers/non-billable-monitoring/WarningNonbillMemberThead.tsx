import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

function WarningNonbillMemberThead() {
    const { nBMByMember } = TEXT_CONFIG_SCREEN.nonBillablemonitoring;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'title'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={nBMByMember + 'dept'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={nBMByMember + 'nbm-consecutive-week'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
}
export default WarningNonbillMemberThead;
