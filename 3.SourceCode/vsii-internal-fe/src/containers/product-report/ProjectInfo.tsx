// Third party
import { Grid, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project import
import { filterSprint, productReportSelector } from 'store/slice/productReportSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { Select } from 'components/extended/Form';
import { formatPrice } from 'utils/common';

const ProductInfo = () => {
    const { sprints, projectDetail, sprintSelectDefaut } = useAppSelector(productReportSelector);

    const dispatch = useAppDispatch();

    return (
        <Grid container gap={{ xs: 2, sm: 0, md: 0 }} justifyContent="space-between">
            <Grid container item xs={12} sm={5.9} md={5.9}>
                <Grid
                    container
                    item
                    xs={12}
                    sx={{
                        mb: 2
                    }}
                >
                    <Grid item xs={3} display="flex" alignItems="center">
                        <FormattedMessage id="product-report.modal.leftSide.prjInfo.project" />
                    </Grid>
                    <Grid
                        item
                        xs={9}
                        sx={{
                            bgcolor: '#eeeeee',
                            p: '10px 5px',
                            borderRadius: '5px',
                            border: '1px solid rgba(0, 0, 0, 0.26)'
                        }}
                    >
                        <Typography>{projectDetail?.projectName}</Typography>
                    </Grid>
                </Grid>
                <Grid container item xs={12}>
                    <Grid item xs={3} display="flex" alignItems="center">
                        <FormattedMessage id="product-report.modal.leftSide.prjInfo.sprint" />
                    </Grid>
                    <Grid item xs={9}>
                        <Select
                            name="sprint"
                            selects={(
                                projectDetail?.sprints.map((sprint) => ({
                                    label: sprint.name,
                                    value: sprint.sprintId.toString()
                                })) || []
                            ).concat([{ value: 'all', label: 'All' }])}
                            handleChangeFullOption={({ value }) => dispatch(filterSprint(value))}
                            defaultValue={sprintSelectDefaut || projectDetail?.sprints[0]?.name || 'all'}
                            isControl={false}
                        />
                    </Grid>
                </Grid>
            </Grid>
            <Grid item xs={12} sm={5.9} md={5.9} gridRow={2}>
                <Grid container item xs={12}>
                    <Grid item xs={3} display="flex" alignItems="center">
                        <FormattedMessage id="product-report.modal.leftSide.prjInfo.sprintCost" />
                    </Grid>
                    <Grid
                        item
                        xs={9}
                        sx={{
                            bgcolor: '#eeeeee',
                            p: '10px 5px',
                            borderRadius: '5px',
                            border: '1px solid rgba(0, 0, 0, 0.26)'
                        }}
                    >
                        <Typography>{formatPrice(sprints.reduce((prev, next) => prev + (next.currentSprintCost || 0), 0))}</Typography>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default ProductInfo;
