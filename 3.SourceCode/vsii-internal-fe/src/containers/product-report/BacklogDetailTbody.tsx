// material-ui
import { TableCell, TableRow } from '@mui/material';

//project import
import ProductReportStatusChip from 'components/extended/ProductReportStatusChip';
import { IRequirement } from 'types';

interface IBacklogDetailTbodyProps {
    data: IRequirement['tasks'][number];
}

const BacklogDetailTbody = (props: IBacklogDetailTbodyProps) => {
    const { data } = props;

    return (
        <TableRow>
            <TableCell sx={{ width: '55%', px: '3px' }}>{data.taskName}</TableCell>
            <TableCell sx={{ width: '30%', px: '3px' }}>{data.assigneeName}</TableCell>
            <TableCell sx={{ width: '15%', px: 0 }}>
                <ProductReportStatusChip status={data.status} />
            </TableCell>
        </TableRow>
    );
};

export default BacklogDetailTbody;
