// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// third party
import { FormattedMessage } from 'react-intl';

const BacklogThead = () => {
    return (
        <TableHead
            sx={{
                position: 'sticky',
                top: '0',
                zIndex: '99'
            }}
        >
            <TableRow>
                <TableCell sx={{ width: '2%', px: '3px' }}>
                    <FormattedMessage id="product-report.modal.rightSide.table.no" />
                </TableCell>
                <TableCell sx={{ width: '35%', px: '3px' }}>
                    <FormattedMessage id="product-report.modal.rightSide.table.requirement" />
                </TableCell>
                <TableCell sx={{ width: '35%', px: '3px' }}>
                    <FormattedMessage id="product-report.modal.rightSide.table.sprint" />
                </TableCell>
                <TableCell sx={{ width: '14%', px: 0 }}>
                    <FormattedMessage id="product-report.modal.rightSide.table.status" />
                </TableCell>
                <TableCell sx={{ width: '14%', px: 0 }}>
                    <FormattedMessage id="product-report.modal.rightSide.table.totalEffort" />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default BacklogThead;
