// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// third party
import { FormattedMessage } from 'react-intl';

const SprintThead = () => {
    return (
        <TableHead
            sx={{
                position: 'sticky',
                top: '0',
                zIndex: '99'
            }}
        >
            <TableRow>
                <TableCell sx={{ width: '3%', px: 0 }} />
                <TableCell sx={{ width: '27%' }}>
                    <FormattedMessage id="product-report.modal.leftSide.table.requirement" />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id="product-report.modal.leftSide.table.taskName" />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id="product-report.modal.leftSide.table.assignee" />
                </TableCell>
                <TableCell sx={{ width: '13%', px: 0 }}>
                    <FormattedMessage id="product-report.modal.leftSide.table.status" />
                </TableCell>
                <TableCell sx={{ width: '12%' }}>
                    <FormattedMessage id="product-report.modal.leftSide.table.sprintCost" />
                </TableCell>
                <TableCell sx={{ width: '10%' }}>
                    <FormattedMessage id="product-report.modal.leftSide.table.effort" />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default SprintThead;
