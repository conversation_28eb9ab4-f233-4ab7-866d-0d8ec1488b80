// material-ui
import { Grid, TableBody, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';

//project import
import BacklogDetailThead from './BacklogDetailThead';
import BacklogDetailTBody from './BacklogDetailTbody';
import { Table } from 'components/extended/Table';
import { gridSpacing } from 'store/constant';
import { IRequirement } from 'types';

interface IBacklogDetailProps {
    data: IRequirement;
}

const BacklogDetail = (props: IBacklogDetailProps) => {
    const { data } = props;

    return (
        <Grid container spacing={gridSpacing}>
            <Grid item xs={12}>
                <Typography variant="h3" sx={{ textDecoration: 'underline', my: '10px' }}>
                    {data.requireName}
                </Typography>
            </Grid>
            <Grid
                item
                xs={12}
                sx={{
                    my: '15px'
                }}
            >
                <Typography variant="h3">
                    <FormattedMessage id="product-report.modal.rightSide.backlog.description" />
                </Typography>
                <Typography component="pre" sx={{ mt: '10px' }}>
                    {data.desc}
                </Typography>
            </Grid>
            <Grid item xs={12}>
                <Table
                    heads={<BacklogDetailThead />}
                    data={data.tasks}
                    sx={{ maxHeight: '50vh' }}
                    heightTableEmpty="200px"
                    borderedEmpty={false}
                >
                    <TableBody>
                        {data.tasks.map((data, index) => (
                            <BacklogDetailTBody key={index} data={data} />
                        ))}
                    </TableBody>
                </Table>
            </Grid>
        </Grid>
    );
};

export default BacklogDetail;
