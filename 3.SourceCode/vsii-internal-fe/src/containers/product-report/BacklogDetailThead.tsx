// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// third party
import { FormattedMessage } from 'react-intl';

const BacklogDetailThead = () => {
    return (
        <TableHead>
            <TableRow>
                <TableCell sx={{ width: '55%', px: '3px' }}>
                    <FormattedMessage id="product-report.modal.rightSide.backlog.table.taskName" />
                </TableCell>
                <TableCell sx={{ width: '30%', px: '3px' }}>
                    <FormattedMessage id="product-report.modal.rightSide.backlog.table.assign" />
                </TableCell>
                <TableCell sx={{ width: '15%', px: 0 }}>
                    <FormattedMessage id="product-report.modal.rightSide.backlog.table.status" />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default BacklogDetailThead;
