import React, { useState } from 'react';

// material-ui
import { IconButton, TableCell, TableRow, TableBody, CircularProgress } from '@mui/material';

//project import
import { getReportByProjectId, productReportSelector, setSprintSelectDefault } from 'store/slice/productReportSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { VerifyWorkingDay } from 'components/icons';
import { formatPrice } from 'utils/common';
import { dateFormat } from 'utils/date';

interface IProductReportTbodyProps {
    page: number;
    size: number;
    setOpenDetailModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const ProductReportTbody = (props: IProductReportTbodyProps) => {
    const { page, size, setOpenDetailModal } = props;
    const [loadingIndex, setLoadingIndex] = useState<number>(-1);

    const { productReport, loading } = useAppSelector(productReportSelector);

    const dispatch = useAppDispatch();

    return (
        <TableBody>
            {productReport?.map((data, index) => (
                <TableRow key={index}>
                    <TableCell>{size * page + index + 1}</TableCell>
                    <TableCell>{data.projectName}</TableCell>
                    <TableCell>{data.name}</TableCell>
                    <TableCell>{data.completed}</TableCell>
                    <TableCell>{data.totalEffors}</TableCell>
                    <TableCell>{dateFormat(data.sprintStartDate)}</TableCell>
                    <TableCell>{dateFormat(data.sprintEndDate)}</TableCell>
                    <TableCell>{formatPrice(data.currentSprintCost)}</TableCell>
                    <TableCell>{formatPrice(data.projectCost)}</TableCell>
                    <TableCell>
                        <IconButton
                            aria-label="list"
                            size="small"
                            onClick={async () => {
                                setLoadingIndex(index);
                                dispatch(setSprintSelectDefault(data.sprintId?.toString()));
                                const resultAction = await dispatch(getReportByProjectId({ projectId: data.projectId }));
                                if (getReportByProjectId.fulfilled.match(resultAction)) {
                                    setOpenDetailModal(true);
                                }
                            }}
                            disabled={loading[getReportByProjectId.typePrefix]}
                        >
                            {loading[getReportByProjectId.typePrefix] && index === loadingIndex ? (
                                <CircularProgress size={24} />
                            ) : (
                                <VerifyWorkingDay />
                            )}
                        </IconButton>
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ProductReportTbody;
