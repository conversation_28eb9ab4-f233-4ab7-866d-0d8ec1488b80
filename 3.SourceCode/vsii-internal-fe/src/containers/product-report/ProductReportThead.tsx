// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

const ProductReportThead = () => {
    const { ProductReport } = TEXT_CONFIG_SCREEN.generalReport;
    return (
        <TableHead
            sx={{
                position: 'sticky',
                top: '0',
                zIndex: '99'
            }}
        >
            <TableRow>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'project'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'sprint'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'completed'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'total-efforts-hours'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'start-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'end-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'current-sprint-cost'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={ProductReport + 'total-project-cost'} />
                </TableCell>
                <TableCell />
            </TableRow>
        </TableHead>
    );
};

export default ProductReportThead;
