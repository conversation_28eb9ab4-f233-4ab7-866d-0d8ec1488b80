import { useEffect, useState } from 'react';

// Third party
import KeyboardDoubleArrowLeftIcon from '@mui/icons-material/KeyboardDoubleArrowLeft';
import { Button, Grid, Typography, IconButton, TableBody, Box } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { FormattedMessage } from 'react-intl';
import uniq from 'lodash/uniq';

// project import
import { productReportSelector } from 'store/slice/productReportSlice';
import { DEFAULT_VALUE_OPTION } from 'constants/Common';
import { Select } from 'components/extended/Form';
import { Table } from 'components/extended/Table';
import Modal from 'components/extended/Modal';
import BacklogDetail from './BacklogDetail';
import { useAppSelector } from 'app/hooks';
import BacklogTbody from './BacklogTbody';
import BacklogThead from './BacklogThead';
import ProductInfo from './ProjectInfo';
import SprintData from './SprintData';
import { IRequirement } from 'types';

interface IProductDetailProps {
    isOpen: boolean;
    handleClose: () => void;
}

const ProductDetail = (props: IProductDetailProps) => {
    const { isOpen, handleClose } = props;

    const [showViewBacklog, setShowViewbacklog] = useState<boolean>(false);
    const [backlogDetail, setBacklogDetail] = useState<IRequirement>();
    const [statusFilter, setStatusFilter] = useState<string>('');

    const { projectDetail } = useAppSelector(productReportSelector);

    const requireMents = projectDetail?.viewBackLogs?.content.map((backlog) => backlog.requireMents).flat() || [];

    const requireMentsFilter = requireMents.filter((requirement) => (statusFilter && requirement.status === statusFilter) || !statusFilter);

    const handleShowBacklog = () => {
        setBacklogDetail(undefined);
        setStatusFilter('');
        setShowViewbacklog((prev) => !prev);
    };

    useEffect(() => {
        if (!isOpen) {
            setBacklogDetail(undefined);
            setShowViewbacklog(false);
            setStatusFilter('');
        }
    }, [isOpen]);

    return (
        <Modal
            isOpen={isOpen}
            title="product-report.modal.title"
            onClose={handleClose}
            keepMounted={false}
            maxWidth="xl"
            footer={
                <Button variant="contained" onClick={handleClose}>
                    <FormattedMessage id="cancel" />
                </Button>
            }
            footerProps={{
                sx: { justifyContent: 'center', display: { xs: showViewBacklog ? 'none' : undefined, md: 'flex' } }
            }}
            bodyProps={{
                sx: { overflow: { xs: showViewBacklog ? 'auto' : undefined } }
            }}
        >
            <Grid container justifyContent="space-between" sx={{ minHeight: '60vh' }}>
                <Grid
                    container
                    item
                    xs={12}
                    md={showViewBacklog ? 7 : 12}
                    gap={2}
                    sx={{
                        visibility: { xs: showViewBacklog ? 'hidden' : 'visible', md: 'visible' },
                        transition: 'width 0.2s, visibility 0s'
                    }}
                >
                    <Grid item xs={12} display="flex" justifyContent="space-between" sx={{ height: '40px' }}>
                        <Typography variant="h3" sx={{ fontSize: '0.875rem', display: 'inline-block' }}>
                            <FormattedMessage id="product-report.modal.leftSide.title" />
                        </Typography>
                        <Button
                            children={<FormattedMessage id="product-report.modal.leftSide.button.viewbacklog" />}
                            variant="contained"
                            onClick={handleShowBacklog}
                        />
                    </Grid>
                    <ProductInfo />
                    <SprintData />
                </Grid>
                <Grid
                    item
                    xs={12}
                    md={showViewBacklog ? 4.9 : 0.000000001}
                    sx={{
                        position: { xs: 'absolute', md: showViewBacklog ? 'relative' : 'absolute' },
                        visibility: showViewBacklog ? 'visible' : 'hidden',
                        right: { xs: showViewBacklog ? 15 : 0, md: showViewBacklog ? 0 : 25 },
                        width: { xs: '95%', md: '100%' },
                        zIndex: { xs: 99 },
                        backgroundColor: '#ffffff',
                        transition: { xs: '0.2s', md: '0.01s' }
                    }}
                >
                    <Grid item xs={12} sx={{ p: '10px', bgcolor: '#3163d4' }} position="relative">
                        {backlogDetail ? (
                            <IconButton
                                onClick={() => setBacklogDetail(undefined)}
                                sx={{ position: 'absolute', left: 8, top: 2, color: '#ffffff' }}
                            >
                                <KeyboardDoubleArrowLeftIcon fontSize="small" />
                            </IconButton>
                        ) : null}
                        <Typography
                            variant="h3"
                            sx={{ textAlign: 'center', fontSize: '0.875rem', color: '#ffffff' }}
                            display={showViewBacklog ? undefined : 'none'}
                        >
                            <FormattedMessage id="product-report.modal.rightSide.title" />
                        </Typography>
                        <IconButton onClick={handleShowBacklog} sx={{ position: 'absolute', right: 8, top: 2, color: '#ffffff' }}>
                            <HighlightOffIcon fontSize="small" />
                        </IconButton>
                    </Grid>
                    {backlogDetail ? (
                        <BacklogDetail data={backlogDetail} />
                    ) : (
                        <Box display={showViewBacklog ? 'flex' : 'none'} flexDirection="column">
                            <Grid container display="flex" justifyContent="space-between" sx={{ my: 2 }}>
                                <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2" sx={{ opacity: 0.7 }}>
                                        <FormattedMessage id="status" />
                                    </Typography>
                                    <Box sx={{ width: 200 }}>
                                        <Select
                                            name="status"
                                            isControl={false}
                                            valueSelect={statusFilter}
                                            selects={[
                                                DEFAULT_VALUE_OPTION,
                                                ...uniq(requireMents.map((require) => require.status))?.map((status) => ({
                                                    value: status,
                                                    label: status
                                                }))
                                            ]}
                                            handleChangeFullOption={({ value }) => setStatusFilter(value.toString())}
                                        />
                                    </Box>
                                </Box>
                                <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2" sx={{ opacity: 0.7 }}>
                                        <FormattedMessage id="product-report.modal.rightSide.table.totalProjectEffort" />
                                    </Typography>
                                    <Typography variant="h3" sx={{ fontSize: '0.875rem', color: '#000000' }}>
                                        {(projectDetail?.viewBackLogs?.totalEffort || 0).toFixed(2)}
                                    </Typography>
                                </Box>
                            </Grid>
                            <Table heads={<BacklogThead />} data={[...requireMentsFilter]} sx={{ maxHeight: '53.5vh' }}>
                                <TableBody>
                                    {requireMentsFilter.map((requirement, index) => (
                                        <BacklogTbody key={index} index={index} data={requirement} setBacklogDetail={setBacklogDetail} />
                                    ))}
                                </TableBody>
                            </Table>
                        </Box>
                    )}
                </Grid>
            </Grid>
        </Modal>
    );
};

export default ProductDetail;
