// Third party
import { Grid, Typography, TableBody, Box, Tooltip } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project import
import ProductReportStatusChip from 'components/extended/ProductReportStatusChip';
import { productReportSelector } from 'store/slice/productReportSlice';
import { Table } from 'components/extended/Table';
import { useAppSelector } from 'app/hooks';
import SprintThead from './SprintThead';
import SprintTbody from './SprintTbody';
import { dateFormat } from 'utils/date';

const SprintData = () => {
    const { sprints } = useAppSelector(productReportSelector);

    return (
        <>
            {sprints.map((sprint, index) => (
                <Grid key={sprint.sprintId} container justifyContent="space-between" sx={{ mt: index > 0 ? 2 : undefined }}>
                    <Grid container item gap={{ xs: 2, md: 0 }} display="flex" xs={12}>
                        <Grid item xs={12} md={6}>
                            <Typography variant="h3" sx={{ fontSize: '0.875rem', display: 'inline-block' }}>
                                {sprint.name} ({dateFormat(sprint.sprintStartDate)} - {dateFormat(sprint.sprintEndDate)})
                            </Typography>
                        </Grid>
                        <Grid
                            container
                            item
                            gap={{ xs: 0.5, md: 2 }}
                            xs={12}
                            md={6}
                            display="flex"
                            direction={{ xs: 'column', md: 'row' }}
                            justifyContent="flex-end"
                        >
                            <Box display="flex" gap={1}>
                                <Typography variant="body2" sx={{ opacity: 0.7 }}>
                                    <FormattedMessage id="product-report.modal.leftSide.chart.completed" />
                                </Typography>
                                <Typography variant="h3" sx={{ fontSize: '0.875rem', color: '#027804' }}>
                                    {(sprint.completed || 0).toFixed(1)}%
                                </Typography>
                            </Box>
                            <Box display="flex" gap={1}>
                                <Typography variant="body2" sx={{ opacity: 0.7 }}>
                                    <FormattedMessage id="product-report.modal.leftSide.chart.notCompleted" />
                                </Typography>
                                <Typography variant="h3" sx={{ fontSize: '0.875rem', color: '#D74642' }}>
                                    {(100 - (sprint.completed || 0)).toFixed(1)}%
                                </Typography>
                            </Box>
                            <Tooltip
                                placement="top"
                                title={
                                    <Grid
                                        sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            width: '200px',
                                            padding: '5px',
                                            rowGap: '5px'
                                        }}
                                    >
                                        <Grid display="flex" item xs={12}>
                                            <Grid item xs={2.5}>
                                                <ProductReportStatusChip status="Task" color="#4E81E5" />
                                            </Grid>
                                            <Grid item xs={5.5}>
                                                <Typography variant="body2" sx={{ color: '#ffffff' }}>
                                                    <FormattedMessage id="product-report.modal.leftSide.chart.tooltipTask" />
                                                </Typography>
                                            </Grid>
                                            <Grid item xs={4}>
                                                <Typography variant="body2" sx={{ color: '#ffffff', textAlign: 'end' }}>
                                                    {(sprint.effortTask || 0).toFixed(2)}
                                                </Typography>
                                            </Grid>
                                        </Grid>
                                        <Grid display="flex" item xs={12}>
                                            <Grid item xs={2.5}>
                                                <ProductReportStatusChip status="Bug" color="#F69093" />
                                            </Grid>
                                            <Grid item xs={5.5}>
                                                <Typography variant="body2" sx={{ color: '#ffffff' }}>
                                                    <FormattedMessage id="product-report.modal.leftSide.chart.tooltipBug" />
                                                </Typography>
                                            </Grid>
                                            <Grid item xs={4}>
                                                <Typography variant="body2" sx={{ color: '#ffffff', textAlign: 'end' }}>
                                                    {(sprint.effortBug || 0).toFixed(2)}
                                                </Typography>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                }
                            >
                                <Box display="flex" gap={1}>
                                    <Typography variant="body2" sx={{ opacity: 0.7 }}>
                                        <FormattedMessage id="product-report.modal.leftSide.chart.totalEffort" />
                                    </Typography>
                                    <Typography variant="h3" sx={{ fontSize: '0.875rem', color: '#000000' }}>
                                        {(sprint.totalEffors || 0).toFixed(2)}
                                    </Typography>
                                </Box>
                            </Tooltip>
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Table heads={<SprintThead />} isLoading={false} data={sprint.requireMents} heightTableEmpty="50px">
                            <TableBody>
                                {sprint.requireMents.map((requirement, index) => (
                                    <SprintTbody key={index} data={requirement} />
                                ))}
                            </TableBody>
                        </Table>
                    </Grid>
                </Grid>
            ))}
        </>
    );
};

export default SprintData;
