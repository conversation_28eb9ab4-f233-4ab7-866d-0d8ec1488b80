import { useState } from 'react';
import { Collapse, IconButton, TableCell, TableRow } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

//project import
import ProductReportStatusChip from 'components/extended/ProductReportStatusChip';
import { formatPrice } from 'utils/common';
import { IRequirement } from 'types';

interface ISprintTbodyProps {
    data: IRequirement;
}

const SprintTbody = (props: ISprintTbodyProps) => {
    const { data } = props;

    const [open, setOpen] = useState<boolean>(false);

    return (
        <>
            <TableRow
                sx={{
                    '& > .MuiTableCell-root': {
                        borderBottom: open ? undefined : 'none'
                    }
                }}
            >
                <TableCell sx={{ width: '3%', px: 0 }}>
                    {data.tasks?.length ? (
                        <IconButton sx={{ p: 0 }} aria-label="expand row" size="small" onClick={() => setOpen(!open)}>
                            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                        </IconButton>
                    ) : null}
                </TableCell>
                <TableCell sx={{ width: '27%' }}>{data.requireName}</TableCell>
                <TableCell sx={{ width: '20%' }} />
                <TableCell sx={{ width: '15%' }} />
                <TableCell sx={{ width: '13%', px: 0 }}>
                    <ProductReportStatusChip status={data.status} />
                </TableCell>
                <TableCell sx={{ width: '12%' }}>{formatPrice(data.requirementCost || 0)}</TableCell>
                <TableCell sx={{ width: '10%' }}>{data.effort?.toFixed(1)}</TableCell>
            </TableRow>
            <TableRow>
                <TableCell colSpan={2} sx={{ padding: open ? '6px 0' : 0, width: '30%' }} />
                <TableCell colSpan={5} sx={{ padding: open ? '6px 0' : 0, width: '70%' }}>
                    <Collapse in={open} timeout="auto" unmountOnExit>
                        {data?.tasks.map((task, i) => (
                            <TableRow
                                key={i}
                                sx={{
                                    display: 'table',
                                    width: '100%',
                                    '& > .MuiTableCell-root': {
                                        borderBottom: i < data?.tasks.length - 1 ? undefined : 'none'
                                    }
                                }}
                            >
                                <TableCell sx={{ width: '28.6%' }} colSpan={1}>
                                    {task.taskName}
                                </TableCell>
                                <TableCell sx={{ width: '21.5%' }} colSpan={1}>
                                    {task.assigneeName}
                                </TableCell>
                                <TableCell sx={{ width: '18.6%', px: 0 }} colSpan={1}>
                                    <ProductReportStatusChip status={task.status} />
                                </TableCell>
                                <TableCell sx={{ width: '17.1%' }} colSpan={1} />
                                <TableCell sx={{ width: '14.2%' }} colSpan={1}>
                                    {task.effort?.toFixed(1)}
                                </TableCell>
                            </TableRow>
                        ))}
                    </Collapse>
                </TableCell>
            </TableRow>
        </>
    );
};

export default SprintTbody;
