// material-ui
import { Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { SearchForm } from '../search';
import { IProductReportConfig, productReportSchema } from 'pages/Config';
import { Autocomplete } from 'components/extended/Form';
import { addProjectSuccessSelector } from 'store/slice/projectSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { getProjectReportOption, productReportSelector } from 'store/slice/productReportSlice';
import { IOption } from 'types';

//third-party
import { FormattedMessage } from 'react-intl';
import { useEffect, useState } from 'react';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IProductReportSearchProps {
    projectDefault: IProductReportConfig['projectId'];
    handleSearch: (value: any) => void;
}

const ProductReportSearch = (props: IProductReportSearchProps) => {
    const { projectDefault, handleSearch } = props;

    const [formReset, setFormReset] = useState<IProductReportConfig['projectId']>(projectDefault);

    const addProjectSuccess = useAppSelector(addProjectSuccessSelector);
    const { productReportOption } = useAppSelector(productReportSelector);

    const { ProductReport } = TEXT_CONFIG_SCREEN.generalReport;

    const dispatch = useAppDispatch();

    async function getAllProjectReport(projectId: string | number | undefined) {
        const resultAction = await dispatch(getProjectReportOption());
        if (getProjectReportOption.fulfilled.match(resultAction)) {
            if (projectId !== undefined) {
                const option = resultAction.payload.find((proj) => proj.value === projectId);
                if (option) {
                    setFormReset((prev) => ({ ...prev, projectId: option }));
                }
            }
        }
    }

    useEffect(() => {
        getAllProjectReport(projectDefault.value);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [addProjectSuccess, projectDefault.value]);

    return (
        <SearchForm defaultValues={{ projectId: null }} formSchema={productReportSchema} handleSubmit={handleSearch} formReset={formReset}>
            <Grid container justifyContent="space-between" alignItems="center" spacing={2}>
                <Grid item xs={12} lg={4}>
                    <Autocomplete
                        name="projectId"
                        options={productReportOption}
                        label={<FormattedMessage id={ProductReport + 'project'} />}
                        groupBy={(option: IOption) => option.typeCode}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={ProductReport + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ProductReportSearch;
