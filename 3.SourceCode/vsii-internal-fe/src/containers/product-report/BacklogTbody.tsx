// material-ui
import { TableCell, TableRow, Typography } from '@mui/material';

//project import
import ProductReportStatusChip from 'components/extended/ProductReportStatusChip';
import { IRequirement } from 'types';

interface IBacklogTbodyProps {
    index: number;
    data: IRequirement;
    setBacklogDetail: React.Dispatch<React.SetStateAction<IRequirement | undefined>>;
}

const BacklogTbody = (props: IBacklogTbodyProps) => {
    const { data, index, setBacklogDetail } = props;

    return (
        <TableRow>
            <TableCell sx={{ width: '2%', px: '3px' }}>{index + 1}</TableCell>
            <TableCell sx={{ width: '35%', px: '3px' }}>
                <Typography
                    sx={{
                        ':hover': {
                            color: '#3163D4'
                        },
                        cursor: 'pointer'
                    }}
                    onClick={() => setBacklogDetail(data)}
                >
                    {data.requireName}
                </Typography>
            </TableCell>
            <TableCell sx={{ width: '35%', px: '3px' }}>{data.sprintName}</TableCell>
            <TableCell sx={{ width: '14%', px: 0 }}>
                <ProductReportStatusChip status={data.status} />
            </TableCell>
            <TableCell sx={{ width: '14%', px: '3px' }}>{data.effort?.toFixed(2)}</TableCell>
        </TableRow>
    );
};

export default BacklogTbody;
