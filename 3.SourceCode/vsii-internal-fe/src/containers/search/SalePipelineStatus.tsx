import React from 'react';

// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, SALE_PIPELINE_STATUS } from 'constants/Common';
import { searchFormConfig } from './Config';

// third party
import { FormattedMessage } from 'react-intl';
import { SelectChangeEvent } from '@mui/material';

interface ISalePipelineStatusProps {
    name: string;
    required?: boolean;
    isShowAll?: boolean;
    disabled?: boolean;
    label?: string;
    handleChangeStatus?: (e: SelectChangeEvent<unknown>) => void;
}

const SalePipelineStatus = (props: ISalePipelineStatusProps) => {
    const { name, required, isShowAll, disabled, handleChangeStatus, label } = props;
    return (
        <Select
            isMultipleLanguage
            disabled={disabled}
            required={required}
            selects={isShowAll ? [DEFAULT_VALUE_OPTION, ...SALE_PIPELINE_STATUS] : [DEFAULT_VALUE_OPTION_SELECT, ...SALE_PIPELINE_STATUS]}
            name={name}
            label={<FormattedMessage id={label ? label : searchFormConfig.salePipelineStatus.label} />}
            handleChange={handleChangeStatus}
        />
    );
};

SalePipelineStatus.defaultProps = {
    name: searchFormConfig.salePipelineStatus.name
};

export default SalePipelineStatus;
