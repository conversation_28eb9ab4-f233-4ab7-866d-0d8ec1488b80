// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION_SELECT, DEPARTMENT_BIDDING_OPTION } from 'constants/Common';
import { FormattedMessage } from 'react-intl';
import { searchFormConfig } from './Config';

interface IDepartmentBiddingProps {
    name: string;
    required?: boolean;
    disabled?: boolean;
    label?: string;
}

const DepartmentBidding = (props: IDepartmentBiddingProps) => {
    const { required, name, disabled, label } = props;

    return (
        <Select
            required={required}
            disabled={disabled}
            selects={[DEFAULT_VALUE_OPTION_SELECT, ...DEPARTMENT_BIDDING_OPTION]}
            name={name}
            label={<FormattedMessage id={label || searchFormConfig.department.label} />}
        />
    );
};

export default DepartmentBidding;
