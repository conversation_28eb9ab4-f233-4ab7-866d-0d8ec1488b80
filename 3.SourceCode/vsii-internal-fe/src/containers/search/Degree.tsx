import { FormattedMessage } from 'react-intl';

// project imports
import { DEGREE } from 'constants/Common';
import { searchFormConfig } from './Config';
import { MultipleSelect } from 'components/extended/Form';

interface IDegreeProps {
    handleChange?: (values: string[]) => void;
    label?: string;
}

const Degree = ({ handleChange, label }: IDegreeProps) => {
    return (
        <>
            <MultipleSelect
                isMultipleLanguage
                selects={DEGREE}
                handleChange={handleChange}
                name={searchFormConfig.degree.name}
                label={<FormattedMessage id={label || searchFormConfig.degree.label} />}
            />
        </>
    );
};

export default Degree;
