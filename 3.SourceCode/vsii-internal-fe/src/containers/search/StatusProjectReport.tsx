import { PROJECT_REPORT_STATUS } from 'constants/Common';
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';

type IStatusProjectReportProps = {
    select?: boolean | null;
    required?: boolean;
    label?: string;
};

const StatusProjectReport = (props: IStatusProjectReportProps) => {
    const { required, label } = props;
    return (
        <Select isMultipleLanguage required={required} selects={PROJECT_REPORT_STATUS} name={searchFormConfig.status.name} label={label} />
    );
};

export default StatusProjectReport;
