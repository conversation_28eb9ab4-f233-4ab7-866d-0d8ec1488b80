import { FormattedMessage } from 'react-intl';
import { Select } from 'components/extended/Form';
import { BIDDING_STATUS, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';
import { searchFormConfig } from './Config';

interface IBiddingStatus {
    isShowAll?: boolean;
    name: string;
    required?: boolean;
    disabled?: boolean;
    label?: string;
}

const BiddingStatus = (props: IBiddingStatus) => {
    const { required, name, disabled, isShowAll, label } = props;
    const selects = isShowAll ? [DEFAULT_VALUE_OPTION_SELECT, ...BIDDING_STATUS] : BIDDING_STATUS;
    return (
        <Select
            required={required}
            isMultipleLanguage
            disabled={disabled}
            selects={selects}
            name={name}
            label={<FormattedMessage id={label || searchFormConfig.biddingStatus.label} />}
        />
    );
};

BiddingStatus.defaultProps = {
    isShowAll: true,
    name: searchFormConfig.biddingStatus.name
};

export default BiddingStatus;
