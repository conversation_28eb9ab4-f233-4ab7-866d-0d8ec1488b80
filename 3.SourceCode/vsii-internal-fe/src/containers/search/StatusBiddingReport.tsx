import { FormattedMessage } from 'react-intl';

import { DEFAULT_VALUE_OPTION, BIDDING_PACKAGE_STATUS } from 'constants/Common';
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';

type IStatusBiddingReportProps = {
    select?: boolean | null;
    required?: boolean;
    label?: string;
};

const StatusBiddingReport = (props: IStatusBiddingReportProps) => {
    const { select, required, label } = props;
    return (
        <Select
            isMultipleLanguage
            required={required}
            selects={!select ? [DEFAULT_VALUE_OPTION, ...BIDDING_PACKAGE_STATUS] : BIDDING_PACKAGE_STATUS}
            name={searchFormConfig.status.name}
            label={<FormattedMessage id={label || searchFormConfig.status.label} />}
        />
    );
};

export default StatusBiddingReport;
