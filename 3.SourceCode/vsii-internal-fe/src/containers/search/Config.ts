export const searchFormConfig = {
    year: {
        label: 'year',
        name: 'year'
    },
    month: {
        label: 'month',
        name: 'month'
    },
    week: {
        label: 'weeks',
        name: 'week'
    },
    timeStatus: {
        label: 'time-status',
        name: 'timeStatus'
    },
    department: {
        label: 'department',
        name: 'departmentId',
        manage: {
            label: 'department-full',
            name: 'deptId'
        }
    },
    project: {
        label: 'project',
        name: 'projectId'
    },
    userId: {
        label: 'member',
        name: 'userId'
    },
    userName: {
        label: 'user-name',
        name: 'userName'
    },
    status: {
        label: 'status',
        name: 'status'
    },
    memberCode: {
        label: 'member-code',
        name: 'memberCode'
    },
    titleCode: {
        label: 'location',
        name: 'titleCode'
    },
    rankId: {
        label: 'level',
        name: 'rankId'
    },
    projectType: {
        label: 'project-type',
        name: 'projectType',
        manage: {
            label: 'project-type',
            name: 'typeCode'
        }
    },
    holidayType: {
        label: 'holiday-type',
        name: 'type'
    },
    billable: {
        label: 'Billable',
        name: 'billable'
    },
    headcount: {
        label: 'Headcount',
        name: 'roleId'
    },
    period: {
        label: 'period',
        name: 'period'
    },
    specialHours: {
        label: 'special-holiday-type',
        name: 'type'
    },
    productivityType: {
        label: 'service-type',
        name: 'serviceType'
    },
    contractType: {
        label: 'contract-type',
        name: 'contractType'
    },
    currency: {
        label: 'currency',
        name: 'currency'
    },
    roleType: {
        label: 'role',
        name: 'role'
    },
    paymentTermType: {
        label: 'payment-term',
        name: 'paymentTerm'
    },
    partnerName: {
        label: 'partner-name',
        name: 'partnerName'
    },
    statusRequestsChecking: {
        label: 'status',
        name: 'status'
    },
    receivedDate: {
        label: 'received-date',
        name: 'receivedDate'
    },
    picUserName: {
        label: 'pic-user-name',
        name: 'picUserName'
    },
    possibility: {
        label: 'possibility',
        name: 'possibility'
    },
    date: {
        label: 'date',
        name: 'date'
    },
    supplierName: {
        label: 'supplier-name',
        name: 'supplierName'
    },
    workType: {
        label: 'work-type',
        name: 'workType'
    },
    weekDays: {
        label: 'weekdays',
        name: 'timeSendMail.weekdays'
    },
    emailType: {
        label: 'email-type',
        name: 'emailType'
    },
    dayInMonth: {
        label: 'day-in-month',
        name: 'timeSendMail.day'
    },
    groupType: {
        label: 'group-type',
        name: 'groupType'
    },
    contractor: {
        label: 'contractor',
        name: 'contractor'
    },
    projectManager: {
        label: 'project-manager',
        name: 'projectManager'
    },
    idHexString: {
        label: 'idHexString',
        name: 'idHexString'
    },
    salePipelineType: {
        label: 'sale-pipeline-type',
        name: 'type'
    },
    salePipelineStatus: {
        label: 'sale-pipeline-status',
        name: 'status'
    },
    productionPerformance: {
        label: 'project',
        name: 'productionPerformanceIdHexString'
    },
    salePipelineBudgetingPlanServiceType: {
        label: 'service-type',
        name: 'type'
    },
    biddingStatus: {
        label: 'status',
        name: 'status'
    },
    type: {
        label: 'type',
        name: 'type'
    },
    titleCodeSkillReport: {
        label: 'title-code',
        name: 'titleCode'
    },
    skill: {
        label: 'skill',
        name: 'skill'
    },
    dataSource: {
        label: 'data-source',
        name: 'dataSource'
    },
    levelSKill: {
        label: 'level',
        name: 'level'
    },
    degree: {
        label: 'degree',
        name: 'degree'
    },
    language: {
        label: 'language',
        name: 'language'
    },
    technologies: {
        label: 'technologies',
        name: 'technologies'
    },
    unit: {
        label: 'unit',
        name: 'unit'
    },
    budgetingPlanType: {
        label: 'type',
        name: 'pipelineType'
    },
    biddingPackageName: {
        label: 'bidding-package-name',
        name: 'biddingPackagesName'
    },
    address: {
        label: 'address',
        name: 'address'
    },
    reportName: {
        label: 'report-name',
        name: 'reportName'
    },
    columnName: {
        label: 'column-name',
        name: 'columnName'
    },
    inputType: {
        label: 'input-type',
        name: 'inputType'
    },
    layoutOption: {
        label: 'layout',
        name: 'layout'
    },
    conditionType: {
        label: 'condition',
        name: 'conditionType'
    }
};
