import { FormattedMessage } from 'react-intl';

// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, SALE_PIPELINE_BUDGETING_PLAN_SERVICE_TYPE } from 'constants/Common';
import { searchFormConfig } from './Config';

// third party

interface ISalePipelineDubgetingPlanServiceTypeProps {
    name: string;
    required?: boolean;
    isShowAll?: boolean;
    disabled?: boolean;
    label?: string;
}

const BudgetingPlanServiceType = (props: ISalePipelineDubgetingPlanServiceTypeProps) => {
    const { name, required, isShowAll, disabled, label } = props;
    return (
        <Select
            required={required}
            disabled={disabled}
            isMultipleLanguage
            selects={
                isShowAll
                    ? [DEFAULT_VALUE_OPTION, ...SALE_PIPELINE_BUDGETING_PLAN_SERVICE_TYPE]
                    : [DEFAULT_VALUE_OPTION_SELECT, ...SALE_PIPELINE_BUDGETING_PLAN_SERVICE_TYPE]
            }
            name={name}
            label={<FormattedMessage id={label || searchFormConfig.salePipelineBudgetingPlanServiceType.label} />}
        />
    );
};

BudgetingPlanServiceType.defaultProps = {
    name: searchFormConfig.salePipelineBudgetingPlanServiceType.name
};

export default BudgetingPlanServiceType;
