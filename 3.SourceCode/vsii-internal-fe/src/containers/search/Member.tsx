/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState, ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';

// project imports
import { E_IS_LOGTIME, STATUS_USER } from 'constants/Common';
import { Autocomplete } from 'components/extended/Form';
import { IMember, IMemberList } from 'types/member';
import { IOption, IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import { searchFormConfig } from './Config';
import Api from 'constants/Api';
// props filter members

interface IMemberProps {
    name: string;
    label: string | ReactNode;
    isDefaultAll?: boolean;
    disabled: boolean;
    handleChange?: (data: any) => void;
    handleClose?: () => void;
    isLogTime: E_IS_LOGTIME;
    isFindAll: boolean;
    required?: boolean;
    isIdHexString?: boolean;
    isUserName?: boolean;
    year?: number;
    month?: string | number;
    autoFilter?: any;
    findAllType?: string;
    isFindSkill?: boolean;
}

const Member = (props: IMemberProps) => {
    const {
        name,
        label,
        isDefaultAll,
        handleChange,
        handleClose,
        disabled,
        isLogTime,
        isFindAll,
        findAllType,
        required,
        isIdHexString,
        isUserName,
        isFindSkill,
        year,
        month,
        autoFilter,
    } = props;
    const [members, setMembers] = useState<IOption[]>([]);
    const [listUser, setListUser] = useState<IMember[]>([]);
    const handleChangeMember = (option: IOption) => {
        const userInfoSelected = option
            ? listUser.filter((user) =>
                  isIdHexString
                      ? user.idHexString === option.value
                      : isUserName
                      ? user.userName === option.value
                      : user.userId === option.value
              )
            : null;
        handleChange && handleChange(userInfoSelected ? userInfoSelected[0] : null);
    };

    async function getAllMember() {
        const response: IResponseList<IMemberList> = await sendRequest(Api.master.findAllUserLoginTime, {
            status: STATUS_USER.active,
            logtime: isLogTime,
            findAll: isFindAll ? 'All' : isFindSkill ? 'SCREEN_SKILL' : findAllType || 'SCREEN_REGISTER_WORKING_CALENDAR',
            year: year,
            month: month,
            ...autoFilter,
            timeStatus: !autoFilter?.timeStatus
                ? null
                : typeof autoFilter.timeStatus == 'string'
                ? autoFilter.timeStatus
                : autoFilter.timeStatus.join(','),
            titleCode: autoFilter?.titleCode?.value ? autoFilter.titleCode.value : null,
            skill: !autoFilter?.skill ? null : autoFilter.skill.map((skill: IOption) => skill.value).join(','),
            degree: !autoFilter?.degree ? null : typeof autoFilter.degree == 'string' ? autoFilter.degree : autoFilter.degree.join(','),
            level: !autoFilter?.level ? null : typeof autoFilter.level == 'string' ? autoFilter.level : autoFilter.level.join(',')
        });
        if (response) {
            const { status, result } = response;
            if (status) {
                setListUser([]);
                const newMembers: IOption[] = [];
                result.content.forEach((member: IMember) => {
                    let memberOption = {
                        value:
                            name === searchFormConfig.userName.name ||
                            name === searchFormConfig.picUserName.name ||
                            name === searchFormConfig.projectManager.name ||
                            isUserName
                                ? member.userName
                                : name === searchFormConfig.idHexString.name || isIdHexString
                                ? member.idHexString!
                                : member.userId,
                        label: `${member.firstName} ${member.lastName}`,
                        color: member.effortArise
                    };
                    newMembers.push(memberOption);
                    setListUser((listUser) => [...listUser, member]);
                });
                setMembers(newMembers);
            } else setMembers([]);
        }
    }

    useEffect(() => {
        getAllMember();
    }, [autoFilter]);

    return (
        <Autocomplete
            required={required}
            options={members}
            name={name}
            label={label}
            handleChange={handleChangeMember}
            handleClose={handleClose}
            disabled={disabled}
            isDefaultAll={isDefaultAll}
        />
    );
};

Member.defaultProps = {
    name: searchFormConfig.userId.name,
    label: <FormattedMessage id={searchFormConfig.userId.label} />,
    isShowAll: true,
    disabled: false,
    isLogTime: E_IS_LOGTIME.ALL,
    isFindAll: false
};

export default Member;
