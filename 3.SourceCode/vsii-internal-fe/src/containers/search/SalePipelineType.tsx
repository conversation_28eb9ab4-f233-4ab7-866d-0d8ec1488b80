// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, SALE_PIPELINE_TYPE } from 'constants/Common';
import { searchFormConfig } from './Config';

// third party
import { FormattedMessage } from 'react-intl';
import { SelectChangeEvent } from '@mui/material';

interface ISalePipelineTypeProps {
    name: string;
    required?: boolean;
    isShowAll?: boolean;
    label?: string;
    disabled?: boolean;
    handleChangeSalePipelineType?: (e: SelectChangeEvent<unknown>) => void;
}

const SalePipelineStatus = (props: ISalePipelineTypeProps) => {
    const { name, required, isShowAll, disabled, label, handleChangeSalePipelineType } = props;
    return (
        <Select
            required={required}
            disabled={disabled}
            isMultipleLanguage
            selects={isShowAll ? [DEFAULT_VALUE_OPTION, ...SALE_PIPELINE_TYPE] : [DEFAULT_VALUE_OPTION_SELECT, ...SALE_PIPELINE_TYPE]}
            name={name}
            label={<FormattedMessage id={label} />}
            handleChange={handleChangeSalePipelineType}
        />
    );
};

SalePipelineStatus.defaultProps = {
    name: searchFormConfig.salePipelineType.name,
    label: searchFormConfig.type.label
};

export default SalePipelineStatus;
