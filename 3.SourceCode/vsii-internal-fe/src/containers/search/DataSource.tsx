// third party
import { FormattedMessage } from 'react-intl';

// material-ui
import { SelectChangeEvent } from '@mui/material';

// project imports
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';
import { IOption } from 'types';

interface IDataSourceProps {
    disabled?: boolean;
    required?: boolean;
    name: string;
    dataSourceOption: IOption[];
    handleChange?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
}

const DataSource = (props: IDataSourceProps) => {
    const { disabled, required, name, handleChange, dataSourceOption } = props;

    return (
        <Select
            required={required}
            disabled={disabled}
            selects={dataSourceOption}
            name={name}
            handleChange={handleChange}
            label={<FormattedMessage id={searchFormConfig.dataSource.label} />}
        />
    );
};

DataSource.defaultProps = {
    name: searchFormConfig.dataSource.name
};

export default DataSource;
