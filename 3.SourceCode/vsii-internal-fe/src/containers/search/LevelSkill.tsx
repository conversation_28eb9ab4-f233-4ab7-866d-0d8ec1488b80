import { FormattedMessage } from 'react-intl';

// project imports
import { MultipleSelect } from 'components/extended/Form';
import { searchFormConfig } from './Config';
import { LEVEL } from 'constants/Common';

interface ILevelSkillProps {
    label?: string;
    handleChange?: (values: string[]) => void;
}

const LevelSkill = ({ handleChange, label }: ILevelSkillProps) => {
    return (
        <>
            <MultipleSelect
                isMultipleLanguage
                selects={LEVEL}
                handleChange={handleChange}
                name={searchFormConfig.levelSKill.name}
                label={<FormattedMessage id={label || searchFormConfig.levelSKill.label} />}
            />
        </>
    );
};

export default LevelSkill;
