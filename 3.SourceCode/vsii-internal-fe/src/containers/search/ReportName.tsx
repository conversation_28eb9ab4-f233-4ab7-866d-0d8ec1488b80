import { Input } from 'components/extended/Form';
import React from 'react';

type ReportNameProps = {
    name: string;
    required?: boolean;
    disabled?: boolean;
    label?: string | React.ReactNode;
};

const ReportName = ({ name, required, disabled, label }: ReportNameProps) => {
    return <Input name={name} required={required} type="text" disabled={disabled} label={label} />;
};
ReportName.defaultProps = {
    isShowAll: true,
    name: 'reportName'
};

export default ReportName;
