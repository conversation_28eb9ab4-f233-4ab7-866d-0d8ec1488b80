import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, LEAVES_STATUS_OPTIONS } from 'constants/Common';

// project imports
import { FormattedMessage } from 'react-intl';

interface IApproveStatusProps {
    name: string;
    isShowAll?: boolean;
    label?: string;
}

const ApproveStatus = (props: IApproveStatusProps) => {
    const { isShowAll, name, label } = props;
    const selects = isShowAll ? [DEFAULT_VALUE_OPTION, ...LEAVES_STATUS_OPTIONS] : [DEFAULT_VALUE_OPTION_SELECT, ...LEAVES_STATUS_OPTIONS];

    return <Select selects={selects} name={name} isMultipleLanguage label={<FormattedMessage id={label || 'status'} />} />;
};

ApproveStatus.defaultProps = {
    isShowAll: true
};

export default ApproveStatus;
