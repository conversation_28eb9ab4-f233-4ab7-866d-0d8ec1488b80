import { FormattedMessage } from 'react-intl';

// project imports
import { Input } from 'components/extended/Form';
import { searchFormConfig } from './Config';

interface IBiddingPackageNameProps {
    label?: string;
}

const BiddingPackageName = ({ label }: IBiddingPackageNameProps) => {
    return (
        <Input
            name={searchFormConfig.biddingPackageName.name}
            label={<FormattedMessage id={label || searchFormConfig.biddingPackageName.label} />}
        />
    );
};

export default BiddingPackageName;
