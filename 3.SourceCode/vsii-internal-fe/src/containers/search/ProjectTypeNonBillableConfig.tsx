import { useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';

// project imports
import { IProjectType, IGetProjectTypeResponse } from 'types/projectType';
import { DEFAULT_VALUE_OPTION } from 'constants/Common';
import { Select } from 'components/extended/Form';
import { SelectChangeEvent } from '@mui/material';
import { IOption, IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import { searchFormConfig } from './Config';
import Api from 'constants/Api';

interface IProjectTypeProps {
    name: string;
    nonBillable?: boolean;
    required?: boolean;
    disabled?: boolean;
    handleChangeProjectType?: (e: SelectChangeEvent<unknown>) => void;
    disableLabel?: boolean;
    noDefaultOption?: boolean;
}

const ProjectTypeNonBillableConfig = ({
    name,
    required,
    nonBillable,
    disabled,
    handleChangeProjectType,
    disableLabel,
    noDefaultOption
}: IProjectTypeProps) => {
    const [projectType, setProjectType] = useState<IOption[]>([DEFAULT_VALUE_OPTION]);

    async function getAllProjectType(nonBillable?: boolean) {
        const response: IResponseList<IGetProjectTypeResponse> = await sendRequest(Api.master.getProjectTypeNonBillableConfig(nonBillable));
        if (!response) return;
        const { status, result } = response;
        if (status) {
            setProjectType([
                ...(noDefaultOption ? [] : [DEFAULT_VALUE_OPTION]),
                ...result.content.map((proType: IProjectType) => ({
                    value: proType.typeCode,
                    label: proType.projectTypeName
                }))
            ]);
        }
    }

    useEffect(() => {
        getAllProjectType(nonBillable);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [nonBillable]);

    return (
        <Select
            required={required}
            selects={projectType}
            name={name}
            label={disableLabel ? '' : <FormattedMessage id={searchFormConfig.projectType.label} />}
            disabled={disabled}
            handleChange={handleChangeProjectType}
        />
    );
};

ProjectTypeNonBillableConfig.defaultProps = {
    name: searchFormConfig.projectType.name
};

export default ProjectTypeNonBillableConfig;
