import { ChangeEvent, useEffect, useState } from 'react';

// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION_SELECT, E_SCREEN_SALES_YEAR } from 'constants/Common';
import { IOption, IResponseList } from 'types';
import { searchFormConfig } from './Config';

// third party
import { FormattedMessage } from 'react-intl';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';

interface ISalesYearProps {
    disabled?: boolean;
    handleChangeYear?: (year: ChangeEvent<HTMLInputElement>) => void;
    reverse?: boolean;
    required?: boolean;
    screen: E_SCREEN_SALES_YEAR;
    isRefresh?: boolean;
    label?: string;
}

const SalesYear = (props: ISalesYearProps) => {
    const { disabled, handleChangeYear, reverse, required, screen, isRefresh, label } = props;
    const [years, setYears] = useState<IOption[]>([DEFAULT_VALUE_OPTION_SELECT]);

    const handleChange = (e: any) => {
        handleChangeYear && handleChangeYear(e);
    };

    const getListSalesYear = async () => {
        const payload = { screen };
        const response: IResponseList<{ content: number[] }> = await sendRequest(Api.sale_pipeline.getListYear, payload);
        if (!response) return;
        const { status, result } = response;
        if (status) {
            let arrOption: IOption[] = [];
            result.content.forEach((y: number) => {
                let yearOption: IOption = {
                    value: y,
                    label: y.toString()
                };
                arrOption.push(yearOption);
            });
            setYears(arrOption);
        }
    };

    useEffect(() => {
        getListSalesYear();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isRefresh]);

    return (
        <Select
            required={required}
            disabled={disabled}
            handleChange={handleChange}
            selects={years}
            name={searchFormConfig.year.name}
            label={reverse ? '' : <FormattedMessage id={label || searchFormConfig.year.label} />}
        />
    );
};

export default SalesYear;
