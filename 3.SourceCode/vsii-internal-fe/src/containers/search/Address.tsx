import { FormattedMessage } from 'react-intl';

// project imports
import { Input } from 'components/extended/Form';
import { searchFormConfig } from './Config';
interface IAddressProps {
    label?: string;
}
const Address = ({ label }: IAddressProps) => {
    return <Input name={searchFormConfig.address.name} label={<FormattedMessage id={label || searchFormConfig.address.label} />} />;
};

export default Address;
