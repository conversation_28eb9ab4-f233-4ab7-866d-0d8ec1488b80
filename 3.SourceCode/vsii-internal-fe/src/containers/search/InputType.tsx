import React from 'react';
import { SelectChangeEvent } from '@mui/material';

import { TYPE_HOLIDAY_OPTIONS, TYPE_INPUT } from 'constants/Common';
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';

type Props = {
    select?: boolean | null;
    required?: boolean;
    label?: string;
    name?: string;
    handleChange?: (e: SelectChangeEvent<unknown>) => void;
    disabled?: boolean;
};

const InputType: React.FC<Props> = ({ select, required, label, name, handleChange, disabled }) => {
    return (
        <Select
            isMultipleLanguage
            required={required}
            selects={!select ? TYPE_INPUT : TYPE_HOLIDAY_OPTIONS}
            name={name || searchFormConfig.holidayType.name}
            label={label}
            handleChange={handleChange}
            disabled={disabled}
        />
    );
};

export default InputType;
