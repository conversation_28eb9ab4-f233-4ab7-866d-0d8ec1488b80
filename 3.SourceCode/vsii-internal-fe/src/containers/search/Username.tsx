import { FormattedMessage } from 'react-intl';

// project imports
import { Input } from 'components/extended/Form';
import { searchFormConfig } from './Config';
interface IUsernameProps {
    label?: string;
}
const Username = ({ label }: IUsernameProps) => {
    return <Input name={searchFormConfig.userName.name} label={<FormattedMessage id={label || searchFormConfig.userName.label} />} />;
};

export default Username;
