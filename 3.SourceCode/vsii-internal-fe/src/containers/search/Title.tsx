// project imports
import { Select } from 'components/extended/Form';
import Api from 'constants/Api';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';
import { useEffect, useState } from 'react';
import sendRequest from 'services/ApiService';
import { ITitle, ITitleList, IOption, IResponseList } from 'types';
import { searchFormConfig } from './Config';

// third party
import { FormattedMessage } from 'react-intl';

interface ITitleProps {
    isShowAll: boolean;
    isShowLabel?: boolean;
    name?: string;
    disabled?: boolean;
    isShowTitleName?: boolean;
    label?: string;
}

const Title = (props: ITitleProps) => {
    const { isShowAll, isShowLabel, name, disabled, isShowTitleName, label } = props;
    const [titles, setTitles] = useState<IOption[]>(isShowAll ? [DEFAULT_VALUE_OPTION] : [DEFAULT_VALUE_OPTION_SELECT]);

    async function getAllTitle() {
        const response: IResponseList<ITitleList> = await sendRequest(Api.master.getAllTitle);
        if (response?.status) {
            const { result } = response;
            result.content.forEach((title: ITitle) => {
                let option = {
                    value: title.titleCode,
                    label: isShowTitleName ? `[${title.titleCode}] - ${title.titleName}` : title.titleCode
                };
                setTitles((titles) => [...titles, option]);
            });
        } else return;
    }

    useEffect(() => {
        getAllTitle();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <Select
            selects={titles}
            name={!name ? searchFormConfig.titleCode.name : name}
            disabled={disabled}
            label={!isShowLabel && <FormattedMessage id={label || searchFormConfig.titleCode.label} />}
        />
    );
};

Title.defaultProps = {
    isShowAll: true,
    isShowTitleName: true
};

export default Title;
