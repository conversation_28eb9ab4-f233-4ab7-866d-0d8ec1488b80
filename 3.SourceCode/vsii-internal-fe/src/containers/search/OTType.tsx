// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, OT_TYPE_OPTIONS } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

interface ILeavesTypeProps {
    name: string;
    isShowAll?: boolean;
    required?: boolean;
    isDisable?: boolean;
}

const OTType = (props: ILeavesTypeProps) => {
    const { isShowAll, name, isDisable } = props;
    const selects = isShowAll ? [DEFAULT_VALUE_OPTION, ...OT_TYPE_OPTIONS] : [DEFAULT_VALUE_OPTION_SELECT, ...OT_TYPE_OPTIONS];

    return <Select disabled={isDisable} selects={selects} name={name} label={<FormattedMessage id="ot-type" />} isMultipleLanguage />;
};

OTType.defaultProps = {
    isShowAll: true
};

export default OTType;
