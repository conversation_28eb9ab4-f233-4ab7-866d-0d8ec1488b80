import { useEffect, useState, useCallback } from 'react';
import { FormattedMessage } from 'react-intl';
import { Input } from 'components/extended/Form';
import Api from 'constants/Api';
import sendRequest from 'services/ApiService';
import { useFormContext, useWatch } from 'react-hook-form';
import LeaveDaysInfo from 'containers/working-calendar/LeaveDayInfor';
import { Box } from '@mui/system';
import { SxProps } from '@mui/material';
import { ILeaveDaysInformation } from 'types/leave-days';

interface IRemainLeaveDayProps {
    name: string;
    required?: boolean;
    disabled?: boolean;
    sx?: SxProps;
}

const RemainLeaveDay = (props: IRemainLeaveDayProps) => {
    const { name, disabled, sx } = props;
    const [remainingLeaveDays, setRemainingLeaveDays] = useState<number | null>(null);
    const [leaveData, setLeaveData] = useState<ILeaveDaysInformation | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const methods = useFormContext();

    const member = useWatch({
        control: methods.control,
        name: 'member'
    });

    const memberIdHexString = member?.value || '';

    const fetchRemainingLeaveDays = useCallback(async () => {
        if (!memberIdHexString) {
            setRemainingLeaveDays(0);
            setLeaveData(null);
            return;
        }
        setIsLoading(true);
        try {
            const response = await sendRequest(Api.leave_day.getLeaveDaysInfo(memberIdHexString));
            if (response?.status) {
                const { result } = response;
                setRemainingLeaveDays(result.content.totalLeaveDaysMonthly);
                setLeaveData(result.content);
            } else {
                setRemainingLeaveDays(0);
                setLeaveData(null);
            }
        } catch (error) {
            setRemainingLeaveDays(0);
            setLeaveData(null);
        } finally {
            setIsLoading(false);
        }
    }, [memberIdHexString]);

    useEffect(() => {
        fetchRemainingLeaveDays();
    }, [fetchRemainingLeaveDays]);

    useEffect(() => {
        if (remainingLeaveDays !== null) {
            methods.setValue(name, remainingLeaveDays.toString(), {
                shouldValidate: true
            });
        }
    }, [remainingLeaveDays, name, methods]);

    return (
        <Input
            label={
                <Box display="flex" alignItems="center" sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id="remaining-leave-days" />
                    {memberIdHexString && leaveData && (
                        <Box
                            component="span"
                            ml={0.5}
                            sx={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                transform: 'scale(0.85)',
                                transformOrigin: 'left center'
                            }}
                        >
                            <LeaveDaysInfo memberId={memberIdHexString} leaveData={leaveData} />
                        </Box>
                    )}
                </Box>
            }
            name={name}
            disabled={disabled || isLoading}
            sx={sx}
        />
    );
};

RemainLeaveDay.defaultProps = {
    required: false,
    disabled: true
};

export default RemainLeaveDay;
