// project imports
import { Select } from 'components/extended/Form';
import { IOption } from 'types';
import { SelectChangeEvent } from '@mui/material';
import { DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';

interface ISkillsProps {
    name: string;
    required?: boolean;
    handleTechnologySkill?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
    technologySkillSelect: IOption[];
}

const Skills = (props: ISkillsProps) => {
    const { name, required, handleTechnologySkill, technologySkillSelect } = props;

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        handleTechnologySkill && handleTechnologySkill(e);
    };

    return (
        <Select
            required={required}
            isMultipleLanguage={false}
            selects={[DEFAULT_VALUE_OPTION_SELECT, ...technologySkillSelect]}
            name={name}
            handleChange={(e) => {
                handleChange && handleChange(e);
            }}
        />
    );
};

Skills.defaultProps = {
    isShowAll: false
};

export default Skills;
