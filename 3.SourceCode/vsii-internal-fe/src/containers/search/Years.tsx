import { SelectChangeEvent } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { useEffect, useState } from 'react';

// project imports
import { DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';
import { getNumberOfCurrentYears } from 'utils/date';
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';
import { IOption } from 'types';

interface IYearsProps {
    disabled?: boolean;
    handleChangeYear?: (year: SelectChangeEvent<unknown>) => void;
    reverse?: boolean;
    required?: boolean;
    ignoreDefault?: boolean;
    name?: string;
    disableLabel?: boolean;
    label?: string;
}

const Years = (props: IYearsProps) => {
    const { disabled, handleChangeYear, reverse, required, ignoreDefault, name, disableLabel, label } = props;
    const [years, setYears] = useState<IOption[]>(ignoreDefault ? [] : [DEFAULT_VALUE_OPTION_SELECT]);
    const AMOUNT_RECENT_YEAR = 5;

    const handleChange = (e: SelectChangeEvent<unknown>) => {
        handleChangeYear && handleChangeYear(e);
    };

    useEffect(() => {
        getNumberOfCurrentYears(AMOUNT_RECENT_YEAR, reverse).forEach((year) => {
            let recentYear = {
                value: year,
                label: String(year)
            };
            setYears((years) => [...years, recentYear]);
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <Select
            required={required}
            disabled={disabled}
            handleChange={handleChange}
            selects={years}
            name={name ? name : searchFormConfig.year.name}
            label={disableLabel ? '' : <FormattedMessage id={label || searchFormConfig.year.label} />}
        />
    );
};

export default Years;
