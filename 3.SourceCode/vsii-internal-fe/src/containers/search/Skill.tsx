import { FormattedMessage } from 'react-intl';
import { memo, useEffect, useState } from 'react';

// project imports
import { IOption, IResponseList, ITechs, ITechsResponse } from 'types';
import { Autocomplete } from 'components/extended/Form';
import sendRequest from 'services/ApiService';
import { searchFormConfig } from './Config';
import Api from 'constants/Api';

interface ISkillProps {
    label?: string;
    handleChange?: (values: IOption[]) => void;
}

const Skill = ({ handleChange, label }: ISkillProps) => {
    const [techs, setTechs] = useState<IOption[]>([]);
    async function getAllTech() {
        const response: IResponseList<ITechsResponse> = await sendRequest(Api.skills_manage.getTechAll);
        if (!response) return;
        const { status, result } = response;
        if (status) {
            let arrOption: IOption[] = [];
            result.content.forEach((tech: ITechs) => {
                let techOption = {
                    value: tech.name,
                    label: tech.name,
                    typeCode: tech.type
                };
                arrOption.push(techOption);
            });
            setTechs(arrOption);
        }
    }

    useEffect(() => {
        getAllTech();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            <Autocomplete
                multiple
                name={searchFormConfig.skill.name}
                label={<FormattedMessage id={label || searchFormConfig.skill.label} />}
                options={techs}
                handleChange={handleChange}
                groupBy={(option: IOption) => option.typeCode}
            />
        </>
    );
};

export default memo(Skill);
