import { memo, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { FormattedMessage } from 'react-intl';

// third party

// project imports
import { Autocomplete } from 'components/extended/Form';
import Api from 'constants/Api';
import sendRequest from 'services/ApiService';
import { IOption, IProject, IProjectList, IResponseList } from 'types';
import { searchFormConfig } from './Config';
import { addProjectSuccessSelector } from 'store/slice/projectSlice';

interface IProjectProps {
    label: any;
    name: string;
    disabled?: boolean;
    isDefaultAll?: boolean;
    isDisableClearable?: boolean;
    departmentId?: string;
    month?: any;
    isNotStatus?: boolean;
    required?: boolean;
    projectAuthorization?: string;
    week?: any;
    handleChange?: (data: any) => void;
    setProjectsAll?: React.Dispatch<React.SetStateAction<IOption[]>>;
}

const Project = (props: IProjectProps) => {
    const {
        name,
        isDefaultAll,
        disabled,
        isDisableClearable,
        departmentId,
        month,
        isNotStatus,
        required,
        projectAuthorization,
        week,
        label,
        handleChange,
        setProjectsAll
    } = props;

    const [projects, setProjects] = useState<IOption[]>([]);
    const addProjectSuccess = useSelector(addProjectSuccessSelector);

    const params = { ...month, ...week, projectAuthorization, departmentId, size: 1000, status: isNotStatus ? null : '1' };
    async function getAllProject() {
        const response: IResponseList<IProjectList> = await sendRequest(Api.project.getAll, params);
        if (!response) return;
        const { status, result } = response;
        if (status) {
            let arrOption: IOption[] = [];
            result.content.forEach((pro: IProject) => {
                let projectOption = {
                    value: pro.projectId,
                    label: pro.projectName,
                    typeCode: pro.typeCode,
                    color: pro.effortArise
                };
                arrOption.push(projectOption);
            });
            setProjects(arrOption);
            setProjectsAll?.(arrOption);
        }
    }

    useEffect(() => {
        getAllProject();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [month, week, addProjectSuccess]);

    return (
        <Autocomplete
            required={required}
            disabled={disabled}
            isDisableClearable={isDisableClearable}
            options={projects}
            name={name}
            label={label}
            groupBy={(option: IOption) => option.typeCode}
            isDefaultAll={isDefaultAll}
            handleChange={handleChange}
        />
    );
};

Project.defaultProps = {
    label: <FormattedMessage id={searchFormConfig.project.label} />,
    name: searchFormConfig.project.name,
    projectAuthorization: 'true'
};

export default memo(Project);
