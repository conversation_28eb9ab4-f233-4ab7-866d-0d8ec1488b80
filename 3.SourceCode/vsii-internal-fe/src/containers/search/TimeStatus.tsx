import { SelectChangeEvent } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project imports
import { DEFAULT_VALUE_OPTION, TIME_STATUS, TIME_STATUS_BY_NON_BILL } from 'constants/Common';
import { Select, MultipleSelect } from 'components/extended/Form';
import { searchFormConfig } from './Config';

interface ITimeStatusProps {
    isMultiple?: boolean;
    isNonBill?: boolean;
    onChange?: (timeStatus: any) => void;
    label?: string;
}

const TimeStatus = ({ isMultiple, isNonBill, onChange, label }: ITimeStatusProps): JSX.Element => {
    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value as string;
        onChange && onChange(value);
    };
    const handleChangeStatus = (values: any) => {
        onChange && onChange(values);
    };
    return (
        <>
            {isMultiple ? (
                <MultipleSelect
                    isMultipleLanguage
                    selects={TIME_STATUS}
                    name={searchFormConfig.timeStatus.name}
                    handleChange={handleChangeStatus}
                    label={<FormattedMessage id={label || searchFormConfig.timeStatus.label} />}
                />
            ) : (
                <Select
                    isMultipleLanguage
                    handleChange={handleChange}
                    selects={isNonBill ? TIME_STATUS_BY_NON_BILL : [DEFAULT_VALUE_OPTION, ...TIME_STATUS]}
                    name={searchFormConfig.timeStatus.name}
                    label={<FormattedMessage id={label || searchFormConfig.timeStatus.label} />}
                />
            )}
        </>
    );
};

TimeStatus.defaultProps = {
    isNonBill: false
};

export default TimeStatus;
