import { useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';

// project imports
import Api from 'constants/Api';
import sendRequest from 'services/ApiService';
import { IOption, IResponseList } from 'types';
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';
import { DEFAULT_VALUE_OPTION } from 'constants/Common';
import { IProjectType, IGetProjectTypeResponse } from 'types/projectType';
import { SelectChangeEvent } from '@mui/material';

interface IProjectTypeProps {
    name: string;
    required?: boolean;
    fixCost?: boolean;
    disabled?: boolean;
    handleChangeProjectType?: (e: SelectChangeEvent<unknown>) => void;
    disableLabel?: boolean;
    noDefaultOption?: boolean;
    label?: string;
}

const ProjectType = ({
    name,
    required,
    fixCost,
    disabled,
    handleChangeProjectType,
    disableLabel,
    noDefaultOption,
    label
}: IProjectTypeProps) => {
    const [projectType, setProjectType] = useState<IOption[]>([DEFAULT_VALUE_OPTION]);

    async function getAllProjectType(fixCost?: boolean) {
        const response: IResponseList<IGetProjectTypeResponse> = await sendRequest(Api.master.getProjectType(fixCost));
        if (!response) return;
        const { status, result } = response;
        if (status) {
            setProjectType([
                ...(noDefaultOption ? [] : [DEFAULT_VALUE_OPTION]),
                ...result.content.map((proType: IProjectType) => ({
                    value: proType.typeCode,
                    label: proType.projectTypeName
                }))
            ]);
        }
    }

    useEffect(() => {
        getAllProjectType(fixCost);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fixCost]);

    return (
        <Select
            required={required}
            selects={projectType}
            name={name}
            label={disableLabel ? '' : <FormattedMessage id={label || searchFormConfig.projectType.label} />}
            disabled={disabled}
            handleChange={handleChangeProjectType}
        />
    );
};

ProjectType.defaultProps = {
    name: searchFormConfig.projectType.name
};

export default ProjectType;
