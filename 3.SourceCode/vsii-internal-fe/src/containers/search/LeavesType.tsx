// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT, LEAVES_TYPE_OPTIONS } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';
import { SxProps } from '@mui/material';

interface ILeavesTypeProps {
    name: string;
    isShowAll?: boolean;
    required?: boolean;
    label?: string;
    isDisable?: boolean;
    sx?: SxProps;
}

const LeavesType = (props: ILeavesTypeProps) => {
    const { isShowAll, name, label, isDisable, sx } = props;
    const selects = isShowAll ? [DEFAULT_VALUE_OPTION, ...LEAVES_TYPE_OPTIONS] : [DEFAULT_VALUE_OPTION_SELECT, ...LEAVES_TYPE_OPTIONS];

    return (
        <Select
            selects={selects}
            name={name}
            label={<FormattedMessage id={label || 'leaves-type'} />}
            isMultipleLanguage
            required
            sx={sx}
            disabled={isDisable}
        />
    );
};

LeavesType.defaultProps = {
    isShowAll: true
};

export default LeavesType;
