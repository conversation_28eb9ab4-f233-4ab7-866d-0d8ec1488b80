import { FormattedMessage } from 'react-intl';
import { SelectChangeEvent } from '@mui/material';

// project imports
import { Select } from 'components/extended/Form';
import Api from 'constants/Api';
import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';
import { useEffect, useState } from 'react';
import sendRequest from 'services/ApiService';
import { IOption, IResponseList } from 'types';
import { searchFormConfig } from './Config';
import { IDepartment, IGetDepartmentResponse } from 'types/department';

interface IDepartmentProps {
    isShowAll: boolean;
    name: string;
    required?: boolean;
    disabled?: boolean;
    onChange?: (department: string) => void;
    label?: string;
}

const Department = (props: IDepartmentProps) => {
    const { isShowAll, required, name, disabled, onChange, label } = props;
    const [department, setDepartment] = useState<IOption[]>(isShowAll ? [DEFAULT_VALUE_OPTION] : [DEFAULT_VALUE_OPTION_SELECT]);
    async function getAllDepartment() {
        const response: IResponseList<IGetDepartmentResponse> = await sendRequest(Api.department.getAll);
        if (response?.status) {
            const { result } = response;
            result.content.forEach((dept: IDepartment) => {
                let departmentOption = {
                    value: dept.deptId,
                    label: dept.deptId
                };
                setDepartment((department) => [...department, departmentOption]);
            });
        } else return;
    }

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value as string;
        onChange && onChange(value);
    };

    useEffect(() => {
        getAllDepartment();
    }, []);

    return (
        <Select
            disabled={disabled}
            required={required}
            selects={department}
            handleChange={handleChange}
            name={name}
            label={<FormattedMessage id={label || searchFormConfig.department.label} />}
        />
    );
};

Department.defaultProps = {
    isShowAll: true,
    name: searchFormConfig.department.name
};

export default Department;
