import React from 'react';
// project imports
import { SelectChangeEvent } from '@mui/material';

import { BILLABLE_OPTIONS } from 'constants/Common';
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';

interface IBillableProps {
    required?: boolean;
    defaultOption?: { value: string | number; label: string };
    handleChangeBillable?: (e: SelectChangeEvent<unknown>) => void;
    label?: string | React.ReactNode;
}

const Billable = ({ required, defaultOption, handleChangeBillable, label }: IBillableProps) => {
    const billableOptions = defaultOption ? [defaultOption, ...BILLABLE_OPTIONS] : BILLABLE_OPTIONS;

    return (
        <Select
            required={required}
            selects={billableOptions}
            name={searchFormConfig.billable.name}
            label={label || searchFormConfig.billable.label}
            defaultValue={defaultOption?.value as string}
            handleChange={handleChangeBillable}
        />
    );
};

export default Billable;
