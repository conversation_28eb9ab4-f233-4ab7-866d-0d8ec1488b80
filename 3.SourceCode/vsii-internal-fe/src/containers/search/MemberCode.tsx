import { FormattedMessage } from 'react-intl';

// project imports
import { Input } from 'components/extended/Form';
import { searchFormConfig } from './Config';
interface IMemberCodeProps {
    label?: string;
}
const MemberCode = ({ label }: IMemberCodeProps) => {
    return <Input name={searchFormConfig.memberCode.name} label={<FormattedMessage id={label || searchFormConfig.memberCode.label} />} />;
};

export default MemberCode;
