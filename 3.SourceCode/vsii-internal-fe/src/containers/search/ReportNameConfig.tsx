import React, { useEffect, useState } from 'react';
import { SelectChangeEvent } from '@mui/material';

import { IGetReportNameResponse } from 'types/flexible-report';
import { Select } from 'components/extended/Form';
import { IOption, IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';

interface Props {
    name: string;
    required?: boolean;
    disabled?: boolean;
    isSetDefaultValue?: boolean;
    label?: string | React.ReactNode;
    onChange?: (reportName: string, isSetDefaultValue?: boolean) => void;
}

const ReportNameConfig: React.FC<Props> = ({ required, name, disabled, label, isSetDefaultValue, onChange }) => {
    const [data, setData] = useState<IOption[]>([]);

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value as string;
        onChange?.(value);
    };

    useEffect(() => {
        (async () => {
            const response: IResponseList<IGetReportNameResponse> = await sendRequest(Api.flexible_report.get);
            if (response.status) {
                const data = response.result.content.map((report) => ({
                    value: report.id,
                    label: report.reportName
                }));
                if (data[0] && isSetDefaultValue) {
                    onChange?.(data[0].value, true);
                }
                setData(data);
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isSetDefaultValue]);

    return (
        <Select
            required={required}
            disabled={disabled}
            handleChange={handleChange}
            selects={data}
            name={name}
            label={label}
            placeholder="select-option"
        />
    );
};

export default ReportNameConfig;
