import { SelectChangeEvent } from '@mui/material';
// project imports
import { STATUS, STATUS_PROJECT_OPTIONS } from 'constants/Common';
import { Select } from 'components/extended/Form';
import { FormattedMessage } from 'react-intl';
import { searchFormConfig } from './Config';

interface IStatusProps {
    name: string;
    isShowAll?: boolean;
    isShowProjectStatus: boolean;
    required?: boolean;
    disabled?: boolean;
    onChange?: (value: string) => void;
    label?: string;
}

const Status = (props: IStatusProps) => {
    const { isShowAll, isShowProjectStatus, required, name, disabled, onChange, label } = props;
    let statusOptions = isShowAll ? STATUS : STATUS.filter((el) => el.value);
    if (isShowProjectStatus) statusOptions = isShowAll ? STATUS_PROJECT_OPTIONS : STATUS_PROJECT_OPTIONS.filter((el) => el.value);
    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value as string;
        onChange && onChange(value);
    };

    return (
        <Select
            isMultipleLanguage
            required={required}
            selects={statusOptions}
            disabled={disabled}
            handleChange={handleChange}
            name={name}
            label={<FormattedMessage id={label || searchFormConfig.status.label} />}
        />
    );
};

Status.defaultProps = {
    isShowAll: true,
    name: searchFormConfig.status.name,
    isShowProjectStatus: false
};

export default Status;
