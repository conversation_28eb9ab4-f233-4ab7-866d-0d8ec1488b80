// React imports
import { ReactNode, useEffect, useState } from 'react';

// Project imports
import { Select } from 'components/extended/Form';

// Types
import { DEFAULT_VALUE_OPTION_SELECT, GROUP_ID_APPROVER } from 'constants/Common';
import { SelectChangeEvent } from '@mui/material';
import sendRequest from 'services/ApiService';
import { IOption } from 'types';
import Api from 'constants/Api';

interface IDirectApproverProps {
    name: string;
    required?: boolean;
    disabled?: boolean;
    onChange?: (directApproverIdHexString: string) => void;
    label?: string | ReactNode;
    idHexString?: string;
}

const DirectApprover = (props: IDirectApproverProps) => {
    const { name, required, disabled, onChange, label, idHexString } = props;
    const [managers, setManagers] = useState<IOption[]>([DEFAULT_VALUE_OPTION_SELECT]);

    async function fetchManagers() {
        const response = await sendRequest(Api.member.getAll, { groupId: GROUP_ID_APPROVER.MANAGEMENT_LEVEL, size: 1000 });
        if (response?.status) {
            const { result } = response;
            const arrOptions = [DEFAULT_VALUE_OPTION_SELECT];
            result.content
                .filter((user: any) => user.idHexString !== idHexString)
                .forEach((user: any) => {
                    const managerOption = {
                        value: user.idHexString,
                        label: user.fullName || `${user.firstName} ${user.lastName}`.trim()
                    };
                    arrOptions.push(managerOption);
                });

            setManagers(arrOptions);
        }
    }

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value as string;
        onChange && onChange(value);
    };

    useEffect(() => {
        fetchManagers();
    }, []);
    return <Select disabled={disabled} required={required} selects={managers} handleChange={handleChange} name={name} label={label} />;
};

DirectApprover.defaultProps = {
    required: false,
    disabled: false
};

export default DirectApprover;
