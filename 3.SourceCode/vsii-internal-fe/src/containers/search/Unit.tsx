import { SelectChangeEvent } from '@mui/material';

// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, UNIT_SELECT_OPTION } from 'constants/Common';
import { searchFormConfig } from './Config';

// third party
import { FormattedMessage } from 'react-intl';

interface IUnitProps {
    name: string;
    disabled?: boolean;
    required?: boolean;
    isShowAll?: boolean;
    handleChange?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
    label?: string;
}

const Unit = (props: IUnitProps) => {
    const { disabled, required, isShowAll, name, handleChange, label } = props;

    return (
        <Select
            required={required}
            disabled={disabled}
            name={name}
            handleChange={handleChange}
            selects={isShowAll ? [DEFAULT_VALUE_OPTION, ...UNIT_SELECT_OPTION] : [...UNIT_SELECT_OPTION]}
            label={<FormattedMessage id={label || searchFormConfig.unit.label} />}
        />
    );
};

Unit.defaultProps = {
    isShowAll: false,
    name: searchFormConfig.unit.name
};

export default Unit;
