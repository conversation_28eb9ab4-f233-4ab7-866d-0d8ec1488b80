import { useEffect, useState } from 'react';

// third-party
import { SelectChangeEvent } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project imports
import { Select, MultipleSelect } from 'components/extended/Form';
import { MONTH_SALARY_13TH_OPTION } from 'constants/Common';
import { searchFormConfig } from './Config';
import { IOption } from 'types';
import { getCurrentMonth, getCurrentYear } from 'utils/date';

interface IMonthsProps {
    disabled?: boolean;
    months: IOption[];
    isMultiple?: boolean;
    onChange?: (month: string) => void;
    isShow13MonthSalary: boolean;
    required?: boolean;
    isMonth?: number | string;
    setIsMonth?: React.Dispatch<React.SetStateAction<number | string>>;
    name?: string;
    disabledLabel?: boolean;
    isFilter?: boolean;
    year?: number;
    label?: string;
}

const Months = (props: IMonthsProps) => {
    const {
        disabled,
        months,
        isMultiple,
        onChange,
        isShow13MonthSalary,
        name,
        required,
        setIsMonth,
        isMonth,
        disabledLabel,
        isFilter,
        year,
        label
    } = props;

    const [selectMonths, setSelectMonths] = useState(months);

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value as string;
        onChange && onChange(value);
        if (setIsMonth) {
            setIsMonth(value);
        }
    };

    useEffect(() => {
        if (year === getCurrentYear() && isFilter) {
            const currentMonths = months.filter((month) => (month.value as number) <= getCurrentMonth());
            if (isShow13MonthSalary && currentMonths.length >= 12) {
                setSelectMonths([...currentMonths, MONTH_SALARY_13TH_OPTION]);
            } else setSelectMonths(currentMonths);
        } else {
            isShow13MonthSalary ? setSelectMonths([...months, MONTH_SALARY_13TH_OPTION]) : setSelectMonths(months);
        }
    }, [isFilter, isShow13MonthSalary, months, year]);

    return (
        <>
            {isMultiple ? (
                <MultipleSelect
                    selects={selectMonths}
                    name={searchFormConfig.month.name}
                    label={<FormattedMessage id={searchFormConfig.month.label} />}
                    isOrder
                    isMultipleLanguage={false}
                />
            ) : (
                <Select
                    disabled={disabled}
                    handleChange={handleChange}
                    selects={selectMonths}
                    name={name ? name : searchFormConfig.month.name}
                    label={disabledLabel ? '' : <FormattedMessage id={label || searchFormConfig.month.label} />}
                    required={required}
                    valueSelect={isMonth as string}
                />
            )}
        </>
    );
};

Months.defaultProps = {
    isShow13MonthSalary: false
};

export default Months;
