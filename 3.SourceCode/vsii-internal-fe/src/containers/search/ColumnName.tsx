import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { DEFAULT_VALUE_OPTION, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';
import { IGetColumnNameResponse } from 'types/flexible-report';
import { Autocomplete } from 'components/extended/Form';
import { IOption, IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';

interface Props {
    name: string;
    required?: boolean;
    disabled?: boolean;
    isShowAll?: boolean;
    label?: string | React.ReactNode;
    flexibleReportId: string | undefined;
    value?: string;
    onChange?: (data: any) => void;
}

const ColumnName: React.FC<Props> = ({ isShowAll, required, name, disabled, label, flexibleReportId, onChange, value }) => {
    const [data, setData] = useState<IOption[]>(isShowAll ? [DEFAULT_VALUE_OPTION] : [DEFAULT_VALUE_OPTION_SELECT]);

    const methods = useFormContext();

    useEffect(() => {
        if (!flexibleReportId) return;
        (async () => {
            const response: IResponseList<IGetColumnNameResponse> = await sendRequest(Api.flexible_columns.get, {
                flexibleReportId,
                page: 1,
                size: 1000
            });
            if (response.status) {
                const data = response.result.content.map((report) => ({
                    value: report.id,
                    label: report.columnName
                }));

                setData(data as IOption[]);
            }
        })();
    }, [flexibleReportId, isShowAll]);

    useEffect(() => {
        value &&
            methods.setValue(
                name,
                data.find((item) => item.value === value)
            );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value, data]);

    return <Autocomplete options={data} name={name} label={label} handleChange={onChange} disabled={disabled} required={required} />;
};

export default ColumnName;
