import { FormattedMessage } from 'react-intl';

// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION_SELECT, PRODUCTIVITY_TYPE } from 'constants/Common';
import { searchFormConfig } from './Config';

interface IProductivityType {
    required?: boolean;
    label?: string;
}

const ProductivityType = (props: IProductivityType) => {
    const { required, label } = props;
    return (
        <Select
            required={required}
            isMultipleLanguage={false}
            selects={[DEFAULT_VALUE_OPTION_SELECT, ...PRODUCTIVITY_TYPE]}
            name={searchFormConfig.productivityType.name}
            label={<FormattedMessage id={label || searchFormConfig.productivityType.label} />}
        />
    );
};

export default ProductivityType;
