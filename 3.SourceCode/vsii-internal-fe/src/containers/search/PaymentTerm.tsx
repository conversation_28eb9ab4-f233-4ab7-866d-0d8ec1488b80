// third party
import { FormattedMessage } from 'react-intl';

// material-ui
import { SelectChangeEvent } from '@mui/material';

// project imports
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION_SELECT, PAYMENT_TERM } from 'constants/Common';
import { searchFormConfig } from './Config';

interface IPaymentTermProps {
    name: string;
    disabled?: boolean;
    handleChange?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
    required?: boolean;
    isShowAll?: boolean;
    label?: string;
}

const PaymentTerm = (props: IPaymentTermProps) => {
    const { disabled, handleChange, required, isShowAll, name, label } = props;

    return (
        <Select
            required={required}
            disabled={disabled}
            selects={!isShowAll ? PAYMENT_TERM : [DEFAULT_VALUE_OPTION_SELECT, ...PAYMENT_TERM]}
            handleChange={handleChange}
            name={name}
            label={<FormattedMessage id={label || searchFormConfig.paymentTermType.label} />}
        />
    );
};

PaymentTerm.defaultProps = {
    isShowAll: false,
    name: searchFormConfig.paymentTermType.name
};

export default PaymentTerm;
