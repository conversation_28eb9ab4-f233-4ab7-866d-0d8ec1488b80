import { FormattedMessage } from 'react-intl';
import { SelectChangeEvent } from '@mui/material';

// project imports
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';
import { IOption } from 'types';
import { useFormContext } from 'react-hook-form';
import { useEffect, useState } from 'react';

interface IConditionTypeProps {
    isShowAll: boolean;
    name: string;
    required?: boolean;
    disabled?: boolean;
    onChange?: (conditionType: string) => void;
    selectedValues?: string[];
    conditionTypes: IOption[];
}

const ConditionType = (props: IConditionTypeProps) => {
    const { required, name, disabled, onChange, selectedValues, conditionTypes } = props;

    const methods = useFormContext();

    const [valueIsSelected, setValueIsSelected] = useState<boolean>(false);

    const handleChange = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => {
        const value = e.target.value as string;
        onChange && onChange(value);
        value && setValueIsSelected(true);
    };

    useEffect(() => {
        methods.getValues(name) && setValueIsSelected(false);
        valueIsSelected && methods.setValue(name, '');
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [valueIsSelected, methods]);

    return (
        <Select
            disabled={disabled}
            required={required}
            selects={
                selectedValues
                    ? conditionTypes.map((option) => ({
                          ...option,
                          disabled: selectedValues.includes(option.value as string)
                      }))
                    : conditionTypes
            }
            handleChange={handleChange}
            name={name}
            label={<FormattedMessage id={searchFormConfig.conditionType.label} />}
            placeholder="select-option"
        />
    );
};

ConditionType.defaultProps = {
    isShowAll: true,
    name: searchFormConfig.conditionType.name
};

export default ConditionType;
