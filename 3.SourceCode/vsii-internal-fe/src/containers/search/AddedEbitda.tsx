import { FormattedMessage } from 'react-intl';

// project imports
import { Select } from 'components/extended/Form';
import { ADDED_EBITDA } from 'constants/Common';

interface IServiceTypeProps {
    required?: boolean;
    name: string;
    label?: string;
}

const AddedEbitdaType = (props: IServiceTypeProps) => {
    const { required, name, label } = props;
    return (
        <>
            <Select required={required} selects={[...ADDED_EBITDA]} name={name} label={<FormattedMessage id={label || 'added-ebitda'} />} />
        </>
    );
};

AddedEbitdaType.defaultProps = {
    isShowAll: false,
    name: 'projectKPIBonus.addedEbitda'
};

export default AddedEbitdaType;
