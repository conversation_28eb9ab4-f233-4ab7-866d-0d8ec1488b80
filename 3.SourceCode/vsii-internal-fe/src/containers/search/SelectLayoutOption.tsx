import { SelectChangeEvent } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project imports
import { Select } from 'components/extended/Form';
import { searchFormConfig } from './Config';
import { IOption } from 'types';

interface ISelectLayoutOptionProps {
    disabled?: boolean;
    handleChangeYear?: (year: SelectChangeEvent<unknown>) => void;
    required?: boolean;
    name?: string;
    disableLabel?: boolean;
}

const layouts: IOption[] = [
    {
        value: 'Top',
        label: 'Top'
    },
    {
        value: 'Middle',
        label: 'Middle'
    },
    {
        value: 'Bottom',
        label: 'Bottom'
    }
];

const SelectLayoutOption = (props: ISelectLayoutOptionProps) => {
    const { disabled, handleChangeYear, required, name, disableLabel } = props;

    const handleChange = (e: SelectChangeEvent<unknown>) => {
        handleChangeYear && handleChangeYear(e);
    };

    return (
        <Select
            required={required}
            disabled={disabled}
            handleChange={handleChange}
            selects={layouts}
            name={name ? name : searchFormConfig.layoutOption.name}
            label={disableLabel ? '' : <FormattedMessage id={searchFormConfig.layoutOption.label} />}
        />
    );
};

export default SelectLayoutOption;
