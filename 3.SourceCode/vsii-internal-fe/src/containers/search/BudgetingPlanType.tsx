import { FormattedMessage } from 'react-intl';
import { Select } from 'components/extended/Form';
import { DEFAULT_VALUE_OPTION, TYPE_BUDGETING_PLAN_SELECT_OPTION, DEFAULT_VALUE_OPTION_SELECT } from 'constants/Common';
import { searchFormConfig } from './Config';

interface IBudgetingPlanType {
    isShowAll?: boolean;
    name: string;
    required?: boolean;
    disabled?: boolean;
    label?: string;
}

const BudgetingPlanType = (props: IBudgetingPlanType) => {
    const { isShowAll, required, name, disabled, label } = props;
    return (
        <Select
            required={required}
            disabled={disabled}
            selects={
                isShowAll
                    ? [DEFAULT_VALUE_OPTION, ...TYPE_BUDGETING_PLAN_SELECT_OPTION]
                    : [DEFAULT_VALUE_OPTION_SELECT, ...TYPE_BUDGETING_PLAN_SELECT_OPTION]
            }
            name={name}
            label={<FormattedMessage id={label || searchFormConfig.budgetingPlanType.label} />}
        />
    );
};

BudgetingPlanType.defaultProps = {
    isShowAll: false,
    name: searchFormConfig.budgetingPlanType.name
};

export default BudgetingPlanType;
