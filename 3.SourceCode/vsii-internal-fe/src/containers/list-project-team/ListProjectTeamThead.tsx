import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ListProjectTeamThead = () => {
    const { ResourcesInProjects } = PERMISSIONS.report;

    const { resourcesInProject } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={resourcesInProject + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={resourcesInProject + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={resourcesInProject + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={resourcesInProject + 'title'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={resourcesInProject + 'dept'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={resourcesInProject + 'main-headcount'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={resourcesInProject + 'secondary-headcount'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={resourcesInProject + 'non-billable-project'} />
                </TableCell>
                {checkAllowedPermission(ResourcesInProjects.commentDetail) && (
                    <TableCell>
                        <FormattedMessage id={resourcesInProject + 'actions'} />
                    </TableCell>
                )}
                {/* <TableCell align="center">
                    <FormattedMessage id="no-logtime" />
                </TableCell> */}
            </TableRow>
        </TableHead>
    );
};

export default ListProjectTeamThead;
