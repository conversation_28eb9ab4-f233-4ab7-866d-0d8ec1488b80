import { FormattedMessage } from 'react-intl';

// material-ui
import ErrorIcon from '@mui/icons-material/Error';
import { Grid, Typography } from '@mui/material';

// project imports
import { IListProjectTeamConfig, listProjectTeamConfig, listProjectTeamSchema } from 'pages/Config';
import { E_IS_LOGTIME, TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import { Department, Member, SearchForm, Weeks, Years } from '../search';
import { searchFormConfig } from '../search/Config';
import { Label } from 'components/extended/Form';
import { IMember } from 'types/member';
import { Button } from 'components';
import { IOption } from 'types';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
interface IListProjectTeamSearchProps {
    formReset: IListProjectTeamConfig;
    weeks: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    handleChangeWeek?: (value: string) => void;
    handleChangeDepartmentId?: (value: string) => void;
    handleChangeMember?: (value: IMember) => void;
}

const ListProjectTeamSearch = (props: IListProjectTeamSearchProps) => {
    const { formReset, weeks, handleChangeYear, handleSearch, handleChangeWeek, handleChangeDepartmentId, handleChangeMember } = props;

    const { resourcesInProject } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={listProjectTeamConfig}
            formSchema={listProjectTeamSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Years handleChangeYear={handleChangeYear} label={resourcesInProject + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Weeks weeks={weeks} onChange={handleChangeWeek} label={resourcesInProject + 'weeks'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Department onChange={handleChangeDepartmentId} label={resourcesInProject + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Member
                        autoFilter={formReset}
                        handleChange={handleChangeMember}
                        findAllType="SCREEN_EFFORT"
                        isUserName
                        isLogTime={E_IS_LOGTIME.YES}
                        name={searchFormConfig.userName.name}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={resourcesInProject + 'members'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={resourcesInProject + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ListProjectTeamSearch;
