import { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ack, Typography } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { useNavigate } from 'react-router-dom';
import { LoadingButton } from '@mui/lab';

import { authSelector, forgotPW, resetAuthState } from 'store/slice/authSlice';
import { IForgotConfig, forgotConfig, forgotSchema } from 'pages/Config';
import { FormProvider, Input } from 'components/extended/Form';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { ROUTER } from 'constants/Routers';

const AuthForgot = () => {
    const { forgotPWSuccessfully, loading } = useAppSelector(authSelector);

    const dispatch = useAppDispatch();
    const navigate = useNavigate();

    const handleSubmit = async (value: IForgotConfig) => {
        const resultAction = await dispatch(forgotPW(value));
        if (forgotPW.fulfilled.match(resultAction)) {
            if (resultAction.payload?.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content,
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
            } else {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content?.message,
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
            }
        }
    };

    useEffect(() => {
        return () => {
            dispatch(resetAuthState());
        };
    }, [dispatch]);

    return (
        <FormProvider form={{ defaultValues: forgotConfig, resolver: yupResolver(forgotSchema) }} onSubmit={handleSubmit}>
            {!forgotPWSuccessfully ? (
                <Grid container gap={1} direction="column">
                    <Typography textAlign="center">Please enter your email address to search for your account.</Typography>
                    <br />
                    <Input name="email" label="Email address" required />
                </Grid>
            ) : (
                <Grid container gap={1} direction="column">
                    <Typography>Please check your email!</Typography>
                    <br />
                    <br />
                    <Typography>
                        Click the following <Link href={window.location.origin}>link</Link> to login to InstantView:
                    </Typography>
                </Grid>
            )}
            <Stack direction="column" alignItems="center" sx={{ mt: !forgotPWSuccessfully ? 8 : 3 }}>
                {!forgotPWSuccessfully ? (
                    <>
                        <LoadingButton variant="contained" type="submit" sx={{ width: '200px' }} loading={loading[forgotPW.typePrefix]}>
                            Confirm
                        </LoadingButton>
                        <Button
                            variant="text"
                            onClick={() => navigate(`/${ROUTER.authentication.login}`)}
                            sx={{
                                mt: 3,
                                textDecoration: 'none',
                                '&:hover': {
                                    textDecoration: 'underline',
                                    background: 'none'
                                }
                            }}
                        >
                            Back to Login
                        </Button>
                    </>
                ) : (
                    <Button variant="contained" sx={{ width: '150px' }} onClick={() => navigate(`/${ROUTER.authentication.login}`)}>
                        Back to Login
                    </Button>
                )}
            </Stack>
        </FormProvider>
    );
};

export default AuthForgot;
