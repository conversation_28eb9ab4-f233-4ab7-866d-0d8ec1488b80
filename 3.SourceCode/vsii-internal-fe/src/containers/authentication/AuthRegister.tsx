import { useEffect, useMemo } from 'react';
import { Stack, Button, Grid, Typography, Box, Tooltip, FormHelperText, Link } from '@mui/material';
import InfoIcon from '@mui/icons-material/InfoOutlined';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';

import { getAllPackageAccount, authSelector, register, resetAuthState } from 'store/slice/authSlice';
import { FormProvider, Input, Select } from 'components/extended/Form';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { registerConfig, registerSchema } from 'pages/Config';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { IRegisterRequest } from 'types/authentication';
import { formatPrice } from 'utils/common';
import { ROUTER } from 'constants/Routers';

const AuthRegister = () => {
    const { packageAccount, registerSuccessfully } = useAppSelector(authSelector);

    const navigate = useNavigate();

    const methods = useForm({
        defaultValues: registerConfig,
        resolver: yupResolver(registerSchema),
        mode: 'all'
    });

    const resetForm = useMemo(() => ({ ...registerConfig, packageAccountId: packageAccount?.[0]?.id || '' }), [packageAccount]);

    const dispatch = useAppDispatch();

    const handleSubmit = async (data: IRegisterRequest) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="confirm-register" />,
                width: '400px',
                handleConfirm: async () => {
                    const resultAction = await dispatch(register(data));
                    if (register.fulfilled.match(resultAction)) {
                        if (resultAction.payload?.status) {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload?.result?.content,
                                    variant: 'alert',
                                    alert: { color: 'success' }
                                })
                            );
                        } else {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload?.result?.content?.message,
                                    variant: 'alert',
                                    alert: { color: 'error' }
                                })
                            );
                        }
                    }
                    dispatch(closeConfirm());
                }
            })
        );
    };

    useEffect(() => {
        dispatch(getAllPackageAccount());
        return () => {
            dispatch(resetAuthState());
        };
    }, [dispatch]);

    return (
        <FormProvider formReturn={methods} onSubmit={handleSubmit} formReset={resetForm}>
            {!registerSuccessfully ? (
                <Grid container gap={1}>
                    <Controller
                        name="packageAccountId"
                        control={methods.control}
                        render={({ fieldState: { error } }) => (
                            <Grid item xs={12}>
                                <Grid item container xs={12} columnSpacing={1}>
                                    {packageAccount.map((pkg, index) => (
                                        <Grid key={index} item xs={12 / packageAccount.length}>
                                            <Box
                                                onClick={() =>
                                                    methods.setValue('packageAccountId', pkg.id, {
                                                        shouldValidate: true
                                                    })
                                                }
                                                sx={{
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    flexDirection: 'column',
                                                    padding: '10px',
                                                    width: '100%',
                                                    cursor: 'pointer',
                                                    position: 'relative',
                                                    backgroundColor: methods.watch('packageAccountId') === pkg.id ? '#3163D4' : '#dfdddd',
                                                    color: methods.watch('packageAccountId') === pkg.id ? '#ffffff' : undefined,
                                                    '&:hover': {
                                                        backgroundColor: '#3163D4',
                                                        color: '#ffffff'
                                                    }
                                                }}
                                                gap={1.5}
                                            >
                                                <Typography fontWeight={500}>
                                                    {pkg.name.charAt(0).toUpperCase() + pkg.name.slice(1)}
                                                </Typography>
                                                <Typography fontWeight={500}>
                                                    {pkg.price ? `${formatPrice(pkg.price)} ₫/${pkg.unit}` : `${pkg.expiresIn} days free`}
                                                </Typography>
                                                <Tooltip
                                                    placement="top"
                                                    title={
                                                        <>
                                                            {pkg.description.split('\\n').map((des, index) => (
                                                                <Typography key={index}>{des}</Typography>
                                                            ))}
                                                        </>
                                                    }
                                                >
                                                    <InfoIcon
                                                        sx={{
                                                            position: 'absolute',
                                                            top: 3,
                                                            right: 3,
                                                            fontSize: '1.1rem'
                                                        }}
                                                    />
                                                </Tooltip>
                                            </Box>
                                        </Grid>
                                    ))}
                                </Grid>
                                {error && (
                                    <FormHelperText sx={{ color: '#f44336' }}>
                                        <FormattedMessage id={error.message} />
                                    </FormHelperText>
                                )}
                            </Grid>
                        )}
                    />
                    <Grid item xs={12} sx={{ mt: 3 }}>
                        <Select
                            name="accountType"
                            label="Account Type"
                            inRow
                            selectWidth={200}
                            handleChange={(e) => {
                                const values = methods.getValues();
                                if (e.target.value !== values.accountType) {
                                    methods.reset({
                                        ...values,
                                        accountType: e.target.value?.toString() || 'PERSONAL',
                                        firstName: '',
                                        lastName: '',
                                        companyName: '',
                                        shortName: '',
                                        citizenId: '',
                                        businessId: '',
                                        taxCode: ''
                                    });
                                }
                            }}
                            selects={[
                                {
                                    label: 'Personal',
                                    value: 'PERSONAL'
                                },
                                {
                                    label: 'Business',
                                    value: 'BUSINESS'
                                }
                            ]}
                        />
                    </Grid>
                    <Grid item container columnSpacing={2}>
                        <Grid item xs={6}>
                            {methods.watch('accountType') === 'PERSONAL' ? (
                                <Input name="firstName" label="First Name" required />
                            ) : (
                                <Input name="companyName" label="Company Name" required />
                            )}
                        </Grid>
                        <Grid item xs={6}>
                            {methods.watch('accountType') === 'PERSONAL' ? (
                                <Input name="lastName" label="Last Name" required />
                            ) : (
                                <Input name="shortName" label="Abbreviated Name (if any)" />
                            )}
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Input name="username" label="Username" required />
                    </Grid>
                    <Grid item xs={12}>
                        <Input name="phone" label="Phone" required />
                    </Grid>
                    <Grid item xs={12}>
                        {methods.watch('accountType') === 'PERSONAL' ? (
                            <Input name="citizenId" label="Citizen ID Number" required />
                        ) : (
                            <Input name="businessId" label="Business License Number" required />
                        )}
                    </Grid>
                    <Grid item xs={12}>
                        <Input name="email" label="Email address" required />
                    </Grid>
                    {methods.watch('accountType') !== 'PERSONAL' ? (
                        <Grid item xs={12}>
                            <Input name="taxCode" label="Tax Code" required />
                        </Grid>
                    ) : null}
                </Grid>
            ) : (
                <Grid container gap={1} direction="column">
                    <Typography>Please check your email!</Typography>
                    <br />
                    <br />
                    <Typography>
                        Click the following <Link href={window.location.origin}>link</Link> to login to InstantView:
                    </Typography>
                </Grid>
            )}

            <Stack direction="column" alignItems="center" sx={{ mt: 4 }}>
                {!registerSuccessfully ? (
                    <>
                        <Button variant="contained" type="submit" sx={{ width: '200px' }}>
                            Register
                        </Button>
                        <Button
                            variant="text"
                            onClick={() => navigate(`/${ROUTER.authentication.login}`)}
                            sx={{
                                mt: 3,
                                textDecoration: 'none',
                                '&:hover': {
                                    textDecoration: 'underline',
                                    background: 'none'
                                }
                            }}
                        >
                            Back to Login
                        </Button>
                    </>
                ) : (
                    <Button variant="contained" sx={{ width: '150px' }} onClick={() => navigate(`/${ROUTER.authentication.login}`)}>
                        Back to Login
                    </Button>
                )}
            </Stack>
        </FormProvider>
    );
};

export default AuthRegister;
