import { useLayoutEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

//yup
import { yupResolver } from '@hookform/resolvers/yup';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, Grid, IconButton, InputAdornment, Link, Stack, Typography } from '@mui/material';

// project imports
import { authSelector, createPassword, resetAuthState, verifyToken } from 'store/slice/authSlice';
import { ICreatePasswordParams, createPasswordConfig, createPasswordSchema } from 'pages/Config';
import { FormProvider, Input } from 'components/extended/Form';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { ROUTER } from 'constants/Routers';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

const AuthCreatePassword = () => {
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);

    const { createPasswordSuccessfully, verifyTokenStatus, loading } = useAppSelector(authSelector);

    const [searchParams] = useSearchParams();

    const navigate = useNavigate();

    const dispatch = useAppDispatch();

    const handleSubmit = async (value: ICreatePasswordParams) => {
        const token = searchParams.get('token');
        const userId = searchParams.get('userId');

        if (!token || !userId) return;

        const resultAction = await dispatch(verifyToken({ token, userId }));

        if (verifyToken.fulfilled.match(resultAction) && !resultAction.payload?.status) {
            if (!resultAction.payload?.status || !resultAction.payload?.result?.content?.statusVerify) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content?.message || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            }
        }

        const resultAction2 = await dispatch(createPassword({ password: value.confirmPassword, userId, token }));

        if (createPassword.fulfilled.match(resultAction2)) {
            const message =
                (resultAction2.payload?.result?.content as { message: string })?.message ||
                resultAction2.payload?.result?.content.toString();
            dispatch(
                openSnackbar({
                    open: true,
                    message,
                    variant: 'alert',
                    alert: { color: resultAction2.payload.status ? 'success' : 'error' }
                })
            );
        }
    };

    const handleCancel = () => {
        navigate(`/${ROUTER.authentication.login}`);
    };

    useLayoutEffect(() => {
        const token = searchParams.get('token');
        const userId = searchParams.get('userId');

        if (token && userId) {
            dispatch(verifyToken({ token, userId }));
        }

        return () => {
            dispatch(resetAuthState());
        };
    }, [dispatch, navigate, searchParams]);

    return !createPasswordSuccessfully && verifyTokenStatus !== 'CREATED' ? (
        <FormProvider
            form={{
                defaultValues: createPasswordConfig,
                resolver: yupResolver(createPasswordSchema)
            }}
            onSubmit={handleSubmit}
        >
            <Grid item container gap={3}>
                <Grid item xs={12}>
                    <Input
                        name="password"
                        label="Password"
                        textFieldProps={{
                            size: 'small',
                            type: showNewPassword ? 'text' : 'password',
                            InputProps: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            aria-label="toggle password visibility"
                                            onClick={() => setShowNewPassword((prev) => !prev)}
                                            edge="end"
                                            size="small"
                                        >
                                            {showNewPassword ? <Visibility /> : <VisibilityOff />}
                                        </IconButton>
                                    </InputAdornment>
                                )
                            }
                        }}
                    />
                </Grid>
                <Grid item xs={12}>
                    <Input
                        name="confirmPassword"
                        label="Confirm Password"
                        textFieldProps={{
                            size: 'small',
                            type: showConfirmPassword ? 'text' : 'password',
                            InputProps: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            aria-label="toggle password visibility"
                                            onClick={() => setShowConfirmPassword((prev) => !prev)}
                                            edge="end"
                                            size="small"
                                        >
                                            {showConfirmPassword ? <Visibility /> : <VisibilityOff />}
                                        </IconButton>
                                    </InputAdornment>
                                )
                            }
                        }}
                    />
                </Grid>
            </Grid>
            <Stack direction="row" justifyContent="space-evenly" sx={{ mt: '40px' }}>
                <LoadingButton
                    loading={loading[createPassword.typePrefix] || loading[verifyToken.typePrefix]}
                    variant="contained"
                    type="submit"
                    sx={{ width: '100px' }}
                >
                    Submit
                </LoadingButton>
                <Button variant="contained" sx={{ width: '100px' }} onClick={handleCancel}>
                    Cancel
                </Button>
            </Stack>
        </FormProvider>
    ) : (
        <>
            <Grid container gap={1} direction="column">
                <Typography>
                    {verifyTokenStatus === 'CREATED' ? (
                        "The user has created a password. Please don't create a new password"
                    ) : (
                        <>
                            Click the following <Link href={window.location.origin}>link</Link> to login to InstantView:
                        </>
                    )}
                </Typography>
            </Grid>
            <Stack direction="column" alignItems="center" sx={{ mt: 3 }}>
                <Button variant="contained" sx={{ width: '150px' }} onClick={() => navigate(`/${ROUTER.authentication.login}`)}>
                    Back to Login
                </Button>
            </Stack>
        </>
    );
};

export default AuthCreatePassword;
