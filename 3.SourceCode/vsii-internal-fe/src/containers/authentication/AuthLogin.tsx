import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

//yup
import { yupResolver } from '@hookform/resolvers/yup';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, Grid, IconButton, InputAdornment, Stack } from '@mui/material';

// project imports
import { authSelector, getUserInfo, loginRequest } from 'store/slice/authSlice';
import { Checkbox, FormProvider, Input } from 'components/extended/Form';
import { encryptByAES, setCookieByKeyObject } from 'utils/cookies';
import { ILoginConfig, loginConfig, loginSchema } from 'pages/Config';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { ROUTER } from 'constants/Routers';
import useConfig from 'hooks/useConfig';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

// ============================|| LOGIN ||============================ //

const AuthLogin = () => {
    const [showPassword, setShowPassword] = useState(false);

    const { loading } = useAppSelector(authSelector);

    const dispatch = useAppDispatch();

    const navigate = useNavigate();

    const { onChangeLocale } = useConfig();

    const handleSubmit = async (value: ILoginConfig) => {
        let payload = value;
        if (value.ldap) {
            payload = { ...value, username: encryptByAES(value.username), password: encryptByAES(value.password) };
        }
        const resultAction = await dispatch(loginRequest(payload));
        if (loginRequest.fulfilled.match(resultAction)) {
            if (resultAction?.payload?.status) {
                const {
                    result: { content }
                } = resultAction.payload;
                if (!content.authen) {
                    dispatch(
                        openSnackbar({
                            open: true,
                            message: 'error-login',
                            variant: 'alert',
                            alert: { color: 'error' }
                        })
                    );
                } else {
                    setCookieByKeyObject(content.token);
                    onChangeLocale('en');
                    await dispatch(getUserInfo({ showLoadingScreen: false }));
                }
            } else {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: 'error-login',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
            }
        }
    };

    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };

    return (
        <FormProvider
            form={{
                defaultValues: loginConfig,
                resolver: yupResolver(loginSchema)
            }}
            onSubmit={handleSubmit}
        >
            <Grid item container gap={3}>
                <Grid item xs={12}>
                    <Input
                        name="username"
                        label="Username"
                        textFieldProps={{
                            autoComplete: 'username',
                            size: 'small'
                        }}
                    />
                </Grid>
                <Grid item xs={12}>
                    <Input
                        name="password"
                        label="Password"
                        textFieldProps={{
                            size: 'small',
                            type: showPassword ? 'text' : 'password',
                            autoComplete: 'current-password',
                            InputProps: {
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            aria-label="toggle password visibility"
                                            onClick={handleClickShowPassword}
                                            edge="end"
                                            size="small"
                                        >
                                            {showPassword ? <Visibility /> : <VisibilityOff />}
                                        </IconButton>
                                    </InputAdornment>
                                )
                            }
                        }}
                    />
                </Grid>
            </Grid>
            <Stack sx={{ my: '10px' }}>
                <Checkbox name="ldap" label="LDAP Account" />
            </Stack>
            <Stack direction="row" justifyContent="center" sx={{ my: '10px' }}>
                <LoadingButton
                    loading={loading[loginRequest.typePrefix] || loading[getUserInfo.typePrefix]}
                    variant="contained"
                    type="submit"
                    sx={{ width: '200px' }}
                >
                    Login
                </LoadingButton>
            </Stack>
            <Stack direction="row" justifyContent="space-around" sx={{ mt: '30px' }}>
                <Button
                    variant="text"
                    onClick={() => navigate(`/${ROUTER.authentication.register}`)}
                    sx={{
                        textDecoration: 'none',
                        '&:hover': {
                            textDecoration: 'underline',
                            background: 'none'
                        }
                    }}
                >
                    Register
                </Button>
                <Button
                    variant="text"
                    onClick={() => navigate(`/${ROUTER.authentication.forgot}`)}
                    sx={{
                        textDecoration: 'none',
                        '&:hover': {
                            textDecoration: 'underline',
                            background: 'none'
                        }
                    }}
                >
                    Forgot password
                </Button>
            </Stack>
        </FormProvider>
    );
};

export default AuthLogin;
