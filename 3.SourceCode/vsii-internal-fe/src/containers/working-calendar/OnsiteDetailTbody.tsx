// material-ui
import { TableCell, TableBody, TableRow } from '@mui/material';
import { IWorkingOnsite } from 'types/working-calendar';

interface IProps {
    data: IWorkingOnsite['content'];
}

const OnsiteDetailTbody = ({ data }: IProps) => {
    return (
        <TableBody>
            {data.map((value, index) => (
                <TableRow key={value.idHexString}>
                    <TableCell align="center" sx={{ width: '3%', px: '3px' }}>
                        {index + 1}
                    </TableCell>
                    <TableCell sx={{ width: '10%', px: '3px' }}>{value.userId}</TableCell>
                    <TableCell sx={{ width: '17%', px: '3px' }}>
                        {value.firstName} {value.lastName}
                    </TableCell>
                    <TableCell sx={{ width: '9%', px: '3px' }}>{value.lastName}</TableCell>
                    <TableCell sx={{ width: '11%', px: '3px' }}>{value.onsiteDays}</TableCell>
                    <TableCell sx={{ width: '6%', px: '3px' }}>{value.onsiteLastMonth}</TableCell>
                    <TableCell sx={{ width: '6%', px: '3px' }}>{value.onsiteThisMonth}</TableCell>
                    <TableCell sx={{ width: '8%', px: '3px' }}>{value.team}</TableCell>
                    <TableCell sx={{ width: '30%', px: '3px' }}>{value.projectList?.join(', ')}</TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};
export default OnsiteDetailTbody;
