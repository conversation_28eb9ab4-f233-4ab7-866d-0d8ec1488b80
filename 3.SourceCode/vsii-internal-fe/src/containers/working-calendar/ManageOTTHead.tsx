// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// third party
import { FormattedMessage } from 'react-intl';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

const ManageOTTHead = () => {
    const { manageLeaves } = PERMISSIONS.workingCalendar;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id="no" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="member" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="approver" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="department" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="ot-type" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="from-date" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="to-date" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="status" />
                </TableCell>
                <TableCell>
                    <FormattedMessage id="approved-date" />
                </TableCell>
                {checkAllowedPermission(manageLeaves.approve) || checkAllowedPermission(manageLeaves.edit) ? (
                    <TableCell align="center">
                        <FormattedMessage id="action" />
                    </TableCell>
                ) : (
                    <></>
                )}
            </TableRow>
        </TableHead>
    );
};

export default ManageOTTHead;
