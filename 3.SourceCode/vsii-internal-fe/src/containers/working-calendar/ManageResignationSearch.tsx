// material-ui
import { Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { ApproveStatus, Member, SearchForm, Years } from 'containers/search';
import { manageResignationDefaultValues, manageResignationSearchSchema } from 'pages/manage-resignation/Config';

// third party
import { FormattedMessage } from 'react-intl';

interface IManageResignationSearchProps {
    formReset: any;
    handleSearch: (values: any) => void;
}

const ManageResignationSearch = (props: IManageResignationSearchProps) => {
    const { formReset, handleSearch } = props;

    const { manage_resignation } = TEXT_CONFIG_SCREEN.workingCalendar;

    return (
        <SearchForm
            defaultValues={manageResignationDefaultValues}
            formSchema={manageResignationSearchSchema}
            formReset={formReset}
            handleSubmit={handleSearch}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Years label={manage_resignation + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Member name="memberId" isIdHexString label={<FormattedMessage id={manage_resignation + 'members'} />} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <ApproveStatus name="status" label={manage_resignation + 'status'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={manage_resignation + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ManageResignationSearch;
