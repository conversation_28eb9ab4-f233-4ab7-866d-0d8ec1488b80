import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid, SelectChangeEvent } from '@mui/material';
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { E_IS_LOGTIME, TEXT_CONFIG_SCREEN } from 'constants/Common';

// project imports
import { IOption } from 'types';
import { Department, Member, Months, SearchForm, Years } from '../search';
import { searchFormConfig } from 'containers/search/Config';
import { IWorkingCalendarSearch, workingCalendarSearhSchema, workingCalenderSearchConfig } from 'pages/register-working-calendar/Config';

interface IRegisterWorkingCalendarSearchProps {
    formReset: IWorkingCalendarSearch;
    months: IOption[];
    handleChangeYear: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
    handleSearch: (value: IWorkingCalendarSearch) => void;
    year: number;
    month: number | string;
    setIsMonth?: React.Dispatch<React.SetStateAction<number | string>>;
}

const RegisterWorkingCalenderSearch = (props: IRegisterWorkingCalendarSearchProps) => {
    const { formReset, months, handleChangeYear, handleSearch, year, month, setIsMonth } = props;

    const { register_working_calendar } = TEXT_CONFIG_SCREEN.workingCalendar;

    return (
        <SearchForm
            defaultValues={workingCalenderSearchConfig}
            formSchema={workingCalendarSearhSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Years handleChangeYear={handleChangeYear} label={register_working_calendar + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Months months={months} setIsMonth={setIsMonth} isMonth={month} label={register_working_calendar + 'month'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Department label={register_working_calendar + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Member
                        isLogTime={E_IS_LOGTIME.ALL}
                        name={searchFormConfig.idHexString.name}
                        year={year}
                        month={month}
                        label={<FormattedMessage id={register_working_calendar + 'members'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={register_working_calendar + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default RegisterWorkingCalenderSearch;
