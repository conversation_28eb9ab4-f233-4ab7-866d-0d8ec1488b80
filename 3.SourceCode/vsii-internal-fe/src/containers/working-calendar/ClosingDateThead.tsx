import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow, Typography } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ClosingDateWorkingCalendarThead = () => {
    const { register_working_calendar } = TEXT_CONFIG_SCREEN.workingCalendar;
    return (
        <TableHead
            sx={{
                position: 'sticky',
                top: 0,
                zIndex: '20',
                '& span': {
                    marginRight: '5px'
                }
            }}
        >
            <TableRow>
                <TableCell rowSpan={2}>
                    <Typography sx={{ color: '#3163D4', fontWeight: '700 !important', textAlign: 'center' }}>
                        <FormattedMessage id={register_working_calendar + 'year'} />
                    </Typography>
                </TableCell>
                <TableCell>
                    <Typography sx={{ color: '#3163D4', fontWeight: '700 !important', textAlign: 'center' }}>
                        <FormattedMessage id={register_working_calendar + 'month'} />
                    </Typography>
                </TableCell>
                <TableCell>
                    <Typography sx={{ color: '#3163D4', fontWeight: '700 !important', textAlign: 'center' }}>
                        <FormattedMessage id={register_working_calendar + 'closing-date'} />
                    </Typography>
                </TableCell>
                <TableCell></TableCell>
            </TableRow>
        </TableHead>
    );
};
export default ClosingDateWorkingCalendarThead;
