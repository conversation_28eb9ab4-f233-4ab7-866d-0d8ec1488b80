// material-ui
import { Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { DatePicker, Label } from 'components/extended/Form';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { ApproveStatus, Department, LeavesType, Member, SearchForm } from 'containers/search';
import { IManageLeavesDefaultValues, manageLeavesDefaultValues, manageLeavesSearchSchema } from 'pages/manage-leaves/Config';

// third party
import { FormattedMessage } from 'react-intl';
import { checkAllowedPermission } from 'utils/authorization';

interface IManageLeavesSearchProps {
    formReset: IManageLeavesDefaultValues;
    handleSearch: (values: any) => void;
}

const ManageLeavesSearch = (props: IManageLeavesSearchProps) => {
    const { formReset, handleSearch } = props;

    const { manage_leaves } = TEXT_CONFIG_SCREEN.workingCalendar;
    const { manageLeaves } = PERMISSIONS.workingCalendar;

    return (
        <SearchForm
            defaultValues={manageLeavesDefaultValues}
            formSchema={manageLeavesSearchSchema}
            formReset={formReset}
            handleSubmit={handleSearch}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <DatePicker name="fromDate" label={<FormattedMessage id={manage_leaves + 'from-date'} />} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <DatePicker name="toDate" label={<FormattedMessage id={manage_leaves + 'to-date'} />} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Member
                        name="memberId"
                        isIdHexString
                        label={<FormattedMessage id={manage_leaves + 'members'} />}
                        disabled={!checkAllowedPermission(manageLeaves.approve)}
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Department name="dept" label={manage_leaves + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <LeavesType name="type" label={manage_leaves + 'leaves-type'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <ApproveStatus name="status" label={manage_leaves + 'status'} />
                </Grid>
                <Grid item xs={12} lg={3}></Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={manage_leaves + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ManageLeavesSearch;
