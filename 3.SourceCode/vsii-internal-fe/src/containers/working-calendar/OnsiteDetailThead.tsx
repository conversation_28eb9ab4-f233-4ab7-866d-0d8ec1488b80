import { FormattedMessage } from 'react-intl';
import moment from 'moment';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import useConfig from 'hooks/useConfig';

interface IProps {
    year: number;
    month: number;
}

const OnsiteDetailThead = ({ month, year }: IProps) => {
    const { locale } = useConfig();

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center" rowSpan={2} sx={{ width: '3%', px: '3px' }}>
                    <FormattedMessage id="onsite-detail.modal.body.table.no" />
                </TableCell>
                <TableCell align="center" rowSpan={2} sx={{ width: '10%', px: '3px' }}>
                    <FormattedMessage id="onsite-detail.modal.body.table.employee-id" />
                </TableCell>
                <TableCell align="center" rowSpan={2} sx={{ width: '17%', px: '3px' }}>
                    <FormattedMessage id="onsite-detail.modal.body.table.employee-name" />
                </TableCell>
                <TableCell align="center" rowSpan={2} sx={{ width: '9%', px: '3px' }}>
                    <FormattedMessage id="onsite-detail.modal.body.table.first-name" />
                </TableCell>
                <TableCell align="center" rowSpan={1} colSpan={3}>
                    {moment()
                        .locale(locale === 'vi' ? 'vi' : 'en-gb')
                        .set({ year, month })
                        .format('MMM')}
                </TableCell>
                <TableCell align="center" rowSpan={2} sx={{ width: '8%', px: '3px' }}>
                    <FormattedMessage id="onsite-detail.modal.body.table.team" />
                </TableCell>
                <TableCell align="center" rowSpan={2} sx={{ width: '30%', px: '3px' }}>
                    <FormattedMessage id="onsite-detail.modal.body.table.project-name" />
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell align="center" rowSpan={1} sx={{ width: '11%', px: '3px' }}>
                    <FormattedMessage id="onsite-detail.modal.body.table.onsite-days" />
                </TableCell>
                <TableCell align="center" rowSpan={1} sx={{ width: '6%', px: '3px' }}>
                    {moment()
                        .locale(locale === 'vi' ? 'vi' : 'en-gb')
                        .set({ year, month })
                        .subtract(1, 'months')
                        .format('MMM')}
                </TableCell>
                <TableCell align="center" rowSpan={1} sx={{ width: '6%', px: '3px' }}>
                    {moment()
                        .locale(locale === 'vi' ? 'vi' : 'en-gb')
                        .set({ year, month })
                        .format('MMM')}
                </TableCell>
            </TableRow>
        </TableHead>
    );
};
export default OnsiteDetailThead;
