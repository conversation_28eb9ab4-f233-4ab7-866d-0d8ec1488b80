/* eslint-disable prettier/prettier */

// material-ui
import { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project imports
import { ILeavesItem } from 'types/working-calendar';
import { dateFormat } from 'utils/date';
import { GROUP_ID_APPROVER, STATUS_COLORS } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// assets
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import { useAppSelector } from 'app/hooks';
import { authSelector } from 'store/slice/authSlice';

interface IManageLeavesTBodyProps {
    pageNumber: number;
    pageSize: number;
    leaves: ILeavesItem[];
    handleEdit: (item?: ILeavesItem) => void;
    handleDelete: (id: string) => void;
    handleDownload: (id: string) => void;
}

const ManageLeavesTBody = (props: IManageLeavesTBodyProps) => {
    const { pageNumber, pageSize, leaves, handleEdit, handleDelete, handleDownload } = props;
    const { manageLeaves } = PERMISSIONS.workingCalendar;

    // Common style for all table cells to prevent wrapping
    const tableCellSx = {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
    };
    const fullContentCellSx = {
        whiteSpace: 'normal',
        overflow: 'visible'
    };

    const { userInfo } = useAppSelector(authSelector);

    const userGroup = userInfo?.role?.map((item) => item.groupId);

    return (
        <TableBody>
            {leaves.map((item, key) => (
                <TableRow key={key}>
                    <TableCell sx={tableCellSx}>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell sx={tableCellSx}>{item.memberName}</TableCell>
                    <TableCell sx={tableCellSx}>{item.approveName}</TableCell>
                    <TableCell sx={tableCellSx}>{item.dept}</TableCell>
                    <TableCell sx={tableCellSx}>
                        {!item.leaveType ? (
                            '-'
                        ) : item.leaveType.includes(',') ? (
                            <Tooltip
                                title={
                                    <Stack direction="column" spacing={0.5}>
                                        {item.leaveType.split(',').map((type, index) => (
                                            <Typography key={index} variant="body2" color="white">
                                                <FormattedMessage id={type.trim()} />
                                            </Typography>
                                        ))}
                                    </Stack>
                                }
                                placement="top"
                                arrow
                            >
                                <Typography>
                                    <FormattedMessage id={item.leaveType.split(',')[0].trim()} />
                                    <Typography component="span" color="textSecondary">
                                        {' '}
                                        +{item.leaveType.split(',').length - 1}
                                    </Typography>
                                </Typography>
                            </Tooltip>
                        ) : (
                            <Typography>
                                <FormattedMessage id={item.leaveType} />
                            </Typography>
                        )}
                    </TableCell>
                    <TableCell sx={tableCellSx}>{dateFormat(item.fromDate)}</TableCell>
                    <TableCell sx={tableCellSx}>{dateFormat(item.toDate)}</TableCell>
                    <TableCell sx={fullContentCellSx}>
                        {!item.status ? (
                            '-'
                        ) : (
                            <Typography color={STATUS_COLORS[item.status as keyof typeof STATUS_COLORS] || 'textPrimary'}>
                                <FormattedMessage id={item.status} />
                            </Typography>
                        )}
                    </TableCell>
                    <TableCell sx={tableCellSx}>{item.approvedDate && dateFormat(item.approvedDate)}</TableCell>
                    <TableCell sx={tableCellSx}>
                        <Stack direction="row" justifyContent="center" alignItems="center">
                            {/* Show edit button for both approvers and non-approvers */}
                            <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleEdit(item)}>
                                <IconButton aria-label="edit" size="small">
                                    <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                </IconButton>
                            </Tooltip>
                            {/* Additional options only for approvers who are HR */}
                            {checkAllowedPermission(manageLeaves.approve) && userGroup && userGroup.includes(GROUP_ID_APPROVER.HR) && (
                                <>
                                    <Tooltip
                                        placement="top"
                                        title={<FormattedMessage id="download" />}
                                        onClick={() => handleDownload(item.id)}
                                    >
                                        <IconButton aria-label="download" size="small">
                                            <DownloadOutlinedIcon sx={{ fontSize: '1.1rem' }} />
                                        </IconButton>
                                    </Tooltip>
                                    {checkAllowedPermission(manageLeaves.delete) && (
                                        <Tooltip
                                            placement="top"
                                            title={<FormattedMessage id="delete" />}
                                            onClick={() => handleDelete(item.id)}
                                        >
                                            <IconButton aria-label="delete" size="small">
                                                <DeleteOutlineOutlinedIcon sx={{ fontSize: '1.1rem' }} />
                                            </IconButton>
                                        </Tooltip>
                                    )}
                                </>
                            )}
                        </Stack>
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ManageLeavesTBody;
