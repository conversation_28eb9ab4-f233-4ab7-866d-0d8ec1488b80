import React from 'react';
import { Grid, IconButton } from '@mui/material';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import DatePicker from 'components/extended/Form/DatePicker';
import Input from 'components/extended/Form/Input';
import { FormattedMessage } from 'react-intl';
import AddBoxIcon from '@mui/icons-material/AddBox';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
interface IOvertimeRecordRowProps {
    field: any;
    index: number;
    remove: (index: number) => void;
    onAdd: () => void;
    totalRecords: number;
    disabled?: boolean;
}

const OvertimeRecordRow: React.FC<IOvertimeRecordRowProps> = ({ field, index, remove, onAdd, totalRecords, disabled }) => {
    const { manage_ot } = TEXT_CONFIG_SCREEN.workingCalendar;
    return (
        <Grid container spacing={1} alignItems="center" sx={{ mb: index === totalRecords - 1 ? 0 : 2 }}>
            <Grid item xs={1} sx={{ display: 'flex', justifyContent: 'center' }}>
                {index === 0 ? (
                    <IconButton
                        onClick={onAdd}
                        size="small"
                        disabled={disabled}
                        sx={{
                            color: 'primary.main',
                            borderRadius: '8px',
                            '&:hover': {
                                backgroundColor: '#E0E0E0'
                            },
                            marginRight: 1
                        }}
                    >
                        <AddBoxIcon />
                    </IconButton>
                ) : (
                    <IconButton
                        onClick={() => remove(index)}
                        color="error"
                        size="small"
                        disabled={disabled}
                        sx={{
                            borderRadius: '8px',
                            '&:hover': {
                                backgroundColor: '#E0E0E0'
                            },
                            marginRight: 1
                        }}
                    >
                        <RemoveCircleOutlineIcon />
                    </IconButton>
                )}
            </Grid>
            <Grid item xs={11}>
                <Grid container spacing={2}>
                    <Grid item md={4.5} xs={12}>
                        <DatePicker
                            name={`overtimeRecords.${index}.overtimeFrom`}
                            label={<FormattedMessage id={manage_ot + 'overtime-from'} />}
                            required
                            disabled={disabled}
                        />
                    </Grid>
                    <Grid item md={4.5} xs={12}>
                        <DatePicker
                            name={`overtimeRecords.${index}.overtimeTo`}
                            label={<FormattedMessage id={manage_ot + 'overtime-to'} />}
                            required
                            disabled={disabled}
                        />
                    </Grid>
                    <Grid item md={3} xs={12}>
                        <Input
                            name={`overtimeRecords.${index}.overtimeHours`}
                            label={<FormattedMessage id={manage_ot + 'overtime-hours'} />}
                            required
                            disabled={disabled}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default OvertimeRecordRow;
