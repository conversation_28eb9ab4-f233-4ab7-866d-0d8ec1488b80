import { FormattedMessage } from 'react-intl';

// materia-ui
import { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';
import { ILeaveDays } from 'types/working-calendar';

interface ILeaveDaysTBodyProps {
    pageNumber: number;
    pageSize: number;
    leaveDays: ILeaveDays[];
    handleOpen: (leaveDay?: ILeaveDays) => void;
}

const LeaveDaysTBody = (props: ILeaveDaysTBodyProps) => {
    const { pageNumber, pageSize, leaveDays, handleOpen } = props;
    const { manageLeaveDay } = PERMISSIONS.workingCalendar;

    return (
        <TableBody>
            {leaveDays.map((item: ILeaveDays, key: number) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>
                        {item.firstName} {item.lastName}
                    </TableCell>
                    <TableCell>{item.memberCode}</TableCell>
                    <TableCell>{item.titleCode}</TableCell>
                    <TableCell>{item.departmentId}</TableCell>
                    <TableCell>{item.leaveDaysInformation.totalLeaveDays}</TableCell>
                    <TableCell>{item.leaveDaysInformation.totalLeaveDaysUsed}</TableCell>
                    <TableCell>{item.leaveDaysInformation.totalLeaveDaysRemain}</TableCell>
                    {checkAllowedPermission(manageLeaveDay.editManageLeaveDay) && (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleOpen(item)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default LeaveDaysTBody;
