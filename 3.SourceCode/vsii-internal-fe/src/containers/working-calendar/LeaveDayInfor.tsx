import { useState, useRef, useEffect } from 'react';
import { IconButton, Popover, Grid, Typography, Box, MenuItem, Select, FormControl, TextField, Divider } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import { ILeaveDaysInformation, ILeaveOption } from 'types/leave-days';
import { FormattedMessage, useIntl } from 'react-intl';

interface ILeaveDaysInfoProps {
    memberId: string;
    leaveData?: ILeaveDaysInformation | null;
}

const commonInputSx = {
    maxWidth: 100,
    '& .MuiInputBase-root': {
        height: 26,
        borderRadius: '4px',
        padding: 0
    },
    '& .MuiOutlinedInput-input': {
        padding: '0 8px',
        height: '100%',
        lineHeight: '26px',
        borderRadius: '4px'
    },
    '& .MuiOutlinedInput-notchedOutline': {
        border: 'none'
    }
};

const LeaveDaysInfo = ({ memberId, leaveData: propLeaveData }: ILeaveDaysInfoProps) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [leaveData, setLeaveData] = useState<ILeaveDaysInformation | null>(propLeaveData || null);
    const [leaveOptions, setLeaveOptions] = useState<ILeaveOption[]>([]);
    const [selectedLeave, setSelectedLeave] = useState<ILeaveOption | null>(null);
    const [isHovering, setIsHovering] = useState(false);
    const intl = useIntl();

    useEffect(() => {
        if (propLeaveData) {
            setLeaveData(propLeaveData);
            const options: ILeaveOption[] = [
                { label: 'leave-days-wedding', value: 'totalLeaveDaysWedding', days: propLeaveData.totalLeaveDaysWedding },
                { label: 'leave-days-funeral', value: 'totalLeaveDaysFuneral', days: propLeaveData.totalLeaveDaysFuneral },
                { label: 'leave-days-maternity', value: 'totalLeaveDaysMaternity', days: propLeaveData.totalLeaveDaysMaternity }
            ];
            setLeaveOptions(options);
            setSelectedLeave(options[0]);
            return;
        }

        if (memberId) {
            fetchLeaveDays();
        }
    }, [memberId, propLeaveData]);

    useEffect(() => {
        if (propLeaveData) {
            setLeaveData(propLeaveData);
            const options: ILeaveOption[] = [
                { label: 'leave-days-wedding', value: 'totalLeaveDaysWedding', days: propLeaveData.totalLeaveDaysWedding },
                { label: 'leave-days-funeral', value: 'totalLeaveDaysFuneral', days: propLeaveData.totalLeaveDaysFuneral },
                { label: 'leave-days-maternity', value: 'totalLeaveDaysMaternity', days: propLeaveData.totalLeaveDaysMaternity }
            ];
            setLeaveOptions(options);
            if (!selectedLeave) {
                setSelectedLeave(options[0]);
            }
        }
    }, [propLeaveData]);

    useEffect(() => {
        if (!isHovering) {
            const timeout = setTimeout(() => {
                setAnchorEl(null);
            }, 400);
            return () => clearTimeout(timeout);
        }
    }, [isHovering]);

    const fetchLeaveDays = async () => {
        try {
            const response = await sendRequest(Api.leave_day.getLeaveDaysInfo(memberId));

            if (response?.status) {
                const info = response.result.content;

                const options: ILeaveOption[] = [
                    { label: 'leave-days-wedding', value: 'totalLeaveDaysWedding', days: info.totalLeaveDaysWedding },
                    { label: 'leave-days-funeral', value: 'totalLeaveDaysFuneral', days: info.totalLeaveDaysFuneral },
                    { label: 'leave-days-maternity', value: 'totalLeaveDaysMaternity', days: info.totalLeaveDaysMaternity }
                ];

                setLeaveData(info);
                setLeaveOptions(options);
                setSelectedLeave(options[0]);
            } else {
                console.error('Error fetching leave days info:', response?.result?.messages?.[0]?.message || 'Unknown error');
            }
        } catch (error) {
            console.error('Error fetching leave days info:', error);
        }
    };

    const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
        setIsHovering(true);
    };

    const handlePopoverClose = () => {
        setIsHovering(false);
    };

    const open = Boolean(anchorEl);

    const handleLeaveChange = (event: any) => {
        const selected = leaveOptions.find((option) => option.value === event.target.value);
        if (selected) {
            setSelectedLeave(selected);
        }
    };

    const capitalizeFirstLetter = (text: string) => {
        return text.charAt(0).toUpperCase() + text.slice(1);
    };

    if (!leaveData) return null;

    return (
        <Box sx={{ lineHeight: 0 }}>
            <Box onMouseEnter={handlePopoverOpen} onMouseLeave={handlePopoverClose}>
                <IconButton size="small" sx={{ padding: '2px' }}>
                    <InfoOutlinedIcon fontSize="small" sx={{ fontSize: '1rem' }} />
                </IconButton>
            </Box>

            <Popover
                id="mouse-over-popover"
                open={open}
                anchorEl={anchorEl}
                onClose={() => setAnchorEl(null)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                PaperProps={{
                    onMouseEnter: () => setIsHovering(true),
                    onMouseLeave: handlePopoverClose,
                    sx: {
                        pointerEvents: 'auto',
                        mt: 1,
                        p: 2,
                        borderRadius: 2,
                        boxShadow: 3,
                        maxWidth: 350
                    }
                }}
            >
                <Typography variant="h3" mb={2} fontSize="1rem">
                    <FormattedMessage id="leave-days-title" />
                </Typography>
                <Divider />

                <Grid container spacing={2} mt={1}>
                    {[
                        ['leave-days-remaining', leaveData.totalLeaveDaysMonthly],
                        ['leave-days-seniority', leaveData.totalLeaveDaysSeniority],
                        ['leave-days-compensation', leaveData.totalLeaveDaysCompansation],
                        ['leave-days-annual', leaveData.totalLeaveDays],
                        ['leave-days-sick', leaveData.totalLeaveDaysSick],
                        ['leave-days-unpaid', leaveData.totalLeaveDaysUnpaid]
                    ].map(([label, value], i) => (
                        <Grid item xs={12} key={i}>
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                                <Typography fontSize={14}>
                                    <FormattedMessage id={label as string} />
                                </Typography>
                                <TextField
                                    value={value}
                                    size="small"
                                    sx={commonInputSx}
                                    InputProps={{ readOnly: true }}
                                    variant="outlined"
                                    disabled
                                />
                            </Box>
                        </Grid>
                    ))}

                    <Grid item xs={12}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography fontSize={14}>
                                <FormattedMessage id="leave-days-other" />
                            </Typography>
                            <FormControl
                                size="small"
                                sx={{
                                    ...commonInputSx,
                                    '& .MuiOutlinedInput-notchedOutline': {
                                        border: '1px solid #ccc',
                                        borderRadius: '4px'
                                    }
                                }}
                            >
                                <Select value={selectedLeave?.value ?? ''} onChange={handleLeaveChange}>
                                    {leaveOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value}>
                                            <FormattedMessage id={option.label} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>
                    </Grid>

                    {selectedLeave && (
                        <Grid item xs={12}>
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                                <Typography fontSize={14}>
                                    <FormattedMessage
                                        id="number-days"
                                        defaultMessage="Số ngày {leaveType}"
                                        values={{
                                            leaveType:
                                                intl.locale === 'en'
                                                    ? capitalizeFirstLetter(intl.formatMessage({ id: selectedLeave.label }))
                                                    : intl.formatMessage({ id: selectedLeave.label }).toLowerCase()
                                        }}
                                    />
                                </Typography>
                                <TextField
                                    value={selectedLeave.days}
                                    size="small"
                                    sx={commonInputSx}
                                    InputProps={{ readOnly: true }}
                                    variant="outlined"
                                    disabled
                                />
                            </Box>
                        </Grid>
                    )}
                </Grid>
                <Divider sx={{ my: 2 }} />
                <Box p={1} />
            </Popover>
        </Box>
    );
};

export default LeaveDaysInfo;
