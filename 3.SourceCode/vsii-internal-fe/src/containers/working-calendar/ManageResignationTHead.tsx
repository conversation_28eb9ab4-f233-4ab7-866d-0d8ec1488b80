// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// third party
import { FormattedMessage } from 'react-intl';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ManageResignationTHead = () => {
    const { manageResignation } = PERMISSIONS.workingCalendar;

    const { manage_resignation } = TEXT_CONFIG_SCREEN.workingCalendar;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'approver'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'title'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'dept'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'status'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_resignation + 'approved-date'} />
                </TableCell>
                {checkAllowedPermission(manageResignation.edit) || checkAllowedPermission(manageResignation.approve) ? (
                    <TableCell align="center">
                        <FormattedMessage id={manage_resignation + 'actions'} />
                    </TableCell>
                ) : (
                    <></>
                )}
            </TableRow>
        </TableHead>
    );
};

export default ManageResignationTHead;
