// material-ui
import { Button, Stack, Typography } from '@mui/material';

// project imports
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { EApproveStatus } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

interface IApproveModalProps {
    open: boolean;
    handleClose: () => void;
    handleApprove: (status: string) => void;
    content: string;
}

const ApproveModal = (props: IApproveModalProps) => {
    const { open, handleClose, handleApprove, content } = props;

    return (
        <Modal isOpen={open} title="approve" onClose={handleClose} maxWidth="xs">
            <Typography textAlign="center">
                <FormattedMessage id={content} />
            </Typography>
            <Stack direction="row" spacing={1} justifyContent="center" sx={{ mt: gridSpacing }}>
                <Button variant="contained" onClick={() => handleApprove(EApproveStatus.APPROVED)}>
                    <FormattedMessage id="approve" />
                </Button>
                <Button variant="contained" onClick={() => handleApprove(EApproveStatus.DECLINED)}>
                    <FormattedMessage id="declines" />
                </Button>
            </Stack>
        </Modal>
    );
};

export default ApproveModal;
