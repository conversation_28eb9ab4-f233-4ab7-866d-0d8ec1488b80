import { FormattedMessage, useIntl } from 'react-intl';
import { useDispatch } from 'react-redux';
import { useState } from 'react';

// material-ui
import { Grid, Button, DialogActions, Stack } from '@mui/material';
import { LoadingButton } from '@mui/lab';
// react-hook-form
import { UseFormReturn, SubmitHandler } from 'react-hook-form';
import React from 'react';

// project imports
import FormProvider from 'components/extended/Form/FormProvider';
import DatePicker from 'components/extended/Form/DatePicker';
import Input from 'components/extended/Form/Input';
import Modal from 'components/extended/Modal';
import { openSnackbar } from 'store/slice/snackbarSlice';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';
import { ILeaveRequestData } from 'pages/manage-leaves/Config';

export interface IRejectLeaveModalProps {
    isOpen: boolean;
    onClose: () => void;
    data: ILeaveRequestData;
    formReturn: UseFormReturn<IRejectLeaveFormValues>;
    onSuccess?: () => void;
    leaveIdHexString?: string | number;
    apiType?: 'leave' | 'ot';
}

export interface IRejectLeaveFormValues {
    reason: string;
    fromDate: Date;
    toDate: Date;
    fullName: string;
    totalDays: number | string;
}

export const RejectLeaveModal: React.FC<IRejectLeaveModalProps> = ({
    isOpen,
    onClose,
    data,
    formReturn,
    onSuccess,
    leaveIdHexString,
    apiType = 'leave'
}) => {
    const intl = useIntl();
    const { reset, clearErrors, trigger } = formReturn;
    const dispatch = useDispatch();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit: SubmitHandler<IRejectLeaveFormValues> = async (formData) => {
        const isValid = await trigger();
        if (!isValid) {
            return;
        }
        setIsSubmitting(true);

        if (!leaveIdHexString) {
            setIsSubmitting(false);
            return;
        }

        try {
            const endpoint = apiType === 'ot' ? Api.manage_ot.putReject : Api.manage_leaves.putReject;

            const response = await sendRequest(endpoint, {
                idHexString: leaveIdHexString,
                reason: formData.reason
            });

            if (response?.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: 'decline-success',
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
                handleClose();
                if (onSuccess) onSuccess();
            } else {
                dispatch(
                    openSnackbar({
                        open: true,
                        isMultipleLanguage: true,
                        message: response?.result?.content?.message || response?.message || 'failed',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                handleClose();
            }
        } catch (error) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'An error occurred',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
            handleClose();
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleOpen = () => {
        reset({
            ...data,
            reason: '',
            fromDate: new Date(data.fromDate),
            toDate: new Date(data.toDate)
        });
        clearErrors();
    };

    const handleClose = () => {
        reset({
            reason: '',
            fromDate: new Date(),
            toDate: new Date(),
            fullName: '',
            totalDays: ''
        });
        clearErrors();
        onClose();
    };

    React.useEffect(() => {
        if (isOpen) {
            handleOpen();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen]);

    return (
        <FormProvider formReturn={formReturn} onSubmit={handleSubmit}>
            <Modal
                isOpen={isOpen}
                title={apiType === 'ot' ? 'reject-ot-request' : 'reject-leave-request'}
                onClose={handleClose}
                keepMounted={false}
                footer={
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose} disabled={isSubmitting}>
                                <FormattedMessage id="cancel" />
                            </Button>
                            <LoadingButton
                                loading={isSubmitting}
                                variant="contained"
                                type="button"
                                onClick={formReturn.handleSubmit(handleSubmit)}
                            >
                                <FormattedMessage id="send-request" />
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                }
            >
                <Grid container spacing={3}>
                    <Grid item xs={6}>
                        <Input
                            name="fullName"
                            label={<FormattedMessage id="full-name" />}
                            textFieldProps={{ InputProps: { style: { color: '#131E29' } } }}
                            disabled
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Input
                            name="totalDays"
                            label={<FormattedMessage id={apiType === 'ot' ? 'total-ot-hours' : 'total-leave-day'} />}
                            disabled
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <DatePicker
                            name="fromDate"
                            label={<FormattedMessage id={apiType === 'ot' ? 'ot-from-date' : 'leave-from-date'} />}
                            disabled
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <DatePicker
                            name="toDate"
                            label={<FormattedMessage id={apiType === 'ot' ? 'ot-to-date' : 'leave-to-date'} />}
                            disabled
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <Input
                            name="reason"
                            label={<FormattedMessage id="reject-reason" />}
                            required
                            textFieldProps={{
                                multiline: true,
                                rows: 3,
                                placeholder: intl.formatMessage({ id: 'enter-reject-reason' }),
                                InputProps: {
                                    style: { color: '#131E29' }
                                }
                            }}
                        />
                    </Grid>
                </Grid>
            </Modal>
        </FormProvider>
    );
};

export default RejectLeaveModal;
