// react
import React, { useEffect } from 'react';

// third party
import { useForm, SubmitHandler, useFieldArray, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid, Stack, Button, Box } from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project imports
import { IOvertimeReport, overtimeReportSchema } from 'pages/manage-ot-requests/Config';
import {
    compensateTypeOptions,
    EApproveStatus,
    isApproved,
    isApprovedOrAwaiting,
    isAwaitingHR,
    isAwaitingQLKT,
    isAwaitingQLTT,
    isDeclined,
    MESSAGE_APPROVE_RESPONSE,
    overtimeReasonOptions,
    TEXT_CONFIG_SCREEN
} from 'constants/Common';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { ILeaveRequestData } from 'pages/manage-leaves/Config';
import { checkAllowedPermission } from 'utils/authorization';
import DirectApprover from 'containers/search/DirectApprover';
import DatePicker from 'components/extended/Form/DatePicker';
import { useAppSelector, useAppDispatch } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { FormProvider } from 'components/extended/Form';
import Select from 'components/extended/Form/Select';
import { authSelector } from 'store/slice/authSlice';
import OvertimeRecordRow from './OvertimeRecordRow';
import Input from 'components/extended/Form/Input';
import { PERMISSIONS } from 'constants/Permission';
import { checkIsHoliday } from 'utils/common';
import Modal from 'components/extended/Modal';
import sendRequest from 'services/ApiService';
import { Project } from 'containers/search';
import { IOption } from 'types';
import Api from 'constants/Api';
interface IOvertimeReportModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: SubmitHandler<IOvertimeReport>;
    defaultValues: IOvertimeReport & { idHexString?: string };
    statusOt?: string;
    isEdit?: boolean;
    onReject?: (data: ILeaveRequestData) => void;
    onSuccess?: () => void;
    statusOverTimeTicket?: string;
}

const AddOrEditOvertimeReportModal: React.FC<IOvertimeReportModalProps> = ({
    isOpen,
    onClose,
    onSubmit,
    defaultValues,
    statusOt,
    isEdit,
    onReject,
    onSuccess,
    statusOverTimeTicket
}) => {
    // Redux hooks
    const dispatch = useAppDispatch();
    const { userInfo } = useAppSelector(authSelector);
    const { manageOt } = PERMISSIONS.workingCalendar;

    //Local state
    const [loading, setLoading] = React.useState(false);
    const [holidays, setHolidays] = React.useState<string[]>([]);
    const { manage_ot } = TEXT_CONFIG_SCREEN.workingCalendar;
    //Check
    const userGroup = userInfo?.role?.map((item) => item.groupName);
    const isUserCreator = userInfo?.idHexString === defaultValues?.userIdHexString;
    const isUserManager = userInfo?.idHexString === defaultValues?.manager;
    const isUserNextManager = userInfo?.idHexString === defaultValues?.nextManager;
    const isCBUser = userGroup?.includes('C&B');

    const checkUserIsManagerApprove = () => {
        if (isAwaitingQLTT(statusOverTimeTicket)) return isUserManager;
        if (isAwaitingQLKT(statusOverTimeTicket)) return isUserNextManager;
        if (isAwaitingHR(statusOverTimeTicket)) return isCBUser;
        if (isApprovedOrAwaiting(statusOverTimeTicket) || isDeclined(statusOverTimeTicket)) {
            return isUserManager || isUserNextManager || isCBUser;
        }
        return false;
    };

    const checkManager = () => {
        if (!isEdit) return true;
        if (isUserCreator && isApprovedOrAwaiting(statusOverTimeTicket)) return true;
        if (checkUserIsManagerApprove() && !isAwaitingQLTT(statusOverTimeTicket)) return true;
        if (!checkUserIsManagerApprove() && checkAllowedPermission(manageOt.view)) return true;
        return false;
    };

    const isDisableStatus = isDeclined(statusOverTimeTicket) || isApprovedOrAwaiting(statusOverTimeTicket);

    const isCheckedApprove = () => {
        return isEdit && checkAllowedPermission(PERMISSIONS.workingCalendar.manageOt.approve) && checkUserIsManagerApprove();
    };

    const showApproveButtons = isCheckedApprove() && !isDeclined(statusOverTimeTicket) && !isApproved(statusOverTimeTicket);

    const checkDisable =
        (isEdit && isUserCreator && !(isAwaitingQLTT(statusOverTimeTicket) || isDeclined(statusOverTimeTicket))) ||
        (!isUserCreator && isDisableStatus);
    //Form setup
    const methods = useForm<IOvertimeReport>({
        defaultValues,
        mode: 'all',
        reValidateMode: 'onChange',
        resolver: yupResolver(overtimeReportSchema),
        context: {
            currentUserId: userInfo?.idHexString || ''
        }
    });

    const { fields, remove, append } = useFieldArray({
        control: methods.control,
        name: 'overtimeRecords'
    });

    const overtimeRecords = useWatch({
        control: methods.control,
        name: 'overtimeRecords'
    });

    const watchCompensateType = methods.watch('compensateType');
    const watchovertimeReason = methods.watch('overtimeReason');

    const calculateEquivalentDays = (totalCompensateHours: number) => {
        const rawDays = (totalCompensateHours / 4) * 0.5;
        const rounded = Math.round(rawDays * 2) / 2;
        return totalCompensateHours > 0 ? Math.max(0.5, rounded) : 0;
    };

    //Reset form when defaultValues change
    useEffect(() => {
        if (isOpen) {
            methods.reset(defaultValues);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isOpen, defaultValues]);
    //Calculate total hours and compensation
    useEffect(() => {
        if (overtimeRecords) {
            let totalCompensateHours = 0;

            overtimeRecords.forEach((record) => {
                if (record?.overtimeFrom && record?.overtimeHours) {
                    const fromDate = new Date(record.overtimeFrom);
                    const toDate = record.overtimeTo ? new Date(record.overtimeTo) : fromDate;
                    const hours = parseFloat(record.overtimeHours.toString()) || 0;

                    // Tính cho từng bản ghi
                    if (fromDate.toDateString() !== toDate.toDateString()) {
                        // làm thêm qua nhiều ngày
                        const daysDiff = Math.ceil((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
                        const hoursPerDay = hours / daysDiff;

                        let currentDate = new Date(fromDate);
                        while (currentDate <= toDate) {
                            const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;
                            const isHoliday = checkIsHoliday(currentDate, holidays);
                            const coefficient = isWeekend || isHoliday ? 2.2 : 1.5;

                            totalCompensateHours += hoursPerDay * coefficient;
                            currentDate.setDate(currentDate.getDate() + 1);
                        }
                    } else {
                        // làm thêm trong 1 ngày
                        const isWeekend = fromDate.getDay() === 0 || fromDate.getDay() === 6;
                        const isHoliday = checkIsHoliday(fromDate, holidays);
                        const coefficient = isWeekend || isHoliday ? 2.2 : 1.5;

                        totalCompensateHours += hours * coefficient;
                    }
                }
            });

            const totalHours = overtimeRecords.reduce((sum, record) => {
                return sum + (parseFloat(record.overtimeHours?.toString() || '0') || 0);
            }, 0);

            const equivalentDays = calculateEquivalentDays(totalCompensateHours);

            methods.setValue('totalHours', totalHours.toString());
            methods.setValue('totalCompensateHours', totalCompensateHours.toFixed(2));
            methods.setValue('equivalentDays', equivalentDays.toFixed(1));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [overtimeRecords, holidays]);

    useEffect(() => {
        fetchHolidayList();
    }, []);

    useEffect(() => {
        if (overtimeRecords && overtimeRecords.length > 0) {
            const earliestFrom = new Date(Math.min(...overtimeRecords.map((rec) => new Date(rec.overtimeFrom ?? new Date()).getTime())));
            const latestTo = new Date(
                Math.max(...overtimeRecords.map((rec) => new Date(rec.overtimeTo ?? rec.overtimeFrom ?? new Date()).getTime()))
            );

            methods.setValue('fromDate', earliestFrom);
            methods.setValue('toDate', latestTo);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [overtimeRecords]);

    //Event handlers
    const handleSubmit: SubmitHandler<IOvertimeReport> = async (formData) => {
        setLoading(true);
        try {
            await overtimeReportSchema.validate(formData, {
                context: { currentUserId: userInfo?.idHexString }
            });
            const projectId = typeof formData.projectName === 'object' && formData.projectName !== null ? formData.projectName.value : 0;
            const apiPayload = {
                ...formData,
                idHexString: isEdit ? defaultValues.idHexString : null,
                userIdHexString: isEdit ? defaultValues.userIdHexString : userInfo?.idHexString || '',
                projectId,
                projectName: formData.projectName,
                managerIdHexString: formData.manager,
                nextManagerIdHexString: formData.nextManager,
                overTimeType: formData.overtimeReason,
                detailReason: formData.detail,
                otherReason: formData.otherReason,
                hoursCompensation: formData.totalCompensateHours || 0,
                daysCompensation: formData.equivalentDays || 0,
                compensationLeaveType: formData.compensateType,
                leaveFrom: formData.compensateFrom,
                leaveTo: formData.compensateTo,
                status: 'new',
                totalHours: formData.totalHours || 0,
                overTimeHistory:
                    formData.overtimeRecords?.map((rec) => ({
                        fromDate: rec.overtimeFrom,
                        toDate: rec.overtimeTo,
                        hours: rec.overtimeHours
                    })) || []
            };

            const apiConfig = isEdit
                ? Api.overtime_report.updateDetailOvertimeTicket(defaultValues.idHexString)
                : Api.overtime_report.postOvertimeTicket;

            const response = await sendRequest(apiConfig, apiPayload);

            dispatch(
                openSnackbar({
                    open: true,
                    message:
                        response?.status === true
                            ? response?.result.content.message || 'add-success'
                            : response?.result.content.message || 'overtime-request-error',
                    variant: 'alert',
                    alert: { color: response?.status === true ? 'success' : 'error' }
                })
            );
            if (response?.status) {
                await onSubmit(formData);
                onClose();
            }
            if (onSuccess) onSuccess();
        } catch (error) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'overtime-request-error',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
        } finally {
            setLoading(false);
        }
    };

    const handleAddOvertimeRecord = () => {
        append({
            overtimeFrom: new Date(),
            overtimeTo: new Date(),
            overtimeHours: ''
        });
    };

    const fetchHolidayList = async () => {
        try {
            const response = await sendRequest(Api.holiday.getAll, {
                page: 1,
                size: 100
            });
            if (response?.status) {
                const holidayDates: string[] = [];
                response.result.content.forEach((item: any) => {
                    const from = new Date(item.fromDate);
                    const to = item.toDate ? new Date(item.toDate) : from;
                    let current = new Date(from);
                    while (current <= to) {
                        const dateStr = `${current.getDate().toString().padStart(2, '0')}/${(current.getMonth() + 1)
                            .toString()
                            .padStart(2, '0')}/${current.getFullYear()}`;
                        if (!holidayDates.includes(dateStr)) holidayDates.push(dateStr);
                        current.setDate(current.getDate() + 1);
                    }
                });
                setHolidays(holidayDates);
            }
        } catch (error) {
            setHolidays([]);
        }
    };

    const handleOpenApproveConfirm = async () => {
        const isValid = await methods.trigger();
        if (!isValid) {
            return;
        }
        dispatch(
            openConfirm({
                title: <FormattedMessage id="confirm-information" />,
                content: <FormattedMessage id="confirm-approve-overtime" />,
                handleConfirm: async () => {
                    await handleApprove(EApproveStatus.APPROVED);
                    dispatch(closeConfirm());
                }
            })
        );
    };

    const handleApprove = async (status: string) => {
        try {
            const formValues = methods.getValues();

            const response = await sendRequest(Api.overtime_report.approvedOvertimeTicket(defaultValues.idHexString), {
                status: defaultValues.status,
                nextApproverIdHexString: defaultValues.status === EApproveStatus.AWAITING_QLTT ? formValues.nextManager : null
            });

            dispatch(
                openSnackbar({
                    open: true,
                    message:
                        response?.status === true
                            ? response?.result.content.message || MESSAGE_APPROVE_RESPONSE.SUCCESS
                            : response?.result.content.message || MESSAGE_APPROVE_RESPONSE.ERROR,
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            onClose();
            if (onSubmit) {
                await onSubmit(methods.getValues());
            }
            if (onSuccess) onSuccess();
        } catch (error) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'overtime-approve-error',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
        }
    };

    // Get reject leave data
    const getRejectOtData = () => {
        const overtimeRecords = methods.getValues('overtimeRecords');

        // Đảm bảo lấy đúng fromDate từ phần tử đầu tiên
        const fromDate = overtimeRecords && overtimeRecords.length > 0 ? overtimeRecords[0].overtimeFrom : new Date();

        // Đảm bảo lấy đúng toDate từ phần tử cuối cùng
        const toDate = overtimeRecords && overtimeRecords.length > 0 ? overtimeRecords[overtimeRecords.length - 1].overtimeTo : new Date();

        // Tính tổng số giờ từ tất cả các bản ghi
        let totalHours = 0;
        if (overtimeRecords && overtimeRecords.length > 0) {
            overtimeRecords.forEach((record) => {
                if (record?.overtimeHours) {
                    const hoursValue = String(record.overtimeHours || '0');
                    totalHours += parseFloat(hoursValue) || 0;
                }
            });
        }

        return {
            fromDate: fromDate as Date,
            toDate: toDate as Date,
            fullName: methods.getValues('fullName'),
            totalDays: totalHours.toString()
        };
    };

    // Handle open reject modal
    const handleOpenRejectModal = () => {
        if (onReject) {
            const rejectData = {
                ...getRejectOtData(),
                leaveIdHexString: defaultValues.idHexString
            };
            onReject(rejectData);
        }
        onClose();
    };

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={
                isEdit && isUserCreator
                    ? manage_ot + 'edit-overtime-request'
                    : isEdit && checkUserIsManagerApprove()
                    ? manage_ot + 'approve-overtime-request'
                    : manage_ot + 'overtime-modal-title'
            }
            footer={
                <Stack direction="row" spacing={1} justifyContent="flex-end" width="100%" pl={2}>
                    <Button onClick={onClose} color="error">
                        <FormattedMessage id={manage_ot + 'cancel'} />
                    </Button>
                    {showApproveButtons && (
                        <>
                            <Button variant="contained" sx={{ bgcolor: 'rgba(161, 0, 0, 1)' }} onClick={handleOpenRejectModal}>
                                <FormattedMessage id={manage_ot + 'declines'} />
                            </Button>
                            <LoadingButton loading={loading} variant="contained" onClick={handleOpenApproveConfirm}>
                                <FormattedMessage id={manage_ot + 'approve'} />
                            </LoadingButton>
                        </>
                    )}
                    {(!isEdit || (isUserCreator && (isAwaitingQLTT(statusOverTimeTicket) || isDeclined(statusOverTimeTicket)))) && (
                        <Button
                            type="submit"
                            form="overtime-form"
                            variant="contained"
                            color="primary"
                            disabled={loading}
                            onClick={async (e) => {
                                e.preventDefault();
                                const isValid = await methods.trigger();
                                if (!isValid) {
                                    return;
                                }
                                methods.handleSubmit(handleSubmit)();
                            }}
                        >
                            <FormattedMessage id={manage_ot + 'send-request'} />
                        </Button>
                    )}
                </Stack>
            }
        >
            <FormProvider formReturn={methods}>
                <Box display="flex" flexDirection="column">
                    <Grid container spacing={2}>
                        <Grid item md={6} xs={12}>
                            <Input name="fullName" label={<FormattedMessage id={manage_ot + 'full-name'} />} required disabled={true} />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            <Input
                                name="department"
                                label={<FormattedMessage id={manage_ot + 'department-overtime'} />}
                                required
                                disabled={true}
                            />
                        </Grid>
                        {checkManager() && (
                            <Grid item md={6} xs={12}>
                                <DirectApprover
                                    name="manager"
                                    required
                                    label={<FormattedMessage id={manage_ot + 'direct-manager-overtime'} />}
                                    onChange={(value) => methods.setValue('manager', value)}
                                    disabled={checkDisable}
                                    idHexString={!isEdit ? userInfo?.idHexString : undefined}
                                />
                            </Grid>
                        )}

                        {isEdit &&
                            (!isUserCreator ||
                                (isUserCreator && !isAwaitingQLTT(statusOverTimeTicket) && !isDeclined(statusOverTimeTicket))) && (
                                <Grid item md={6} xs={12}>
                                    <DirectApprover
                                        name="nextManager"
                                        required
                                        label={<FormattedMessage id={manage_ot + 'next-manager-overtime'} />}
                                        onChange={(value) => methods.setValue('nextManager', value)}
                                        disabled={!isAwaitingQLTT(statusOverTimeTicket)}
                                        idHexString={defaultValues.userIdHexString}
                                    />
                                </Grid>
                            )}

                        {(!isEdit ||
                            (isAwaitingQLTT(statusOverTimeTicket) && (isUserManager || isUserCreator)) ||
                            (isUserCreator && isDeclined(statusOverTimeTicket))) && <Grid item md={6} xs={12}></Grid>}

                        <Grid item md={6} xs={12}>
                            <Select
                                name="overtimeReason"
                                label={<FormattedMessage id={manage_ot + 'overtime-reason'} />}
                                required
                                selects={overtimeReasonOptions}
                                isMultipleLanguage
                                disabled={checkDisable}
                            />
                        </Grid>
                        <Grid item md={6} xs={12}>
                            {watchovertimeReason === 'overtime-reason-1' && (
                                <Project
                                    name="projectName"
                                    projectAuthorization="false"
                                    isDefaultAll={true}
                                    required
                                    handleChange={(value: IOption | null) =>
                                        methods.setValue('projectName', value, { shouldValidate: true })
                                    }
                                    disabled={checkDisable}
                                />
                            )}
                        </Grid>
                        <Grid item xs={12}>
                            <Input name="detail" label={<FormattedMessage id={manage_ot + 'overtime-detail'} />} disabled={checkDisable} />
                        </Grid>

                        <Grid item xs={12}>
                            <Input
                                name="otherReason"
                                label={<FormattedMessage id={manage_ot + 'overtime-other-reason'} />}
                                disabled={checkDisable}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            {fields.map((field, index) => (
                                <OvertimeRecordRow
                                    key={field.id}
                                    field={field}
                                    index={index}
                                    remove={remove}
                                    onAdd={handleAddOvertimeRecord}
                                    totalRecords={fields.length}
                                    disabled={checkDisable}
                                />
                            ))}
                        </Grid>

                        <Grid item md={4} xs={12}>
                            <Input
                                name="totalHours"
                                label={<FormattedMessage id={manage_ot + 'overtime-total-hours'} />}
                                required
                                disabled={true}
                            />
                        </Grid>
                        <Grid item md={4} xs={12}>
                            <Input
                                name="totalCompensateHours"
                                label={<FormattedMessage id={manage_ot + 'overtime-total-compensate-hours'} />}
                                required
                                disabled={true}
                            />
                        </Grid>
                        <Grid item md={4} xs={12}>
                            <Input
                                name="equivalentDays"
                                label={<FormattedMessage id={manage_ot + 'overtime-equivalent-days'} />}
                                required
                                disabled={true}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <Select
                                name="compensateType"
                                label={<FormattedMessage id={manage_ot + 'compensate-type'} />}
                                required
                                selects={compensateTypeOptions}
                                isMultipleLanguage
                                disabled={checkDisable}
                            />
                        </Grid>
                        {watchCompensateType === 'overtime-compensate-1' && (
                            <>
                                <Grid item md={6} xs={12}>
                                    <DatePicker
                                        name="compensateFrom"
                                        label={<FormattedMessage id={manage_ot + 'compensate-from'} />}
                                        disabled={checkDisable}
                                        required
                                    />
                                </Grid>
                                <Grid item md={6} xs={12}>
                                    <DatePicker
                                        name="compensateTo"
                                        label={<FormattedMessage id={manage_ot + 'compensate-to'} />}
                                        disabled={checkDisable}
                                        required
                                    />
                                </Grid>
                            </>
                        )}
                    </Grid>
                </Box>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditOvertimeReportModal;
