/* eslint-disable react-hooks/exhaustive-deps */
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { useCallback, useState } from 'react';

// material-ui
import { IconButton, SelectChangeEvent, Stack, TableCell, TableRow, TextField, Typography, useMediaQuery, useTheme } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';

// project import
import { WorkDayShape, WorkingCalendarFormShape } from 'pages/register-working-calendar/Config';
import { TYPE_REGISTER_WORKING_CALENDAR } from 'constants/Common';
import { checkAllowedPermission } from 'utils/authorization';
import { IWorkingCalendar } from 'types/working-calendar';
import Total from 'containers/working-calendar/Total';
import { authSelector } from 'store/slice/authSlice';
import { VerifyWorkingDay } from 'components/icons';
import { PERMISSIONS } from 'constants/Permission';
import { getBackgroundColor } from 'utils/common';
import { useAppSelector } from 'app/hooks';
import { IOption } from 'types';

interface EditRowProps {
    idx: number;
    handleCancel: (item: IWorkingCalendar) => void;
    item: IWorkingCalendar;
    form: UseFormReturn<WorkingCalendarFormShape, any>;
    dataLength: number;
    dbSelected: IOption[];
}

const EditRow = (props: EditRowProps) => {
    const {
        idx,
        handleCancel,
        item,
        form: { control, register, setValue },
        dataLength,
        dbSelected
    } = props;
    const { fields } = useFieldArray({ control, name: 'workdays' });
    const [typeSelected, setTypeSelected] = useState<string[]>([]);
    const [afternoonTypeSelected, setAfternoonTypeSelected] = useState<string[]>([]);
    const [actionsCell, setActionsCell] = useState<any>(null);
    const [memberCodeCell, setMemberCodeCell] = useState<any>(null);
    const [memberCell, setMemberCell] = useState<any>(null);
    const { registerWorkingCalendar } = PERMISSIONS.workingCalendar;
    const theme = useTheme();

    const { userInfo } = useAppSelector(authSelector);

    const actionsCellRef = useCallback(
        (domNode: any) => {
            if (dataLength > 0 && domNode) {
                setActionsCell(domNode.getBoundingClientRect());
            }
        },
        [dataLength]
    );

    const memberCodeRef = useCallback(
        (domNode: any) => {
            if (domNode) {
                setMemberCodeCell(domNode.getBoundingClientRect());
            }
        },
        [dataLength]
    );

    const memberRef = useCallback(
        (domNode: any) => {
            if (domNode) {
                setMemberCell(domNode.getBoundingClientRect());
            }
        },
        [dataLength]
    );

    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const handleChangeType = (event: SelectChangeEvent<string | unknown>, index: number, session: 'morning' | 'afternoon') => {
        const value = event.target.value as string;
        if (session === 'morning') {
            setTypeSelected((prev) => {
                const newTypeSelect = [...prev];
                newTypeSelect[index] = value;
                return newTypeSelect;
            });
            setValue(`workdays.${index}.sessionCalendar.morning`, value);
        } else {
            setAfternoonTypeSelected((prev) => {
                const newAfternoonTypeSelected = [...prev];
                newAfternoonTypeSelected[index] = value;
                return newAfternoonTypeSelected;
            });
            setValue(`workdays.${index}.sessionCalendar.afternoon`, value);
        }

        if (value === TYPE_REGISTER_WORKING_CALENDAR.X) {
            if (session === 'morning') {
                setAfternoonTypeSelected((prev) => {
                    const newTypeAfterSelect = [...prev];
                    newTypeAfterSelect[index] = TYPE_REGISTER_WORKING_CALENDAR.X;
                    return newTypeAfterSelect;
                });
                setValue(`workdays.${index}.sessionCalendar.afternoon`, TYPE_REGISTER_WORKING_CALENDAR.X);
            } else {
                setTypeSelected((prev) => {
                    const newTypeSelect = [...prev];
                    newTypeSelect[index] = TYPE_REGISTER_WORKING_CALENDAR.X;
                    return newTypeSelect;
                });
                setValue(`workdays.${index}.sessionCalendar.morning`, TYPE_REGISTER_WORKING_CALENDAR.X);
            }
        }

        if (value !== TYPE_REGISTER_WORKING_CALENDAR.X && afternoonTypeSelected[index] === TYPE_REGISTER_WORKING_CALENDAR.X) {
            if (session === 'morning') {
                setAfternoonTypeSelected((prevAfternoonTypeSelected) => {
                    const newAfternoonTypeSelected = [...prevAfternoonTypeSelected];
                    newAfternoonTypeSelected[index] = TYPE_REGISTER_WORKING_CALENDAR.WAO;
                    return newAfternoonTypeSelected;
                });
                setValue(`workdays.${index}.sessionCalendar.afternoon`, TYPE_REGISTER_WORKING_CALENDAR.WAO);
            } else {
                setTypeSelected((prevTypeSelected) => {
                    const newTypeSelected = [...prevTypeSelected];
                    newTypeSelected[index] = TYPE_REGISTER_WORKING_CALENDAR.WAO;
                    return newTypeSelected;
                });
                setValue(`workdays.${index}.sessionCalendar.morning`, TYPE_REGISTER_WORKING_CALENDAR.WAO);
            }
        }
    };

    const renderColorByType = (type: string | null) => {
        const selectedOption = dbSelected.find((option) => option.value === type);
        return selectedOption ? selectedOption.color : '';
    };

    const renderSelect = (session: 'morning' | 'afternoon', index: number) => {
        const fieldName = `workdays.${index}.sessionCalendar.${session}` as keyof WorkingCalendarFormShape;
        const selectedType =
            session === 'morning'
                ? typeSelected[index] || fields[index]?.sessionCalendar[session]
                : afternoonTypeSelected[index] || fields[index]?.sessionCalendar[session] || '';

        return (
            <select
                key={fieldName}
                {...register(fieldName, {
                    onChange: (event) => handleChangeType(event, index, session)
                })}
                style={{ backgroundColor: renderColorByType(selectedType) }}
                value={selectedType}
            >
                <option className={'backgroud-select-option'} value=""></option>
                {dbSelected.map((type: IOption) => (
                    <option key={type.value} value={type.value} className={'backgroud-select-option'}>
                        {type.value}
                    </option>
                ))}
            </select>
        );
    };

    return (
        <TableRow key={idx} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
            <TableCell
                ref={actionsCellRef}
                sx={{ position: 'sticky', left: !!matches ? 0 : 'unset', background: '#ffffff', padding: '0 !important', zIndex: 9 }}
                component="th"
                scope="row"
            >
                <Stack direction="row">
                    <IconButton type="submit" size="small">
                        <CheckIcon fontSize="small" />
                    </IconButton>
                    <IconButton type="button" size="small" onClick={() => handleCancel(item)}>
                        <CloseIcon fontSize="small" />
                    </IconButton>
                </Stack>
            </TableCell>
            <TableCell
                ref={memberCodeRef}
                sx={{ position: 'sticky', left: !!matches ? dataLength && actionsCell?.width : 'unset', background: '#ffffff', zIndex: 9 }}
                component="th"
                scope="row"
            >
                {item.memberCode}
            </TableCell>
            <TableCell
                ref={memberRef}
                sx={{
                    position: 'sticky',
                    left: !!matches ? dataLength && actionsCell?.width + memberCodeCell?.width : 'unset',
                    background: '#ffffff',
                    zIndex: 9
                }}
                component="th"
                scope="row"
            >
                {item.firstName} {item.lastName}
            </TableCell>
            <TableCell
                sx={{
                    position: 'sticky',
                    left: !!matches ? dataLength && actionsCell?.width + memberCodeCell?.width + memberCell?.width : 'unset',
                    background: '#ffffff',
                    zIndex: 9
                }}
                component="th"
                scope="row"
            >
                {item?.rank && item.rank?.title ? item.rank.title : ''}
            </TableCell>
            <TableCell
                sx={{
                    position: 'sticky',
                    background: '#ffffff'
                }}
                component="th"
                scope="row"
            >
                {item.departmentId}
            </TableCell>
            <TableCell
                sx={{
                    position: 'sticky',
                    background: '#ffffff'
                }}
                component="th"
                scope="row"
            >
                {item.status ? <FormattedMessage id={item.status} /> : ''}
            </TableCell>
            <TableCell
                sx={{
                    position: 'sticky',
                    background: '#ffffff'
                }}
                component="th"
                scope="row"
            >
                <TextField {...register('workTime.late')} sx={{ width: '50px' }} id="workTime.late" defaultValue={item.workTime.late} />
            </TableCell>
            <TableCell
                sx={{
                    position: 'sticky',
                    background: '#ffffff'
                }}
                component="th"
                scope="row"
            >
                <TextField {...register('workTime.early')} sx={{ width: '50px' }} id="workTime.early" defaultValue={item.workTime.early} />
            </TableCell>
            {fields.map((field: WorkDayShape, index: number) => (
                <TableCell key={index} align="center" sx={{ backgroundColor: getBackgroundColor(field.dayOfWeek) }}>
                    <Typography sx={{ width: '50px' }}>
                        {!field.verified || userInfo?.groups?.includes(registerWorkingCalendar.verifyWorkingCalendar) ? (
                            <>
                                {renderSelect('morning', index)}
                                {renderSelect('afternoon', index)}
                            </>
                        ) : (
                            <>
                                <Typography>{field.sessionCalendar.morning}</Typography>
                                <Typography>{field.sessionCalendar.afternoon}</Typography>
                            </>
                        )}
                    </Typography>
                </TableCell>
            ))}
            <TableCell
                component="th"
                scope="row"
                sx={{
                    background: '#ffffff',
                    right: !!matches ? 0 : 'unset',
                    position: 'sticky'
                }}
            >
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                        <Total item={item} typeList={dbSelected} />
                    </div>
                    <div>
                        {item.status !== 'approve' && (
                            <IconButton aria-label="list" size="small">
                                {checkAllowedPermission(registerWorkingCalendar.aproveWorkingCalendar) && <VerifyWorkingDay />}
                            </IconButton>
                        )}
                    </div>
                </div>
            </TableCell>
            <TableCell
                sx={{
                    position: 'sticky',
                    background: '#ffffff'
                }}
                component="th"
                scope="row"
            >
                {item.status ? <FormattedMessage id={item.status} /> : ''}
            </TableCell>
        </TableRow>
    );
};

export default EditRow;
