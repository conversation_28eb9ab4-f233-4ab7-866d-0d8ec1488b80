// material-ui
import { Button, Stack, Typography } from '@mui/material';

// project imports
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { EApproveStatus } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

interface IDeleteModalProps {
    open: boolean;
    handleClose: () => void;
    handleDelete: (id: string) => void;
    content: string;
}

const DeleteModal = (props: IDeleteModalProps) => {
    const { open, handleClose, handleDelete, content } = props;

    return (
        <Modal isOpen={open} title="delete" onClose={handleClose} maxWidth="xs">
            <Typography textAlign="center">
                <FormattedMessage id={content} />
            </Typography>
            <Stack direction="row" spacing={1} justifyContent="center" sx={{ mt: gridSpacing }}>
                <Button variant="contained" onClick={() => handleDelete(EApproveStatus.APPROVED)}>
                    <FormattedMessage id="delete" />
                </Button>
                <Button variant="contained" onClick={() => handleDelete(EApproveStatus.DECLINED)}>
                    <FormattedMessage id="declines" />
                </Button>
            </Stack>
        </Modal>
    );
};

export default DeleteModal;
