import { FormattedMessage, useIntl } from 'react-intl';
import { gridSpacing } from 'store/constant';
import { useDispatch } from 'react-redux';
import { openSnackbar } from 'store/slice/snackbarSlice';

import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { Box, Grid, IconButton, Typography } from '@mui/material';
import AddBoxIcon from '@mui/icons-material/AddBox';

import { DatePicker, Input, Label } from 'components/extended/Form';
import { LeavesType } from 'containers/search';
import { useFormContext, useWatch } from 'react-hook-form';
import { calculateLeaveDays } from './AddOrEditLeaves';
import { useEffect, useState } from 'react';
import { ELeaveType, TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ILeaveRequestDetailProps {
    index: number;
    onRemove?: () => void;
    onAdd?: () => void;
    isFirst?: boolean;
    isDisable: boolean;
}
export interface LeaveTypeValidation {
    maxDays?: number;
    exactDays?: number;
    errorMessage: string;
}

export const leaveTypeValidations: Record<ELeaveType, LeaveTypeValidation> = {
    [ELeaveType.FULL_ANNUAL]: {
        maxDays: 12,
        errorMessage: 'leave-annual-exceed-limit'
    },
    [ELeaveType.HALF_ANNUAL]: {
        exactDays: 0.5,
        errorMessage: 'leave-type-half-dayoff-limit'
    },
    [ELeaveType.FULL_SICK]: {
        maxDays: 2,
        errorMessage: 'leave-full-sick-exceed-limit'
    },
    [ELeaveType.HALF_SICK]: {
        exactDays: 0.5,
        errorMessage: 'leave-type-half-dayoff-limit'
    },
    [ELeaveType.SPECIAL]: {
        maxDays: 3,
        errorMessage: 'leave-special-exceed-limit'
    }
};
const LeaveRequestDetail = ({ index, onRemove, onAdd, isFirst = false, isDisable }: ILeaveRequestDetailProps) => {
    const { setValue, control, trigger } = useFormContext();
    const intl = useIntl();
    const dispatch = useDispatch();
    const [validationError, setValidationError] = useState<string>('');
    const { manage_leaves } = TEXT_CONFIG_SCREEN.workingCalendar;

    const leaveType = useWatch({
        control,
        name: `leaveDetails.${index}.leaveType`
    });

    const fromDate = useWatch({
        control,
        name: `leaveDetails.${index}.fromDate`
    });

    const toDate = useWatch({
        control,
        name: `leaveDetails.${index}.toDate`
    });

    const validateLeaveRequest = (leaveType: string, fromDate: string | null, toDate: string | null, days: number) => {
        if (!leaveType || !fromDate || !toDate) return null;

        const validation = leaveTypeValidations[leaveType as ELeaveType];
        if (!validation) return null;

        // For half-day leaves, check if fromDate and toDate are the same day
        if (leaveType === ELeaveType.HALF_ANNUAL || leaveType === ELeaveType.HALF_SICK) {
            if (fromDate && toDate) {
                const sameDay =
                    new Date(fromDate).getDate() === new Date(toDate).getDate() &&
                    new Date(fromDate).getMonth() === new Date(toDate).getMonth() &&
                    new Date(fromDate).getFullYear() === new Date(toDate).getFullYear();

                if (!sameDay) {
                    return validation.errorMessage;
                }
            }
        }

        if (validation.exactDays !== undefined && days !== validation.exactDays) {
            return validation.errorMessage;
        }

        if (validation.maxDays !== undefined && days > validation.maxDays) {
            return validation.errorMessage;
        }

        return null;
    };
    const disabledInputStyle = {
        '& .MuiOutlinedInput-root': {
            backgroundColor: '#eeeeee',
            borderRadius: '4px',
            boxShadow: 'none',
            '&::before, &::after': {
                display: 'none',
                border: 'none'
            },
            '& fieldset': {
                border: 'none !important'
            },
            '& .MuiOutlinedInput-notchedOutline': {
                borderWidth: '0 !important',
                borderColor: 'transparent !important',
                border: 'none !important',
                boxShadow: 'none !important',
                outline: 'none !important'
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
                borderWidth: '0 !important',
                borderColor: 'transparent !important',
                border: 'none !important',
                boxShadow: 'none !important',
                outline: 'none !important'
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderWidth: '0 !important',
                borderColor: 'transparent !important',
                border: 'none !important',
                boxShadow: 'none !important',
                outline: 'none !important'
            },
            '&.Mui-disabled': {
                backgroundColor: '#eeeeee',
                '& .MuiOutlinedInput-notchedOutline': {
                    borderWidth: '0 !important',
                    border: 'none !important'
                }
            }
        },
        // Apply to all MUI TextField variants
        '& .MuiTextField-root': {
            '& .MuiOutlinedInput-notchedOutline': {
                border: 'none !important'
            }
        },
        '& .MuiInputBase-input.Mui-disabled': {
            color: 'rgba(0, 0, 0, 0.87)',
            WebkitTextFillColor: 'rgba(0, 0, 0, 0.87)'
        }
    };

    useEffect(() => {
        if (fromDate && toDate && leaveType) {
            const result = calculateLeaveDays(new Date(fromDate), new Date(toDate), leaveType);
            setValue(`leaveDetails.${index}.numberOfDays`, result.days);
            if (result.error) {
                setValidationError(intl.formatMessage({ id: result.error }));
            } else {
                const customError = validateLeaveRequest(leaveType, fromDate, toDate, result.days);
                if (customError) {
                    setValidationError(intl.formatMessage({ id: 'manage-leaves-invalid-selection' }));
                    dispatch(
                        openSnackbar({
                            open: true,
                            message: customError,
                            variant: 'alert',
                            alert: { color: 'error' },
                            isMultipleLanguage: true
                        })
                    );
                } else {
                    setValidationError('');
                }
            }
            setValue(`leaveDetails.${index}.hasError`, !!result.error || !!validateLeaveRequest(leaveType, fromDate, toDate, result.days));
            trigger(`leaveDetails.${index}`);
        } else if (leaveType === '') {
            setValue(`leaveDetails.${index}.numberOfDays`, 0);
            setValidationError('');
            setValue(`leaveDetails.${index}.hasError`, false);
        }
    }, [leaveType, fromDate, toDate, index, setValue, trigger]);

    return (
        <Grid container spacing={gridSpacing}>
            <Grid item xs={12} md={8} sx={{ mt: 0 }}>
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12}>
                        <Label name="leaves-type" label={<FormattedMessage id="leaves-type" />} required />
                        <Grid container spacing={2} alignItems="center">
                            <Grid item sx={{ width: 40 }}>
                                {isFirst ? (
                                    <IconButton disabled={isDisable} color="primary" onClick={onAdd} sx={{ p: 0.5 }}>
                                        <AddBoxIcon />
                                    </IconButton>
                                ) : (
                                    <IconButton disabled={isDisable} color="error" onClick={onRemove} sx={{ p: 0.5 }}>
                                        <RemoveCircleOutlineIcon />
                                    </IconButton>
                                )}
                            </Grid>
                            <Grid item xs>
                                <Box
                                    sx={{
                                        '& .MuiFormLabel-root': { display: 'none' },
                                        '& .MuiFormHelperText-root': {
                                            position: 'absolute',
                                            marginTop: '0px',
                                            bottom: '-18px',
                                            left: '0',
                                            fontSize: '0.7rem'
                                        }
                                    }}
                                >
                                    <LeavesType
                                        isDisable={isDisable}
                                        name={`leaveDetails.${index}.leaveType`}
                                        isShowAll={false}
                                        required
                                        sx={isDisable ? disabledInputStyle : undefined}
                                    />
                                    {validationError && (
                                        <Typography color="error" variant="caption" sx={{ display: 'block', mt: 0.5, mb: -1 }}>
                                            {validationError}
                                        </Typography>
                                    )}
                                    <Input name={`leaveDetails.${index}.hasError`} type="hidden" sx={{ display: 'none' }} />
                                </Box>
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12} sx={{ mt: '-6px', mb: '20px' }}>
                        <Grid container spacing={2}>
                            <Grid item sx={{ width: 40 }}>
                                {/* Khoảng trống có chiều rộng bằng với icon */}
                            </Grid>
                            <Grid item xs>
                                <Grid
                                    container
                                    spacing={2}
                                    sx={{
                                        '& .MuiFormHelperText-root': {
                                            marginTop: '0px',
                                            bottom: '-18px',
                                            left: '0',
                                            fontSize: '0.7rem'
                                        }
                                    }}
                                >
                                    <Grid item xs={6}>
                                        <DatePicker
                                            name={`leaveDetails.${index}.fromDate`}
                                            label={<FormattedMessage id={manage_leaves + 'from-date'} />}
                                            disabled={isDisable}
                                            required
                                            sx={isDisable ? disabledInputStyle : undefined}
                                        />
                                    </Grid>
                                    <Grid item xs={6}>
                                        <DatePicker
                                            name={`leaveDetails.${index}.toDate`}
                                            label={<FormattedMessage id={manage_leaves + 'to-date'} />}
                                            disabled={isDisable}
                                            required
                                            sx={isDisable ? disabledInputStyle : undefined}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>

            <Grid item xs={12} md={4} sx={{ mt: 0 }}>
                <Grid
                    container
                    spacing={2}
                    sx={{
                        '& .MuiFormHelperText-root': {
                            position: 'absolute',
                            marginTop: '0px',
                            bottom: '-18px',
                            left: '0',
                            fontSize: '0.7rem'
                        }
                    }}
                >
                    <Grid item xs={12} pb={validationError && 1}>
                        <Input
                            label={<FormattedMessage id={manage_leaves + 'number-of-days'} />}
                            name={`leaveDetails.${index}.numberOfDays`}
                            disabled
                            required
                            sx={{
                                ...disabledInputStyle,
                                '& .MuiInputBase-root': {
                                    mt: '3px'
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} sx={{ pt: '13px !important' }}>
                        <Input
                            disabled={isDisable}
                            label={<FormattedMessage id="note" />}
                            name={`leaveDetails.${index}.note`}
                            sx={isDisable ? disabledInputStyle : undefined}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default LeaveRequestDetail;
