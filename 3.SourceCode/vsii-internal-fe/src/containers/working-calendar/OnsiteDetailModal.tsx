import { useEffect, useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import copy from 'copy-to-clipboard';
import moment from 'moment';
import 'moment/locale/en-gb';
import 'moment/locale/vi';

import { openSnackbar } from 'store/slice/snackbarSlice';
import { IWorkingOnsite } from 'types/working-calendar';
import OnsiteDetailTbody from './OnsiteDetailTbody';
import OnsiteDetailThead from './OnsiteDetailThead';
import { Table } from 'components/extended/Table';
import Modal from 'components/extended/Modal';
import sendRequest from 'services/ApiService';
import { useAppDispatch } from 'app/hooks';
import useConfig from 'hooks/useConfig';
import { Response } from 'types';
import Api from 'constants/Api';

interface IProps {
    isOpen: boolean;
    handleClose: () => void;
    year: number;
    month: number;
}

const OnsiteDetailModal = (props: IProps) => {
    const { isOpen, month, year, handleClose } = props;

    const [data, setData] = useState<IWorkingOnsite['content']>([]);
    const [loading, setLoading] = useState<boolean>(false);

    const { locale } = useConfig();

    const intl = useIntl();

    const dispatch = useAppDispatch();

    const handleCopy = () => {
        const header = [
            [
                '',
                '',
                '',
                '',
                '',
                moment()
                    .locale(locale === 'vi' ? 'vi' : 'en-gb')
                    .set({ year, month: month - 1 })
                    .format('MMM'),
                '',
                '',
                ''
            ],
            [
                intl.formatMessage({ id: 'onsite-detail.modal.body.table.no' }),
                intl.formatMessage({ id: 'onsite-detail.modal.body.table.employee-id' }),
                intl.formatMessage({ id: 'onsite-detail.modal.body.table.employee-name' }),
                intl.formatMessage({ id: 'onsite-detail.modal.body.table.first-name' }),
                intl.formatMessage({ id: 'onsite-detail.modal.body.table.onsite-days' }),
                moment()
                    .locale(locale === 'vi' ? 'vi' : 'en-gb')
                    .set({ year, month: month - 1 })
                    .subtract(1, 'months')
                    .format('MMM'),
                moment()
                    .locale(locale === 'vi' ? 'vi' : 'en-gb')
                    .set({ year, month: month - 1 })
                    .format('MMM'),
                intl.formatMessage({ id: 'onsite-detail.modal.body.table.team' }),
                intl.formatMessage({ id: 'onsite-detail.modal.body.table.project-name' })
            ]
        ];

        const dataConverted = data.map((value, index) => [
            index + 1,
            value.userId,
            `${value.firstName} ${value.lastName}`,
            value.lastName,
            value.onsiteDays,
            value.onsiteLastMonth,
            value.onsiteThisMonth,
            value.team,
            value.projectList?.join(', ')
        ]);

        const text = [...header, ...dataConverted].map((row) => row?.join('	'))?.join('\n');

        copy(text, {
            format: 'text/plain'
        });

        dispatch(
            openSnackbar({
                open: true,
                message: 'onsite-detail.modal.copied',
                variant: 'alert',
                transition: 'SlideDown',
                close: false,
                duration: 1000,
                alert: {
                    color: 'success'
                },
                anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'center'
                }
            })
        );
    };

    useEffect(() => {
        (async () => {
            setLoading(true);
            const response: Response<IWorkingOnsite> = await sendRequest(Api.working_calendar.findOnsite, {
                year,
                month,
                type: 'OS'
            });

            const { content } = response?.result;
            if (content && response.status) {
                setData(content);
            }
            setLoading(false);
        })();
    }, [year, month]);

    return (
        <Modal
            isOpen={isOpen}
            title="onsite-detail.modal.title"
            onClose={handleClose}
            keepMounted={false}
            maxWidth="md"
            footer={
                <Box display="flex" flexDirection="row" gap={4}>
                    <Button variant="text" color="error" onClick={handleClose}>
                        <FormattedMessage id="onsite-detail.modal.footer.cancel" />
                    </Button>
                    <Button variant="contained" onClick={handleCopy}>
                        <FormattedMessage id="onsite-detail.modal.footer.copy" />
                    </Button>
                </Box>
            }
            footerProps={{
                sx: { justifyContent: 'center' }
            }}
        >
            <Box sx={{ minHeight: '30vh' }}>
                <Typography sx={{ fontWeight: '500', color: '#3162D2' }}>
                    <FormattedMessage id="onsite-detail.modal.body.total-member" /> {data.length}
                </Typography>
                <Table
                    heads={<OnsiteDetailThead month={month - 1} year={year} />}
                    isLoading={loading}
                    data={data}
                    heightTableEmpty="300px"
                    borderedEmpty={false}
                >
                    <OnsiteDetailTbody data={data} />
                </Table>
            </Box>
        </Modal>
    );
};

export default OnsiteDetailModal;
