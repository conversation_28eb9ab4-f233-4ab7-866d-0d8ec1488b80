/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useState } from 'react';
import { FormattedMessage } from 'react-intl';

// material-ui
import { IconButton, Stack, TableCell, TableRow, Typography, useMediaQuery, useTheme } from '@mui/material';
import { IWorkdays, IWorkingCalendar } from 'types/working-calendar';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import { checkAllowedPermission } from 'utils/authorization';
import { VerifyWorkingDay, Comment } from 'components/icons';
import { PERMISSIONS } from 'constants/Permission';
import { getBackgroundColor } from 'utils/common';
import { LoadingButton } from '@mui/lab';
import { IOption } from 'types';
import Total from './Total';

interface ReadOnlyProps {
    idx: number;
    toggle: string;
    item: IWorkingCalendar;
    handleEdit: (item: IWorkingCalendar) => void;
    loading: boolean;
    dataLength: number;
    handleVerify: (item: IWorkingCalendar) => void;
    handleComment: (item: IWorkingCalendar) => void;
    isCheckEdit: boolean;
    handleFilterMember: (memberCode: string) => void;
    dbSelected: IOption[];
}

const ReadOnly = (props: ReadOnlyProps) => {
    const { idx, item, handleEdit, loading, toggle, dataLength, handleVerify, handleComment, isCheckEdit, handleFilterMember, dbSelected } =
        props;
    const [actionsCell, setActionsCell] = useState<any>(null);
    const [memberCodeCell, setMemberCodeCell] = useState<any>(null);
    const [memberCell, setMemberCell] = useState<any>(null);
    const theme = useTheme();
    const { registerWorkingCalendar } = PERMISSIONS.workingCalendar;

    const actionsCellRef = useCallback(
        (domNode: any) => {
            if (dataLength > 0 && domNode) {
                setActionsCell(domNode.getBoundingClientRect());
            }
        },
        [dataLength]
    );

    const memberCodeRef = useCallback(
        (domNode: any) => {
            if (domNode) {
                setMemberCodeCell(domNode.getBoundingClientRect());
            }
        },
        [dataLength]
    );

    const memberRef = useCallback(
        (domNode: any) => {
            if (domNode) {
                setMemberCell(domNode.getBoundingClientRect());
            }
        },
        [dataLength]
    );

    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const getTextColor = (type: string) => {
        const option = dbSelected.find((opt) => opt.value === type);
        return option ? option.color : '#000000';
    };

    const getStatusBackgroundColor = (status: string) => {
        return status === 'approve' ? '#ffffff' : '#fdd1b7';
    };

    return (
        <TableRow
            key={idx}
            sx={{
                '&:last-child td, &:last-child th': { border: 0 },
                td: { textAlign: 'left' }
            }}
        >
            <TableCell
                ref={actionsCellRef}
                sx={{
                    position: 'sticky',
                    left: !!matches ? 0 : 'unset',
                    backgroundColor: getStatusBackgroundColor(item.status),
                    zIndex: 1
                }}
                component="th"
                scope="row"
            >
                <Stack direction="row">
                    {loading && toggle === item.idHexString ? (
                        <LoadingButton loading={loading} size="small">
                            <span>disabled</span>
                        </LoadingButton>
                    ) : (
                        isCheckEdit && (
                            <IconButton type="button" size="small" onClick={() => handleEdit(item)}>
                                {checkAllowedPermission(registerWorkingCalendar.editWorkingCalendar) && (
                                    <EditOutlinedIcon fontSize="small" />
                                )}
                            </IconButton>
                        )
                    )}
                </Stack>
            </TableCell>
            {/* <div onClick={() => handleFilterMember(item.memberCode)}> */}
            <TableCell
                ref={memberCodeRef}
                sx={{
                    position: 'sticky',
                    left: !!matches ? dataLength && actionsCell?.width : 'unset',
                    backgroundColor: getStatusBackgroundColor(item.status),
                    zIndex: 1
                }}
                component="th"
                scope="row"
            >
                {item.memberCode}
            </TableCell>
            <TableCell
                ref={memberRef}
                sx={{
                    position: 'sticky',
                    left: !!matches ? dataLength && actionsCell?.width + memberCodeCell?.width : 'unset',
                    backgroundColor: getStatusBackgroundColor(item.status),
                    zIndex: 1
                }}
                component="th"
                scope="row"
                onClick={() => handleFilterMember(item.memberCode)}
            >
                {item.firstName} {item.lastName}
            </TableCell>
            <TableCell
                component="th"
                scope="row"
                sx={{
                    position: 'sticky',
                    left: !!matches ? dataLength && actionsCell?.width + memberCodeCell?.width + memberCell?.width : 'unset',
                    backgroundColor: getStatusBackgroundColor(item.status),
                    zIndex: 1
                }}
            >
                {item.rank && item.rank.title ? item.rank.title : ''}
            </TableCell>
            <TableCell component="th" scope="row" sx={{ backgroundColor: getStatusBackgroundColor(item.status) }}>
                {item.departmentId}
            </TableCell>
            <TableCell sx={{ backgroundColor: getStatusBackgroundColor(item.status) }} component="th" scope="row">
                <FormattedMessage id={item.status} />
            </TableCell>
            <TableCell component="th" scope="row" sx={{ backgroundColor: getStatusBackgroundColor(item.status) }}>
                {item.workTime.late}
            </TableCell>
            <TableCell component="th" scope="row" sx={{ backgroundColor: getStatusBackgroundColor(item.status) }}>
                {item.workTime.early}
            </TableCell>
            {/* </div> */}

            {item.workdays.map((workday: IWorkdays, index: number) => (
                <TableCell
                    key={index}
                    align="center"
                    sx={{
                        backgroundColor:
                            item.status !== 'approve' ? getStatusBackgroundColor(item.status) : getBackgroundColor(workday.dayOfWeek)
                    }}
                >
                    <Typography
                        sx={{ width: '50px', fontWeight: 500, color: getTextColor(workday.sessionCalendar.morning), textAlign: 'center' }}
                    >
                        {workday.sessionCalendar.morning}
                    </Typography>
                    <Typography
                        sx={{ width: '50px', fontWeight: 500, color: getTextColor(workday.sessionCalendar.afternoon), textAlign: 'center' }}
                    >
                        {workday.sessionCalendar.afternoon}
                    </Typography>
                </TableCell>
            ))}
            <TableCell
                component="th"
                scope="row"
                sx={{
                    backgroundColor: getStatusBackgroundColor(item.status),
                    right: !!matches ? item && actionsCell?.width : 'unset',
                    position: 'sticky'
                }}
            >
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                        <Total item={item} typeList={dbSelected} />
                    </div>
                    <div>
                        {item.status !== 'approve' && (
                            <IconButton aria-label="list" size="small" onClick={() => handleVerify(item)}>
                                {checkAllowedPermission(registerWorkingCalendar.aproveWorkingCalendar) && <VerifyWorkingDay />}
                            </IconButton>
                        )}
                    </div>
                </div>
            </TableCell>
            <TableCell
                component="th"
                ref={actionsCellRef}
                sx={{
                    position: 'sticky',
                    backgroundColor: getStatusBackgroundColor(item.status),
                    right: !!matches ? 0 : 'unsset'
                }}
                scope="row"
            >
                <IconButton
                    sx={{ border: '2px solid #000000', borderRadius: '6px' }}
                    aria-label="list"
                    size="small"
                    onClick={() => handleComment(item)}
                >
                    {checkAllowedPermission(registerWorkingCalendar.aproveWorkingCalendar) && <Comment />}
                </IconButton>
            </TableCell>
        </TableRow>
    );
};

export default ReadOnly;
