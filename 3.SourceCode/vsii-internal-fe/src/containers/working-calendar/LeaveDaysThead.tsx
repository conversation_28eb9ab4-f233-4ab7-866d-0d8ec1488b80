import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const LeaveDaysThead = () => {
    const { manageLeaveDay } = PERMISSIONS.workingCalendar;

    const { manage_leave_days } = TEXT_CONFIG_SCREEN.workingCalendar;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'title'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'dept'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'total-leave-days'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'number-of-leave-days-used'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leave_days + 'remaining-leave-days'} />
                </TableCell>
                {checkAllowedPermission(manageLeaveDay.editManageLeaveDay) && (
                    <TableCell align="center">
                        <FormattedMessage id={manage_leave_days + 'actions'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default LeaveDaysThead;
