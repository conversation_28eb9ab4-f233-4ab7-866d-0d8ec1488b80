// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// third party
import { FormattedMessage } from 'react-intl';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ManageLeavesTHead = () => {
    const { manageLeaves } = PERMISSIONS.workingCalendar;

    const { manage_leaves } = TEXT_CONFIG_SCREEN.workingCalendar;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'approver'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'dept'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'leaves-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'to-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'status'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_leaves + 'approved-date'} />
                </TableCell>
                {checkAllowedPermission(manageLeaves.approve) || checkAllowedPermission(manageLeaves.edit) ? (
                    <TableCell align="center">
                        <FormattedMessage id={manage_leaves + 'actions'} />
                    </TableCell>
                ) : (
                    <></>
                )}
            </TableRow>
        </TableHead>
    );
};

export default ManageLeavesTHead;
