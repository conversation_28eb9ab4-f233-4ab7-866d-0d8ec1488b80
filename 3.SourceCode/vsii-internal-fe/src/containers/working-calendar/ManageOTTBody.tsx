/* eslint-disable prettier/prettier */

// material-ui
import { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project imports
import { IOTItem } from 'types/working-calendar';
import { dateFormat } from 'utils/date';
import { EApproveStatus, GROUP_ID_APPROVER } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// assets
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import { useAppSelector } from 'app/hooks';
import { authSelector } from 'store/slice/authSlice';

interface IManageLeavesTBodyProps {
    pageNumber: number;
    pageSize: number;
    otList: IOTItem[];
    handleEdit: (item?: IOTItem) => void;
    handleDelete: (id: string) => void;
}

const ManageOTTBody = (props: IManageLeavesTBodyProps) => {
    const { pageNumber, pageSize, otList, handleEdit, handleDelete } = props;
    const { manageOt } = PERMISSIONS.workingCalendar;
    const { userInfo } = useAppSelector(authSelector);

    const userGroup = userInfo?.role?.map((item) => item.groupId);

    return (
        <TableBody>
            {otList.map((item, key) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{item.memberName}</TableCell>
                    <TableCell>{item.approveName}</TableCell>
                    <TableCell>{item.dept}</TableCell>
                    <TableCell>
                        {!item.overTimeType ? (
                            '-'
                        ) : (
                            <Tooltip
                                title={
                                    item.overTimeType.includes(',') ? (
                                        <Stack direction="column" spacing={0.5}>
                                            {item.overTimeType.split(',').map((type, index) => (
                                                <Typography key={index} variant="body2">
                                                    <FormattedMessage id={type.trim()} />
                                                </Typography>
                                            ))}
                                        </Stack>
                                    ) : null
                                }
                                placement="top"
                                arrow
                            >
                                <Typography>
                                    {item.overTimeType.includes(',') ? (
                                        <>
                                            <FormattedMessage id={item.overTimeType.split(',')[0].trim()} />
                                            <Typography component="span" color="textSecondary">
                                                +{item.overTimeType.split(',').length - 1}
                                            </Typography>
                                        </>
                                    ) : (
                                        <FormattedMessage id={item.overTimeType} />
                                    )}
                                </Typography>
                            </Tooltip>
                        )}
                    </TableCell>
                    <TableCell>{dateFormat(item.fromDate)}</TableCell>
                    <TableCell>{dateFormat(item.toDate)}</TableCell>
                    <TableCell>
                        {!item.status ? (
                            '-'
                        ) : (
                            <Typography
                                color={
                                    item.status === EApproveStatus.APPROVED
                                        ? '#3163D4'
                                        : item.status === EApproveStatus.DECLINED
                                        ? '#A10000'
                                        : item.status === EApproveStatus.AWAITING_QLTT ||
                                          item.status === EApproveStatus.AWAITING_QLKT ||
                                          item.status === EApproveStatus.AWAITING_HR
                                        ? '#616161'
                                        : 'textPrimary'
                                }
                            >
                                <FormattedMessage id={item.status} />
                            </Typography>
                        )}
                    </TableCell>
                    <TableCell>{item.approvedDate && dateFormat(item.approvedDate)}</TableCell>
                    <TableCell sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        <Stack direction="row" justifyContent="center" alignItems="center">
                            <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleEdit(item)}>
                                <IconButton aria-label="edit" size="small">
                                    <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                </IconButton>
                            </Tooltip>
                            {checkAllowedPermission(manageOt.approve) && userGroup && userGroup.includes(GROUP_ID_APPROVER.HR) && (
                                <>
                                    <Tooltip placement="top" title={<FormattedMessage id="download" />} onClick={() => handleEdit(item)}>
                                        <IconButton aria-label="download" size="small">
                                            <DownloadOutlinedIcon sx={{ fontSize: '1.1rem' }} />
                                        </IconButton>
                                    </Tooltip>
                                    {checkAllowedPermission(manageOt.delete) && (
                                        <Tooltip
                                            placement="top"
                                            title={<FormattedMessage id="delete" />}
                                            onClick={() => handleDelete(item.id)}
                                        >
                                            <IconButton aria-label="delete" size="small">
                                                <DeleteOutlineOutlinedIcon sx={{ fontSize: '1.1rem' }} />
                                            </IconButton>
                                        </Tooltip>
                                    )}
                                </>
                            )}
                        </Stack>
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ManageOTTBody;
