// react
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// third party
import { FormattedMessage } from 'react-intl';
import { UseFormReturn, useFieldArray, useWatch } from 'react-hook-form';

// material-ui
import { Box, Button, DialogActions, Grid } from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project imports
import LeaveRequestDetail from 'containers/working-calendar/LeaveRequestDetail';
import { addOrEditLeavesSchema, IAddOrEditLeaves, ILeaveRequestData } from 'pages/manage-leaves/Config';
import { DatePicker, FormProvider, Input } from 'components/extended/Form';
import { checkAllowedPermission } from 'utils/authorization';
import DirectApprover from 'containers/search/DirectApprover';
import RemainLeaveDay from 'containers/search/RemainLeaveDay';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { PERMISSIONS } from 'constants/Permission';
import { EApproveStatus, GROUP_ID_APPROVER } from 'constants/Common';
import Modal from 'components/extended/Modal';
import sendRequest from 'services/ApiService';
import { gridSpacing } from 'store/constant';
import { dateFormat } from 'utils/date';
import Api from 'constants/Api';
import { authSelector } from 'store/slice/authSlice';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditLeavesProps {
    open: boolean;
    handleClose: () => void;
    loading?: boolean;
    isEdit?: boolean;
    formReturn: UseFormReturn<IAddOrEditLeaves>;
    handleApprove: () => void;
    onSuccess?: () => void;
    onReject?: (data: ILeaveRequestData) => void;
    leaveInfo?: IAddOrEditLeaves;
}
/**
 * Calculate number of days between dates considering leave type
 * @param fromDate Start date
 * @param toDate End date
 * @param leaveType Type of leave
 * @returns Object containing days and potential error message
 */
export function calculateLeaveDays(
    fromDate: Date,
    toDate: Date,
    leaveType?: string
): {
    days: number;
    error?: string;
} {
    if (!fromDate || !toDate) return { days: 0 };

    const isHalfDayLeave =
        leaveType && (leaveType.includes('half-') || leaveType === 'half-annual-leave' || leaveType === 'half-sick-leave');

    if (isHalfDayLeave) {
        return { days: 0.5 };
    }

    const startDate = new Date(fromDate);
    const endDate = new Date(toDate);
    let days = 0;

    while (startDate <= endDate) {
        if (startDate.getDay() !== 0 && startDate.getDay() !== 6) {
            days++;
        }
        startDate.setDate(startDate.getDate() + 1);
    }

    return { days };
}
const defaultLeaveDetail = {
    leaveType: '',
    numberOfDays: 0,
    fromDate: null,
    toDate: null,
    note: '',
    hasError: false
};
const AddOrEditLeaves = (props: IAddOrEditLeavesProps) => {
    // Props
    const { open, handleClose, loading, isEdit, formReturn, onSuccess, handleApprove, onReject, leaveInfo } = props;
    const { manageLeaves } = PERMISSIONS.workingCalendar;
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { fields, append, remove } = useFieldArray({
        control: formReturn.control,
        name: 'leaveDetails'
    });

    const dispatch = useDispatch();
    const { userInfo } = useSelector(authSelector);
    const { setValue } = formReturn;

    const leaveDetails = useWatch({
        control: formReturn.control,
        name: 'leaveDetails'
    });
    const updatingRef = useRef(false);

    // Tính toán trước các giá trị boolean cơ bản
    const isCreator = userInfo?.idHexString === leaveInfo?.member?.value;
    const isDirectApprover = userInfo?.idHexString === leaveInfo?.directApproverIdHexString;
    const isAwaitingQLTT = leaveInfo?.status === EApproveStatus.AWAITING_QLTT;
    const isAwaitingQLKT = leaveInfo?.status === EApproveStatus.AWAITING_QLKT;
    const canEdit = isCreator && isAwaitingQLTT;

    // Giữ nguyên các hàm phức tạp hơn
    const isCheckedEdit = () => {
        return isCreator && isAwaitingQLTT;
    };

    const isCheckedApprove = () => {
        const userGroup = userInfo?.role?.map((item) => item.groupId);

        if (isAwaitingQLTT && isDirectApprover) return true;
        else if (isAwaitingQLKT && userInfo?.idHexString === leaveInfo?.nextApproverIdHexString) return true;
        else if (leaveInfo?.status === EApproveStatus.AWAITING_HR && userGroup?.includes(GROUP_ID_APPROVER.HR)) return true;
        return false;
    };

    const shouldShowDirectManager = () => {
        // hide QLTT when: editing + is approver + not creator + waiting QLTT
        return !(isEdit && isDirectApprover && !isCreator && isAwaitingQLTT);
    };

    const shouldShowIndirectManager = () => {
        if (!isEdit) return false;
        // hide QLKT when waiting QLTT and not QLTT (is creator or not related)
        if (isAwaitingQLTT && !isDirectApprover) return false;

        return true;
    };

    const isIndirectManagerDisabled = () => {
        // allow edit QLKT when: waiting QLTT + is QLTT
        return !(isAwaitingQLTT && isDirectApprover);
    };

    // get column size
    const getColumnLayout = () => {
        if (!isEdit) return 4;
        return !isAwaitingQLTT || (isCreator && isDirectApprover) ? 3 : 4;
    };
    const columnSize = getColumnLayout();

    const updateTotalLeaveDays = () => {
        if (updatingRef.current || !leaveDetails) return;

        let totalDays = 0;
        let needsUpdate = false;

        leaveDetails.forEach((detail, index) => {
            if (detail?.fromDate && detail?.toDate) {
                const result = calculateLeaveDays(detail.fromDate, detail.toDate, detail.leaveType);

                if (result.error) {
                    dispatch(
                        openSnackbar({
                            open: true,
                            message: result.error,
                            variant: 'alert',
                            alert: { color: 'error' },
                            isMultipleLanguage: true
                        })
                    );
                    setValue(`leaveDetails.${index}.fromDate`, null);
                    setValue(`leaveDetails.${index}.toDate`, null);
                    needsUpdate = true;
                } else if (detail.numberOfDays !== result.days) {
                    setValue(`leaveDetails.${index}.numberOfDays`, result.days);
                    needsUpdate = true;
                    totalDays += result.days;
                } else {
                    totalDays += detail.numberOfDays || 0;
                }
            } else {
                totalDays += detail?.numberOfDays || 0;
            }
        });

        if (needsUpdate || formReturn.getValues('totalLeaveDays') !== totalDays) {
            updatingRef.current = true;
            setValue('totalLeaveDays', totalDays);
            setTimeout(() => {
                updatingRef.current = false;
            }, 0);
        }
    };

    const updateDateRange = () => {
        if (leaveDetails && leaveDetails.length > 0) {
            const sortedLeaveDetails = [...leaveDetails].sort((a, b) => {
                if (!a.fromDate || !b.fromDate) return 0;
                return new Date(a.fromDate).getTime() - new Date(b.fromDate).getTime();
            });
            const firstDate = sortedLeaveDetails[0]?.fromDate;
            const lastDate = sortedLeaveDetails[sortedLeaveDetails.length - 1]?.toDate;

            if (firstDate) {
                setValue('fromDate', firstDate);
            }
            if (lastDate) {
                setValue('toDate', lastDate);
            }
        }
    };

    useEffect(() => {
        updateTotalLeaveDays();
        updateDateRange();
    }, [leaveDetails, setValue]);

    useEffect(() => {
        const currentLeaveDetails = formReturn.getValues('leaveDetails');
        if (!currentLeaveDetails || !currentLeaveDetails.length) {
            formReturn.setValue('leaveDetails', [defaultLeaveDetail], { shouldValidate: true });
        }
    }, []);

    const { manage_leaves } = TEXT_CONFIG_SCREEN.workingCalendar;

    // Submit
    const handleSubmit = async (values: IAddOrEditLeaves) => {
        const hasValidationErrors = values.leaveDetails?.some((detail) => detail.hasError);

        if (hasValidationErrors) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'form-has-errors',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
            return;
        }

        setIsSubmitting(true);
        await addOrEditLeavesSchema.validate(values, {
            context: { currentUserId: userInfo?.idHexString }
        });
        const { member, leaveDetails, ...clonePayload } = values;

        const formatPayload = {
            ...clonePayload,
            memberIdHexString: member?.value,
            fromDate: dateFormat(values.fromDate),
            toDate: dateFormat(values.toDate),
            createdDate: dateFormat(values.requestDay, 'DD/MM/YYYY'),
            status: isEdit ? undefined : EApproveStatus.AWAITING_QLTT,
            leaveTypes:
                leaveDetails?.map((detail) => ({
                    leaveType: detail.leaveType,
                    fromDate: dateFormat(detail.fromDate),
                    toDate: dateFormat(detail.toDate),
                    numberOfDaysOff: detail.numberOfDays,
                    note: detail.note || ''
                })) || []
        };

        const api = isEdit ? Api.manage_leaves.putUpdate(values.id!) : Api.manage_leaves.postCreate;

        const response = await sendRequest(api, formatPayload);

        if (response?.status) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: isEdit ? 'update-success' : 'add-success',
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            handleClose();
            if (onSuccess) onSuccess(); // Call the callback to refresh data
        } else {
            dispatch(
                openSnackbar({
                    open: true,
                    isMultipleLanguage: true,
                    message: response?.message || response?.result.content.message || 'leave-request-error',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
        }
        setIsSubmitting(false);
    };

    const handleAddDetail = useMemo(() => {
        return () => append(defaultLeaveDetail);
    }, [append]);
    const dialogActionStyles = useMemo(
        () => ({
            display: 'flex',
            justifyContent: 'flex-end',
            borderTop: '1px solid rgba(0, 0, 0, 0.12)',
            padding: '16px 24px',
            mt: 3,
            mx: -3,
            width: 'calc(100% + 48px)',
            mb: -3,
            pb: 0
        }),
        []
    );
    const disabledInputStyle = {
        '& .MuiOutlinedInput-root': {
            backgroundColor: '#eeeeee',
            borderRadius: '4px',
            boxShadow: 'none',
            '&::before, &::after': {
                display: 'none',
                border: 'none'
            },
            '& fieldset': {
                border: 'none !important'
            },
            '& .MuiOutlinedInput-notchedOutline': {
                borderWidth: '0 !important',
                borderColor: 'transparent !important',
                border: 'none !important',
                boxShadow: 'none !important',
                outline: 'none !important'
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
                borderWidth: '0 !important',
                borderColor: 'transparent !important',
                border: 'none !important',
                boxShadow: 'none !important',
                outline: 'none !important'
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderWidth: '0 !important',
                borderColor: 'transparent !important',
                border: 'none !important',
                boxShadow: 'none !important',
                outline: 'none !important'
            },
            '&.Mui-disabled': {
                backgroundColor: '#eeeeee',
                '& .MuiOutlinedInput-notchedOutline': {
                    borderWidth: '0 !important',
                    border: 'none !important'
                }
            }
        },
        // Apply to all MUI TextField variants
        '& .MuiTextField-root': {
            '& .MuiOutlinedInput-notchedOutline': {
                border: 'none !important'
            }
        },
        '& .MuiInputBase-input.Mui-disabled': {
            color: 'rgba(0, 0, 0, 0.87)',
            WebkitTextFillColor: 'rgba(0, 0, 0, 0.87)'
        }
    };

    // Prepare data for reject modal
    const getRejectLeaveData = () => {
        const values = formReturn.getValues();
        return {
            fullName: values.member?.label || '',
            totalDays: values.totalLeaveDays || 0,
            fromDate: values.fromDate || new Date(),
            toDate: values.toDate || new Date()
        };
    };
    const handleOpenRejectModal = () => {
        if (onReject) {
            const rejectData = getRejectLeaveData();
            onReject(rejectData);
        }

        handleClose();
    };

    return (
        <>
            <Modal
                isOpen={open}
                title={
                    isEdit
                        ? checkAllowedPermission(manageLeaves.approve)
                            ? manage_leaves + 'leave-request'
                            : manage_leaves + 'edit-leaves'
                        : manage_leaves + 'add-leaves'
                }
                onClose={handleClose}
            >
                <FormProvider formReturn={formReturn} onSubmit={handleSubmit}>
                    <Box
                        display="flex"
                        flexDirection="column"
                        gap={1}
                        sx={{
                            mt: 3,
                            '& .MuiInputBase-input': {
                                padding: '0.25rem 0.5rem'
                            }
                        }}
                    >
                        <Grid container spacing={gridSpacing}>
                            {/* Các thông tin cá nhân - chỉ hiển thị, không sửa được */}
                            <Grid item xs={12} md={4}>
                                <Input
                                    label={<FormattedMessage id={manage_leaves + 'full-name'} />}
                                    name="member.label"
                                    disabled
                                    required
                                    sx={disabledInputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <Input
                                    label={<FormattedMessage id={manage_leaves + 'member-code'} />}
                                    name="codeMember"
                                    disabled
                                    required
                                    sx={disabledInputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <Input
                                    label={<FormattedMessage id={manage_leaves + 'position'} />}
                                    name="titleMember"
                                    disabled
                                    required
                                    sx={disabledInputStyle}
                                />
                            </Grid>

                            <Grid item xs={12} md={4}>
                                <Input
                                    label={<FormattedMessage id={manage_leaves + 'dept'} />}
                                    name="department"
                                    disabled
                                    required
                                    sx={disabledInputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <Input
                                    label={<FormattedMessage id={manage_leaves + 'group'} />}
                                    name="groupMember"
                                    disabled
                                    required
                                    sx={disabledInputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <Input
                                    label={<FormattedMessage id={manage_leaves + 'contract-type'} />}
                                    name="contractType"
                                    disabled
                                    required
                                    sx={disabledInputStyle}
                                />
                            </Grid>

                            {/* Trường QLTT */}
                            {shouldShowDirectManager() && (
                                <Grid item xs={12} md={columnSize === 3 ? 3 : 4}>
                                    <DirectApprover
                                        name="directApproverIdHexString"
                                        label={<FormattedMessage id={manage_leaves + 'direct-manager'} />}
                                        disabled={isEdit && !canEdit}
                                        required
                                        // onChange={(value) => methods.setValue('manager', value)}
                                        idHexString={!isEdit ? userInfo?.idHexString : undefined}
                                    />
                                </Grid>
                            )}

                            {shouldShowIndirectManager() && (
                                <Grid item xs={12} md={columnSize === 3 ? 3 : 4}>
                                    <DirectApprover
                                        name="nextApproverIdHexString"
                                        label={<FormattedMessage id={manage_leaves + 'indirect-management'} />}
                                        disabled={isIndirectManagerDisabled()}
                                        required
                                        idHexString={!isEdit ? userInfo?.idHexString : undefined}
                                    />
                                </Grid>
                            )}

                            <Grid item xs={12} md={columnSize === 3 ? 3 : 4}>
                                <Input
                                    label={<FormattedMessage id={manage_leaves + 'total-leave-days'} />}
                                    name="totalLeaveDays"
                                    disabled
                                    required
                                    sx={disabledInputStyle}
                                />
                            </Grid>

                            <Grid item xs={12} md={columnSize === 3 ? 3 : 4}>
                                <RemainLeaveDay name="remainingLeaveDays" required disabled sx={disabledInputStyle} />
                            </Grid>

                            <Grid item xs={12} md={4}>
                                <DatePicker
                                    label={<FormattedMessage id={manage_leaves + 'request-date'} />}
                                    name="requestDay"
                                    disabled={
                                        isEdit
                                            ? leaveInfo?.status === EApproveStatus.APPROVED ||
                                              leaveInfo?.status === EApproveStatus.DECLINED ||
                                              (leaveInfo?.status === EApproveStatus.AWAITING_QLTT && !isCheckedEdit()) ||
                                              (!isCheckedEdit() &&
                                                  checkAllowedPermission(manageLeaves.approve) &&
                                                  shouldShowIndirectManager())
                                            : false
                                    }
                                    required
                                    sx={
                                        isEdit &&
                                        (leaveInfo?.status === EApproveStatus.APPROVED ||
                                            leaveInfo?.status === EApproveStatus.DECLINED ||
                                            (leaveInfo?.status === EApproveStatus.AWAITING_QLTT && !isCheckedEdit()) ||
                                            (!isCheckedEdit() &&
                                                checkAllowedPermission(manageLeaves.approve) &&
                                                shouldShowIndirectManager()))
                                            ? disabledInputStyle
                                            : undefined
                                    }
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <DatePicker
                                    label={<FormattedMessage id={manage_leaves + 'from-date'} />}
                                    name="fromDate"
                                    required
                                    disabled
                                    sx={disabledInputStyle}
                                />
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <DatePicker
                                    label={<FormattedMessage id={manage_leaves + 'to-date'} />}
                                    name="toDate"
                                    required
                                    disabled
                                    sx={disabledInputStyle}
                                />
                            </Grid>

                            {/* Thêm phần các loại nghỉ */}
                            <Grid item xs={12}>
                                <Grid container spacing={0}>
                                    {fields.map((field, index) => (
                                        <LeaveRequestDetail
                                            key={field.id}
                                            index={index}
                                            isFirst={index === 0}
                                            onAdd={handleAddDetail}
                                            onRemove={() => remove(index)}
                                            isDisable={isEdit ? (!isCheckedEdit() ? true : false) : false}
                                        />
                                    ))}
                                </Grid>
                            </Grid>
                        </Grid>
                    </Box>

                    <DialogActions sx={dialogActionStyles}>
                        <Button onClick={handleClose} sx={{ color: '#3162D2' }} disabled={loading}>
                            <FormattedMessage id={manage_leaves + 'cancel'} />
                        </Button>

                        {isEdit ? (
                            <>
                                {isCheckedApprove() && leaveInfo?.status !== EApproveStatus.APPROVED && (
                                    <>
                                        <Button
                                            variant="contained"
                                            sx={{ bgcolor: 'rgba(161, 0, 0, 1)' }}
                                            onClick={() => handleOpenRejectModal()}
                                        >
                                            <FormattedMessage id={manage_leaves + 'declines'} />
                                        </Button>
                                        <LoadingButton loading={loading} variant="contained" onClick={() => handleApprove()}>
                                            <FormattedMessage id={manage_leaves + 'approve'} />
                                        </LoadingButton>
                                    </>
                                )}
                                {canEdit && (
                                    <LoadingButton loading={loading} variant="contained" type="submit">
                                        <FormattedMessage id={manage_leaves + 'edit'} />
                                    </LoadingButton>
                                )}
                            </>
                        ) : (
                            <LoadingButton loading={isSubmitting} variant="contained" color="primary" type="submit">
                                <FormattedMessage id={manage_leaves + 'submit'} />
                            </LoadingButton>
                        )}
                    </DialogActions>
                </FormProvider>
            </Modal>
        </>
    );
};

export default AddOrEditLeaves;
