// material-ui
import { Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { DatePicker, Label } from 'components/extended/Form';
import { ApproveStatus, Department, Member, OTType, SearchForm } from 'containers/search';
import { IManageLeavesDefaultValues, manageLeavesDefaultValues, manageLeavesSearchSchema } from 'pages/manage-leaves/Config';

// third party
import { FormattedMessage } from 'react-intl';

interface IManageOTSearchProps {
    formReset: IManageLeavesDefaultValues;
    handleSearch: (values: any) => void;
}

const ManageOTSearch = (props: IManageOTSearchProps) => {
    const { formReset, handleSearch } = props;

    return (
        <SearchForm
            defaultValues={manageLeavesDefaultValues}
            formSchema={manageLeavesSearchSchema}
            formReset={formReset}
            handleSubmit={handleSearch}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <DatePicker name="fromDate" label={<FormattedMessage id="from-date" />} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <DatePicker name="toDate" label={<FormattedMessage id="to-date" />} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Member name="memberId" isIdHexString />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Department name="dept" />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <OTType name="type" />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <ApproveStatus name="status" />
                </Grid>
                <Grid item xs={12} lg={3}></Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id="search" />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ManageOTSearch;
