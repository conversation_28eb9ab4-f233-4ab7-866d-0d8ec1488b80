import { FormattedMessage } from 'react-intl';

// material-ui
import { Button, DialogActions, Grid } from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project imports
import { CommentWorkingCalendarConfig } from 'pages/register-working-calendar/Config';
import { FormProvider, Input } from 'components/extended/Form';
import { IWorkingCalendar } from 'types/working-calendar';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { useEffect, useState } from 'react';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// ==============================|| Comment Working Calendar Modal ||============================== //

interface ICommentCalendarModalProps {
    loading?: boolean;
    open: boolean;
    handleClose: () => void;
    item?: IWorkingCalendar;
    onSubmitComment: (comment: string) => void;
}

const CommentCalendarModal = ({ loading, open, handleClose, item, onSubmitComment }: ICommentCalendarModalProps) => {
    const { register_working_calendar } = TEXT_CONFIG_SCREEN.workingCalendar;
    const [isSubmit, setIsSubmit] = useState<boolean>(true);
    const [comment, setComment] = useState<string>(item?.comment || '');

    useEffect(() => {
        if (open && item) {
            const initialComment = item.comment || '';
            setComment(initialComment);
            setIsSubmit(true);
        }
    }, [open, item]);

    const handleCommentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newComment = event.target.value;
        setComment(newComment);
        setIsSubmit(newComment === (item?.comment || ''));
    };

    const handleSubmit = () => {
        onSubmitComment(comment);
    };

    return (
        <Modal isOpen={open} title={register_working_calendar + 'comments'} onClose={handleClose} keepMounted={false}>
            <FormProvider
                form={{
                    defaultValues: CommentWorkingCalendarConfig
                }}
                onSubmit={handleSubmit}
                formReset={{
                    ...item,
                    comment: item?.comment ? item.comment : ''
                }}
            >
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12}>
                        <Input
                            textFieldProps={{
                                multiline: true,
                                rows: 4,
                                onChange: handleCommentChange,
                                value: comment
                            }}
                            name="comment"
                        />
                    </Grid>
                </Grid>

                <DialogActions>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id={register_working_calendar + 'cancel'} />
                    </Button>
                    <LoadingButton loading={loading} variant="contained" disabled={isSubmit} type="submit">
                        <FormattedMessage id={register_working_calendar + 'submit'} />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default CommentCalendarModal;
