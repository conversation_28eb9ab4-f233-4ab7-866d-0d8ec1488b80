// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid, Stack } from '@mui/material';

// project imports
import { DatePicker, FormProvider, Input } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { Department, Member, Title } from 'containers/search';
import { IAddOrEditResignation } from 'pages/manage-resignation/Config';
import { gridSpacing } from 'store/constant';

// third party
import { UseFormReturn } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { IMember } from 'types/member';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditResignationProps {
    open: boolean;
    handleClose: () => void;
    loading?: boolean;
    isEdit?: boolean;
    formReturn: UseFormReturn<IAddOrEditResignation>;
    addOrEditResignation: (payload: IAddOrEditResignation, idHexString?: string) => void;
    handleChangeMember: (member: IMember) => void;
}

const AddOrEditResignation = (props: IAddOrEditResignationProps) => {
    // Props
    const { open, handleClose, loading, isEdit, formReturn, addOrEditResignation, handleChangeMember } = props;

    const { manage_resignation } = TEXT_CONFIG_SCREEN.workingCalendar;

    // Submit
    const handleSubmit = (values: any) => {
        if (isEdit) {
            addOrEditResignation(values, values.idHexString);
        } else {
            addOrEditResignation(values);
        }
    };

    return (
        <Modal isOpen={open} title={isEdit ? 'edit-resignation' : 'add-resignation'} onClose={handleClose}>
            <FormProvider formReturn={formReturn} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={6}>
                        <Member
                            name="member"
                            isIdHexString
                            isDefaultAll
                            required
                            disabled={isEdit}
                            handleChange={handleChangeMember}
                            label={<FormattedMessage id={manage_resignation + 'members'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Title name="title" disabled isShowAll={false} isShowTitleName={false} label={manage_resignation + 'title'} />
                    </Grid>
                    <Grid item xs={6}>
                        <Department name="dept" disabled isShowAll={false} label={manage_resignation + 'dept'} />
                    </Grid>
                    <Grid item xs={6}>
                        <DatePicker name="fromDate" required label={<FormattedMessage id={manage_resignation + 'from-date'} />} />
                    </Grid>
                    <Grid item xs={6}>
                        <Member
                            name="approver"
                            isIdHexString
                            isDefaultAll
                            required
                            label={<FormattedMessage id={manage_resignation + 'approver'} />}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <Grid item xs={12}>
                            <Input
                                textFieldProps={{ multiline: true, rows: 4 }}
                                name="reason"
                                label={<FormattedMessage id={manage_resignation + 'reason'} />}
                            />
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <DialogActions>
                            <Stack direction="row" spacing={1} justifyContent="flex-end">
                                <Button color="error" onClick={handleClose} disabled={loading}>
                                    <FormattedMessage id={manage_resignation + 'cancel'} />
                                </Button>
                                <LoadingButton loading={loading} variant="contained" type="submit">
                                    <FormattedMessage id={manage_resignation + 'submit'} />
                                </LoadingButton>
                            </Stack>
                        </DialogActions>
                    </Grid>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditResignation;
