import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid } from '@mui/material';

// project imports
import { FormProvider, Input, NumericFormatCustom } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { saveOrUpdateLeaveDayConfig, saveOrUpdateLeaveDaySchema } from 'pages/manage-leave-days/config';
import { gridSpacing } from 'store/constant';
import { ILeaveDays } from 'types/working-calendar';

// ==============================|| EDIT LEAVE DAY ||============================== //

interface IAddOrEditLeaveDayProps {
    leaveDay: ILeaveDays;
    loading?: boolean;
    open: boolean;
    handleClose: () => void;
    setLeaveDay: React.Dispatch<any>;
    editLeaveDay: (leaveDay: ILeaveDays) => void;
}

const AddOrEditLeaveDay = ({ leaveDay, loading, open, handleClose, editLeaveDay, setLeaveDay }: IAddOrEditLeaveDayProps) => {
    const handleSubmit = (values: ILeaveDays) => {
        editLeaveDay(values);
    };

    const defaultValues = {
        ...saveOrUpdateLeaveDayConfig,
        leaveDaysInformation: {
            ...saveOrUpdateLeaveDayConfig.leaveDaysInformation
        },
        member: `${saveOrUpdateLeaveDayConfig.lastName} ${saveOrUpdateLeaveDayConfig.firstName}`
    };

    // useForm
    const methods = useForm({
        defaultValues,
        mode: 'all',
        resolver: yupResolver(saveOrUpdateLeaveDaySchema)
    });

    useEffect(() => {
        methods.reset({
            ...leaveDay,
            leaveDaysInformation: {
                ...leaveDay.leaveDaysInformation
            },
            member: `${leaveDay.lastName} ${leaveDay.firstName}`
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [leaveDay]);

    return (
        <Modal isOpen={open} title={'update-leave-days'} onClose={handleClose} keepMounted={false}>
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                {/* Tabs  */}
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12} lg={6}>
                        <Input name="member" label={<FormattedMessage id="member" />} disabled />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input name="titleCode" label={<FormattedMessage id="table-title" />} disabled />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input name="departmentId" label={<FormattedMessage id="department" />} disabled />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input name="leaveDaysInformation.totalLeaveDays" label={<FormattedMessage id="total-leave-days" />} disabled />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name="leaveDaysInformation.totalLeaveDaysLastYear"
                            label={<FormattedMessage id="total-leave-days-last-year" />}
                        />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name="leaveDaysInformation.totalLeaveDayThisYear"
                            label={<FormattedMessage id="total-leave-days-this-year" />}
                        />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input
                            textFieldProps={{
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name="leaveDaysInformation.totalLeaveDaysUsed"
                            label={<FormattedMessage id="number-leave-days-used" />}
                        />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input
                            name="leaveDaysInformation.totalLeaveDaysRemain"
                            label={<FormattedMessage id="remaining-leave-days" />}
                            disabled
                        />
                    </Grid>
                </Grid>
                {/* </TabPanel> */}

                <DialogActions>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id="cancel" />
                    </Button>
                    <LoadingButton loading={loading} variant="contained" type="submit">
                        <FormattedMessage id="submit" />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditLeaveDay;
