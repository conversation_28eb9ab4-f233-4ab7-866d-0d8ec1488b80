/* eslint-disable prettier/prettier */
// material-ui
import { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';

// project imports
import { dateFormat } from 'utils/date';
import { IResignationItem } from 'types/working-calendar';
import { EApproveStatus } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';

// third party
import { FormattedMessage } from 'react-intl';

// assets
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

interface IManageResignationTBodyProps {
    pageNumber: number;
    pageSize: number;
    resignations: IResignationItem[];
    handleEdit: (item?: IResignationItem) => void;
    handleOpenApprove: (idHexString: string) => void;
}

const ManageResignationTBody = (props: IManageResignationTBodyProps) => {
    const { pageNumber, pageSize, resignations, handleEdit, handleOpenApprove } = props;
    const { manageResignation } = PERMISSIONS.workingCalendar;

    const { userInfo } = useAppSelector(authSelector);

    return (
        <TableBody>
            {resignations.map((item: IResignationItem, key: number) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{item.member.fullName}</TableCell>
                    <TableCell>{item.approver.fullName}</TableCell>
                    <TableCell>{item.title}</TableCell>
                    <TableCell>{item.dept}</TableCell>
                    <TableCell>{dateFormat(item.fromDate)}</TableCell>
                    <TableCell>
                        <FormattedMessage id={item.status} />
                    </TableCell>
                    <TableCell>{item.approvedDate && dateFormat(item.approvedDate)}</TableCell>
                    {checkAllowedPermission(manageResignation.edit) || checkAllowedPermission(manageResignation.approve) ? (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                {item.status === EApproveStatus.AWAITING_QLTT &&
                                checkAllowedPermission(manageResignation.approve) &&
                                userInfo?.idHexString === item.approver.idHexString ? (
                                    <Tooltip
                                        placement="top"
                                        title={<FormattedMessage id="approve" />}
                                        onClick={() => handleOpenApprove(item.idHexString)}
                                    >
                                        <IconButton aria-label="approve" size="small">
                                            <EventAvailableIcon sx={{ fontSize: '1.1rem' }} />
                                        </IconButton>
                                    </Tooltip>
                                ) : (
                                    <></>
                                )}
                                <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleEdit(item)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    ) : (
                        <></>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ManageResignationTBody;
