import { FormattedMessage } from 'react-intl';

// material-ui
import { IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import LockResetIcon from '@mui/icons-material/LockReset';

// project imports
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { checkAllowedPermission } from 'utils/authorization';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { resetPassowrd } from 'store/slice/memberSlice';
import { PERMISSIONS } from 'constants/Permission';
import { convertStatus } from 'utils/common';
import { useAppDispatch } from 'app/hooks';
import { IMember } from 'types/member';

interface IUserTBodyProps {
    page: number;
    size: number;
    users: IMember[];
    handleOpen: (user?: IMember) => void;
}

const UserTBody = (props: IUserTBodyProps) => {
    const { page, size, users, handleOpen } = props;
    const { userPermission } = PERMISSIONS.admin;

    const dispatch = useAppDispatch();

    const handleResetPassword = (user: IMember) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="reset-password" />,
                width: '400px',
                handleConfirm: async () => {
                    if (!user.idHexString) return;
                    const resultAction = await dispatch(resetPassowrd({ userId: user.idHexString }));
                    if (resetPassowrd.fulfilled.match(resultAction)) {
                        if (resultAction.payload?.status) {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result.content,
                                    variant: 'alert',
                                    alert: { color: 'success' }
                                })
                            );
                        } else {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result?.content?.message || 'Error',
                                    variant: 'alert',
                                    alert: { color: 'error' }
                                })
                            );
                        }
                    }
                    dispatch(closeConfirm());
                }
            })
        );
    };

    return (
        <TableBody>
            {users?.map((user: IMember, key) => (
                <TableRow key={key}>
                    <TableCell>{size * page + key + 1}</TableCell>
                    <TableCell>{user.memberCode}</TableCell>
                    <TableCell>{user.userName}</TableCell>
                    <TableCell>{user.firstName}</TableCell>
                    <TableCell>{user.lastName}</TableCell>
                    <TableCell>{user.userRankHistoryList?.[user.userRankHistoryList.length - 1]?.titleCode}</TableCell>
                    <TableCell>{user.userRankHistoryList?.[user.userRankHistoryList.length - 1]?.rankId}</TableCell>
                    <TableCell>{user.departmentId}</TableCell>
                    <TableCell>
                        <FormattedMessage id={convertStatus(user.status) as string} />
                    </TableCell>

                    {checkAllowedPermission(userPermission.edit) && (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip
                                    placement="top"
                                    title={<FormattedMessage id="reset-password" />}
                                    onClick={() => handleResetPassword(user)}
                                >
                                    <IconButton aria-label="delete" size="small">
                                        <LockResetIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip placement="top" title={<FormattedMessage id="edit" />} onClick={() => handleOpen(user)}>
                                    <IconButton aria-label="delete" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default UserTBody;
