import { FormattedMessage } from 'react-intl';

// mui import
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const UserBillableTableTHead = () => {
    const { manage_user } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_user + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'from-date'} />
                    <span className="MuiInputLabel-asterisk"> *</span>
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'to-date'} />
                    <span className="MuiInputLabel-asterisk"> *</span>
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={manage_user + 'action'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default UserBillableTableTHead;
