import { Button, DialogActions, Grid } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { createTitle, editTitle, getSearchTitle, titleConfigSelector } from 'store/slice/titleSlice';
import { ITitleFilterConfig, createOrEditTitleSchema } from 'pages/administration/Config';
import { ICreateTitleRequest, ITitleConfig } from 'types/titleConfig';
import { FormProvider, Input } from 'components/extended/Form';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditTitleConfigProps {
    title: ITitleConfig | undefined;
    conditions: ITitleFilterConfig;
    open: boolean;
    handleClose: () => void;
}

const AddOrEditTitleConfig = (props: IAddOrEditTitleConfigProps) => {
    const { title, open, conditions, handleClose } = props;

    const { title_config } = TEXT_CONFIG_SCREEN.administration;
    const dispatch = useAppDispatch();

    const { loading } = useAppSelector(titleConfigSelector);

    const methods = useForm({
        defaultValues: {
            titleCode: title?.titleCode || '',
            titleName: title?.titleName || ''
        },
        resolver: yupResolver(createOrEditTitleSchema),
        mode: 'all'
    });

    const handleSubmit = async (values: ICreateTitleRequest) => {
        if (title) {
            const resultAction = await dispatch(editTitle({ ...values, id: title.id }));
            if (editTitle.fulfilled.match(resultAction) && !resultAction.payload.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            } else if (editTitle.rejected.match(resultAction)) {
                return;
            }
        } else {
            const resultAction = await dispatch(createTitle(values));
            if (createTitle.fulfilled.match(resultAction) && !resultAction.payload.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            } else if (createTitle.rejected.match(resultAction)) {
                return;
            }
        }
        dispatch(
            openSnackbar({
                open: true,
                message: title ? 'update-success' : 'add-success',
                variant: 'alert',
                alert: { color: 'success' }
            })
        );
        dispatch(getSearchTitle(conditions));
        handleClose();
    };

    return (
        <Modal
            isOpen={open}
            title={title ? title_config + 'edit-title-config' : title_config + 'add-title-config'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="xs"
        >
            <FormProvider onSubmit={handleSubmit} formReturn={methods}>
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12}>
                        <Input required name="titleCode" label={<FormattedMessage id={title_config + 'title'} />} disabled={!!title} />
                    </Grid>
                    <Grid item xs={12}>
                        <Input required name="titleName" label={<FormattedMessage id={title_config + 'title-name'} />} />
                    </Grid>
                </Grid>

                <DialogActions>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id="cancel" />
                    </Button>
                    <LoadingButton
                        loading={loading[createTitle.typePrefix] || loading[editTitle.typePrefix]}
                        variant="contained"
                        type="submit"
                    >
                        <FormattedMessage id={title_config + 'submit'} />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditTitleConfig;
