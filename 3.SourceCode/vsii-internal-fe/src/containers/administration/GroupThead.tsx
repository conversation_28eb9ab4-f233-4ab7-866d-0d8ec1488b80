// third party
import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ManageGroupThead = () => {
    const { groupPermission } = PERMISSIONS.admin;

    const { manage_group } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_group + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'group-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'group-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'group-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'note'} />
                </TableCell>
                {checkAllowedPermission(groupPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={manage_group + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default ManageGroupThead;
