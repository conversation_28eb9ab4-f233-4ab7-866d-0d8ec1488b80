// material-ui
import { TableBody, TableCell, TableRow, Stack, Tooltip, IconButton } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';

// assets
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

// project import
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// third party
import { FormattedMessage } from 'react-intl';
import { ILanguage } from 'types';

interface ILanguageConfigTBodyProps {
    languages: ILanguage[];
    handleOpen: (language: ILanguage) => void;
    pageSize: number;
    pageNumber: number;
    handleDelete: (language: ILanguage) => void;
}
const LanguageConfigTBody = (props: ILanguageConfigTBodyProps) => {
    const { languages, handleOpen, pageSize, pageNumber, handleDelete } = props;
    const { cVConfigLanguagePermission } = PERMISSIONS.admin;

    return (
        <TableBody>
            {languages?.map((language: ILanguage, key: number) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{language.name}</TableCell>
                    {checkAllowedPermission(cVConfigLanguagePermission.edit) && (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(language)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip placement="top" title={<FormattedMessage id="delete" />} onClick={() => handleDelete(language)}>
                                    <IconButton aria-label="delete" size="small">
                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default LanguageConfigTBody;
