import { useSearchParams } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';
import { Grid } from '@mui/material';

import { IDepartmentFilterConfig } from 'pages/administration/Config';
import { FormProvider, Input, Label } from 'components/extended/Form';
import { searchFormConfig } from 'containers/search/Config';
import { transformObject } from 'utils/common';
import { Button } from 'components';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IDepartmentSearchProps {
    conditions: IDepartmentFilterConfig;
    setConditions: React.Dispatch<React.SetStateAction<IDepartmentFilterConfig>>;
}

const DepartmentSearch = ({ conditions, setConditions }: IDepartmentSearchProps) => {
    const [, setSearchParams] = useSearchParams();
    const { manage_department } = TEXT_CONFIG_SCREEN.administration;

    const handleSearch = (value: IDepartmentFilterConfig) => {
        const newValue = transformObject({ ...value, page: 1 });
        setSearchParams(newValue as any);
        setConditions(newValue);
    };

    return (
        <FormProvider
            form={{
                defaultValues: conditions
            }}
            onSubmit={handleSearch}
        >
            <Grid container justifyContent="space-between">
                <Grid item xs={12} lg={3}>
                    <Input
                        name={searchFormConfig.department.manage.name}
                        label={<FormattedMessage id={manage_department + 'department'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={manage_department + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </FormProvider>
    );
};

export default DepartmentSearch;
