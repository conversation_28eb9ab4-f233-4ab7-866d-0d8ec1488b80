import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const UserThead = () => {
    const { userPermission } = PERMISSIONS.admin;
    const { manage_user } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_user + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'username'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'first-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'last-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'title'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'level'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'department'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'status'} />
                </TableCell>
                {checkAllowedPermission(userPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={manage_user + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default UserThead;
