import { FormattedMessage } from 'react-intl';

// material-ui
import { Box, Button, Stack, useTheme } from '@mui/material';

// project imports
import { Table } from 'components/extended/Table';
import { gridSpacing } from 'store/constant';
import { quotaUpdateHistoryDefault } from 'pages/administration/Config';
import QuotaUpdateHistoryThead from './QuotaUpdateHistoryThead';
import QuotaUpdateHistoryTBody from './QuotaUpdateHistoryTBody';
import { IQuotaUpdateHistory } from 'types';

interface IQuotaUpdateHistoryProps {
    quotaUpdateHistories: IQuotaUpdateHistory[];
    handleClose: () => void;
}

const QuotaUpdateHistory = (props: IQuotaUpdateHistoryProps) => {
    const theme = useTheme();
    const { quotaUpdateHistories, handleClose } = props;

    return (
        <>
            <Table heads={<QuotaUpdateHistoryThead />} data={quotaUpdateHistories || quotaUpdateHistoryDefault}>
                <QuotaUpdateHistoryTBody quotaUpdateHistories={quotaUpdateHistories || quotaUpdateHistoryDefault} />
            </Table>
            <Box sx={{ marginTop: theme.spacing(gridSpacing) }}>
                <Stack direction="row" spacing={1} justifyContent="flex-end">
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id="cancel" />
                    </Button>
                </Stack>
            </Box>
        </>
    );
};

export default QuotaUpdateHistory;
