import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ExchangeRateConfigThead = () => {
    const { exchange_rate_config } = TEXT_CONFIG_SCREEN.administration;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={exchange_rate_config + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={exchange_rate_config + 'year'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={exchange_rate_config + 'currency'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={exchange_rate_config + 'exchange-rate'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={exchange_rate_config + 'actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default ExchangeRateConfigThead;
