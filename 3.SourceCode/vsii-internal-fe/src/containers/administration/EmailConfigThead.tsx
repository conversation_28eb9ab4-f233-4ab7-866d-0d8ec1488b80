import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

//project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const EmailConfigThead = () => {
    const { emailConfigPermission } = PERMISSIONS.admin;

    const { email_config } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={email_config + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={email_config + 'email-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={email_config + 'send-to'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={email_config + 'send-cc'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={email_config + 'send-bcc'} />
                </TableCell>
                {checkAllowedPermission(emailConfigPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={email_config + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default EmailConfigThead;
