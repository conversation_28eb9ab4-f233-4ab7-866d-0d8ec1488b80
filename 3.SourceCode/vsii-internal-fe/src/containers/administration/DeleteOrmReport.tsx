import { useState } from 'react';

import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import DialogActions from '@mui/material/DialogActions';
import { ButtonBase, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { LoadingButton } from '@mui/lab';
import { Box } from '@mui/system';
import { useIntl } from 'react-intl';

import { openSnackbar } from 'store/slice/snackbarSlice';
import sendRequest from 'services/ApiService';
import Modal from 'components/extended/Modal';
import { useAppDispatch } from 'app/hooks';
import Api from 'constants/Api';

type UploadORMReportProps = {
    id: string;
    updateTable: () => void;
};

const DeleteOrmReport = ({ id, updateTable }: UploadORMReportProps) => {
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const dispatch = useAppDispatch();

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };
    const handleDeleteOrmReport = async () => {
        try {
            setLoading(true);
            const response = await sendRequest(Api.monthly_efford.deleteOrmReport(id));
            dispatch(
                openSnackbar({
                    open: true,
                    message: response.status ? 'Deleted ORM Report Successfully' : response.result.content.message,
                    variant: 'alert',
                    alert: response.status ? { color: 'success' } : { color: 'error' }
                })
            );
        } catch (error) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'Deleted This ORM Report Fail',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
        } finally {
            setLoading(false);
            setOpen(false);
        }
        updateTable();
    };

    return (
        <>
            <ButtonBase onClick={handleClickOpen} color="inherit">
                <HighlightOffIcon />
            </ButtonBase>
            <Modal
                title={useIntl().formatMessage({ id: 'warning' })}
                isOpen={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                maxWidth="xs"
            >
                <Box sx={{ mb: 2 }}>
                    <Typography fontSize={16} fontWeight={500}>
                        <FormattedMessage id="confirm-delete" />
                    </Typography>
                </Box>

                <DialogActions sx={{ display: 'flex' }}>
                    <LoadingButton color="error" disabled={loading} size="large" onClick={handleClose}>
                        <FormattedMessage id="cancel" />
                    </LoadingButton>

                    <LoadingButton
                        loading={loading}
                        disabled={loading}
                        variant="contained"
                        size="large"
                        type="submit"
                        onClick={handleDeleteOrmReport}
                    >
                        <FormattedMessage id="confirm" />
                    </LoadingButton>
                </DialogActions>
            </Modal>
        </>
    );
};

export default DeleteOrmReport;
