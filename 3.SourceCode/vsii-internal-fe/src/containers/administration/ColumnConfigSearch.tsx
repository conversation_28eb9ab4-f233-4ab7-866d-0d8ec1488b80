import React, { useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSearchParams } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { Grid } from '@mui/material';

import { flexibleReportingConfigSchema } from 'pages/administration/Config';
import ReportNameConfig from 'containers/search/ReportNameConfig';
import { ISearchColumnConfigParams } from 'types/flexible-report';
import { FormProvider, Label } from 'components/extended/Form';
import ColumnName from 'containers/search/ColumnName';
import { transformObject } from 'utils/common';
import { Button } from 'components';
import { IOption } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface Props {
    conditions: ISearchColumnConfigParams;
    setConditions: React.Dispatch<React.SetStateAction<ISearchColumnConfigParams>>;
}

const ColumnConfigSearch: React.FC<Props> = ({ conditions, setConditions }) => {
    const { column_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    const [reportNameId, setReportNameId] = useState<string>(conditions.flexibleReportId);

    const [formReset] = useState<ISearchColumnConfigParams>({
        ...conditions,
        flexibleColumnName: conditions.flexibleColumnId
            ? ({ value: conditions.flexibleColumnId, label: conditions.flexibleColumnName } as IOption)
            : { value: '', label: '' }
    });

    const [, setSearchParams] = useSearchParams();

    const methods = useForm({
        defaultValues: conditions,
        resolver: yupResolver(flexibleReportingConfigSchema)
    });

    const handleSearch = (value: ISearchColumnConfigParams) => {
        const newValue = transformObject({ ...value, page: 1 });
        setSearchParams({
            ...(newValue as any),
            flexibleColumnName: newValue.flexibleColumnName ? newValue.flexibleColumnName?.label : '',
            flexibleColumnId: newValue.flexibleColumnName ? newValue.flexibleColumnName?.value : ''
        });
        setConditions({
            ...newValue,
            flexibleColumnName: newValue.flexibleColumnName?.label
        });
    };

    const handleSelectReportName = (id: string, isSetDefaultValue?: boolean) => {
        setReportNameId(id);
        if (isSetDefaultValue) {
            methods.setValue('flexibleReportId', id);
            handleSearch(methods.getValues());
        }
    };

    return (
        <FormProvider formReturn={methods} formReset={formReset} onSubmit={handleSearch}>
            <Grid container spacing={2}>
                <Grid item xs={12} lg={2.5}>
                    <ReportNameConfig
                        name="flexibleReportId"
                        isSetDefaultValue={!conditions.flexibleReportId}
                        label={<FormattedMessage id={column_config + 'report-name'} />}
                        onChange={handleSelectReportName}
                    />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <ColumnName
                        name="flexibleColumnName"
                        isShowAll
                        flexibleReportId={reportNameId}
                        label={<FormattedMessage id={column_config + 'column-name'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={5}></Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={column_config + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </FormProvider>
    );
};

export default ColumnConfigSearch;
