import React, { useMemo, useState } from 'react';
import { Button, <PERSON>alogActions, <PERSON>rid, Stack, Typography } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { languageConfigSelector } from 'store/slice/languageConfigSlice';
import { Autocomplete, FormProvider } from 'components/extended/Form';
import { addLanguageSchema } from 'pages/administration/Config';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { LANGUAGE_LIST, TEXT_CONFIG_SCREEN } from 'constants/Common';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';

interface Props {
    open: boolean;
    handleClose: () => void;
    handlekAfterAdd?: (languageCode: string) => void;
}

const AddLanguage: React.FC<Props> = ({ open, handleClose, handlekAfterAdd }) => {
    const { language_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    const [loading, setloading] = useState(false);
    const dispatch = useAppDispatch();

    const { laguageConfigList } = useAppSelector(languageConfigSelector);

    const languageList = useMemo(() => {
        return LANGUAGE_LIST.filter((language) => !laguageConfigList.some((item) => item.languageCode === language.value));
    }, [laguageConfigList]);

    const methods = useForm({
        defaultValues: { language: { value: '', label: '' } },
        resolver: yupResolver(addLanguageSchema)
    });

    const handleSubmit = async (values: { language: { value: string; label: string } }) => {
        setloading(true);
        const res = await sendRequest(Api.flexible_textConfig.addLanguage, {
            languageCode: values.language.value,
            languageName: values.language.label
        });

        dispatch(
            openSnackbar({
                open: true,
                message: res.status ? res.result.content : res.result.content.message,
                variant: 'alert',
                alert: { color: res.status ? 'success' : 'error' }
            })
        );

        if (res.status) {
            handleClose();
            handlekAfterAdd?.(values.language.value);
        }
        setloading(false);
    };

    return (
        <Modal isOpen={open} title={language_config + 'Add-language'} onClose={handleClose} maxWidth="sm">
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing} padding={2}>
                    <Grid item xs={12} display="flex" justifyContent="space-between" alignItems="center">
                        <Grid item xs={2.5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id={language_config + 'language'} />
                                <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={9}>
                            <Autocomplete name="language" options={languageList} isDefaultAll />
                        </Grid>
                    </Grid>
                </Grid>
                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={language_config + 'cancel'} />
                            </Button>
                            <LoadingButton variant="contained" type="submit" loading={loading}>
                                <FormattedMessage id={language_config + 'submit'} />
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddLanguage;
