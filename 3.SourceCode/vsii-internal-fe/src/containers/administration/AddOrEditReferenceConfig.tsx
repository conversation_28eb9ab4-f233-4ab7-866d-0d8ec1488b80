// material-ui
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid, Stack } from '@mui/material';

// project imports
import { FormProvider, Input } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { addOrEditReferenceConfigFormDefault, addOrEditReferenceConfigSchema } from 'pages/administration/Config';

// third party
import { FormattedMessage } from 'react-intl';
import { gridSpacing } from 'store/constant';
import { IReference } from 'types';
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditReferenceConfigProps {
    open: boolean;
    isEdit?: boolean;
    handleClose: () => void;
    loading?: boolean;
    reference?: IReference;
    handleAddReferenceConfig: (referenceConfigValue: any) => void;
    handleEditReferenceConfig: (referenceConfigValue: any) => void;
}

const AddOrEditReferenceConfig = (props: IAddOrEditReferenceConfigProps) => {
    const { open, isEdit, handleClose, loading, reference, handleAddReferenceConfig, handleEditReferenceConfig } = props;

    const { cv_config } = TEXT_CONFIG_SCREEN.administration;

    const { userInfo } = useAppSelector(authSelector);

    const handleSubmit = (value: any) => {
        const payload = {
            idHexString: isEdit ? reference?.idHexString : '',
            displayName: value.displayName,
            fullName: value.fullName,
            organization: value.organization,
            position: value.position,
            address: value.address,
            phoneNumber: value.phoneNumber,
            email: value.email,
            userUpdate: userInfo?.userName
        };
        if (isEdit) {
            handleAddReferenceConfig(payload);
        } else {
            handleEditReferenceConfig(payload);
        }
    };

    return (
        <Modal
            isOpen={open}
            title={isEdit ? 'edit-reference-config' : 'add-reference-config'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="sm"
        >
            <FormProvider
                form={{
                    defaultValues: addOrEditReferenceConfigFormDefault,
                    resolver: yupResolver(addOrEditReferenceConfigSchema)
                }}
                formReset={reference}
                onSubmit={handleSubmit}
            >
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12} lg={12}>
                        <Input name="fullName" label={<FormattedMessage id={cv_config + 'full-name'} />} required />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Input name="organization" label={<FormattedMessage id={cv_config + 'organization'} />} />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Input name="position" label={<FormattedMessage id={cv_config + 'position'} />} />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Input name="address" label={<FormattedMessage id={cv_config + 'address'} />} />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Input name="phoneNumber" label={<FormattedMessage id={cv_config + 'phone-number'} />} />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Input name="email" label={<FormattedMessage id={cv_config + 'email'} />} />
                    </Grid>
                </Grid>
                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={cv_config + 'cancel'} />
                            </Button>
                            <LoadingButton loading={loading} variant="contained" type="submit">
                                {isEdit ? <FormattedMessage id={cv_config + 'submit'} /> : <FormattedMessage id={cv_config + 'add'} />}
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditReferenceConfig;
