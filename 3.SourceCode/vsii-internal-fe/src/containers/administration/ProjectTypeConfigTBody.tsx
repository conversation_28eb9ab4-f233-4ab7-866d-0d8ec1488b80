import { I<PERSON><PERSON><PERSON><PERSON>, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { FormattedMessage } from 'react-intl';

import { deleteProjectType, getSearchProjectType } from 'store/slice/projectTypeSlice';
import { IProjectTypeFilterConfig } from 'pages/administration/Config';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { checkAllowedPermission } from 'utils/authorization';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { Checkbox } from 'components/extended/Form';
import { PERMISSIONS } from 'constants/Permission';
import { IProjectType } from 'types/projectType';
import { DATE_FORMAT } from 'constants/Common';
import { useAppDispatch } from 'app/hooks';
import { dateFormat } from 'utils/date';

interface IProjectTypeTBodyProps {
    data: IProjectType[];
    conditions: IProjectTypeFilterConfig;
    handleOpen: (prjType: IProjectType) => void;
}

const ProjectTypeTBody = (props: IProjectTypeTBodyProps) => {
    const { conditions, data, handleOpen } = props;
    const { projectTypeConfigPermission } = PERMISSIONS.admin;

    const dispatch = useAppDispatch();

    const handleDelete = (prjType: IProjectType) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-record" />,
                width: '400px',
                handleConfirm: async () => {
                    const resultAction = await dispatch(deleteProjectType(prjType.id));
                    if (deleteProjectType.fulfilled.match(resultAction)) {
                        if (resultAction.payload?.status) {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result.content,
                                    variant: 'alert',
                                    alert: { color: 'success' }
                                })
                            );
                            await dispatch(getSearchProjectType(conditions));
                        } else {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result?.content || 'Error',
                                    variant: 'alert',
                                    alert: { color: 'error' }
                                })
                            );
                        }
                    }
                    dispatch(closeConfirm());
                }
            })
        );
    };

    return (
        <TableBody>
            {data?.map((value, key) => (
                <TableRow key={key}>
                    <TableCell align="center" sx={{ width: '5%' }}>
                        {conditions.size * (conditions.page - 1) + key + 1}
                    </TableCell>
                    <TableCell sx={{ width: '15%' }}>{value.typeCode}</TableCell>
                    <TableCell sx={{ width: '30%' }}>{value.projectTypeName}</TableCell>
                    <TableCell sx={{ width: '15%' }}>{dateFormat(value.lastUpdate, DATE_FORMAT.DDMMYYYY)}</TableCell>
                    <TableCell sx={{ width: '15%' }}>{value.userUpdate}</TableCell>
                    <TableCell sx={{ width: '10%', textAlign: 'center' }}>
                        <Checkbox name="fixCost" isControl={false} valueChecked={value.fixCost} disabled sx={{ mr: 0 }} />
                    </TableCell>
                    {checkAllowedPermission(projectTypeConfigPermission.edit) && (
                        <TableCell sx={{ width: '10%' }}>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(value)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip placement="top" title={<FormattedMessage id="delete" />} onClick={() => handleDelete(value)}>
                                    <IconButton aria-label="delete" size="small">
                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ProjectTypeTBody;
