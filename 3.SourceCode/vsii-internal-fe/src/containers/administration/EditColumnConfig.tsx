import React from 'react';
import { Box, Button, DialogActions, Grid, SelectChangeEvent, Stack, Typography } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { editColumn, flexiableReportSelector, getListColumns } from 'store/slice/flexiableReportSlice';
import { IColumnConfig, ISearchColumnConfigParams } from 'types/flexible-report';
import { Checkbox, FormProvider, Input } from 'components/extended/Form';
import { editColumnConfigSchema } from 'pages/administration/Config';
import ReportNameConfig from 'containers/search/ReportNameConfig';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import ColumnName from 'containers/search/ColumnName';
import InputType from 'containers/search/InputType';
import { transformObject } from 'utils/common';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface Props {
    open: boolean;
    conditions: ISearchColumnConfigParams;
    data: IColumnConfig | undefined;
    handleClose: () => void;
}

const EditColumnConfig: React.FC<Props> = ({ open, data, conditions, handleClose }) => {
    const { column_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    const { loading } = useAppSelector(flexiableReportSelector);

    const dispatch = useAppDispatch();

    const methods = useForm({
        defaultValues: data,
        resolver: yupResolver(editColumnConfigSchema)
    });

    const handleSubmit = async (value: IColumnConfig) => {
        transformObject(value, ['columnName', 'code', 'flexibleReportId']);
        const { id } = value;
        const resultAction = await dispatch(
            editColumn({ ...value, id: typeof id === 'object' && 'value' in id ? (id.value as string) : '' })
        );

        if (editColumn.fulfilled.match(resultAction) && !resultAction.payload.status) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: resultAction.payload?.result?.content?.message || 'Error',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
            return;
        }

        dispatch(
            openSnackbar({
                open: true,
                message: 'update-success',
                variant: 'alert',
                alert: { color: 'success' }
            })
        );
        dispatch(getListColumns(conditions));
        handleClose();
    };

    const handleChangeInputType = (e: SelectChangeEvent<unknown>) => {
        methods.setValue('isPercentage', false);
    };

    return (
        <Modal isOpen={open} title="Edit Config" onClose={handleClose} maxWidth="sm">
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing} paddingX={2}>
                    <Grid item xs={12} lg={12} display="flex">
                        <Grid item xs={5} lg={5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id={column_config + 'report-name'} /> <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={7} lg={7}>
                            <ReportNameConfig name="flexibleReportId" disabled />
                        </Grid>
                    </Grid>

                    <Grid item xs={12} lg={12} display="flex">
                        <Grid item xs={5} lg={5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id={column_config + 'column-name'} /> <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={7} lg={7}>
                            <ColumnName name="id" flexibleReportId={data?.flexibleReportId} disabled value={data?.id as string} />
                        </Grid>
                    </Grid>

                    <Grid item xs={12} lg={12} display="flex">
                        <Grid item xs={5} lg={5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id={column_config + 'calculate'} />
                            </Typography>
                        </Grid>
                        <Grid item xs={7} lg={7}>
                            <Checkbox name="isCalculate" />
                        </Grid>
                    </Grid>

                    <Grid item xs={12} lg={12} display="flex">
                        <Grid item xs={5} lg={5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id={column_config + 'input-type'} />
                            </Typography>
                        </Grid>
                        <Grid item xs={7} lg={7} display="flex" gap={1}>
                            <InputType name="inputType" handleChange={handleChangeInputType} disabled />
                            <Box display={methods.watch('inputType') === 'number' ? 'block' : 'none'}>
                                <Checkbox name="isPercentage" label="%" />
                            </Box>
                        </Grid>
                    </Grid>

                    <Grid item xs={12} lg={12} display="flex">
                        <Grid item xs={5} lg={5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id={column_config + 'note'} />
                            </Typography>
                        </Grid>
                        <Grid item xs={7} lg={7}>
                            <Input
                                textFieldProps={{
                                    multiline: true,
                                    rows: 2
                                }}
                                name="note"
                            />
                        </Grid>
                    </Grid>
                </Grid>
                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={column_config + 'cancel'} />
                            </Button>
                            <LoadingButton variant="contained" type="submit" loading={loading[editColumn.typePrefix]}>
                                <FormattedMessage id={column_config + 'submit'} />
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default EditColumnConfig;
