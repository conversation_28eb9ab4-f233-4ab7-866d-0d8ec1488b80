import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid } from '@mui/material';

// project import
import { Button } from 'components';
import { SpecialHoursType, SearchForm, Member } from 'containers/search';
import { ISpecialHoursSearchConfig, specialHoursSearchConfig, holidaySearchSchema } from 'pages/administration/Config';
import { Label } from 'components/extended/Form';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IManageSpecialHoursSearchProps {
    handleSearch: (value: ISpecialHoursSearchConfig) => void;
    formReset: ISpecialHoursSearchConfig;
}

const ManageSpecialHolidaySearch = (props: IManageSpecialHoursSearchProps) => {
    const { handleSearch, formReset } = props;

    const { manage_special_hours } = TEXT_CONFIG_SCREEN.administration;

    return (
        <SearchForm
            defaultValues={specialHoursSearchConfig}
            formSchema={holidaySearchSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid>
                <Grid container alignItems="center" spacing={2}>
                    <Grid item xs={12} lg={3} sm={3} md={3} xl={3}>
                        <SpecialHoursType label={manage_special_hours + 'special-hours-type'} />
                    </Grid>
                    <Grid item xs={12} lg={3} sm={3} md={3} xl={3}>
                        <Member isIdHexString name="userIdHexString" label={<FormattedMessage id={manage_special_hours + 'members'} />} />
                    </Grid>
                    <Grid item xs={12} lg={3} sm={3} md={3} xl={3}></Grid>
                    <Grid item xs={12} lg={3} sm={3} md={3} xl={3}>
                        <Label label="&nbsp;" />
                        <Button
                            type="submit"
                            size="medium"
                            children={<FormattedMessage id={manage_special_hours + 'search'} />}
                            variant="contained"
                        />
                    </Grid>
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ManageSpecialHolidaySearch;
