import { FormattedMessage } from 'react-intl';

import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ColumnConfigTHead = () => {
    const { column_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    return (
        <TableHead>
            <TableRow>
                <TableCell sx={{ width: '5%' }}>
                    <FormattedMessage id={column_config + 'no'} />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id={column_config + 'report-name'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={column_config + 'column-name'} />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id={column_config + 'input-type'} />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id={column_config + 'calculate'} />
                </TableCell>
                <TableCell align="center" sx={{ width: '10%' }}>
                    <FormattedMessage id={column_config + 'actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default ColumnConfigTHead;
