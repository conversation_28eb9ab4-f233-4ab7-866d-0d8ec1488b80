/* eslint-disable prettier/prettier */
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage, useIntl } from 'react-intl';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid } from '@mui/material';

// project imports
import { DatePicker, FormProvider, Input } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { DATE_FORMAT, E_IS_LOGTIME, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { Member } from 'containers/search';
import SpecialHoursType from 'containers/search/SpecialHoursType';
import { saveOrUpdateSpecialHoursConfig, saveOrUpdateSpecialHoursSchema } from 'pages/administration/Config';
import { gridSpacing } from 'store/constant';
import { ISpecialHours } from 'types';
import { authSelector } from 'store/slice/authSlice';
import { dateFormat } from 'utils/date';
import { useAppSelector } from 'app/hooks';
import { IMember } from 'types/member';

// ==============================|| ADD NEW SPECIAL HOURS ||============================== //

interface IAddOrEditSpecialHoursProps {
    specialHour?: any;
    loading?: boolean;
    open: boolean;
    isEdit: boolean;
    handleClose: () => void;
    setSpecialHour: React.Dispatch<any>;
    addSpecialHours: (specialHour: ISpecialHours) => void;
    editSpecialHours: (specialHour: ISpecialHours) => void;
}

const AddOrEditSpecialHours = (props: IAddOrEditSpecialHoursProps) => {
    const { specialHour, loading, open, isEdit, handleClose, addSpecialHours, editSpecialHours, setSpecialHour } = props;

    const { manage_special_hours } = TEXT_CONFIG_SCREEN.administration;

    const { userInfo } = useAppSelector(authSelector);
    const intl = useIntl();

    const handleChangeMember = (userSelected: IMember) => {
        userSelected &&
            setSpecialHour({
                ...specialHour,
                userIdHexString: { value: userSelected.idHexString, label: `${userSelected.firstName} ${userSelected.lastName}` },
                userName: userSelected.userName,
                memberCode: userSelected.memberCode,
                lastName: userSelected.lastName,
                firstName: userSelected.firstName
            });
    };

    const handleSubmit = (values: any) => {
        const currentDate = dateFormat(new Date());
        const fromDate = dateFormat(values.fromDate, DATE_FORMAT.DDMMYYYY);
        const toDate = dateFormat(values.toDate, DATE_FORMAT.DDMMYYYY);
        const payload = { ...values, fromDate, toDate };
        const userIdHexString = values.userIdHexString.value;
        // const userId = specialHour.userId;
        const newSpecialHours = { ...payload, fromDate: values.fromDate, toDate: values.toDate };

        if (isEdit) {
            editSpecialHours({
                ...payload,
                userUpdate: userInfo?.userName,
                lastUpdate: currentDate,
                userIdHexString,
                idHexString: specialHour?.idHexString
            });
        } else {
            addSpecialHours({ ...payload, userCreate: userInfo?.userName, dateCreate: currentDate, userIdHexString });
        }
        setSpecialHour(newSpecialHours);
    };

    return (
        <Modal
            isOpen={open}
            title={isEdit ? manage_special_hours + 'edit-special-hours' : manage_special_hours + 'add-special-hours'}
            onClose={handleClose}
            keepMounted={false}
        >
            <FormProvider
                form={{
                    defaultValues: saveOrUpdateSpecialHoursConfig,
                    resolver: yupResolver(saveOrUpdateSpecialHoursSchema)
                }}
                formReset={{
                    ...specialHour,
                    userIdHexString: !!specialHour.userIdHexString
                        ? {
                              value: isEdit ? specialHour.userIdHexString : specialHour.userIdHexString.value,
                              label: `${specialHour.firstName} ${specialHour.lastName}`
                          }
                        : specialHour.userIdHexString
                }}
                onSubmit={handleSubmit}
            >
                {/* Tabs  */}
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={6}>
                        <Member
                            required
                            isLogTime={E_IS_LOGTIME.YES}
                            label={<FormattedMessage id={manage_special_hours + 'user'} />}
                            handleChange={handleChangeMember}
                            disabled={isEdit}
                            isIdHexString
                            isDefaultAll
                            name="userIdHexString"
                        />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input name="hourPerDay" label={<FormattedMessage id={manage_special_hours + 'hours-day'} />} />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <DatePicker required name="fromDate" label={<FormattedMessage id={manage_special_hours + 'from-date'} />} />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <DatePicker required name="toDate" label={<FormattedMessage id={manage_special_hours + 'to-date'} />} />
                    </Grid>
                    <Grid item xs={12} md={6}>
                        <SpecialHoursType required select={true} />
                    </Grid>
                    <Grid item xs={12}>
                        <Input
                            textFieldProps={{
                                placeholder: intl.formatMessage({ id: 'note_placeholder' }),
                                multiline: true,
                                rows: 4
                            }}
                            name="note"
                            label={<FormattedMessage id={manage_special_hours + 'note'} />}
                        />
                    </Grid>
                </Grid>
                {/* </TabPanel> */}

                <DialogActions>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id={manage_special_hours + 'cancel'} />
                    </Button>
                    <LoadingButton loading={loading} variant="contained" type="submit">
                        <FormattedMessage id={manage_special_hours + 'submit'} />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditSpecialHours;
