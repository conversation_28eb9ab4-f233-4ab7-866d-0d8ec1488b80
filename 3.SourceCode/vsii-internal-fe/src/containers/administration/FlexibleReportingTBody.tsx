import { TableBody, TableCell, TableRow, <PERSON>ltip, IconButton } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { FormattedMessage } from 'react-intl';

import { checkAllowedPermission } from 'utils/authorization';
import { IFlexibleReports } from 'types/flexible-report';
import { PERMISSIONS } from 'constants/Permission';

interface IFlexibleReportingTBodyProps {
    reports: IFlexibleReports[];
    handleOpen: (exchange: IFlexibleReports) => void;
    handleDeleteConfig?: (id: string) => void;
}

const FlexibleReportingTBody = (props: IFlexibleReportingTBodyProps) => {
    const { reports, handleOpen, handleDeleteConfig } = props;

    const { flexibleReportConfig } = PERMISSIONS.admin.flexibleReportingConfigPermission;

    return (
        <TableBody>
            {reports?.map((report: IFlexibleReports, key: number) => (
                <TableRow key={key}>
                    <TableCell>{key + 1}</TableCell>
                    <TableCell>{report.reportName}</TableCell>
                    <TableCell>{report.defaultTextNameENG}</TableCell>
                    <TableCell>{report.languageConfigs?.find((item) => item.languageCode === 'en')?.newText}</TableCell>
                    <TableCell>{report.note}</TableCell>
                    <TableCell align="center" width={100}>
                        {checkAllowedPermission(flexibleReportConfig.edit) && (
                            <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(report)}>
                                <IconButton aria-label="edit" size="small">
                                    <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                </IconButton>
                            </Tooltip>
                        )}
                        {checkAllowedPermission(flexibleReportConfig.delete) && (
                            <Tooltip
                                placement="top"
                                title={<FormattedMessage id={'delete'} />}
                                onClick={() => handleDeleteConfig?.(report.id)}
                            >
                                <IconButton aria-label="delete" size="small">
                                    <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                </IconButton>
                            </Tooltip>
                        )}
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default FlexibleReportingTBody;
