import { FormattedMessage } from 'react-intl';

// project imports
import { PERMISSIONS } from 'constants/Permission';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { checkAllowedPermission } from 'utils/authorization';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const TechnologyConfigThead = () => {
    const { emailConfigPermission } = PERMISSIONS.admin;

    const { cv_config } = TEXT_CONFIG_SCREEN.administration;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={cv_config + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={cv_config + 'technology'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={cv_config + 'skill'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={cv_config + 'last-update'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={cv_config + 'user-update'} />
                </TableCell>
                {checkAllowedPermission(emailConfigPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={cv_config + 'actions'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default TechnologyConfigThead;
