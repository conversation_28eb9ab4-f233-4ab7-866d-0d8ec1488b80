import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ManageHolidayThead = () => {
    const { holidayPermission } = PERMISSIONS.admin;
    const { Manage_holidays } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={Manage_holidays + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_holidays + 'from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_holidays + 'to-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_holidays + 'holiday-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_holidays + 'note'} />
                </TableCell>
                {checkAllowedPermission(holidayPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={Manage_holidays + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default ManageHolidayThead;
