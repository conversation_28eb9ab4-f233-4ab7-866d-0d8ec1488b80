import { TableCell, TableHead, TableRow } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';

const ProjectTypeTHead = () => {
    const { projectTypeConfigPermission } = PERMISSIONS.admin;

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center" sx={{ width: '5%' }}>
                    <FormattedMessage id="no" />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id="project-type-short-name" />
                </TableCell>
                <TableCell sx={{ width: '30%' }}>
                    <FormattedMessage id="project-type-name" />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id="last-update" />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id="user-update" />
                </TableCell>
                <TableCell sx={{ width: '10%', textAlign: 'center' }}>
                    <FormattedMessage id="fix-cost" />
                </TableCell>
                {checkAllowedPermission(projectTypeConfigPermission.edit) && <TableCell sx={{ width: '10%' }} />}
            </TableRow>
        </TableHead>
    );
};

export default ProjectTypeTHead;
