import { FormattedMessage } from 'react-intl';
// material-ui
import { Grid } from '@mui/material';

// project import
import { Button } from 'components';
import { Member, SearchForm } from 'containers/search';
import { searchFormConfig } from 'containers/search/Config';
import { ILeaveDaySearchConfig, leaveDaySearchConfig, leaveDaySearchSchema } from 'pages/administration/Config';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ILeaveDaySearchProps {
    handleSearch: (value: ILeaveDaySearchConfig) => void;
    formReset: ILeaveDaySearchConfig;
}

const LeaveDaySearch = (props: ILeaveDaySearchProps) => {
    const { handleSearch, formReset } = props;

    const { manage_leave_days } = TEXT_CONFIG_SCREEN.workingCalendar;

    return (
        <SearchForm
            defaultValues={leaveDaySearchConfig}
            formSchema={leaveDaySearchSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid>
                <Grid container alignItems="center" justifyContent="space-between" spacing={2}>
                    <Grid item xs={12} lg={3} sm={3} md={3} xl={3} justifyContent="flex-start">
                        <Member name={searchFormConfig.idHexString.name} label={<FormattedMessage id={manage_leave_days + 'members'} />} />
                    </Grid>

                    <Grid item xs={12} lg={3} sm={3} md={3} xl={3} justifyContent="flex-end">
                        <Button
                            type="submit"
                            size="medium"
                            children={<FormattedMessage id={manage_leave_days + 'search'} />}
                            variant="contained"
                        />
                    </Grid>
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default LeaveDaySearch;
