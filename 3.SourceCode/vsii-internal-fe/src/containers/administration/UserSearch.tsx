import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid } from '@mui/material';

// project imports
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { IUserFilterConfig, userFilterConfig, userFilterSchema } from 'pages/administration/Config';
import { SearchForm, MemberCode, Department, Status, Username, Contractor } from '../search';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IUserSearchProps {
    formReset: IUserFilterConfig;
    handleSearch: (value: any) => void;
}

const UserSearch = (props: IUserSearchProps) => {
    const { formReset, handleSearch } = props;

    const { manage_user } = TEXT_CONFIG_SCREEN.administration;

    return (
        <SearchForm defaultValues={userFilterConfig} formSchema={userFilterSchema} handleSubmit={handleSearch} formReset={formReset}>
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2}>
                    <MemberCode label={manage_user + 'member-code'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Username label={manage_user + 'username'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Department label={manage_user + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Contractor label={manage_user + 'contractor'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Status isShowAll={false} label={manage_user + 'status'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={manage_user + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default UserSearch;
