// material-ui
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid, Stack } from '@mui/material';

// project imports
import { FormProvider, Input } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { addOrEditLanguageConfigFormDefault, addOrEditLanguagelConfigSchema } from 'pages/administration/Config';

// third party
import { FormattedMessage } from 'react-intl';
import { ILanguage } from 'types';
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditLanguageConfigProps {
    open: boolean;
    isEdit?: boolean;
    handleClose: () => void;
    loading?: boolean;
    language?: ILanguage;
    handleAddLanguageConfig: (languageConfigValue: ILanguage) => void;
    handleEditLanguageConfig: (languageConfigValue: ILanguage) => void;
}

const AddOrEditLanguageConfig = (props: IAddOrEditLanguageConfigProps) => {
    const { open, isEdit, handleClose, loading, language, handleAddLanguageConfig, handleEditLanguageConfig } = props;

    const { cv_config } = TEXT_CONFIG_SCREEN.administration;
    const { userInfo } = useAppSelector(authSelector);

    const handleSubmit = (value: any) => {
        const payload = {
            idHexString: isEdit ? language?.idHexString : '',
            name: value.name,
            userUpdate: userInfo?.userName
        };
        if (isEdit) {
            handleEditLanguageConfig(payload);
        } else {
            handleAddLanguageConfig(payload);
        }
    };

    return (
        <Modal
            isOpen={open}
            title={isEdit ? cv_config + 'edit-language-config' : cv_config + 'add-language-config'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="sm"
        >
            <FormProvider
                form={{
                    defaultValues: addOrEditLanguageConfigFormDefault,
                    resolver: yupResolver(addOrEditLanguagelConfigSchema)
                }}
                formReset={language}
                onSubmit={handleSubmit}
            >
                <>
                    <Grid item xs={12} lg={12}>
                        <Input name="name" label={<FormattedMessage id={cv_config + 'language-name'} />} required />
                    </Grid>
                </>
                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={cv_config + 'cancel'} />
                            </Button>
                            <LoadingButton loading={loading} variant="contained" type="submit">
                                {isEdit ? <FormattedMessage id={cv_config + 'submit'} /> : <FormattedMessage id={cv_config + 'add'} />}
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditLanguageConfig;
