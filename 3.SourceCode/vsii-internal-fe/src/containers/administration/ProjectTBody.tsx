// material-ui
import { CircularProgress, IconButton, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';

// project imports
import { IProject } from 'types';
import { dateFormat } from 'utils/date';
import { STATUS_PROJECT } from 'constants/Common';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { detailLoadingSelector, getDetailProject, getQuotaUpdateHistory } from 'store/slice/projectSlice';
import { useAppDispatch, useAppSelector } from 'app/hooks';

// assets
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

// third party
import { useState } from 'react';
import { FormattedMessage } from 'react-intl';

interface IManageProjectTBodyProps {
    pageNumber: number;
    pageSize: number;
    projects: IProject[];
    handleOpen: (isEdit?: boolean) => void;
}

const ManageProjectTBody = (props: IManageProjectTBodyProps) => {
    const { pageNumber, pageSize, projects, handleOpen } = props;
    const [loadingIndex, setLoadingIndex] = useState<number>(-1);

    const { projectPermission } = PERMISSIONS.admin;

    const detailLoading = useAppSelector(detailLoadingSelector);

    const dispatch = useAppDispatch();

    return (
        <TableBody>
            {projects.map((project, key) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{project.projectName}</TableCell>
                    <TableCell>
                        {project.projectManager !== null
                            ? `${project.projectManager?.firstName || ''} ${project.projectManager?.lastName || ''}`
                            : ''}
                    </TableCell>
                    <TableCell
                        sx={{
                            textTransform: 'capitalize'
                        }}
                    >
                        {project.billable}
                    </TableCell>
                    <TableCell>{project.type}</TableCell>
                    <TableCell>{dateFormat(project.startDate)}</TableCell>
                    <TableCell>{STATUS_PROJECT[project.projectStatus]}</TableCell>
                    {checkAllowedPermission(projectPermission.edit) && (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip
                                    placement="top"
                                    title={<FormattedMessage id="edit" />}
                                    onClick={async () => {
                                        if (detailLoading) return;
                                        setLoadingIndex(key);
                                        await Promise.all([
                                            dispatch(getQuotaUpdateHistory({ projectId: project.projectId })),
                                            dispatch(getDetailProject({ projectId: project.projectId }))
                                        ]);
                                        handleOpen(true);
                                    }}
                                >
                                    <IconButton aria-label="delete" size="small">
                                        {detailLoading && key === loadingIndex ? (
                                            <CircularProgress size="1.1rem" />
                                        ) : (
                                            <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                        )}
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ManageProjectTBody;
