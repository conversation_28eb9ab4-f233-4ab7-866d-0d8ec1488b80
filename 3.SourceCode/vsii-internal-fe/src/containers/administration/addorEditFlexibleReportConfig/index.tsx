import { useEffect, useState } from 'react';
import { Box, Button, ButtonBase, DialogActions, Divider, Grid, Stack, Tooltip, Typography } from '@mui/material';
import FormatItalicOutlinedIcon from '@mui/icons-material/FormatItalicOutlined';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';
import FormatColorFillIcon from '@mui/icons-material/FormatColorFill';
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import RotateLeftIcon from '@mui/icons-material/RotateLeft';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useFieldArray, useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { addOrEditFlexibleReportConfigSchema, flexibleReportConfigDetail } from 'pages/administration/Config';
import { Autocomplete, Checkbox, FormProvider, Input, Label, MultipleSelect } from 'components/extended/Form';
import { converStringToCalculationInputsObject, convertCalculationInputsToString } from 'utils/common';
import { IConditionTypes, IFlexibleReports, IOtherTotalReport } from 'types/flexible-report';
import { flexiableReportSelector, getReportName } from 'store/slice/flexiableReportSlice';
import { getLanguage, languageConfigSelector } from 'store/slice/languageConfigSlice';
import { dateFormat, dateFormatStringToDate } from 'utils/date';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import sendRequest from 'services/ApiService';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import useConfig from 'hooks/useConfig';
import Condition from './condition';
import Api from 'constants/Api';
import { IOption } from 'types';
import Sum from './Sum';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
interface IAddorEditFlexibleReportConfigProps {
    open: boolean;
    isEdit?: boolean;
    reportDetail: IFlexibleReports;
    handleClose: () => void;
    getDataTable: () => void;
}

const AddorEditFlexibleReportConfig = ({ open, isEdit, handleClose, reportDetail, getDataTable }: IAddorEditFlexibleReportConfigProps) => {
    const { Flexible_reporting_configuration } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    const [reportId, setReportId] = useState<{ mainReportId?: string; otherReportId?: string }>();
    const [formReset, setFormReset] = useState<IFlexibleReports>(flexibleReportConfigDetail);
    const [conditions, setConditions] = useState<IConditionTypes[]>();
    const [columnsToSum, setColumnsToSum] = useState<IOption[]>();
    const [loading, setLoading] = useState(false);
    const dispatch = useAppDispatch();
    const [displayMultipleReport, setDisplayMultipleReport] = useState(false);

    const { reportName } = useAppSelector(flexiableReportSelector);
    const { laguageConfigList } = useAppSelector(languageConfigSelector);

    const [style, setStyle] = useState({
        bold: formReset?.style?.fontWeight === 'bold' ? true : false,
        italic: formReset?.style?.fontStyle === 'italic' ? true : false,
        underlined: formReset?.style?.textDecoration === 'underline' ? true : false
    });

    const { locale } = useConfig();

    const methods = useForm({
        defaultValues: flexibleReportConfigDetail,
        resolver: yupResolver(addOrEditFlexibleReportConfigSchema)
    });

    const {
        fields: languageConfigs,
        remove,
        update
    } = useFieldArray({
        control: methods.control,
        name: 'languageConfigs'
    });

    const handleChangeLanguage = (value: any[]) => {
        if (value.length) {
            value.forEach((lang, index) => {
                update(index, { languageCode: lang, newText: methods.getValues(`languageConfigs.${index}.newText`) || '' });
            });
            languageConfigs.forEach((config, index) => {
                if (!value.includes(config.languageCode)) {
                    remove(index);
                }
            });
        } else remove();
    };

    const handleSubmit = async (value: IFlexibleReports) => {
        if (
            value.conditions.length > 1 &&
            value.conditions.find((item) => item.length === 0 || (item.length === 1 && !item[0].conditionSelecteted))
        ) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'Please set Conditions when you add by Click Button Or ',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
        } else {
            setLoading(true);
            const createConfigData = {
                ...value,
                reportId: (value.reportId as IOption).value as string,
                conditions: value.conditions.map((data) =>
                    data
                        .filter((item) => item.conditionSelecteted)
                        .map((item) => {
                            if (Array.isArray(item.value)) {
                                return {
                                    ...item,
                                    value: item.value.join(',')
                                };
                            } else if (item.minValue !== undefined && item.maxValue !== undefined) {
                                if (item.type === 'date') {
                                    return {
                                        ...item,
                                        value: `${dateFormat(item.minValue as string)}:${dateFormat(item.maxValue as string)}`
                                    };
                                } else
                                    return {
                                        ...item,
                                        value: `${item.minValue}:${item.maxValue}`
                                    };
                            } else if (item.value && item.type === 'date') {
                                return {
                                    ...item,
                                    value: dateFormat(item.value as string)
                                };
                            } else return { ...item };
                        })
                ),
                calculationInputs:
                    value.isCalculation && Array.isArray(value.calculationInputs)
                        ? convertCalculationInputsToString(value!.calculationInputs)
                        : '',
                otherDataSource: reportId?.otherReportId,
                style: {
                    ...value.style,
                    fontWeight: style.bold ? 'bold' : 'none',
                    fontStyle: style.italic ? 'italic' : 'none',
                    textDecoration: style.underlined ? 'underline' : 'none'
                }
            };

            const res = !isEdit
                ? await sendRequest(Api.flexible_report.createConfig, createConfigData)
                : await sendRequest(Api.flexible_report.editConfig(value.id), createConfigData);
            dispatch(
                openSnackbar({
                    open: true,
                    message: res.status ? res.result.content : res.result.content.message,
                    variant: 'alert',
                    alert: { color: res.status ? 'success' : 'error' }
                })
            );
            setLoading(false);
            if (res.status) {
                handleCloseModal();
                getDataTable();
                setFormReset(flexibleReportConfigDetail);
            }
        }
    };

    const handleCloseModal = () => {
        setDisplayMultipleReport(false);
        handleClose();
    };

    const getConditionTypes = async (reportId: string, language: string, totalReportId?: string) => {
        const res = await sendRequest(Api.flexible_report.getConditionTypes, {
            flexibleReportId: reportId,
            totalReportId: totalReportId,
            language: language
        });
        if (res.status) {
            const { content } = res.result;
            !totalReportId ? setConditions(content) : setConditions(content.dtos);
            totalReportId
                ? setColumnsToSum([
                      ...(content.dtos as IConditionTypes[])
                          .filter((item) => item.isCalculate === true)
                          .map((item) => ({
                              value: item.code,
                              label: item.columnName,
                              typeCode: item.reportName
                          })),
                      ...(content.totalReport as IOtherTotalReport[]).map((item) => ({
                          value: item.id,
                          label: item.text,
                          typeCode: item.reportName
                      }))
                  ])
                : setColumnsToSum(
                      (content as IConditionTypes[])
                          .filter((item) => item.isCalculate === true)
                          .map((item) => ({
                              value: item.code,
                              label: item.columnName,
                              typeCode: item.reportName
                          }))
                  );
        } else {
            setColumnsToSum([]);
            setConditions([]);
        }
    };

    const handleChangeReportName = (data: any) => {
        setReportId({ otherReportId: '', mainReportId: data?.value });
        methods.setValue('selectMultipleReport', false);
        methods.setValue('otherDataSource', []);
        setDisplayMultipleReport(false);
    };
    const hanldeChangeMultipleReport = (e: React.ChangeEvent<HTMLInputElement>) => {
        setDisplayMultipleReport(e.target.checked);
        methods.setValue('otherDataSource', []);
        setReportId((prev) => ({ ...prev, otherReportId: '' }));
    };

    const handleChangeSelectOtherReports = (data: string[]) => {
        setReportId((prev) => ({ ...prev, otherReportId: data.length ? data.join(',') : '' }));
    };

    useEffect(() => {
        setReportId({ mainReportId: reportDetail?.reportId as string, otherReportId: reportDetail?.otherDataSource as string });
        setDisplayMultipleReport(!!reportDetail?.otherDataSource);
        methods.setValue('isCalculation', !!reportName?.find((item) => item.value === reportDetail?.reportId)?.isCalculateVisible);
        dispatch(getReportName());
        dispatch(getLanguage());
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [open]);

    useEffect(() => {
        getConditionTypes(reportId?.mainReportId as string, locale, reportId?.otherReportId);
    }, [reportId, locale]);

    useEffect(() => {
        if (isEdit) {
            setFormReset({
                ...reportDetail,
                reportId: reportName?.find((item) => item.value === reportDetail.reportId) as IOption,
                conditions: reportDetail?.conditions.map((array) =>
                    array.map((item) => ({
                        ...item,
                        value:
                            typeof item.value === 'string'
                                ? item.type === 'select'
                                    ? item.value.split(',')
                                    : item.type === 'date'
                                    ? dateFormatStringToDate(item.value)
                                    : item.value.includes(':')
                                    ? ''
                                    : item.value
                                : item.value,
                        conditionSelecteted: true,
                        minValue:
                            typeof item.value === 'string' && item.value.includes(':')
                                ? item.type === 'number'
                                    ? item.value.split(':')[0]
                                    : item.type === 'date'
                                    ? dateFormatStringToDate(item.value.split(':')[0])
                                    : undefined
                                : undefined,
                        maxValue:
                            typeof item.value === 'string' && item.value.includes(':')
                                ? item.type === 'number'
                                    ? item.value.split(':')[1]
                                    : item.type === 'date'
                                    ? dateFormatStringToDate(item.value.split(':')[1])
                                    : undefined
                                : undefined
                    }))
                ),
                calculationInputs:
                    typeof reportDetail.calculationInputs === 'string' && !!reportDetail.calculationInputs
                        ? converStringToCalculationInputsObject(reportDetail.calculationInputs).map((item) => ({
                              sign: item.sign,
                              code: {
                                  value: item.code as string,
                                  label: columnsToSum?.find((children) => children.value === item.code)?.label as string
                              }
                          }))
                        : [],
                isCalculation: reportDetail.calculationInputs ? true : false,
                otherDataSource: reportId?.otherReportId ? String(reportId.otherReportId).split(',') : [],
                selectMultipleReport: displayMultipleReport,
                style: {
                    ...reportDetail.style,
                    backgroundColor: reportDetail?.style?.backgroundColor || '#ffffff',
                    color: reportDetail?.style?.color || '#000000'
                },
                newText: (reportDetail.languageConfigs?.map((item) => item.languageCode) as string[]) || []
            });
        } else {
            setFormReset(flexibleReportConfigDetail);
        }
    }, [columnsToSum, reportName, isEdit, reportDetail, reportId, displayMultipleReport]);

    return (
        <Modal isOpen={open} title={isEdit ? 'edit-config' : 'add-config'} onClose={handleCloseModal} keepMounted={false} maxWidth="md">
            <FormProvider formReturn={methods} formReset={formReset} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing} padding={2}>
                    <Grid item xs={12} lg={6}>
                        <Autocomplete
                            options={reportName as IOption[]}
                            name="reportId"
                            label={<FormattedMessage id={Flexible_reporting_configuration + 'report-name'} />}
                            isDefaultAll
                            handleChange={handleChangeReportName}
                            disabled={isEdit}
                            required
                        />
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Grid container alignItems="center">
                            <Grid item xs={2.5} mt={2}>
                                <Box display="flex" flexDirection="row" alignItems="center">
                                    <Checkbox name="selectMultipleReport" handleChange={hanldeChangeMultipleReport} />
                                    <Label label={<FormattedMessage id={Flexible_reporting_configuration + 'other'} />} sx={{ ml: -2 }} />
                                </Box>
                            </Grid>
                            <Grid item xs={9.5} sx={{ display: displayMultipleReport && reportId?.mainReportId ? 'block' : 'none' }}>
                                <MultipleSelect
                                    name="otherDataSource"
                                    selects={reportName as IOption[]}
                                    label={<FormattedMessage id={Flexible_reporting_configuration + 'other-data-sources'} />}
                                    isMultipleLanguage={false}
                                    placeholder="select-option"
                                    handleChange={handleChangeSelectOtherReports}
                                    required
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item xs={12} lg={6}>
                        <Input
                            name="defaultTextNameENG"
                            disabled={isEdit}
                            label={<FormattedMessage id={Flexible_reporting_configuration + 'default-text-en'} />}
                            required
                        />
                    </Grid>

                    <Grid item xs={12} lg={6} display={isEdit ? 'block' : 'none'}>
                        <MultipleSelect
                            name="newText"
                            selects={laguageConfigList.map((item) => ({ value: item.languageCode, label: item.languageName })) as IOption[]}
                            label={<FormattedMessage id={Flexible_reporting_configuration + 'new-text'} />}
                            isMultipleLanguage={false}
                            placeholder="select-option"
                            handleChange={handleChangeLanguage}
                        />
                    </Grid>
                    {languageConfigs.map((item, index) => (
                        <Grid item xs={12} lg={6}>
                            <Input
                                name={`languageConfigs.${index}.newText`}
                                label={
                                    <Typography>
                                        <FormattedMessage id={Flexible_reporting_configuration + 'new-text'} />{' '}
                                        {laguageConfigList.find((lang) => lang.languageCode === item.languageCode)?.languageName}
                                    </Typography>
                                }
                            />
                        </Grid>
                    ))}

                    <Grid item xs={12} lg={12}>
                        <Input
                            textFieldProps={{
                                multiline: true,
                                rows: 2
                            }}
                            name="note"
                            label={<FormattedMessage id="note" />}
                        />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Box display="flex" flexDirection="row" alignItems="center">
                            <Checkbox name="show" />
                            <Label label={<FormattedMessage id={Flexible_reporting_configuration + 'show'} />} sx={{ ml: -2 }} />
                        </Box>
                    </Grid>

                    <Grid item xs={12} lg={12}>
                        {reportName?.find((item) => item.value === reportId?.mainReportId)?.isCalculateVisible && (
                            <Sum columnsToSum={columnsToSum ? columnsToSum : []} />
                        )}
                        {reportName?.find((item) => item.value === reportId?.mainReportId)?.isConditionVisible && (
                            <Condition columnsToSum={conditions ? conditions : []} />
                        )}
                    </Grid>

                    {/* style */}
                    <Grid item xs={12} lg={8}>
                        <Typography sx={{ color: '#333', display: 'flex', gap: 1, fontWeight: 600, alignItems: 'center' }} variant="h3">
                            <FormattedMessage id={Flexible_reporting_configuration + 'style'} />
                            <Tooltip title={<FormattedMessage id={Flexible_reporting_configuration + 'reset-style'} />}>
                                <Button
                                    onClick={() => {
                                        methods.setValue('style', flexibleReportConfigDetail.style);
                                        setStyle({
                                            bold: false,
                                            italic: false,
                                            underlined: false
                                        });
                                    }}
                                >
                                    <RotateLeftIcon />
                                </Button>
                            </Tooltip>
                        </Typography>
                        <Divider sx={{ width: '120%' }} />

                        <Grid container spacing={1} mt={1} alignItems="center">
                            <Grid item xs={1}>
                                <ButtonBase
                                    onClick={() => {
                                        setStyle((prev) => ({ ...prev, bold: !prev.bold }));
                                    }}
                                    sx={{ background: style.bold ? '#C2BDBD' : '#ffffff', padding: 0.3 }}
                                >
                                    <Tooltip title="bold">
                                        <FormatBoldIcon sx={{ fontSize: 20 }} />
                                    </Tooltip>
                                </ButtonBase>
                            </Grid>

                            <Grid item xs={1}>
                                <ButtonBase
                                    onClick={() => {
                                        setStyle((prev) => ({ ...prev, italic: !prev.italic }));
                                    }}
                                    sx={{ background: style.italic ? '#C2BDBD' : '#ffffff', padding: 0.3 }}
                                >
                                    <Tooltip title="italic">
                                        <FormatItalicOutlinedIcon sx={{ fontSize: 20 }} />
                                    </Tooltip>
                                </ButtonBase>
                            </Grid>

                            <Grid item xs={1}>
                                <ButtonBase
                                    onClick={() => {
                                        setStyle((prev) => ({ ...prev, underlined: !prev.underlined }));
                                    }}
                                    sx={{ background: style.underlined ? '#C2BDBD' : '#ffffff', padding: 0.3 }}
                                >
                                    <Tooltip title="underline">
                                        <FormatUnderlinedIcon sx={{ fontSize: 20 }} />
                                    </Tooltip>
                                </ButtonBase>
                            </Grid>

                            <Grid item xs={1}>
                                <Input
                                    name="style.color"
                                    type="color"
                                    sx={{
                                        input: {
                                            height: '13px'
                                        }
                                    }}
                                    textFieldProps={{
                                        variant: 'standard',
                                        InputProps: {
                                            disableUnderline: true,
                                            sx: {
                                                width: '25px',
                                                height: '2px'
                                            }
                                        }
                                    }}
                                    label={
                                        <Tooltip title="font color" sx={{ ml: 0.8, color: '#333333' }}>
                                            <Typography fontSize={18}>A</Typography>
                                        </Tooltip>
                                    }
                                />
                            </Grid>
                            <Grid item xs={1}>
                                <Input
                                    name="style.backgroundColor"
                                    type="color"
                                    sx={{
                                        input: {
                                            height: '14px'
                                        }
                                    }}
                                    textFieldProps={{
                                        variant: 'standard',
                                        InputProps: {
                                            disableUnderline: true,
                                            sx: {
                                                width: '25px',
                                                height: '2px'
                                            }
                                        }
                                    }}
                                    styleLabel={{
                                        ml: 0.3,
                                        mb: -0.5
                                    }}
                                    label={
                                        <Tooltip title="background color">
                                            <FormatColorFillIcon sx={{ fontSize: 20, color: '#333333' }} />
                                        </Tooltip>
                                    }
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>

                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={Flexible_reporting_configuration + 'cancel'} />
                            </Button>
                            <LoadingButton loading={loading} variant="contained" type="submit">
                                {isEdit ? (
                                    <FormattedMessage id={Flexible_reporting_configuration + 'submit'} />
                                ) : (
                                    <FormattedMessage id={Flexible_reporting_configuration + 'add'} />
                                )}
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddorEditFlexibleReportConfig;
