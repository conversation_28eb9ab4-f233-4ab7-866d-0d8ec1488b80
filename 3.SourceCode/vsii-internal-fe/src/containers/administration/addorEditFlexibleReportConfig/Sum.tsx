import { useEffect } from 'react';
import HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';
import DataSaverOnOutlinedIcon from '@mui/icons-material/DataSaverOnOutlined';
import { Box, ButtonBase, Divider, Popper, Typography } from '@mui/material';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

import { Autocomplete, Checkbox, Select } from 'components/extended/Form';
import { SIGN_CAlCULATE, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { IOption } from 'types';
interface ISumProps {
    columnsToSum: IOption[];
}

const Sum = ({ columnsToSum }: ISumProps) => {
    const { Flexible_reporting_configuration } = TEXT_CONFIG_SCREEN.administration.flexibleReport;

    const methods = useFormContext();

    const {
        fields: calculationInputs,
        append,
        remove,
        replace
    } = useFieldArray({
        control: methods.control,
        name: 'calculationInputs'
    });

    const handleAddColumnValue = () => {
        append({
            sign: '+',
            code: columnsToSum ? columnsToSum[0] : {}
        });
    };

    useEffect(() => {
        const calculationInputs: { sign: string; code: IOption }[] = methods.getValues('calculationInputs');
        if (
            (!methods.getValues('id') && columnsToSum?.length && calculationInputs.length === 1) ||
            (methods.getValues('id') && !methods.getValues('isCalculation')) ||
            calculationInputs.filter((item: any) => !item.code?.label).length
        ) {
            replace([
                {
                    sign: '+',
                    code: columnsToSum[0]
                }
            ]);
        }
    }, [replace, methods, calculationInputs.length, columnsToSum]);

    return (
        <Box mt={2}>
            <Box>
                <Typography sx={{ color: '#333', display: 'flex', gap: 1, fontWeight: 600 }} variant="h3">
                    <FormattedMessage id={Flexible_reporting_configuration + 'sum'} />
                </Typography>
                <Divider />

                <Box display="flex" gap={1} mt={3} sx={{ overflowX: 'auto' }} pb={2}>
                    <Checkbox name="isCalculation" isControl />
                    {calculationInputs?.map((item, index) => (
                        <Box key={item.id} display="flex" gap={1.25} alignItems="center">
                            <Box width={index !== 0 ? 60 : 0}>
                                {index !== 0 && <Select name={`calculationInputs.${index}.sign`} selects={SIGN_CAlCULATE} />}
                            </Box>
                            <Box width={150}>
                                <Autocomplete
                                    options={columnsToSum ? columnsToSum : []}
                                    name={`calculationInputs.${index}.code`}
                                    groupBy={(option: IOption) => option.typeCode}
                                    isDefaultAll
                                    PopperComponent={(props) => (
                                        <Popper {...props} style={{ width: 250 }}>
                                            {props.children}
                                        </Popper>
                                    )}
                                    isDisableClearable
                                />
                            </Box>

                            {index !== 0 && (
                                <ButtonBase onClick={() => remove(index)}>
                                    <HighlightOffOutlinedIcon />
                                </ButtonBase>
                            )}
                        </Box>
                    ))}
                    <ButtonBase onClick={handleAddColumnValue}>
                        <DataSaverOnOutlinedIcon />
                    </ButtonBase>
                </Box>
            </Box>
        </Box>
    );
};

export default Sum;
