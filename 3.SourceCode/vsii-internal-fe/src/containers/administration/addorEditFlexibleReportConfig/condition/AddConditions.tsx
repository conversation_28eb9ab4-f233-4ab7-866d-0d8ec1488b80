import { useEffect, useMemo, useRef, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Grid } from '@mui/material';
import { Box } from '@mui/system';

import InequalitiesCondition from 'components/condition/InequalitiesCondition';
import { ICondition, IConditionTypes } from 'types/flexible-report';
import EqualConditions from 'components/condition/EqualConditions';
import ConditionType from 'containers/search/ConditionType';
import { Checkbox } from 'components/extended/Form';
import sendRequest from 'services/ApiService';
import { IOption } from 'types';
import Api from 'constants/Api';
import TextCondition from 'components/condition/TextCondition';

type AddConditionsProps = {
    nestingIndex: number;
    columnsToSum?: IConditionTypes[];
};

const AddConditions = ({ nestingIndex, columnsToSum }: AddConditionsProps) => {
    const method = useFormContext();
    const { fields, append, remove } = useFieldArray({
        control: method.control,
        name: `conditions.${nestingIndex}`
    });

    const [width, setWidth] = useState(method.getValues('id') ? 500 : 0);

    const prevColumnsToSumLength = useRef(columnsToSum?.length);

    const conditionTypes: IOption[] = useMemo(() => {
        if (columnsToSum) {
            return columnsToSum?.map((item) => ({
                value: item.code,
                label: item.columnName
            }));
        } else return [];
    }, [columnsToSum]);

    const [conditionSelecteted, setConditionSelecteted] = useState<
        {
            label: string;
            code: string;
            inputType: string;
            options?: IOption[];
            isPercentage: boolean;
        }[]
    >([]);

    const getOptionsConditions = async (key: { name: string }) => {
        const res = await sendRequest(Api.flexible_report.getConditionOptions, key);
        if (res.status) {
            return res.result.content;
        } else return [];
    };

    const handleChangeConditionType = async (conditionType: string) => {
        const conditionDetail = columnsToSum?.find((item) => item.code === conditionType);
        let condition = {};
        switch (conditionDetail?.inputType) {
            case 'select':
                condition = {
                    code: conditionDetail.code,
                    compare: 'is',
                    value: [],
                    type: conditionDetail?.inputType,
                    conditionSelecteted: true,
                    key: conditionDetail.key,
                    isPercentage: conditionDetail.isPercentage
                };
                const options: IOption[] = await getOptionsConditions({ name: conditionDetail.key });
                setConditionSelecteted((prev) => [
                    ...prev,
                    {
                        inputType: conditionDetail?.inputType,
                        label: conditionDetail.columnName,
                        code: conditionDetail.code,
                        options: options,
                        isPercentage: conditionDetail.isPercentage
                    }
                ]);
                break;
            case 'number':
                condition = {
                    code: conditionDetail.code,
                    compare: '>=',
                    value: 0,
                    type: conditionDetail?.inputType,
                    conditionSelecteted: true,
                    key: conditionDetail.key,
                    isPercentage: conditionDetail.isPercentage
                };
                setConditionSelecteted((prev) => [
                    ...prev,
                    {
                        inputType: conditionDetail?.inputType,
                        label: conditionDetail.columnName,
                        code: conditionDetail.code,
                        isPercentage: conditionDetail.isPercentage
                    }
                ]);
                break;
            case 'text':
                condition = {
                    code: conditionDetail.code,
                    compare: 'is',
                    value: '',
                    type: conditionDetail?.inputType,
                    conditionSelecteted: true,
                    key: conditionDetail.key,
                    isPercentage: conditionDetail.isPercentage
                };
                setConditionSelecteted((prev) => [
                    ...prev,
                    {
                        inputType: conditionDetail?.inputType,
                        label: conditionDetail.columnName,
                        code: conditionDetail.code,
                        isPercentage: conditionDetail.isPercentage
                    }
                ]);
                break;
            case 'date':
                condition = {
                    code: conditionDetail.code,
                    compare: '>=',
                    value: new Date(),
                    type: conditionDetail?.inputType,
                    conditionSelecteted: true,
                    key: conditionDetail.key,
                    isPercentage: conditionDetail.isPercentage
                };
                setConditionSelecteted((prev) => [
                    ...prev,
                    {
                        inputType: conditionDetail?.inputType,
                        label: conditionDetail.columnName,
                        code: conditionDetail.code,
                        isPercentage: conditionDetail.isPercentage
                    }
                ]);
                break;
            default:
                break;
        }
        append(condition);
        setWidth(500);
    };

    useEffect(() => {
        if (prevColumnsToSumLength.current !== columnsToSum?.length) {
            remove();
            setConditionSelecteted([]);
        }
    }, [columnsToSum?.length, remove, prevColumnsToSumLength]);

    useEffect(() => {
        if (method.getValues('id')) {
            const conditionsArray: ICondition[] = method.getValues(`conditions.${nestingIndex}`);
            if (conditionsArray?.length) {
                const fetchOptions = async () => {
                    const updatedConditions: {
                        label: string;
                        code: string;
                        inputType: string;
                        options?: IOption[];
                        isPercentage: boolean;
                    }[] = await Promise.all(
                        conditionsArray.map(async (condition) => {
                            const item = (columnsToSum || []).find((item) => item.code === condition.code);
                            return {
                                code: item ? item.code : condition.code,
                                inputType: item ? item.inputType : condition.type,
                                label: item ? item.columnName : condition.name,
                                options: item && item.inputType === 'select' ? await getOptionsConditions({ name: item.key }) : [],
                                isPercentage: item ? item.isPercentage : condition.isPercentage
                            };
                        })
                    );
                    setConditionSelecteted(updatedConditions);
                };
                fetchOptions();
            } else {
                setConditionSelecteted([]);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [nestingIndex, columnsToSum]); // Async options fetching

    return (
        <>
            <Box>
                <Box mb={2} width={100}>
                    <ConditionType
                        onChange={handleChangeConditionType}
                        selectedValues={conditionSelecteted?.map((item) => item.code as string)}
                        conditionTypes={conditionTypes ? conditionTypes : []}
                        name={`conditionsArray.${nestingIndex}`}
                    />
                </Box>
                <Box>
                    {fields.map((item, index) => (
                        <Box key={item.id} minWidth={width}>
                            {conditionSelecteted[index]?.inputType === 'select' && (
                                <Grid container spacing={1} mb={1}>
                                    <Grid item xs={3}>
                                        <Checkbox
                                            name={`conditions.${nestingIndex}.${index}.conditionSelecteted`}
                                            label={conditionSelecteted[index].label}
                                            isControl
                                        />
                                    </Grid>
                                    <Grid item xs={9}>
                                        <EqualConditions
                                            conditionName={`conditions.${nestingIndex}.${index}.compare`}
                                            conditionSelection={
                                                conditionSelecteted[index].options?.length
                                                    ? (conditionSelecteted[index].options as IOption[])
                                                    : []
                                            }
                                            conditionValueName={`conditions.${nestingIndex}.${index}.value`}
                                        />
                                    </Grid>
                                </Grid>
                            )}
                            {conditionSelecteted[index]?.inputType === 'number' && (
                                <Grid container spacing={1} mb={1}>
                                    <Grid item xs={3}>
                                        <Checkbox
                                            name={`conditions.${nestingIndex}.${index}.conditionSelecteted`}
                                            label={conditionSelecteted[index].label}
                                        />
                                    </Grid>
                                    <Grid item xs={9}>
                                        <InequalitiesCondition
                                            conditionName={`conditions.${nestingIndex}.${index}.compare`}
                                            conditionValueName={`conditions.${nestingIndex}.${index}.value`}
                                            minValueName={`conditions.${nestingIndex}.${index}.minValue`}
                                            maxValueName={`conditions.${nestingIndex}.${index}.maxValue`}
                                            isPercentage={conditionSelecteted[index].isPercentage}
                                        />
                                    </Grid>
                                </Grid>
                            )}

                            {conditionSelecteted[index]?.inputType === 'date' && (
                                <Grid container spacing={1} mb={1}>
                                    <Grid item xs={3}>
                                        <Checkbox
                                            name={`conditions.${nestingIndex}.${index}.conditionSelecteted`}
                                            label={conditionSelecteted[index].label}
                                        />
                                    </Grid>
                                    <Grid item xs={9}>
                                        <InequalitiesCondition
                                            conditionName={`conditions.${nestingIndex}.${index}.compare`}
                                            conditionValueName={`conditions.${nestingIndex}.${index}.value`}
                                            minValueName={`conditions.${nestingIndex}.${index}.minValue`}
                                            maxValueName={`conditions.${nestingIndex}.${index}.maxValue`}
                                            isDatePicker
                                        />
                                    </Grid>
                                </Grid>
                            )}

                            {conditionSelecteted[index]?.inputType === 'text' && (
                                <Grid container spacing={1} mb={1}>
                                    <Grid item xs={3}>
                                        <Checkbox
                                            name={`conditions.${nestingIndex}.${index}.conditionSelecteted`}
                                            label={conditionSelecteted[index].label}
                                        />
                                    </Grid>
                                    <Grid item xs={9}>
                                        <TextCondition
                                            conditionName={`conditions.${nestingIndex}.${index}.compare`}
                                            valueName={`conditions.${nestingIndex}.${index}.value`}
                                        />
                                    </Grid>
                                </Grid>
                            )}
                        </Box>
                    ))}
                </Box>
            </Box>
        </>
    );
};

export default AddConditions;
