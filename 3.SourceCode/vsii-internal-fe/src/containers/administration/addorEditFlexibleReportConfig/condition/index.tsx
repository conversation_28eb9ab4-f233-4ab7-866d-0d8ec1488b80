import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { Box, ButtonBase, Chip, Divider, Typography } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

import { IConditionTypes } from 'types/flexible-report';
import AddConditions from './AddConditions';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IConditionProps {
    columnsToSum?: IConditionTypes[];
}

const Condition = ({ columnsToSum }: IConditionProps) => {
    const { Flexible_reporting_configuration } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    const methods = useFormContext();

    const {
        fields: condition,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'conditions'
    });

    const handleOrCondition = () => {
        append({ conditions: [] });
    };

    const handleDeleteCondition = (index: number) => {
        remove(index);
    };

    return (
        <Box mt={2}>
            <>
                <Box minWidth="100%">
                    <Typography sx={{ color: '#333', display: 'flex', gap: 1, fontWeight: 600 }} variant="h3">
                        <FormattedMessage id={Flexible_reporting_configuration + 'conditions'} />
                    </Typography>
                </Box>

                <Box
                    display="flex"
                    sx={{
                        overflowX: 'auto'
                    }}
                    minWidth="100%"
                >
                    {condition.map((item, index) => (
                        <Box key={item.id} maxWidth={600}>
                            <Divider sx={{ minWidth: '130%', mb: 2 }}></Divider>
                            <>
                                <Box display="inline-flex" justifyContent="space-between" width="100%" gap={2}>
                                    <AddConditions nestingIndex={index} columnsToSum={columnsToSum} />
                                    {index !== 0 && (
                                        <ButtonBase onClick={() => handleDeleteCondition(index)}>
                                            <HighlightOffIcon sx={{ fontSize: 25 }} />
                                        </ButtonBase>
                                    )}
                                    {index === condition.length - 1 ? (
                                        <ButtonBase onClick={handleOrCondition}>
                                            <AddCircleOutlineIcon sx={{ fontSize: 25 }} />
                                        </ButtonBase>
                                    ) : (
                                        <Divider orientation="vertical" variant="middle" flexItem>
                                            <Chip label="Or" size="small" />
                                        </Divider>
                                    )}
                                </Box>
                            </>
                        </Box>
                    ))}
                </Box>
            </>
        </Box>
    );
};

export default Condition;
