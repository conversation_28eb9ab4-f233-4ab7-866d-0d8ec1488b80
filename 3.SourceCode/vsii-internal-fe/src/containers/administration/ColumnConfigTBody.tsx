import React from 'react';
import { Icon<PERSON>utton, TableBody, TableCell, TableRow, Tooltip, Stack } from '@mui/material';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { FormattedMessage } from 'react-intl';

import { checkAllowedPermission } from 'utils/authorization';
import { Checkbox } from 'components/extended/Form';
import { IColumnConfig } from 'types/flexible-report';
import { PERMISSIONS } from 'constants/Permission';

interface Props {
    data: IColumnConfig[] | undefined;
    handleOpen: (exchange: IColumnConfig) => void;
    pageSize: number;
    pageNumber: number;
}

const ColumnConfigTBody: React.FC<Props> = ({ data, handleOpen, pageNumber, pageSize }) => {
    const { flexibleReportingConfigPermission } = PERMISSIONS.admin;

    return (
        <TableBody>
            {data?.map((item, key) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * (pageNumber - 1) + key + 1}</TableCell>
                    <TableCell>{item.reportName}</TableCell>
                    <TableCell>{item.columnName}</TableCell>
                    <TableCell sx={{ textTransform: 'capitalize' }}>{item.inputType}</TableCell>
                    <TableCell>
                        <Checkbox name="" isControl={false} disabled valueChecked={item.isCalculate} />
                    </TableCell>
                    <TableCell>
                        {checkAllowedPermission(flexibleReportingConfigPermission.columnConfig.edit) && (
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(item)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        )}
                    </TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ColumnConfigTBody;
