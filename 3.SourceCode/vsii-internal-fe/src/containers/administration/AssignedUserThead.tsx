import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const AssignedUserThead = () => {
    const { manage_group } = TEXT_CONFIG_SCREEN.administration;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_group + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'report-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'user-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'first-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'last-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_group + 'title'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default AssignedUserThead;
