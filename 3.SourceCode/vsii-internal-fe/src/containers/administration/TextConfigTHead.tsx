import { FormattedMessage } from 'react-intl';

import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const TextConfigTHead = () => {
    const { language_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    return (
        <TableHead>
            <TableRow>
                <TableCell sx={{ width: '5%' }}>
                    <FormattedMessage id={language_config + 'no'} />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id={language_config + 'screen-name'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={language_config + 'default-text-en'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={language_config + 'new-text'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={language_config + 'note'} />
                </TableCell>
                <TableCell align="center" sx={{ width: '10%' }}>
                    <FormattedMessage id={language_config + 'actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default TextConfigTHead;
