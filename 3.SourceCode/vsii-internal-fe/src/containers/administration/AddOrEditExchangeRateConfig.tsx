// material-ui
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid, Stack } from '@mui/material';

// project imports
import { FormProvider, Input, NumericFormatCustom } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { E_SCREEN_SALES_YEAR, MONEY_PLACEHOLDER, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { SalesYear } from 'containers/search';
import { addOrEditExchangeRateConfigFormDefault, addOrEditExchangeRateConfigSchema } from 'pages/administration/Config';

// third party
import { FormattedMessage } from 'react-intl';
import { gridSpacing } from 'store/constant';
import { IExchangeRate } from 'types';
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';

interface IAddOrEditExchangeRateConfigProps {
    open: boolean;
    isEdit?: boolean;
    loading?: boolean;
    exchangeRate: IExchangeRate;
    handleClose: () => void;
    handleAdd: (value: IExchangeRate) => void;
    handleEdit: (value: IExchangeRate) => void;
    year?: number;
}

const AddOrEditExchangeRateConfig = (props: IAddOrEditExchangeRateConfigProps) => {
    const { open, isEdit, handleClose, loading, exchangeRate, handleAdd, handleEdit, year } = props;

    const { exchange_rate_config } = TEXT_CONFIG_SCREEN.administration;
    const { userInfo } = useAppSelector(authSelector);

    const handleSubmit = (value: any) => {
        const payload = {
            ...value,
            userUpdate: userInfo?.userName,
            idHexString: isEdit ? exchangeRate?.idHexString : ''
        };
        if (isEdit) {
            handleAdd(payload);
        } else {
            handleEdit(payload);
        }
    };

    return (
        <Modal
            isOpen={open}
            title={isEdit ? exchange_rate_config + 'edit-exchange-rate-config' : exchange_rate_config + 'add-exchange-rate-config'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="sm"
        >
            <FormProvider
                form={{
                    defaultValues: { ...addOrEditExchangeRateConfigFormDefault, year },
                    resolver: yupResolver(addOrEditExchangeRateConfigSchema)
                }}
                formReset={exchangeRate}
                onSubmit={handleSubmit}
            >
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12} lg={12}>
                        <SalesYear screen={E_SCREEN_SALES_YEAR.BIDDING} disabled={isEdit} required />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Input name="currency" label={<FormattedMessage id={exchange_rate_config + 'currency'} />} required />
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Input
                            textFieldProps={{
                                placeholder: MONEY_PLACEHOLDER,
                                InputProps: {
                                    inputComponent: NumericFormatCustom as any
                                }
                            }}
                            name="exchangeRate"
                            label={<FormattedMessage id={exchange_rate_config + 'exchange-rate'} />}
                            required
                        />
                    </Grid>
                </Grid>
                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={exchange_rate_config + 'cancel'} />
                            </Button>
                            <LoadingButton loading={loading} variant="contained" type="submit">
                                {isEdit ? <FormattedMessage id={exchange_rate_config + 'submit'} /> : <FormattedMessage id="add" />}
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditExchangeRateConfig;
