import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ManageProjectThead = () => {
    const { projectPermission } = PERMISSIONS.admin;

    const { manage_project } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_project + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'project-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'project-manager'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'billable'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'project-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'start-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'status'} />
                </TableCell>
                {checkAllowedPermission(projectPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={manage_project + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default ManageProjectThead;
