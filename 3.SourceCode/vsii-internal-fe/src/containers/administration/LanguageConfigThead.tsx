import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const LanguageConfigThead = () => {
    const { cv_config } = TEXT_CONFIG_SCREEN.administration;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={cv_config + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={cv_config + 'language'} />
                </TableCell>

                <TableCell align="center">
                    <FormattedMessage id={cv_config + 'actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default LanguageConfigThead;
