import { SyntheticEvent } from 'react';

// material-ui
import { useTheme } from '@mui/material';

// project imports
import MainCard from 'components/cards/MainCard';
import { Tabs } from 'components/extended/Tabs';
import { gridSpacing } from 'store/constant';
import { ITabs } from 'types';

interface IEditProjectTabsProps {
    tabValue: number;
    handleChangeTab: (event: SyntheticEvent, newTabValue: number) => void;
    projectTab: ITabs[];
}

const EditProjectTabs = (props: IEditProjectTabsProps) => {
    const { tabValue, handleChangeTab, projectTab } = props;
    const theme = useTheme();

    return (
        <MainCard
            sx={{
                border: 'none',
                borderRadius: '0',
                marginBottom: theme.spacing(gridSpacing),
                '& .MuiCardContent-root': {
                    padding: '0 !important'
                }
            }}
        >
            <Tabs tabValue={tabValue} tabList={projectTab} onChange={handleChangeTab} />
        </MainCard>
    );
};

export default EditProjectTabs;
