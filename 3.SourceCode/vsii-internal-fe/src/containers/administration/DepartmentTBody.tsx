import { Icon<PERSON><PERSON><PERSON>, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { FormattedMessage } from 'react-intl';

import { deleteDepartment, getSearchDepartment } from 'store/slice/departmentSlice';
import { IDepartmentFilterConfig } from 'pages/administration/Config';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { checkAllowedPermission } from 'utils/authorization';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { PERMISSIONS } from 'constants/Permission';
import { IDepartment } from 'types/department';
import { DATE_FORMAT } from 'constants/Common';
import { useAppDispatch } from 'app/hooks';
import { dateFormat } from 'utils/date';

interface IDepartmentTBodyProps {
    data: IDepartment[];
    conditions: IDepartmentFilterConfig;
    handleOpen: (department: IDepartment) => void;
}

const DepartmentTBody = (props: IDepartmentTBodyProps) => {
    const { conditions, data, handleOpen } = props;
    const { departmentPermission } = PERMISSIONS.admin;

    const dispatch = useAppDispatch();

    const handleDelete = (department: IDepartment) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-record" />,
                width: '400px',
                handleConfirm: async () => {
                    const resultAction = await dispatch(deleteDepartment(department.id));
                    if (deleteDepartment.fulfilled.match(resultAction)) {
                        if (resultAction.payload?.status) {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result.content,
                                    variant: 'alert',
                                    alert: { color: 'success' }
                                })
                            );
                            await dispatch(getSearchDepartment(conditions));
                        } else {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result?.content || 'Error',
                                    variant: 'alert',
                                    alert: { color: 'error' }
                                })
                            );
                        }
                    }
                    dispatch(closeConfirm());
                }
            })
        );
    };

    return (
        <TableBody>
            {data?.map((value, key) => (
                <TableRow key={key}>
                    <TableCell align="center" sx={{ width: '5%' }}>
                        {conditions.size * (conditions.page - 1) + key + 1}
                    </TableCell>
                    <TableCell sx={{ width: '20%' }}>{value.deptId}</TableCell>
                    <TableCell sx={{ width: '35%' }}>{value.deptName}</TableCell>
                    <TableCell sx={{ width: '15%' }}>{dateFormat(value.lastUpdate, DATE_FORMAT.DDMMYYYY)}</TableCell>
                    <TableCell sx={{ width: '15%' }}>{value.userUpdate}</TableCell>
                    {checkAllowedPermission(departmentPermission.edit) && (
                        <TableCell sx={{ width: '10%' }}>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(value)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip placement="top" title={<FormattedMessage id="delete" />} onClick={() => handleDelete(value)}>
                                    <IconButton aria-label="delete" size="small">
                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default DepartmentTBody;
