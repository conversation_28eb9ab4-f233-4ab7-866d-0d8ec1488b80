import { useEffect, useState } from 'react';
import { FormProvider, Input } from 'components/extended/Form';
import DialogActions from '@mui/material/DialogActions';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import AddIcon from '@mui/icons-material/Add';
import { Button, Box } from '@mui/material';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { defaultFieldsUploadORM, ormReportSchema } from 'pages/administration/Config';
import { getProjectAllForOption } from 'store/slice/monthlyEffortSlice';
import { Department, Months, Years } from 'containers/search';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { authSelector } from 'store/slice/authSlice';
import { convertMonthFromToDate } from 'utils/date';
import sendRequest from 'services/ApiService';
import Modal from 'components/extended/Modal';
import { IOption } from 'types';
import Api from 'constants/Api';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

type UploadORMReportProps = {
    open?: boolean;
    months: IOption[];
    handleChangeYear: (e: any) => void;
    updateAfterUpload: () => void;
};

const UploadORMReport = ({ months, handleChangeYear, updateAfterUpload }: UploadORMReportProps) => {
    const [month, setMonth] = useState({ fromDate: '', toDate: '' });
    const [open, setOpen] = useState(false);
    const { userInfo } = useAppSelector(authSelector);
    const [loading, setLoading] = useState(false);
    const [fileUpload, setFileUpload] = useState<string | File>("'");
    const dispatch = useAppDispatch();
    const methods = useForm({
        defaultValues: defaultFieldsUploadORM,
        resolver: yupResolver(ormReportSchema)
    });

    const { ORMReport } = TEXT_CONFIG_SCREEN.generalReport;
    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };
    const handleFile = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = e.target.files;
        if (files?.length) {
            setFileUpload(files[0]);
        }
    };

    const handleMonthChange = (value: string) => {
        const getMonth = months.filter((month) => {
            return month.value === value;
        });

        return setMonth(convertMonthFromToDate(getMonth[0].label));
    };

    const handleUpload = async (values: any) => {
        setLoading(true);
        const formData = new FormData();
        const uploadORMReprtData = { ...values, file: fileUpload, uploadUser: userInfo?.userName };
        Object.keys(uploadORMReprtData).forEach((key) => {
            const value = uploadORMReprtData[key];
            formData.append(key, value as any);
        });

        const response = await sendRequest(Api.monthly_efford.uploadOrmReport, formData);
        dispatch(
            openSnackbar({
                open: true,
                message: response.status ? response.result.content : response.result.content.message,
                variant: 'alert',
                alert: response.status ? { color: 'success' } : { color: 'error' }
            })
        );
        if (response.status) {
            setOpen(false);
            updateAfterUpload();
            methods.reset();
        }
        setLoading(false);
    };
    useEffect(() => {
        dispatch(getProjectAllForOption({ type: 'month', value: month }));
    }, [dispatch, month]);

    return (
        <>
            <Button
                size="medium"
                startIcon={<AddIcon />}
                onClick={handleClickOpen}
                children={<FormattedMessage id={ORMReport + 'add-new'} />}
                variant="contained"
            />

            <Modal
                title={ORMReport + 'add-new-report'}
                isOpen={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <FormProvider formReturn={methods} onSubmit={handleUpload}>
                    <Box display="flex" flexDirection="column" gap={1}>
                        <Input label={<FormattedMessage id={ORMReport + 'report-name'} />} name="reportName" required />
                        <Department isShowAll={false} required name="department" label={ORMReport + 'department'} />
                        <Years handleChangeYear={handleChangeYear} required label={ORMReport + 'year'} />
                        <Months onChange={handleMonthChange} months={months} required label={ORMReport + 'month'} />
                        <Input
                            name="file"
                            type="file"
                            label={<FormattedMessage id={ORMReport + 'attachment'} />}
                            onChangeInput={handleFile}
                            required
                        />
                    </Box>
                    <DialogActions sx={{ display: 'flex' }}>
                        <LoadingButton color="error" disabled={loading} size="large" onClick={handleClose}>
                            <FormattedMessage id={ORMReport + 'cancel'} />
                        </LoadingButton>

                        <LoadingButton loading={loading} disabled={loading} variant="contained" size="large" type="submit">
                            <FormattedMessage id={ORMReport + 'submit'} />
                        </LoadingButton>
                    </DialogActions>
                </FormProvider>
            </Modal>
        </>
    );
};

export default UploadORMReport;
