import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { Grid } from '@mui/material';

import { flexibleReportingConfigSearchDefault, flexibleReportingConfigSearchSchema } from 'pages/administration/Config';
import { FormProvider, Input, Label } from 'components/extended/Form';
import { FlexibleReportSearchConfig } from 'types/flexible-report';
import ReportNameConfig from 'containers/search/ReportNameConfig';
import { Button } from 'components';

interface IFlexibleReportingConfigTSearchProps {
    formReset?: FlexibleReportSearchConfig;
    handleSearch: (value: FlexibleReportSearchConfig) => void;
}

const FlexibleReportingConfigTSearch = (props: IFlexibleReportingConfigTSearchProps) => {
    const { formReset, handleSearch } = props;

    const methods = useForm({
        defaultValues: flexibleReportingConfigSearchDefault,
        resolver: yupResolver(flexibleReportingConfigSearchSchema)
    });

    const handleSelectReportName = (id: string, isSetDefaultValue?: boolean) => {
        if (isSetDefaultValue) {
            methods.setValue('reportId', id);
            handleSearch(methods.getValues());
        }
    };

    return (
        <FormProvider formReturn={methods} onSubmit={handleSearch} formReset={formReset}>
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <ReportNameConfig
                        isSetDefaultValue={!formReset?.reportId}
                        name="reportId"
                        label={<FormattedMessage id="report-name" />}
                        onChange={handleSelectReportName}
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Input name="textName" label="Text Name" />
                </Grid>
                <Grid item xs={12} lg={3}></Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id="search" />} variant="contained" />
                </Grid>
            </Grid>
        </FormProvider>
    );
};

export default FlexibleReportingConfigTSearch;
