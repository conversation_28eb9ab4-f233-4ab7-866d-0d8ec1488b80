import { TableCell, TableHead, TableRow } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const NonBillablesCongfigTHead = () => {
    const { nonBillablesConfigPermission } = PERMISSIONS.admin;

    const { Non_billables_Config } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center">
                    <FormattedMessage id={Non_billables_Config + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Non_billables_Config + 'config-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Non_billables_Config + 'key'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Non_billables_Config + 'value'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={Non_billables_Config + 'note'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Non_billables_Config + 'last-update'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Non_billables_Config + 'user-update'} />
                </TableCell>
                {checkAllowedPermission(nonBillablesConfigPermission.edit) && <TableCell sx={{ width: '10%' }} />}
            </TableRow>
        </TableHead>
    );
};

export default NonBillablesCongfigTHead;
