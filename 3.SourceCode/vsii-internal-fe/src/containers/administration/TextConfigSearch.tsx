import React from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSearchParams } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { Grid } from '@mui/material';

import { flexibleReportingTextConfigSchema, searchTextConfigFormDefault } from 'pages/administration/Config';
import { FormProvider, Input, Label } from 'components/extended/Form';
import ReportNameConfig from 'containers/search/ReportNameConfig';
import { ISearchTextConfigParams } from 'types/flexible-report';
import { transformObject } from 'utils/common';
import { Button } from 'components';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface Props {
    conditions: ISearchTextConfigParams;
    setConditions: React.Dispatch<React.SetStateAction<ISearchTextConfigParams>>;
}

const ColumnConfigSearch: React.FC<Props> = ({ conditions, setConditions }) => {
    const { language_config } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    const [, setSearchParams] = useSearchParams();

    const methods = useForm({
        defaultValues: searchTextConfigFormDefault,
        resolver: yupResolver(flexibleReportingTextConfigSchema)
    });

    const handleSearch = (value: ISearchTextConfigParams) => {
        const newValue = transformObject({ ...value, page: 1 });
        setSearchParams(newValue as any);
        setConditions(newValue);
    };

    const handleSelectReportName = (id: string, isSetDefaultValue?: boolean) => {
        if (isSetDefaultValue) {
            methods.setValue('flexibleReportId', id);
            handleSearch(methods.getValues());
        }
    };

    return (
        <FormProvider formReturn={methods} formReset={conditions} onSubmit={handleSearch}>
            <Grid container spacing={2}>
                <Grid item xs={12} lg={2.5}>
                    <ReportNameConfig
                        name="flexibleReportId"
                        label={<FormattedMessage id={language_config + 'screen-name'} />}
                        onChange={handleSelectReportName}
                        isSetDefaultValue={!conditions.flexibleReportId}
                    />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <Input name="textName" label={<FormattedMessage id={language_config + 'text-name'} />} />
                </Grid>
                <Grid item xs={12} lg={5}></Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={language_config + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </FormProvider>
    );
};

export default ColumnConfigSearch;
