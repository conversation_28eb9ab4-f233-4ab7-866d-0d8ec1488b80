import React, { useState } from 'react';
import { TableBody } from '@mui/material';

import { ISearchTextConfigParams, ITextConfig } from 'types/flexible-report';
import EditLanguageRow from './EditLanguageRow';

interface Props {
    data: ITextConfig[] | undefined;
    conditions: ISearchTextConfigParams;
    setDataEditLanguage: React.Dispatch<React.SetStateAction<ITextConfig | undefined>>;
}

const TextConfigTBody: React.FC<Props> = ({ data, conditions, setDataEditLanguage }) => {
    const [activeRowIndex, setActiveRowIndex] = useState<number | null>(null);

    const handleButtonClick = (index: number) => {
        setActiveRowIndex(index);
    };
    return (
        <TableBody>
            {data?.map((item, key) => (
                <EditLanguageRow
                    key={key}
                    item={item}
                    conditions={conditions}
                    index={key}
                    setDataEditLanguage={setDataEditLanguage}
                    activeRowIndex={activeRowIndex}
                    handleclickEdit={() => handleButtonClick(key)}
                    handleCancelEdit={() => {
                        setActiveRowIndex(null);
                    }}
                />
            ))}
        </TableBody>
    );
};

export default TextConfigTBody;
