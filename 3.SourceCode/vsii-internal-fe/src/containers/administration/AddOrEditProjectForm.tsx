import { FormattedMessage } from 'react-intl';

// yup
import { yupResolver } from '@hookform/resolvers/yup';

// material-ui
import { LoadingButton } from '@mui/lab';
import { Button, Grid, Stack } from '@mui/material';
import { useAppDispatch, useAppSelector } from 'app/hooks';

// project imports
import { DatePicker, FormProvider, Input, NumericFormatCustom, PercentageFormat } from 'components/extended/Form';
import { MONEY_PLACEHOLDER, PERCENT_PLACEHOLDER, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { Billable, Department, Member, ProjectType, Status } from 'containers/search';
import { searchFormConfig } from 'containers/search/Config';
import { IProjectEditConfig, projectEditSchema } from 'pages/administration/Config';

import { gridSpacing } from 'store/constant';
import { addOrEditProject, editLoadingSelector } from 'store/slice/projectSlice';
import { dateFormat } from 'utils/date';

interface IAddOrEditProjectFormProps {
    isEdit?: boolean;
    dataTable: () => void;
    handleClose: () => void;
    projecFormReset: IProjectEditConfig;
}

const AddOrEditProjectForm = (props: IAddOrEditProjectFormProps) => {
    const { handleClose, projecFormReset, isEdit, dataTable } = props;

    const { manage_project } = TEXT_CONFIG_SCREEN.administration;
    const dispatch = useAppDispatch();
    const editLoading = useAppSelector(editLoadingSelector);

    const handleAddOrEdit = async (value: any) => {
        const { userName } = value;
        await dispatch(
            addOrEditProject({
                ...value,
                userName: userName ? userName.value : null,
                startDate: dateFormat(value.startDate),
                endDate: dateFormat(value.endDate),
                percentageComplete: value.percentageComplete !== null ? +value.percentageComplete / 100 : null
            })
        );
        if (!isEdit) {
            dataTable();
        }
        !editLoading && handleClose();
    };

    return (
        <FormProvider
            form={{ defaultValues: projecFormReset, resolver: yupResolver(projectEditSchema) }}
            formReset={projecFormReset}
            onSubmit={handleAddOrEdit}
        >
            <Grid container spacing={gridSpacing}>
                {isEdit ? (
                    <>
                        <Grid item xs={12} lg={6}>
                            <Input required name="projectId" label={<FormattedMessage id={manage_project + 'project-id'} />} disabled />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            <Input required name="projectName" label={<FormattedMessage id={manage_project + 'project-name'} />} />
                        </Grid>
                    </>
                ) : (
                    <>
                        <Grid item xs={12} lg={6}>
                            <Input required name="projectName" label={<FormattedMessage id={manage_project + 'project-name'} />} />
                        </Grid>
                    </>
                )}

                <Grid item xs={12} lg={6}>
                    <Department required isShowAll={false} label={manage_project + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input name="contractNo" label={<FormattedMessage id={manage_project + 'contract-no'} />} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Billable required label={<FormattedMessage id={manage_project + 'billable'} />} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <ProjectType required label={manage_project + 'project-type'} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <DatePicker required name="startDate" label={<FormattedMessage id={manage_project + 'start-date'} />} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <DatePicker name="endDate" label={<FormattedMessage id={manage_project + 'end-date'} />} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input
                        textFieldProps={{
                            placeholder: MONEY_PLACEHOLDER,
                            InputProps: {
                                inputComponent: NumericFormatCustom as any
                            }
                        }}
                        name="contractSize"
                        label={<FormattedMessage id={manage_project + 'contract-size'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input
                        textFieldProps={{
                            placeholder: MONEY_PLACEHOLDER,
                            InputProps: {
                                inputComponent: NumericFormatCustom as any
                            }
                        }}
                        name="licenseAmount"
                        label={<FormattedMessage id={manage_project + 'license-amount'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input
                        textFieldProps={{
                            placeholder: MONEY_PLACEHOLDER,
                            InputProps: {
                                inputComponent: NumericFormatCustom as any
                            }
                        }}
                        name="projectCostLimit"
                        label={<FormattedMessage id={manage_project + 'cost-limit'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input
                        textFieldProps={{
                            placeholder: MONEY_PLACEHOLDER,
                            InputProps: {
                                inputComponent: NumericFormatCustom as any
                            }
                        }}
                        name="totalQuota"
                        label={<FormattedMessage id={manage_project + 'quota'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input
                        textFieldProps={{
                            placeholder: PERCENT_PLACEHOLDER,
                            InputProps: {
                                inputComponent: PercentageFormat as any
                            }
                        }}
                        name="percentageComplete"
                        label={<FormattedMessage id={manage_project + 'work-completed'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Member
                        name={searchFormConfig.userName.name}
                        label={<FormattedMessage id={manage_project + 'project-manager'} />}
                        isShowAll={false}
                    />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Status required isShowAll={false} isShowProjectStatus />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input name="client" label={<FormattedMessage id={manage_project + 'client'} />} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input name="technology" label={<FormattedMessage id={manage_project + 'technology'} />} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input name="domain" label={<FormattedMessage id={manage_project + 'domain'} />} />
                </Grid>
                <Grid item xs={12} lg={6}>
                    <Input name="effort" label={<FormattedMessage id={manage_project + 'effort'} />} />
                </Grid>
                <Grid item xs={12}>
                    <Input
                        name="desc"
                        label={<FormattedMessage id={manage_project + 'project-description'} />}
                        textFieldProps={{ multiline: true, rows: 5 }}
                    />
                </Grid>
                <Grid item xs={12}>
                    <Input
                        name="note"
                        label={<FormattedMessage id={manage_project + 'note'} />}
                        textFieldProps={{ multiline: true, rows: 5 }}
                    />
                </Grid>
                <Grid item xs={12}>
                    <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <Button color="error" onClick={handleClose}>
                            <FormattedMessage id={manage_project + 'cancel'} />
                        </Button>
                        <LoadingButton loading={editLoading} variant="contained" type="submit">
                            <FormattedMessage id={manage_project + 'submit'} />
                        </LoadingButton>
                    </Stack>
                </Grid>
            </Grid>
        </FormProvider>
    );
};

export default AddOrEditProjectForm;
