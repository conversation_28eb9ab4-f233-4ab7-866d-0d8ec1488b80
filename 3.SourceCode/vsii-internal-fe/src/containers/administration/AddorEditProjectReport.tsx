import { useCallback, useEffect, useState } from 'react';
import { DialogActions, Tab, Tabs, Typography } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';
import { Box } from '@mui/system';

import { convertMonthFromToDate, dateFormat, getCurrentMonth, getCurrentYear, getMonthsOfYear } from 'utils/date';
import SaleandUpSaleTable from 'containers/monthly-effort/AddorEditReportProjectFields/SaleandUpSaleTable';
import ProjectReportInfo from 'containers/monthly-effort/AddorEditReportProjectFields/ProjectReportInfo';
import IssueandRiskTable from 'containers/monthly-effort/AddorEditReportProjectFields/IssueandRiskTable';
import ProgressMilstone from 'containers/monthly-effort/AddorEditReportProjectFields/ProgressMilstone';
import ResourceTable from 'containers/monthly-effort/AddorEditReportProjectFields/ResourceTable';
import NextPlanTable from 'containers/monthly-effort/AddorEditReportProjectFields/NextPlanTable';
import { monthlyEffortProjectReportDefault } from 'pages/monthly-effort/Config';
import { addProjectReportSchema } from 'pages/administration/Config';
import { IMonthlyEffortAddProjectReport, IOption } from 'types';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { FormProvider } from 'components/extended/Form';
import { FIELD_BY_TAB_REPORT, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { getTabValueByFieldError } from 'utils/common';
import { authSelector } from 'store/slice/authSlice';
import { projectReportDefault } from 'pages/Config';
import Modal from 'components/extended/Modal';
import sendRequest from 'services/ApiService';
import Api from 'constants/Api';

interface IAddorEditProjectReportProps {
    projectReport?: IMonthlyEffortAddProjectReport | undefined;
    open: boolean;
    handleClose: () => void;
    updateTable: () => void;
}

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        'aria-controls': `simple-tabpanel-${index}`
    };
}

const AddorEditProjectReport = (props: IAddorEditProjectReportProps) => {
    const { open, projectReport, handleClose, updateTable } = props;
    const [pmName, setPmName] = useState('');

    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const dispatch = useAppDispatch();
    const [value, setValue] = useState(0);
    const [loading, setLoading] = useState(false);
    const { userInfo } = useAppSelector(authSelector);
    const [months, setMonths] = useState<IOption[]>(getMonthsOfYear(monthlyEffortProjectReportDefault.year));
    const [month, setMonth] = useState(convertMonthFromToDate(months[getCurrentMonth() - 1].label));
    const [projects, setProjects] = useState<IOption[]>([]);

    const [formValueProject, setFormValueProject] = useState({
        year: projectReport ? projectReport.projectReportInfo.year : getCurrentYear(),
        month: projectReport ? projectReport.projectReportInfo.month : getCurrentMonth(),
        projectId: projectReport ? projectReport.projectReportInfo.projectId : ''
    });
    const methods = useForm({
        defaultValues: projectReportDefault,
        resolver: yupResolver(addProjectReportSchema)
    });
    const { errors } = methods.formState;

    const getMonthInYears = useCallback(async (y: number) => {
        const monthInYears = await getMonthsOfYear(y);
        return monthInYears;
    }, []);

    const handleChangeYear = (e: any) => {
        const { value: year } = e.target;
        getMonthInYears(year).then((items: IOption[]) => {
            setMonths(items);
        });
        setFormValueProject((value) => ({ ...value, year: year as number }));
    };

    const handleMonthChange = (value: string) => {
        const getMonth = months?.filter((month) => month.value === value);
        setFormValueProject((value) => ({ ...value, month: getMonth[0].value as number }));

        return setMonth(convertMonthFromToDate(getMonth[0].label));
    };

    const handleChangeProject = (item: IOption) => {
        if (item) {
            setFormValueProject((prevState) => ({ ...prevState, projectId: item.value as string }));
        } else {
            setFormValueProject((prevState) => ({ ...prevState, projectId: '' }));
            methods.reset((prev) => ({
                projectReportInfo: {
                    ...projectReportDefault.projectReportInfo,
                    year: prev.projectReportInfo.year,
                    month: prev.projectReportInfo.month
                },
                monthlyReport: {
                    ...projectReportDefault.monthlyReport
                },
                userUpdated: projectReportDefault.userUpdated,
                id: projectReportDefault.id
            }));
        }
    };
    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const handleSubmit = async (values: any) => {
        const projectIdRequest = values.projectReportInfo.projectId.value;
        const formdata: IMonthlyEffortAddProjectReport = {
            id: projectReport?.id,
            ...values,
            projectReportInfo: {
                ...values.projectReportInfo,
                userNamePM: pmName,
                projectId: projectIdRequest as number
            },
            monthlyReport: {
                progressMilestone: {
                    ...values.monthlyReport.progressMilestone,
                    workCompleted: values.monthlyReport.progressMilestone.workCompleted / 100
                },
                ...values.monthlyReport
            },
            userUpdated: userInfo?.userName,
            userCreated: userInfo?.userName
        };

        setLoading(true);
        const response = projectReport
            ? await sendRequest(Api.monthly_efford.editProjectReports(projectReport.id), {
                  ...formdata
              })
            : await sendRequest(Api.monthly_efford.addProjectReports, {
                  ...formdata
              });

        const { result } = response;
        dispatch(
            openSnackbar({
                open: true,
                message: response.status ? result.content : result.content.message,
                variant: 'alert',
                alert: { color: response.status ? 'success' : 'error' }
            })
        );
        if (response.status) {
            handleClose();
        }
        setLoading(false);

        updateTable();
    };
    const getProject = async () => {
        const response = await sendRequest(Api.monthly_efford.geProjectDetailById, formValueProject);
        if (response?.status) {
            const { result } = response;
            const { content } = result;
            setPmName(content.project.projectManager.userName);
            methods.reset(
                !projectReport
                    ? {
                          projectReportInfo: {
                              ...formValueProject,
                              userNamePM: `${content.project.projectManager.firstName} ${content.project.projectManager.lastName}`,
                              endDate: dateFormat(content.project.endDate),
                              projectType: content.project.type,
                              startDate: dateFormat(content.project.startDate),
                              projectId: projects.find((item: IOption) => item.value === formValueProject?.projectId),
                              milestoneApproveEntityList: content.milestoneApproveLastMonth ? content.milestoneApproveLastMonth : []
                          },
                          monthlyReport: {
                              resource: {
                                  totalHC: content.totalHC
                              },
                              progressMilestone: {
                                  workCompleted: content.project.percentageComplete * 100
                              }
                          }
                      }
                    : {
                          ...projectReport,
                          projectReportInfo: {
                              ...projectReport?.projectReportInfo,
                              endDate: dateFormat(content.project.endDate),
                              projectType: content.project.type,
                              startDate: dateFormat(content.project.startDate),
                              userNamePM: `${content.project.projectManager.firstName} ${content.project.projectManager.lastName}`,
                              projectId: projects.find((item: IOption) => item.value === formValueProject?.projectId) || {
                                  value: content.project.projectId as number,
                                  label: content.project.projectName
                              },
                              month: formValueProject.month,
                              year: formValueProject.year,
                              milestoneApproveEntityList: content.milestoneApproveLastMonth ? content.milestoneApproveLastMonth : []
                          },
                          monthlyReport: {
                              ...projectReport?.monthlyReport,
                              resource: {
                                  ...projectReport?.monthlyReport.resource,
                                  totalHC: content.totalHC
                              }
                          }
                      }
            );
        }
    };

    const focusErrors = () => {
        const tabNumber = getTabValueByFieldError(errors, FIELD_BY_TAB_REPORT);

        setValue(tabNumber);
    };

    //get infor project
    useEffect(() => {
        getProject();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [formValueProject, projects, projectReport]);

    useEffect(() => {
        focusErrors();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [errors]);

    return (
        <Modal
            isOpen={open}
            title={
                userInfo?.userName !== projectReport?.userCreated ||
                pmName !== projectReport?.userCreated ||
                userInfo?.role?.find((item) => item.groupId !== 'Admin')
                    ? project_report + 'project-report'
                    : projectReport
                    ? project_report + 'edit-project-report'
                    : project_report + 'add-project-report'
            }
            onClose={handleClose}
            keepMounted={false}
            maxWidth="xl"
        >
            <FormProvider onSubmit={handleSubmit} formReturn={methods}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={value} onChange={handleChange} aria-label="basic tabs example">
                        <Tab label={<FormattedMessage id={project_report + 'project-info'} />} {...a11yProps(0)} />
                        <Tab label={<FormattedMessage id={project_report + 'monthly-report'} />} {...a11yProps(1)} />
                    </Tabs>
                </Box>

                <CustomTabPanel value={value} index={0}>
                    <ProjectReportInfo
                        handleChangeYear={handleChangeYear}
                        handleMonthChange={handleMonthChange}
                        handleChangeProject={handleChangeProject}
                        months={months}
                        disabledProject={projectReport ? true : false}
                        setProjects={setProjects}
                        disableEdit={
                            userInfo?.userName === projectReport?.userCreated ||
                            pmName === projectReport?.userCreated ||
                            userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                            !projectReport
                                ? false
                                : true
                        }
                        methods={methods}
                        month={month}
                    />
                </CustomTabPanel>
                <CustomTabPanel value={value} index={1}>
                    <Typography
                        sx={(theme) => ({
                            color: theme.palette.primary.main,
                            fontWeight: 600
                        })}
                        mb={2}
                    >
                        1. <FormattedMessage id={project_report + 'progress-milestone'} />
                    </Typography>
                    <ProgressMilstone
                        disableEdit={
                            userInfo?.userName === projectReport?.userCreated ||
                            pmName === projectReport?.userCreated ||
                            userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                            !projectReport
                                ? false
                                : true
                        }
                    />

                    <Typography
                        sx={(theme) => ({
                            color: theme.palette.primary.main,
                            fontWeight: 600
                        })}
                        mb={2}
                        mt={2}
                    >
                        2. <FormattedMessage id={project_report + 'issue-risk'} />
                    </Typography>
                    <IssueandRiskTable
                        methods={methods}
                        disableEdit={
                            userInfo?.userName === projectReport?.userCreated ||
                            pmName === projectReport?.userCreated ||
                            userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                            !projectReport
                                ? false
                                : true
                        }
                    />

                    <Typography
                        sx={(theme) => ({
                            color: theme.palette.primary.main,
                            fontWeight: 600
                        })}
                        mb={2}
                        mt={2}
                    >
                        3. <FormattedMessage id={project_report + 'resource'} />
                    </Typography>
                    <ResourceTable
                        methods={methods}
                        disableEdit={
                            userInfo?.userName === projectReport?.userCreated ||
                            pmName === projectReport?.userCreated ||
                            userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                            !projectReport
                                ? false
                                : true
                        }
                    />

                    <Typography
                        sx={(theme) => ({
                            color: theme.palette.primary.main,
                            fontWeight: 600
                        })}
                        mb={2}
                        mt={2}
                    >
                        4. <FormattedMessage id={project_report + 'sale-up-sales'} />
                    </Typography>
                    <SaleandUpSaleTable
                        disableEdit={
                            userInfo?.userName === projectReport?.userCreated ||
                            pmName === projectReport?.userCreated ||
                            userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                            !projectReport
                                ? false
                                : true
                        }
                    />

                    <Typography
                        sx={(theme) => ({
                            color: theme.palette.primary.main,
                            fontWeight: 600
                        })}
                        mb={2}
                        mt={2}
                    >
                        5. <FormattedMessage id={project_report + 'next-plan'} />
                    </Typography>
                    <NextPlanTable
                        methods={methods}
                        disableEdit={
                            userInfo?.userName === projectReport?.userCreated ||
                            pmName === projectReport?.userCreated ||
                            userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                            !projectReport
                                ? false
                                : true
                        }
                    />
                </CustomTabPanel>
                <DialogActions
                    sx={{
                        display:
                            userInfo?.userName === projectReport?.userCreated ||
                            pmName === projectReport?.userCreated ||
                            userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                            !projectReport
                                ? 'flex'
                                : 'none'
                    }}
                >
                    <LoadingButton disabled={loading} color="error" onClick={handleClose}>
                        <FormattedMessage id={project_report + 'cancel'} />
                    </LoadingButton>
                    <LoadingButton loading={loading} disabled={loading} variant="contained" type="submit">
                        <FormattedMessage id={project_report + 'submit'} />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default AddorEditProjectReport;
