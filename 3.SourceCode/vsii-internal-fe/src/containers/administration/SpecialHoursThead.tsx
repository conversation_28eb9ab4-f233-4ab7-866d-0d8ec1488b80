import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const SpecialHoursThead = () => {
    const { specialHoursPermission } = PERMISSIONS.admin;
    const { manage_special_hours } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_special_hours + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_special_hours + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_special_hours + 'from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_special_hours + 'to-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_special_hours + 'special-hours-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_special_hours + 'hours-day'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_special_hours + 'note'} />
                </TableCell>
                {checkAllowedPermission(specialHoursPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={manage_special_hours + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default SpecialHoursThead;
