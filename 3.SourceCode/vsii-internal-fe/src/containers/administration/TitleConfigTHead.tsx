import { TableCell, TableHead, TableRow } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const TitleConfigTHead = () => {
    const { titleConfigPermission } = PERMISSIONS.admin;
    const { title_config } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center" sx={{ width: '5%' }}>
                    <FormattedMessage id={title_config + 'no'} />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id={title_config + 'title'} />
                </TableCell>
                <TableCell sx={{ width: '30%' }}>
                    <FormattedMessage id={title_config + 'title-name'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={title_config + 'last-update'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={title_config + 'user-update'} />
                </TableCell>
                {checkAllowedPermission(titleConfigPermission.edit) && <TableCell sx={{ width: '10%' }} />}
            </TableRow>
        </TableHead>
    );
};

export default TitleConfigTHead;
