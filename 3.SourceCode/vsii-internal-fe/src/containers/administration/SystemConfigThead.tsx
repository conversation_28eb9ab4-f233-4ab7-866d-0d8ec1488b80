import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

//project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const SystemConfigThead = () => {
    const { systemConfigPermission } = PERMISSIONS.admin;

    const { system_config } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={system_config + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={system_config + 'key'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={system_config + 'value'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={system_config + 'note'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={system_config + 'last-update'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={system_config + 'user-update'} />
                </TableCell>
                {checkAllowedPermission(systemConfigPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={system_config + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default SystemConfigThead;
