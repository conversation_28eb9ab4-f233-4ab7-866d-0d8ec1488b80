import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const QuotaUpdateHistoryThead = () => {
    const { manage_project } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_project + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'quota'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'update-date'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default QuotaUpdateHistoryThead;
