import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid } from '@mui/material';

// project import
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { Member, Project, ProjectType, SearchForm, Status } from 'containers/search';
import { searchFormConfig } from 'containers/search/Config';
import { IProjectSearchConfig, projectSearchConfig, projectSearchSchema } from 'pages/administration/Config';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IManageProjectSearchProps {
    formReset: IProjectSearchConfig;
    handleSearch: (value: any) => void;
}

const ManageProjectSearch = (props: IManageProjectSearchProps) => {
    const { formReset, handleSearch } = props;

    const { manage_project } = TEXT_CONFIG_SCREEN.administration;

    return (
        <SearchForm defaultValues={projectSearchConfig} formSchema={projectSearchSchema} handleSubmit={handleSearch} formReset={formReset}>
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.5}>
                    <Project
                        projectAuthorization="false"
                        isDefaultAll={true}
                        isNotStatus
                        label={<FormattedMessage id={manage_project + 'projects'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <ProjectType label={manage_project + 'project-type'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Status isShowProjectStatus label={manage_project + 'status'} />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <Member
                        name={searchFormConfig.projectManager.name}
                        label={<FormattedMessage id={manage_project + 'project-manager'} />}
                        isFindAll
                    />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={manage_project + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ManageProjectSearch;
