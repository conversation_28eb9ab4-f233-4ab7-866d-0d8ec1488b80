// material-ui
import { Grid } from '@mui/material';

// project import
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { E_SCREEN_SALES_YEAR, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { Cur<PERSON>cy, SalesYear, SearchForm } from 'containers/search';
import { exchangeRateSearchConfig, exchangeRateSearchSchema } from 'pages/administration/Config';

// third party
import { FormattedMessage } from 'react-intl';
import { IExchangeRate } from 'types';

interface IExchangeRateConfigSearchSearchProps {
    formReset?: IExchangeRate;
    handleSearch: (value: IExchangeRate) => void;
}

const ExchangeRateConfigSearch = (props: IExchangeRateConfigSearchSearchProps) => {
    const { formReset, handleSearch } = props;

    const { exchange_rate_config } = TEXT_CONFIG_SCREEN.administration;

    return (
        <SearchForm
            defaultValues={exchangeRateSearchConfig}
            formSchema={exchangeRateSearchSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <SalesYear screen={E_SCREEN_SALES_YEAR.ALL} label={exchange_rate_config + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Currency isSelectAll convert="No" label={exchange_rate_config + 'currency'} />
                </Grid>
                <Grid item xs={12} lg={3}></Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={exchange_rate_config + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ExchangeRateConfigSearch;
