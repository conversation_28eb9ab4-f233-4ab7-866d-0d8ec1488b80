import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormattedMessage } from 'react-intl';

const FlexibleReportingTHead = () => {
    const { Flexible_reporting_configuration } = TEXT_CONFIG_SCREEN.administration.flexibleReport;
    return (
        <TableHead>
            <TableRow>
                <TableCell sx={{ width: '5%' }}>
                    <FormattedMessage id={Flexible_reporting_configuration + 'no'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={Flexible_reporting_configuration + 'report-name'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={Flexible_reporting_configuration + 'default-text-en'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={Flexible_reporting_configuration + 'new-text-en'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={Flexible_reporting_configuration + 'note'} />
                </TableCell>
                <TableCell align="center" sx={{ width: '15%' }}>
                    <FormattedMessage id={Flexible_reporting_configuration + 'actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default FlexibleReportingTHead;
