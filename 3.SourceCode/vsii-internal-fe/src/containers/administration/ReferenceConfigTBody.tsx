// material-ui
import { TableBody, TableCell, TableRow, Stack, Tooltip, IconButton } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';

// assets
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

// project import
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';

// third party
import { FormattedMessage } from 'react-intl';
import { IReference } from 'types';

interface IReferenceConfigTBodyProps {
    references: IReference[];
    handleOpen: (language: IReference) => void;
    pageSize: number;
    pageNumber: number;
    handleDelete: (language: IReference) => void;
}

const ReferenceConfigTBody = (props: IReferenceConfigTBodyProps) => {
    const { references, handleOpen, pageNumber, pageSize, handleDelete } = props;
    const { cVConfigReferencePermission } = PERMISSIONS.admin;

    return (
        <TableBody>
            {references?.map((reference: IReference, key: number) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>

                    <TableCell>{reference.fullName}</TableCell>
                    <TableCell>{reference.position}</TableCell>
                    <TableCell>{reference.userUpdate}</TableCell>
                    {checkAllowedPermission(cVConfigReferencePermission.edit) && (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(reference)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip placement="top" title={<FormattedMessage id="delete" />} onClick={() => handleDelete(reference)}>
                                    <IconButton aria-label="delete" size="small">
                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ReferenceConfigTBody;
