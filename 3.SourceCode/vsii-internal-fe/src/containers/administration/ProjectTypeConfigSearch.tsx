import { useSearchParams } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';
import { Grid } from '@mui/material';

import { FormProvider, Input, Label } from 'components/extended/Form';
import { IProjectTypeFilterConfig } from 'pages/administration/Config';
import { searchFormConfig } from 'containers/search/Config';
import { transformObject } from 'utils/common';
import { Button } from 'components';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IProjectTypeConfigSearchProps {
    conditions: IProjectTypeFilterConfig;
    setConditions: React.Dispatch<React.SetStateAction<IProjectTypeFilterConfig>>;
}

const ProjectTypeConfigSearch = ({ conditions, setConditions }: IProjectTypeConfigSearchProps) => {
    const [, setSearchParams] = useSearchParams();

    const { project_type_config } = TEXT_CONFIG_SCREEN.administration;

    const handleSearch = (value: IProjectTypeFilterConfig) => {
        const newValue = transformObject({ ...value, page: 1 });
        setSearchParams(newValue as any);
        setConditions(newValue);
    };

    return (
        <FormProvider
            form={{
                defaultValues: conditions
            }}
            onSubmit={handleSearch}
        >
            <Grid container justifyContent="space-between">
                <Grid item xs={12} lg={3}>
                    <Input
                        name={searchFormConfig.projectType.manage.name}
                        label={<FormattedMessage id={project_type_config + 'project-type'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={project_type_config + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </FormProvider>
    );
};

export default ProjectTypeConfigSearch;
