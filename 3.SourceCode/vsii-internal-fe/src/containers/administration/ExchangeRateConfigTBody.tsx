// material-ui
import { TableBody, TableCell, TableRow, Stack, Tooltip, IconButton } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';

// assets
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

//projects import
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// third party
import { FormattedMessage } from 'react-intl';
import { IExchangeRate } from 'types';
import { formatPrice } from 'utils/common';

interface IExchangeRateConfigProps {
    exchangeRates: IExchangeRate[];
    handleOpen: (exchange: IExchangeRate) => void;
    handleDelete: (exchange: IExchangeRate) => void;
    pageSize: number;
    pageNumber: number;
}

const ExchangeRateConfigTBody = (props: IExchangeRateConfigProps) => {
    const { exchangeRates, handleOpen, handleDelete, pageSize, pageNumber } = props;
    const { exchangeRatePermission } = PERMISSIONS.admin;

    return (
        <TableBody>
            {exchangeRates?.map((exchangeRate: IExchangeRate, key: number) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{exchangeRate.year}</TableCell>
                    <TableCell>{exchangeRate.currency}</TableCell>
                    <TableCell>{formatPrice(exchangeRate.exchangeRate)}</TableCell>
                    {checkAllowedPermission(exchangeRatePermission.edit) && (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(exchangeRate)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip
                                    placement="top"
                                    title={<FormattedMessage id="delete" />}
                                    onClick={() => handleDelete(exchangeRate)}
                                >
                                    <IconButton aria-label="delete" size="small">
                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default ExchangeRateConfigTBody;
