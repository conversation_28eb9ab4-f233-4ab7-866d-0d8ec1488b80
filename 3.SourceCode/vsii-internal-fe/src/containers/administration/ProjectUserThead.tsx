import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ProjectUserThead = () => {
    const { manage_project } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_project + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'user-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'first-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'last-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'headcount'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_project + 'to-date'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={manage_project + 'action'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default ProjectUserThead;
