// material-ui
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Button, DialogActions, Grid, IconButton, Stack, Tooltip } from '@mui/material';
import { DeleteTwoToneIcon } from 'assets/images/icons';
import AddIcon from '@mui/icons-material/Add';

// project imports
import { FormProvider, Input } from 'components/extended/Form';
import Modal from 'components/extended/Modal';
import { addOrEditTechnologylConfigSchema } from 'pages/administration/Config';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { ITechnology } from 'types';
import { checkAndAddIsEdit, convertValueToNewValue } from 'utils/common';
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';

// third party
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditTechnologyConfigProps {
    open: boolean;
    isEdit?: boolean;
    handleClose: () => void;
    loading?: boolean;
    technology?: ITechnology;
    handleAddTechnologyConfig: (technologyConfigValue: ITechnology) => void;
    handleEditTechnologyConfig: (technologyConfigValue: ITechnology) => void;
}

const AddOrEditTechnologyConfig = (props: IAddOrEditTechnologyConfigProps) => {
    const { open, isEdit, handleClose, loading, technology, handleAddTechnologyConfig, handleEditTechnologyConfig } = props;

    const { cv_config } = TEXT_CONFIG_SCREEN.administration;

    const { userInfo } = useAppSelector(authSelector);
    const methods = useForm({
        resolver: yupResolver(addOrEditTechnologylConfigSchema),
        mode: 'all'
    });

    const {
        fields: TechnologyValues,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'skillList'
    });

    const handleAddSkill = () => {
        const skill = { name: '', isDelete: false };
        append(skill);
    };

    const handleSubmit = (value: any) => {
        const skillList = technology?.skillList;
        const skillListNew = value.skillList;
        const skillListNewEdit = checkAndAddIsEdit(skillList!, skillListNew!);

        const skillListNewAdd = convertValueToNewValue(skillListNew!);

        const payloadEdit = {
            techType: value.techType,
            userUpdate: userInfo?.userName,
            skillList: skillListNewEdit
        };
        const payload = { ...value, userUpdate: userInfo?.userName, skillList: isEdit ? skillListNewEdit : skillListNewAdd };

        if (isEdit) {
            handleEditTechnologyConfig(payloadEdit as any);
        } else {
            handleAddTechnologyConfig(payload);
        }
    };

    const handleRemoveSkill = (index: number) => {
        remove(index);
    };
    useEffect(() => {
        methods.reset({ ...technology } as ITechnology);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [technology]);

    return (
        <Modal
            isOpen={open}
            title={isEdit ? cv_config + 'edit-technology-config' : cv_config + 'add-technology-config'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="sm"
        >
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <Grid item xs={12} lg={12}>
                    <Input name="techType" label={<FormattedMessage id={cv_config + 'technology'} />} required />
                </Grid>
                <Grid item xs={12} lg={12} sx={{ marginTop: '10px', color: '#9e9e9e' }}>
                    <span>
                        <FormattedMessage id={`skill`} />
                    </span>
                    {TechnologyValues.map((item, index) => (
                        <Grid item xs={6} lg={6} key={item.id} sx={{ display: 'flex', marginBottom: '20px' }}>
                            <Input name={`skillList.${index}.name`} />
                            {TechnologyValues.length > 1 && (
                                <Grid sx={{ paddingTop: '5px' }}>
                                    <Tooltip
                                        placement="top"
                                        title={<FormattedMessage id={'delete'} />}
                                        onClick={() => handleRemoveSkill(index)}
                                    >
                                        <IconButton aria-label="delete" size="small">
                                            <DeleteTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                        </IconButton>
                                    </Tooltip>
                                </Grid>
                            )}
                        </Grid>
                    ))}
                </Grid>
                <Grid item xs={12} lg={12} sx={{ margin: '30px 0px 50px 0px ' }}>
                    <Button
                        sx={{
                            maxWidth: '550px',
                            width: '100%',
                            border: '1px dashed #007bff',
                            borderRadius: '8px',
                            padding: '8px 16px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                        onClick={() => handleAddSkill()}
                    >
                        <AddIcon sx={{ marginRight: '8px' }} /> <FormattedMessage id={cv_config + 'add-skill'} />
                    </Button>
                </Grid>

                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={cv_config + 'cancel'} />
                            </Button>
                            <LoadingButton loading={loading} variant="contained" type="submit">
                                {isEdit ? <FormattedMessage id={cv_config + 'submit'} /> : <FormattedMessage id={cv_config + 'add'} />}
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditTechnologyConfig;
