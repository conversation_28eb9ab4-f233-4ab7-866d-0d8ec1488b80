import { useLayoutEffect, useState } from 'react';

// project imports
import { useAppDispatch, useAppSelector } from 'app/hooks';
import Modal from 'components/extended/Modal';
import { TabPanel } from 'components/extended/Tabs';
import { projectDetailSelector, quotaUpdateHistorySelector } from 'store/slice/projectSlice';
import AddOrEditProjectForm from './AddOrEditProjectForm';
import EditProjectTabs from './EditProjectTabs';
import ProjectUser from './ProjectUser';
import QuotaUpdateHistory from './QuotaUpdateHistory';
import { projectEditTabs, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { IProjectEditConfig } from 'pages/administration/Config';

interface IAddOrEditProjectProps {
    open: boolean;
    isEdit?: boolean;
    onClose: () => void;
    dataTable: () => void;
}

const initialProject = {
    projectId: null,
    projectName: '',
    departmentId: '',
    contractNo: '',
    billable: 'billable',
    projectType: '',
    startDate: null,
    endDate: null,
    contractSize: 0,
    licenseAmount: 0,
    projectCostLimit: 0,
    totalQuota: 0,
    percentageComplete: 0,
    userName: null,
    status: '1',
    note: '',
    client: '',
    technology: '',
    desc: '',
    domain: '',
    effort: ''
};

const AddOrEditProject = (props: IAddOrEditProjectProps) => {
    // Hooks, State, Variable, Props
    const { open, isEdit, onClose, dataTable } = props;

    const { manage_project } = TEXT_CONFIG_SCREEN.administration;
    const [tabValue, setTabValue] = useState<number>(0);

    const [projecFormReset, setProjecFormReset] = useState<IProjectEditConfig>(initialProject);

    const dispatch = useAppDispatch();

    const quotaUpdateHistories = useAppSelector(quotaUpdateHistorySelector);
    const projectDetail = useAppSelector(projectDetailSelector);

    const handleClose = () => {
        onClose();
        setProjecFormReset(initialProject);
        setTabValue(0);
    };

    // Effect
    useLayoutEffect(() => {
        if (isEdit && projectDetail) {
            const {
                project,
                project: { projectManager }
            } = projectDetail;

            setProjecFormReset({
                projectId: project.projectId,
                projectName: project.projectName,
                departmentId: project.deptId,
                contractNo: project.contractNo,
                billable: project.billable,
                projectType: project.typeCode,
                startDate: project.startDate,
                endDate: project.endDate,
                contractSize: project.contractSize,
                licenseAmount: project.licenseAmount,
                projectCostLimit: project.projectCostLimit,
                totalQuota: project.totalQuota,
                percentageComplete: (+project.percentageComplete! * 100).toFixed(1),
                userName: !!projectManager
                    ? { value: projectManager.userName, label: `${projectManager?.firstName} ${projectManager?.lastName}` }
                    : projectManager,
                status: project.projectStatus,
                note: project.note ? project.note : '',
                client: project.client,
                technology: project.technology,
                desc: project.desc,
                domain: project.domain,
                effort: project.effort
            });
        }
    }, [dispatch, isEdit, projectDetail]);

    return (
        <Modal
            isOpen={open}
            title={isEdit ? manage_project + 'edit-project' : manage_project + 'add-project'}
            keepMounted={false}
            onClose={handleClose}
            maxWidth="md"
        >
            {/* Tabs */}
            <EditProjectTabs
                projectTab={isEdit ? projectEditTabs : [projectEditTabs[0]]}
                tabValue={tabValue}
                handleChangeTab={(_, value) => setTabValue(value)}
            />
            {/* Form edit project */}
            <TabPanel value={tabValue} index={0}>
                <AddOrEditProjectForm isEdit={isEdit} dataTable={dataTable} projecFormReset={projecFormReset} handleClose={handleClose} />
            </TabPanel>
            {/* List user in project */}
            <TabPanel value={tabValue} index={1}>
                <ProjectUser handleClose={handleClose} />
            </TabPanel>
            {/* update quota history */}
            <TabPanel value={tabValue} index={2}>
                <QuotaUpdateHistory quotaUpdateHistories={quotaUpdateHistories!} handleClose={handleClose} />
            </TabPanel>
        </Modal>
    );
};

export default AddOrEditProject;
