import { FormattedMessage } from 'react-intl';

//mui import
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IFieldsOnboardHistoryTableTHeadProps {
    length: number;
}

const FieldsOnboardHistoryTableTHead = (props: IFieldsOnboardHistoryTableTHeadProps) => {
    const { length } = props;
    const { manage_user } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_user + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'contractor'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'onboard-date'} />
                    <span className="MuiInputLabel-asterisk"> *</span>
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'outboard-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'official-business-day'} />
                </TableCell>
                {length > 0 && (
                    <TableCell align="center">
                        <FormattedMessage id={manage_user + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default FieldsOnboardHistoryTableTHead;
