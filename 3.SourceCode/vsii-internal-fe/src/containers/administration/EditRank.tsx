import { useForm, useFieldArray } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

// yup
import { yupResolver } from '@hookform/resolvers/yup';

// material-ui
import { Grid, Stack, Button, TableContainer, TableBody, Tooltip, IconButton, Typography, TextField } from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project import
import { editRankFormSchema, IEditRank, rankCostHistoryFormDefault } from 'pages/administration/Config';
import { AddCircleOutlineIcon } from 'assets/images/icons';
import RankCostHistoryThead from './RankCostHistoryThead';
import RankCostHistoryTBody from './RankCostHistoryTBody';
import { FormProvider } from 'components/extended/Form';
import { authSelector } from 'store/slice/authSlice';
import { Table } from 'components/extended/Table';
import { IRank, IRankCostHistory } from 'types';
import { DATE_FORMAT, TEXT_CONFIG_SCREEN } from 'constants/Common';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { useAppSelector } from 'app/hooks';
import { dateFormat } from 'utils/date';
import { useState } from 'react';

type IEditRankProps = {
    rank: IRank;
    open: boolean;
    handleClose: () => void;
    handleEditRank: (rank: IEditRank) => void;
    loading: boolean;
};

const EditRank = (props: IEditRankProps) => {
    const { open, handleClose, rank, handleEditRank, loading } = props;

    const { Manage_rank } = TEXT_CONFIG_SCREEN.administration;
    const [inputDesc, setInputDesc] = useState(rank.description);

    const { userInfo } = useAppSelector(authSelector);
    const costHistoryListFormDefault: any = [rankCostHistoryFormDefault];
    const methods = useForm({
        defaultValues: {
            rankCostHistoryList: rank ? rank.rankCostHistoryList : costHistoryListFormDefault
        },
        resolver: yupResolver(editRankFormSchema),
        mode: 'all'
    });

    const { fields, append, remove } = useFieldArray({
        control: methods.control,
        name: 'rankCostHistoryList'
    });

    const addRankCostHistoryHandler = () => {
        append(rankCostHistoryFormDefault);
    };
    const handleDesc = (e: React.ChangeEvent<HTMLInputElement>) => {
        setInputDesc(e.target.value);
    };

    const handleSubmit = (values: IEditRank) => {
        const value = values.rankCostHistoryList.map((item: IRankCostHistory) => ({
            fromDate: dateFormat(item.fromDate, DATE_FORMAT.DDMMYYYY),
            toDate: dateFormat(item.toDate, DATE_FORMAT.DDMMYYYY),
            amount: item.amount
        }));
        const payload = {
            rankName: rank.rankName,
            rankCostHistoryList: value,
            userUpdate: userInfo?.userName,
            idHexString: rank.idHexString,
            description: inputDesc
        };
        handleEditRank(payload);
    };

    return (
        <Modal isOpen={open} title={Manage_rank + 'edit-rank'} onClose={handleClose} keepMounted={false} maxWidth="md">
            <FormProvider formReturn={{ ...methods }} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={6} sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle1" sx={{ paddingLeft: '10px' }}>
                            <span style={{ fontWeight: '400' }}>
                                <FormattedMessage id={Manage_rank + 'rank-name'} />
                            </span>
                            : {rank?.rankName}
                        </Typography>
                    </Grid>
                    <Grid item xs={5} sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <Typography variant="subtitle1">
                            <FormattedMessage id={Manage_rank + 'note'} />
                        </Typography>
                        <TextField sx={{ padding: '10px 14px' }} fullWidth size="small" value={inputDesc} onChange={handleDesc} />
                    </Grid>
                    <TableContainer>
                        <Stack direction="row" sx={{ paddingTop: '10px', paddingLeft: '10px', justifyContent: 'left', alignItems: 'left' }}>
                            <Tooltip
                                placement="top"
                                title={<FormattedMessage id={Manage_rank + 'add'} />}
                                onClick={() => addRankCostHistoryHandler()}
                            >
                                <IconButton aria-label="Add" size="small">
                                    <AddCircleOutlineIcon sx={{ fontSize: '1.1rem' }} />
                                </IconButton>
                            </Tooltip>
                        </Stack>
                        <Table heads={<RankCostHistoryThead />} data={fields}>
                            <TableBody>
                                {fields.map((item, key) => (
                                    <RankCostHistoryTBody key={item.id} index={key} remove={remove} count={fields.length} />
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                    <Grid item xs={12}>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={Manage_rank + 'cancel'} />
                            </Button>
                            <LoadingButton loading={loading} variant="contained" type="submit">
                                <FormattedMessage id={Manage_rank + 'submit'} />
                            </LoadingButton>
                        </Stack>
                    </Grid>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default EditRank;
