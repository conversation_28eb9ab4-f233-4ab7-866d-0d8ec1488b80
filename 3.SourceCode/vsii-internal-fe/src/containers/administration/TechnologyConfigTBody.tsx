// material-ui
import { TableBody, TableCell, TableRow, Stack, Tooltip, IconButton } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';

// assets
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';

// project imports
import { PERMISSIONS } from 'constants/Permission';
import { checkAllowedPermission } from 'utils/authorization';

// third party
import { FormattedMessage } from 'react-intl';
import { dateFormat } from 'utils/date';
import { ISkill, ITechnology } from 'types';

interface ITechnologyConfigProps {
    technologies: ITechnology[];
    handleOpen: (technology: ITechnology) => void;
    pageSize: number;
    pageNumber: number;
    handleDelete: (technology: ITechnology) => void;
}

const TechnologyConfigTBody = (props: ITechnologyConfigProps) => {
    const { technologies, handleOpen, pageSize, pageNumber, handleDelete } = props;
    const { cVConfigTechnologyPermission } = PERMISSIONS.admin;

    return (
        <TableBody>
            {technologies?.map((technology: ITechnology, key: number) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{technology.techType}</TableCell>
                    <TableCell>
                        {technology?.skillList.map((skill: ISkill, index: number) => (
                            <span key={index}>
                                {skill.name}
                                {index !== technology.skillList.length - 1 ? ', ' : ''}
                            </span>
                        ))}
                    </TableCell>
                    <TableCell>{dateFormat(technology.lastUpdate)}</TableCell>
                    <TableCell>{technology.userUpdate}</TableCell>
                    {checkAllowedPermission(cVConfigTechnologyPermission.edit) && (
                        <TableCell>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(technology)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip placement="top" title={<FormattedMessage id="delete" />} onClick={() => handleDelete(technology)}>
                                    <IconButton aria-label="delete" size="small">
                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default TechnologyConfigTBody;
