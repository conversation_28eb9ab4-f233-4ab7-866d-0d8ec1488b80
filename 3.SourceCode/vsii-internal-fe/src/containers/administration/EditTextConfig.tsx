import React from 'react';
import { But<PERSON>, <PERSON>alogActions, Grid, Stack, Typography } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { editTextConfig, flexiableReportSelector, getListTextConfig } from 'store/slice/flexiableReportSlice';
import { ITextConfig, ISearchTextConfigParams } from 'types/flexible-report';
import { editTextConfigSchema } from 'pages/administration/Config';
import ReportNameConfig from 'containers/search/ReportNameConfig';
import { FormProvider, Input } from 'components/extended/Form';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { transformObject } from 'utils/common';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import useConfig from 'hooks/useConfig';

interface Props {
    open: boolean;
    conditions: ISearchTextConfigParams;
    data: ITextConfig | undefined;
    handleClose: () => void;
}

const EditTextConfig: React.FC<Props> = ({ open, data, conditions, handleClose }) => {
    const { loading } = useAppSelector(flexiableReportSelector);

    const dispatch = useAppDispatch();

    const { onChangeLocale, locale } = useConfig();

    const methods = useForm({
        defaultValues: data,
        resolver: yupResolver(editTextConfigSchema)
    });

    const handleSubmit = async (value: ITextConfig) => {
        transformObject(value, ['code', 'reportName', 'defaultTextNameENG', 'defaultTextNameVN', 'screenName']);
        const resultAction = await dispatch(editTextConfig(value));
        if (editTextConfig.fulfilled.match(resultAction) && !resultAction.payload?.status) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: resultAction.payload?.result?.content?.message || 'Error',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );

            return;
        }

        dispatch(
            openSnackbar({
                open: true,
                message: 'update-success',
                variant: 'alert',
                alert: { color: 'success' }
            })
        );
        onChangeLocale(locale);
        dispatch(getListTextConfig(conditions));
        handleClose();
    };

    return (
        <Modal isOpen={open} title="Edit Config" onClose={handleClose} maxWidth="md">
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing} paddingX={2}>
                    <Grid item xs={12} display="flex">
                        <Grid item xs={2.5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id="screen-name" />
                                <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={5}>
                            <ReportNameConfig name="flexibleReportId" />
                        </Grid>
                    </Grid>

                    <Grid item xs={12} display="flex">
                        <Grid item xs={2.5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id="text-config.default-text-en" />
                                <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={3}>
                            <Input name="defaultTextNameENG" disabled />
                        </Grid>
                        <Grid item xs={1} />
                        <Grid item xs={2.5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id="text-config.default-text-vi" />
                                <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={3}>
                            <Input name="defaultTextNameVN" disabled />
                        </Grid>
                    </Grid>

                    <Grid item xs={12} display="flex">
                        <Grid item xs={2.5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id="text-config.new-text-en" />
                                <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={3}>
                            <Input name="textNameENG" />
                        </Grid>
                        <Grid item xs={1} />
                        <Grid item xs={2.5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id="text-config.new-text-vi" />
                                <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                            </Typography>
                        </Grid>
                        <Grid item xs={3}>
                            <Input name="textNameVN" />
                        </Grid>
                    </Grid>

                    <Grid item xs={12} display="flex">
                        <Grid item xs={2.5}>
                            <Typography sx={{ color: '#333', display: 'flex', gap: 1 }}>
                                <FormattedMessage id="note" />
                            </Typography>
                        </Grid>
                        <Grid item xs={5}>
                            <Input
                                textFieldProps={{
                                    multiline: true,
                                    rows: 2
                                }}
                                name="note"
                            />
                        </Grid>
                    </Grid>
                </Grid>
                {/* Cancel | Submit */}
                <Grid item xs={12}>
                    <DialogActions>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id="cancel" />
                            </Button>
                            <LoadingButton variant="contained" type="submit" loading={loading[editTextConfig.typePrefix]}>
                                <FormattedMessage id="submit" />
                            </LoadingButton>
                        </Stack>
                    </DialogActions>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default EditTextConfig;
