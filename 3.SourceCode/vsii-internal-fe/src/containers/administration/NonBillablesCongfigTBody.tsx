import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, TableBody, TableCell, TableRow, Tooltip, Typography } from '@mui/material';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { FormattedMessage } from 'react-intl';

import { INonBillablesConfig } from 'types/non-billables-config';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { IPaginationParam } from 'types';
import { dateFormat } from 'utils/date';

interface ITitleConfigTBodyProps {
    data: INonBillablesConfig[];
    conditions: IPaginationParam;
    handleOpen: (title: INonBillablesConfig) => void;
}

const NonBillablesCongfigTBody = (props: ITitleConfigTBodyProps) => {
    const { conditions, data, handleOpen } = props;
    const { nonBillablesConfigPermission } = PERMISSIONS.admin;

    return (
        <TableBody>
            {data?.map((value, key) => (
                <TableRow key={key}>
                    <TableCell align="center">{conditions.size * (conditions.page - 1) + key + 1}</TableCell>
                    <TableCell>{value.name}</TableCell>
                    <TableCell>{value.key}</TableCell>
                    <TableCell width={100}>{value.minValue || value.maxValue ? `${value.minValue}% - ${value.maxValue}%` : null}</TableCell>
                    <TableCell>
                        <Typography
                            sx={{
                                whiteSpace: 'pre-wrap'
                            }}
                        >
                            {value.note}
                        </Typography>
                    </TableCell>
                    <TableCell>{dateFormat(value.lastUpdated)}</TableCell>
                    <TableCell>{value.userUpdated}</TableCell>
                    {checkAllowedPermission(nonBillablesConfigPermission.edit) && (
                        <TableCell sx={{ width: '10%' }}>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(value)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default NonBillablesCongfigTBody;
