import { I<PERSON><PERSON><PERSON><PERSON>, Stack, TableBody, TableCell, TableRow, Tooltip } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { FormattedMessage } from 'react-intl';

import { deleteTitle, getSearchTitle } from 'store/slice/titleSlice';
import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { ITitleFilterConfig } from 'pages/administration/Config';
import { checkAllowedPermission } from 'utils/authorization';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { PERMISSIONS } from 'constants/Permission';
import { DATE_FORMAT } from 'constants/Common';
import { ITitleConfig } from 'types/titleConfig';
import { useAppDispatch } from 'app/hooks';
import { dateFormat } from 'utils/date';

interface ITitleConfigTBodyProps {
    data: ITitleConfig[];
    conditions: ITitleFilterConfig;
    handleOpen: (title: ITitleConfig) => void;
}

const TitleConfigTBody = (props: ITitleConfigTBodyProps) => {
    const { conditions, data, handleOpen } = props;
    const { titleConfigPermission } = PERMISSIONS.admin;

    const dispatch = useAppDispatch();

    const handleDelete = (title: ITitleConfig) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-record" />,
                width: '400px',
                handleConfirm: async () => {
                    const resultAction = await dispatch(deleteTitle(title.id));
                    if (deleteTitle.fulfilled.match(resultAction)) {
                        if (resultAction.payload?.status) {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result.content,
                                    variant: 'alert',
                                    alert: { color: 'success' }
                                })
                            );
                            await dispatch(getSearchTitle(conditions));
                        } else {
                            dispatch(
                                openSnackbar({
                                    open: true,
                                    message: resultAction.payload.result?.content || 'Error',
                                    variant: 'alert',
                                    alert: { color: 'error' }
                                })
                            );
                        }
                    }
                    dispatch(closeConfirm());
                }
            })
        );
    };

    return (
        <TableBody>
            {data?.map((value, key) => (
                <TableRow key={key}>
                    <TableCell align="center" sx={{ width: '5%' }}>
                        {conditions.size * (conditions.page - 1) + key + 1}
                    </TableCell>
                    <TableCell sx={{ width: '20%' }}>{value.titleCode}</TableCell>
                    <TableCell sx={{ width: '35%' }}>{value.titleName}</TableCell>
                    <TableCell sx={{ width: '15%' }}>{dateFormat(value.lastUpdate, DATE_FORMAT.DDMMYYYY)}</TableCell>
                    <TableCell sx={{ width: '15%' }}>{value.userUpdate}</TableCell>
                    {checkAllowedPermission(titleConfigPermission.edit) && (
                        <TableCell sx={{ width: '10%' }}>
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />} onClick={() => handleOpen(value)}>
                                    <IconButton aria-label="edit" size="small">
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip placement="top" title={<FormattedMessage id="delete" />} onClick={() => handleDelete(value)}>
                                    <IconButton aria-label="delete" size="small">
                                        <HighlightOffIcon sx={{ fontSize: '1.1rem' }} />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        </TableCell>
                    )}
                </TableRow>
            ))}
        </TableBody>
    );
};

export default TitleConfigTBody;
