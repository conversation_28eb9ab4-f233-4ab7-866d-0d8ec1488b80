import { But<PERSON><PERSON>ase, TableCell, TableRow, Tooltip } from '@mui/material';
import { Stack } from '@mui/system';
import { PERMISSIONS } from 'constants/Permission';
import React, { useEffect, useMemo, useState } from 'react';
import { ISearchTextConfigParams, ITextConfig } from 'types/flexible-report';
import { checkAllowedPermission } from 'utils/authorization';

import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import DoneIcon from '@mui/icons-material/Done';
import { FormattedMessage } from 'react-intl';
import { Input } from 'components/extended/Form';
import { useFormContext } from 'react-hook-form';

interface IEditLanguageRowProps {
    item: ITextConfig;
    conditions: ISearchTextConfigParams;
    index: number;
    setDataEditLanguage: React.Dispatch<React.SetStateAction<ITextConfig | undefined>>;

    activeRowIndex: number | null;
    handleclickEdit: () => void;
    handleCancelEdit: () => void;
}

const EditLanguageRow = ({
    item,
    conditions,
    index,
    setDataEditLanguage,
    activeRowIndex,
    handleclickEdit,
    handleCancelEdit
}: IEditLanguageRowProps) => {
    const { flexibleReportingConfigPermission } = PERMISSIONS.admin;

    const [isEdited, setisEdited] = useState(false);
    const methods = useFormContext();
    const newText = useMemo(() => {
        return item.languageConfigs?.find((item) => item.languageCode === conditions.code)?.newText || '';
    }, [item, conditions]);

    useEffect(() => {
        if (isEdited) {
            methods.reset({
                newText: newText,
                note: item.note || '',
                languageCode: conditions.code
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isEdited, item]);

    return (
        <TableRow>
            <TableCell>{conditions.size * (conditions.page - 1) + index + 1}</TableCell>
            <TableCell>{item.screenName}</TableCell>
            <TableCell>{item.defaultTextNameENG}</TableCell>

            {isEdited ? (
                <>
                    <TableCell>
                        <Input name="newText" />
                    </TableCell>
                    <TableCell>
                        <Input name="note" />
                    </TableCell>
                    <TableCell>
                        {checkAllowedPermission(flexibleReportingConfigPermission.textConfig.edit) && (
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip title={<FormattedMessage id="cancel"></FormattedMessage>}>
                                    <ButtonBase
                                        onClick={() => {
                                            setisEdited(false);
                                            handleCancelEdit();
                                        }}
                                    >
                                        <HighlightOffIcon sx={{ fontSize: 18 }} />
                                    </ButtonBase>
                                </Tooltip>
                                <Tooltip title={<FormattedMessage id="confirm"></FormattedMessage>}>
                                    <ButtonBase type="submit">
                                        <DoneIcon sx={{ fontSize: 18 }} />
                                    </ButtonBase>
                                </Tooltip>
                            </Stack>
                        )}
                    </TableCell>
                </>
            ) : (
                <>
                    <TableCell>{newText}</TableCell>
                    <TableCell sx={{ whiteSpace: 'pre-line' }}>{item.note}</TableCell>
                    <TableCell>
                        {checkAllowedPermission(flexibleReportingConfigPermission.textConfig.edit) && (
                            <Stack direction="row" justifyContent="center" alignItems="center">
                                <Tooltip placement="top" title={<FormattedMessage id={'edit'} />}>
                                    <ButtonBase
                                        onClick={() => {
                                            setisEdited(true);
                                            handleclickEdit();
                                            setDataEditLanguage(item);
                                        }}
                                        disabled={activeRowIndex !== null && activeRowIndex !== index}
                                    >
                                        <EditTwoToneIcon sx={{ fontSize: '1.1rem' }} />
                                    </ButtonBase>
                                </Tooltip>
                            </Stack>
                        )}
                    </TableCell>
                </>
            )}
        </TableRow>
    );
};

export default EditLanguageRow;
