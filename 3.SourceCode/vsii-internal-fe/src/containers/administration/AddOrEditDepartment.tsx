import { Button, DialogActions, Grid } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { createDepartment, departmentSelector, editDepartment, getSearchDepartment } from 'store/slice/departmentSlice';
import { IDepartmentFilterConfig, createOrEditDepartmentSchema } from 'pages/administration/Config';
import { ICreateDepartmentRequest, IDepartment } from 'types/department';
import { FormProvider, Input } from 'components/extended/Form';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditDepartmentProps {
    department: IDepartment | undefined;
    conditions: IDepartmentFilterConfig;
    open: boolean;
    handleClose: () => void;
}

const AddOrEditDepartment = (props: IAddOrEditDepartmentProps) => {
    const { department, open, conditions, handleClose } = props;

    const { manage_department } = TEXT_CONFIG_SCREEN.administration;

    const dispatch = useAppDispatch();

    const { loading } = useAppSelector(departmentSelector);

    // useForm
    const methods = useForm({
        defaultValues: {
            deptId: department?.deptId || '',
            deptName: department?.deptName || ''
        },
        resolver: yupResolver(createOrEditDepartmentSchema),
        mode: 'all'
    });

    const handleSubmit = async (values: ICreateDepartmentRequest) => {
        if (department) {
            const resultAction = await dispatch(editDepartment({ ...values, id: department.id }));
            if (editDepartment.fulfilled.match(resultAction) && !resultAction.payload.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            } else if (editDepartment.rejected.match(resultAction)) {
                return;
            }
        } else {
            const resultAction = await dispatch(createDepartment(values));
            if (createDepartment.fulfilled.match(resultAction) && !resultAction.payload.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            } else if (createDepartment.rejected.match(resultAction)) {
                return;
            }
        }
        dispatch(
            openSnackbar({
                open: true,
                message: department ? 'update-success' : 'add-success',
                variant: 'alert',
                alert: { color: 'success' }
            })
        );
        dispatch(getSearchDepartment(conditions));
        handleClose();
    };

    return (
        <Modal
            isOpen={open}
            title={department ? manage_department + 'edit-department' : manage_department + 'add-department'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="xs"
        >
            <FormProvider onSubmit={handleSubmit} formReturn={methods}>
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12}>
                        <Input
                            required
                            name="deptId"
                            label={<FormattedMessage id={manage_department + 'department'} />}
                            disabled={!!department}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <Input required name="deptName" label={<FormattedMessage id={manage_department + 'department-name'} />} />
                    </Grid>
                </Grid>

                <DialogActions>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id={manage_department + 'cancel'} />
                    </Button>
                    <LoadingButton
                        loading={loading[createDepartment.typePrefix] || loading[editDepartment.typePrefix]}
                        variant="contained"
                        type="submit"
                    >
                        <FormattedMessage id={manage_department + 'submit'} />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditDepartment;
