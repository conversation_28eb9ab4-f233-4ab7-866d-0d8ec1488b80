import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';

// project imports
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ManageRankThead = () => {
    const { rankPermission } = PERMISSIONS.admin;

    const { Manage_rank } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={Manage_rank + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_rank + 'rank-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_rank + 'note'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_rank + 'last-update'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Manage_rank + 'user-update'} />
                </TableCell>
                {checkAllowedPermission(rankPermission.edit) && (
                    <TableCell align="center">
                        <FormattedMessage id={Manage_rank + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default ManageRankThead;
