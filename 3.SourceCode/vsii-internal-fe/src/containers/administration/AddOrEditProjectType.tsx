import { Button, DialogActions, Grid } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { createProjectType, editProjectType, getSearchProjectType, projectTypeSelector } from 'store/slice/projectTypeSlice';
import { IProjectTypeFilterConfig, createOrEditProjectTypeSchema } from 'pages/administration/Config';
import { Checkbox, FormProvider, Input, Select } from 'components/extended/Form';
import { ICreateProjectTypeRequest, IProjectType } from 'types/projectType';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IAddOrEditProjectTypeProps {
    projectType: IProjectType | undefined;
    conditions: IProjectTypeFilterConfig;
    open: boolean;
    handleClose: () => void;
}

const AddOrEditProjectType = (props: IAddOrEditProjectTypeProps) => {
    const { projectType, open, conditions, handleClose } = props;

    const { project_type_config } = TEXT_CONFIG_SCREEN.administration;

    const dispatch = useAppDispatch();

    const { loading } = useAppSelector(projectTypeSelector);

    // useForm
    const methods = useForm({
        defaultValues: {
            billable: projectType?.billable || 'billable',
            typeCode: projectType?.typeCode || '',
            projectTypeName: projectType?.projectTypeName || '',
            colorCode: projectType?.colorCode || '#000000',
            fixCost: projectType?.fixCost || false
        },
        resolver: yupResolver(createOrEditProjectTypeSchema),
        mode: 'all'
    });

    const handleSubmit = async (values: ICreateProjectTypeRequest) => {
        if (projectType) {
            const resultAction = await dispatch(editProjectType({ ...values, id: projectType.id }));
            if (editProjectType.fulfilled.match(resultAction) && !resultAction.payload.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            } else if (editProjectType.rejected.match(resultAction)) {
                return;
            }
        } else {
            const resultAction = await dispatch(createProjectType(values));
            if (createProjectType.fulfilled.match(resultAction) && !resultAction.payload.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
                return;
            } else if (createProjectType.rejected.match(resultAction)) {
                return;
            }
        }
        dispatch(
            openSnackbar({
                open: true,
                message: projectType ? 'update-success' : 'add-success',
                variant: 'alert',
                alert: { color: 'success' }
            })
        );
        dispatch(getSearchProjectType(conditions));
        handleClose();
    };

    return (
        <Modal
            isOpen={open}
            title={projectType ? project_type_config + 'edit-project-type-config' : project_type_config + 'add-project-type-config'}
            onClose={handleClose}
            keepMounted={false}
            maxWidth="xs"
        >
            <FormProvider onSubmit={handleSubmit} formReturn={methods}>
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12}>
                        <Select
                            required
                            selects={[
                                { label: 'Billable', value: 'billable' },
                                { label: 'Nonbillable', value: 'nonbillable' },
                                { value: ' Billable_Signed the contract', label: ' Billable_Signed the contract' },
                                { value: ' Billable_Not yet sign the contract', label: 'Billable_Not yet sign the contract' }
                            ]}
                            name="billable"
                            label={<FormattedMessage id={project_type_config + 'project-type-create-billable'} />}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <Input
                            required
                            name="typeCode"
                            label={<FormattedMessage id={project_type_config + 'project-type'} />}
                            disabled={!!projectType}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <Input
                            required
                            name="projectTypeName"
                            label={<FormattedMessage id={project_type_config + 'project-type-name'} />}
                        />
                    </Grid>
                    <Grid container item xs={12} spacing={gridSpacing}>
                        <Grid item xs={10}>
                            <Input required name="colorCode" disabled label={<FormattedMessage id={project_type_config + 'color '} />} />
                        </Grid>
                        <Grid item xs={2}>
                            <Input
                                name="colorCode"
                                type="color"
                                sx={{
                                    marginTop: '18px',
                                    input: {
                                        width: '100%',
                                        height: '100%'
                                    }
                                }}
                                textFieldProps={{
                                    variant: 'standard',
                                    InputProps: {
                                        disableUnderline: true,
                                        sx: {
                                            width: '40px',
                                            height: '40px'
                                        }
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                    <Grid item xs={12}>
                        <Checkbox name="fixCost" label={<FormattedMessage id={project_type_config + 'fixed-cost'} />} />
                    </Grid>
                </Grid>

                <DialogActions>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id={project_type_config + 'cancel'} />
                    </Button>
                    <LoadingButton
                        loading={loading[createProjectType.typePrefix] || loading[editProjectType.typePrefix]}
                        variant="contained"
                        type="submit"
                    >
                        <FormattedMessage id={project_type_config + 'submit'} />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default AddOrEditProjectType;
