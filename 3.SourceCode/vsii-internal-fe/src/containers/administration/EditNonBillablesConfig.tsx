/* eslint-disable import/no-extraneous-dependencies */
import { useEffect } from 'react';
import { Box, Button, DialogActions, Grid } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { editNonbillableConfig, getNonbillableConfig, nonBIllableConfigSelector } from 'store/slice/nonBillableConfigSlice';
import { PERCENT_PLACEHOLDER_MAX_VALUE, PERCENT_PLACEHOLDER_MIN_VALUE, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormProvider, Input, Label, PercentageFormat } from 'components/extended/Form';
import { editNonBillablesConfigSchema } from 'pages/administration/Config';
import { ProjectTypeNonBillableConfig } from 'containers/search';
import { INonBillablesConfig } from 'types/non-billables-config';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { authSelector } from 'store/slice/authSlice';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { IPaginationParam } from 'types';

interface IAddOrEditTitleConfigProps {
    config?: INonBillablesConfig | undefined;
    open: boolean;
    handleClose: () => void;
    conditions: IPaginationParam;
}

const EditNonBillablesConfig = (props: IAddOrEditTitleConfigProps) => {
    const { config, open, handleClose, conditions } = props;

    const { Non_billables_Config } = TEXT_CONFIG_SCREEN.administration;
    const dispatch = useAppDispatch();
    const { userInfo } = useAppSelector(authSelector);
    const { loading } = useAppSelector(nonBIllableConfigSelector);
    const methods = useForm<INonBillablesConfig>({
        defaultValues: {
            name: '',
            key: '',
            minValue: null,
            maxValue: null,
            typeCode: '',
            note: '',
            color: ''
        },

        resolver: yupResolver(editNonBillablesConfigSchema),
        mode: 'all'
    });

    const handleSubmit = async (values: INonBillablesConfig) => {
        const resultAction = await dispatch(
            editNonbillableConfig({ ...values, userUpdated: userInfo?.userName as string, id: config?.id as string })
        );

        if (editNonbillableConfig.fulfilled.match(resultAction)) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: resultAction.payload.status
                        ? (resultAction.payload?.result?.content as string)
                        : (resultAction.payload?.result?.content?.message as string),
                    variant: 'alert',
                    alert: { color: resultAction.payload.status ? 'success' : 'error' }
                })
            );
            if (resultAction.payload.status) handleClose();
            dispatch(getNonbillableConfig(conditions));
        } else if (editNonbillableConfig.rejected.match(resultAction)) {
            return;
        }
    };

    useEffect(() => {
        methods.reset({
            name: config?.name,
            key: config?.key,
            value: config?.value ? config?.value : 0,
            minValue: config?.minValue ? config?.minValue : 0,
            maxValue: config?.maxValue ? config?.maxValue : 0,
            typeCode: config?.typeCode ? config?.typeCode : '',
            note: config?.note,
            color: config?.color
        });
    }, [config, methods]);

    return (
        <Modal isOpen={open} title={Non_billables_Config + 'edit-config'} onClose={handleClose} keepMounted={false} maxWidth="sm">
            <FormProvider onSubmit={handleSubmit} formReturn={methods}>
                <Grid container spacing={gridSpacing} paddingX={gridSpacing}>
                    <Grid item xs={12} display="flex">
                        {/* name */}
                        <Grid item xs={4}>
                            <Label label={<FormattedMessage id={Non_billables_Config + 'config-name'} />} required />
                        </Grid>
                        <Grid item xs={8}>
                            <Input name="name" />
                        </Grid>
                    </Grid>
                    {/* key */}
                    <Grid item xs={12} display="flex">
                        <Grid item xs={4}>
                            <Label label={<FormattedMessage id={Non_billables_Config + 'key'} />} required />
                        </Grid>
                        <Grid item xs={8}>
                            <Input name="key" disabled />
                        </Grid>
                    </Grid>
                    {/* values */}

                    <Grid item xs={12} display={config?.maxValue || config?.minValue ? 'flex' : 'none'}>
                        <Grid item xs={3.75}>
                            <Label label={<FormattedMessage id={Non_billables_Config + 'value'} />} required />
                        </Grid>
                        <Grid item xs={8.25}>
                            <Box display="flex" flexDirection="row" gap={1}>
                                <Input
                                    textFieldProps={{
                                        placeholder: PERCENT_PLACEHOLDER_MIN_VALUE,
                                        InputProps: {
                                            inputComponent: PercentageFormat as any
                                        }
                                    }}
                                    name="minValue"
                                    placeholder="Enter min value"
                                />
                                <Input
                                    textFieldProps={{
                                        placeholder: PERCENT_PLACEHOLDER_MAX_VALUE,
                                        InputProps: {
                                            inputComponent: PercentageFormat as any
                                        }
                                    }}
                                    name="maxValue"
                                    placeholder="Enter max value"
                                />
                            </Box>
                        </Grid>
                    </Grid>
                    {/* project type */}
                    <Grid item xs={12} display={config?.key !== 'Not_enough_timesheets' ? 'flex' : 'none'}>
                        <Grid item xs={4}>
                            <Label label={<FormattedMessage id={Non_billables_Config + 'project-type'} />} />
                        </Grid>
                        <Grid item xs={8}>
                            <ProjectTypeNonBillableConfig name="typeCode" disableLabel nonBillable noDefaultOption />
                        </Grid>
                    </Grid>

                    <Grid item xs={12} display="flex">
                        <Grid item xs={4}>
                            <Label label={<FormattedMessage id={Non_billables_Config + 'color'} />} required />
                        </Grid>
                        <Grid item xs={7} display="flex" alignItems="center" mr={0.5}>
                            <Input name="color" disabled />
                        </Grid>
                        <Grid item xs={1}>
                            <Input
                                name="color"
                                type="color"
                                sx={{
                                    input: {
                                        width: '100%',
                                        height: '100%'
                                    }
                                }}
                                textFieldProps={{
                                    variant: 'standard',
                                    InputProps: {
                                        disableUnderline: true,
                                        sx: {
                                            width: '40px',
                                            height: '40px'
                                        }
                                    }
                                }}
                            />
                        </Grid>
                    </Grid>
                    {/* note */}
                    <Grid item xs={12} display="flex">
                        <Grid item xs={4}>
                            <Label label={<FormattedMessage id={Non_billables_Config + 'note'} />} />
                        </Grid>
                        <Grid item xs={8}>
                            <Input name="note" textFieldProps={{ multiline: true, rows: 3 }} />
                        </Grid>
                    </Grid>
                </Grid>
                {/* submit */}
                <DialogActions>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id={Non_billables_Config + 'cancel'} />
                    </Button>
                    <LoadingButton loading={loading[editNonbillableConfig.typePrefix]} variant="contained" type="submit">
                        <FormattedMessage id={Non_billables_Config + 'submit'} />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default EditNonBillablesConfig;
