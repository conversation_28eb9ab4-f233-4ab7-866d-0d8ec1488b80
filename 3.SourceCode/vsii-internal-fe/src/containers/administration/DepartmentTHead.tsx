import { TableCell, TableHead, TableRow } from '@mui/material';
import { FormattedMessage } from 'react-intl';

import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const DepartmentTHead = () => {
    const { departmentPermission } = PERMISSIONS.admin;
    const { manage_department } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center" sx={{ width: '5%' }}>
                    <FormattedMessage id={manage_department + 'no'} />
                </TableCell>
                <TableCell sx={{ width: '15%' }}>
                    <FormattedMessage id={manage_department + 'department'} />
                </TableCell>
                <TableCell sx={{ width: '30%' }}>
                    <FormattedMessage id={manage_department + 'department-name'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={manage_department + 'last-update'} />
                </TableCell>
                <TableCell sx={{ width: '20%' }}>
                    <FormattedMessage id={manage_department + 'user-update'} />
                </TableCell>
                {checkAllowedPermission(departmentPermission.edit) && <TableCell sx={{ width: '10%' }} />}
            </TableRow>
        </TableHead>
    );
};

export default DepartmentTHead;
