import { FormattedMessage } from 'react-intl';

// mui import
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IFieldRankHistoryTableTHeadProps {
    length: number;
    isRequired?: boolean;
}

const FieldRankHistoryTableTHead = (props: IFieldRankHistoryTableTHeadProps) => {
    const { length, isRequired } = props;

    const { manage_user } = TEXT_CONFIG_SCREEN.administration;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={manage_user + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'rank'} />
                    {!isRequired && <span className="MuiInputLabel-asterisk"> *</span>}
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'title'} />
                    {!isRequired && <span className="MuiInputLabel-asterisk"> *</span>}
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'from-date'} />
                    {!isRequired && <span className="MuiInputLabel-asterisk"> *</span>}
                </TableCell>
                <TableCell>
                    <FormattedMessage id={manage_user + 'to-date'} />
                </TableCell>
                {length > 1 && (
                    <TableCell align="center">
                        <FormattedMessage id={manage_user + 'action'} />
                    </TableCell>
                )}
            </TableRow>
        </TableHead>
    );
};

export default FieldRankHistoryTableTHead;
