// materia-ui
import { Button, TableBody, TableCell, TableRow } from '@mui/material';

// project imports
import { Checkbox } from 'components/extended/Form';
import { IUserVerify, IWeeklyEffortProjectDetail } from 'types';
import { compareStyleEffortMember } from 'utils/common';
import { dateFormat } from 'utils/date';

// project imports

interface IWeeklyEffortProjectDetailsTBodyProps {
    projectDetails: IWeeklyEffortProjectDetail[];
    handleCheckOne: (user: IUserVerify) => void;
    selected?: IUserVerify[];
    isCheckAll?: boolean;
    handleOpen: (user: IWeeklyEffortProjectDetail) => void;
}

const WeeklyEffortProjectDetailsTBody = (props: IWeeklyEffortProjectDetailsTBodyProps) => {
    const { projectDetails, handleCheckOne, selected, isCheckAll, handleOpen } = props;

    return (
        <TableBody>
            {projectDetails?.map((user: IWeeklyEffortProjectDetail, index: number) => {
                return (
                    <TableRow key={user.projectId}>
                        <TableCell>
                            <Checkbox
                                name="check"
                                isControl={false}
                                handleChange={() =>
                                    handleCheckOne({
                                        userId: user.userId,
                                        pmVerifiedDate: user.pmVerifiedDate,
                                        qaVerifiedDate: user.qaVerifiedDate
                                    })
                                }
                                valueChecked={isCheckAll || selected?.some((item: IUserVerify) => item.userId === user.userId)}
                            />
                        </TableCell>
                        <TableCell>
                            <Button
                                sx={{
                                    color: '#000',
                                    padding: '0',
                                    whiteSpace: 'nowrap',
                                    textAlign: 'left',
                                    '&.MuiButtonBase-root:hover': {
                                        backgroundColor: 'transparent',
                                        color: '#3163d4',
                                        fontStyle: 'italic'
                                    }
                                }}
                                onClick={() => handleOpen(user)}
                            >{`${user.firstName} ${user.lastName}`}</Button>
                        </TableCell>
                        <TableCell>{user.memberCode}</TableCell>
                        <TableCell sx={compareStyleEffortMember(user?.effortPMVerified, user.effortInWeek)}>
                            {user?.effortPMVerified?.toFixed(1) || 0} / {user?.effortInWeek?.toFixed(1) || 0}
                        </TableCell>
                        <TableCell sx={compareStyleEffortMember(user?.payAbleOTVerified, user.payAbleOT)}>
                            {user?.payAbleOTVerified || 0} / {user.payAbleOT || 0}
                        </TableCell>
                        <TableCell sx={compareStyleEffortMember(user?.nonPayAbleOTVerified, user.nonPayAbleOT)}>
                            {user?.nonPayAbleOTVerified || 0} / {user.nonPayAbleOT || 0}
                        </TableCell>
                        <TableCell>{user?.pmVerified}</TableCell>
                        <TableCell>{dateFormat(user?.pmVerifiedDate)}</TableCell>
                        <TableCell>{user?.qaVerified}</TableCell>
                        <TableCell>{dateFormat(user?.qaVerifiedDate)}</TableCell>
                    </TableRow>
                );
            })}
        </TableBody>
    );
};

export default WeeklyEffortProjectDetailsTBody;
