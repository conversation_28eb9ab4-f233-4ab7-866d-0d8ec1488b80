// materia-ui
import { TableBody, TableCell, TableRow } from '@mui/material';

// project imports
import { rankSelector } from 'store/slice/rankSlice';
import { IWeeklyEffortProject } from 'types';
import { useAppSelector } from 'app/hooks';
import { formatPrice } from 'utils/common';

interface IWeeklyEffortProjectTBodyProps {
    pageNumber: number;
    pageSize: number;
    projects: IWeeklyEffortProject[];
}

const WeeklyEffortProjectTBody = (props: IWeeklyEffortProjectTBodyProps) => {
    const { pageNumber, pageSize, projects } = props;
    const { rank } = useAppSelector(rankSelector);

    return (
        <TableBody>
            {projects.map((item, key) => (
                <TableRow key={key}>
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>{item.projectName}</TableCell>
                    <TableCell>{item.department}</TableCell>
                    <TableCell sx={{ fontWeight: '700 !important' }}>{parseFloat(item.totalEffort).toFixed(1)}</TableCell>
                    {rank.map((r, index) => {
                        const cost = item.rank.find((r2) => r2.level === r.rankName)?.cost || 0;
                        return <TableCell key={index}>{formatPrice(cost)}</TableCell>;
                    })}
                    <TableCell sx={{ fontWeight: '700 !important' }}>{formatPrice(item.totalCost)}</TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default WeeklyEffortProjectTBody;
