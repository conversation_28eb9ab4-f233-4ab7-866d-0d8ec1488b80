// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { Checkbox } from 'components/extended/Form';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormattedMessage } from 'react-intl';

type IWeeklyEffortProjectDetailTheadProps = {
    handleCheckAll: () => void;
    isCheckAll?: boolean;
    isSomeSelected?: boolean;
};

function WeeklyEffortProjectDetailThead(props: IWeeklyEffortProjectDetailTheadProps) {
    const { handleCheckAll, isCheckAll, isSomeSelected } = props;

    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <Checkbox
                        name="checkAll"
                        isControl={false}
                        handleChange={handleCheckAll}
                        valueChecked={isCheckAll}
                        indeterminate={isSomeSelected}
                    />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'member-code'} />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={Weeklyeffort + 'effort-pm-verified'} /> /
                    <br />
                    <FormattedMessage id={Weeklyeffort + 'effort-in-week-hours'} />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={Weeklyeffort + 'payable-ot-verified'} /> /
                    <br />
                    <FormattedMessage id="payable-ot" />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={Weeklyeffort + 'non-payable-ot-verified'} /> /
                    <br />
                    <FormattedMessage id={Weeklyeffort + 'non-payable-ot'} />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={Weeklyeffort + 'pm-verifed'} />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={Weeklyeffort + 'pm-verified-date'} />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={Weeklyeffort + 'qa-verifed'} />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={Weeklyeffort + 'qa-verified-date'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
}
export default WeeklyEffortProjectDetailThead;
