import { useEffect } from 'react';
import { Grid, SelectChangeEvent, Typography } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import { FormattedMessage } from 'react-intl';

import { getWeeklyEffortProjectOption, weeklyEffortSelector } from 'store/slice/weeklyEffortSlice';
import { IWeeklyEffortConfig, weeklyEffortConfig, weeklyEffortProjectSchema } from 'pages/Config';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_VERIFY_TIMESHEET } from 'constants/Common';
import { Autocomplete, Label } from 'components/extended/Form';
import { searchFormConfig } from 'containers/search/Config';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { SearchForm, Weeks, Years } from '../search';
import { getCurrentWeek } from 'utils/date';
import { Button } from 'components';
import { IOption } from 'types';

interface IWeeklyEffortProjectSearchProps {
    formReset: IWeeklyEffortConfig;
    weeks: IOption[];
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    handleSearch: (value: IWeeklyEffortConfig) => void;
}

const WeeklyEffortProjectSearch = ({ formReset, weeks, handleChangeYear, handleSearch }: IWeeklyEffortProjectSearchProps) => {
    const { projectOptions } = useAppSelector(weeklyEffortSelector);

    const dispatch = useAppDispatch();

    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;

    const handleChangeWeek = (week: string | number) => {
        dispatch(getWeeklyEffortProjectOption({ week, color: true }));
    };

    useEffect(() => {
        dispatch(getWeeklyEffortProjectOption({ week: formReset.week ? formReset.week : getCurrentWeek().value, color: true }));
    }, [dispatch, formReset]);

    return (
        <SearchForm
            defaultValues={weeklyEffortConfig}
            formSchema={weeklyEffortProjectSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Years handleChangeYear={handleChangeYear} ignoreDefault label={Weeklyeffort + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Weeks weeks={weeks} onChange={handleChangeWeek} label={Weeklyeffort + 'weeks'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Autocomplete
                        options={projectOptions}
                        name={searchFormConfig.project.name}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={Weeklyeffort + 'projects'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_VERIFY_TIMESHEET} width="150px">
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                        groupBy={(option: IOption) => option.typeCode}
                        isDefaultAll
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={Weeklyeffort + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default WeeklyEffortProjectSearch;
