// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormattedMessage } from 'react-intl';

function WeeklyEffortMemberThead() {
    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'level'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'department'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'effort-in-week-hours'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'difference-hours'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'projects'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
}
export default WeeklyEffortMemberThead;
