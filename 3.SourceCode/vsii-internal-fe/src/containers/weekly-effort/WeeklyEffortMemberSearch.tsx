import { FormattedMessage } from 'react-intl';
import { Grid, SelectChangeEvent, Typography } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';

import { IWeeklyEffortConfig, weeklyEffortConfig, weeklyEffortMemberSchema } from 'pages/Config';
import { E_IS_LOGTIME, TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import { Member, SearchForm, TimeStatus, Weeks, Years } from '../search';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { Label } from 'components/extended/Form';
import { IMember } from 'types/member';
import { Button } from 'components';
import { IOption } from 'types';

interface IWeeklyEffortMemberSearchProps {
    weeks: IOption[];
    formReset: IWeeklyEffortConfig;
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    handleSearch: (value: IWeeklyEffortConfig) => void;
    handleChangeWeek?: (value: string) => void;
    handleChangeTimeStatus?: (value: string[]) => void;
    handleChangeMember?: (value: IMember) => void;
}

const WeeklyEffortMemberSearch = ({
    weeks,
    formReset,
    handleChangeYear,
    handleSearch,
    handleChangeWeek,
    handleChangeTimeStatus,
    handleChangeMember
}: IWeeklyEffortMemberSearchProps) => {
    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={weeklyEffortConfig}
            formSchema={weeklyEffortMemberSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Years handleChangeYear={handleChangeYear} ignoreDefault label={Weeklyeffort + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Weeks weeks={weeks} onChange={handleChangeWeek} label={Weeklyeffort + 'weeks'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <TimeStatus isMultiple onChange={handleChangeTimeStatus} label={Weeklyeffort + 'timesheet-status'} />
                </Grid>

                <Grid item xs={12} lg={2.4}>
                    <Member
                        autoFilter={formReset}
                        handleChange={handleChangeMember}
                        isLogTime={E_IS_LOGTIME.YES}
                        findAllType="SCREEN_EFFORT"
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={Weeklyeffort + 'members'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={Weeklyeffort + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default WeeklyEffortMemberSearch;
