// material-ui
import { TableCell, TableHead, TableRow, Tooltip } from '@mui/material';
import { FormattedMessage } from 'react-intl';

// project imports
import { rankSelector } from 'store/slice/rankSlice';
import { useAppSelector } from 'app/hooks';
import { Box } from '@mui/system';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const WeeklyEffortProjectThead = () => {
    const { rank } = useAppSelector(rankSelector);

    const { Weeklyeffort } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'projects'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={Weeklyeffort + 'department'} />
                </TableCell>
                <TableCell sx={{ fontWeight: '700 !important' }}>
                    <FormattedMessage id={Weeklyeffort + 'total-efforts-manhours'} />
                </TableCell>
                {rank?.map((item, index) => (
                    <TableCell key={index}>
                        <Tooltip
                            title={item.description}
                            placement="top"
                            componentsProps={{
                                tooltip: {
                                    sx: {
                                        color: '#616161',
                                        backgroundColor: '#EEEEEE',
                                        border: '1px solid #616161'
                                    }
                                }
                            }}
                        >
                            <Box> {item.rankName}</Box>
                        </Tooltip>
                    </TableCell>
                ))}
                <TableCell sx={{ fontWeight: '700 !important' }}>
                    <FormattedMessage id={Weeklyeffort + 'total-cost-vnd'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default WeeklyEffortProjectThead;
