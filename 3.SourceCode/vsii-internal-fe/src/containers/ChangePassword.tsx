import { useState } from 'react';
import { Button, DialogActions, Grid, IconButton, InputAdornment } from '@mui/material';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import Visibility from '@mui/icons-material/Visibility';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { changePasswordConfig, changePasswordSchema } from 'pages/Config';
import { authSelector, changePassword } from 'store/slice/authSlice';
import { FormProvider, Input } from 'components/extended/Form';
import { IChangePasswordRequest } from 'types/authentication';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { openSnackbar } from 'store/slice/snackbarSlice';
import Modal from 'components/extended/Modal';

interface IChangePasswordModalProps {
    open: boolean;
    handleClose: () => void;
}

const ChangePasswordModal = ({ open, handleClose }: IChangePasswordModalProps) => {
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);

    const { userInfo, loading } = useAppSelector(authSelector);

    const dispatch = useAppDispatch();

    const methods = useForm({
        defaultValues: { ...changePasswordConfig, userId: userInfo?.idHexString || '' },
        resolver: yupResolver(changePasswordSchema)
    });

    const handleSubmit = async (values: IChangePasswordRequest) => {
        const resultAction = await dispatch(changePassword(values));
        if (changePassword.fulfilled.match(resultAction)) {
            if (resultAction.payload?.status) {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content,
                        variant: 'alert',
                        alert: { color: 'success' }
                    })
                );
                handleClose();
            } else {
                dispatch(
                    openSnackbar({
                        open: true,
                        message: resultAction.payload?.result?.content?.message || 'Error',
                        variant: 'alert',
                        alert: { color: 'error' }
                    })
                );
            }
        }
    };

    return (
        <Modal isOpen={open} title="change-password" onClose={handleClose} maxWidth="xs">
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <Grid item container gap={3}>
                    <Grid item xs={12}>
                        <Input
                            name="passwordNow"
                            label={<FormattedMessage id="current-password" />}
                            required
                            textFieldProps={{
                                size: 'small',
                                type: showCurrentPassword ? 'text' : 'password',
                                InputProps: {
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={() => setShowCurrentPassword((prev) => !prev)}
                                                edge="end"
                                                size="small"
                                            >
                                                {showCurrentPassword ? <Visibility /> : <VisibilityOff />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <Input
                            name="newPassword"
                            label={<FormattedMessage id="new-password" />}
                            required
                            textFieldProps={{
                                size: 'small',
                                type: showNewPassword ? 'text' : 'password',
                                InputProps: {
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={() => setShowNewPassword((prev) => !prev)}
                                                edge="end"
                                                size="small"
                                            >
                                                {showNewPassword ? <Visibility /> : <VisibilityOff />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                }
                            }}
                        />
                    </Grid>
                    <Grid item xs={12}>
                        <Input
                            name="confirmPassword"
                            label={<FormattedMessage id="confirm-password" />}
                            required
                            textFieldProps={{
                                size: 'small',
                                type: showConfirmPassword ? 'text' : 'password',
                                InputProps: {
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={() => setShowConfirmPassword((prev) => !prev)}
                                                edge="end"
                                                size="small"
                                            >
                                                {showConfirmPassword ? <Visibility /> : <VisibilityOff />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                }
                            }}
                        />
                    </Grid>
                </Grid>
                <DialogActions sx={{ mt: 5 }}>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id="cancel" />
                    </Button>
                    <LoadingButton loading={loading[changePassword.typePrefix]} variant="contained" type="submit">
                        <FormattedMessage id="submit" />
                    </LoadingButton>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default ChangePasswordModal;
