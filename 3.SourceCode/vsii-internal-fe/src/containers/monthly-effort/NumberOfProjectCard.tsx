import { useEffect, useMemo, useRef, useState } from 'react';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { Grid, IconButton, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { FormattedMessage } from 'react-intl';
import Chart from 'react-apexcharts';

import SettingProjectTypeByDepartment from './SettingProjectTypeByDepartment';
import SkeletonSummaryCard from 'components/cards/Skeleton/SummaryCard';
import useLocalStorage from 'hooks/useLocalStorage';
import MainCard from 'components/cards/MainCard';
import { IProjectTypeByDepartment } from 'types';
import { SystemConfig } from 'components/icons';
import { gridSpacing } from 'store/constant';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// =========================|| NUMBER OF PROJECT CHART CARD ||========================= //

interface INumberOfProjectCardProps {
    data: IProjectTypeByDepartment[];
    isLoading: boolean;
}

const NumberOfProjectCard = ({ data = [], isLoading }: INumberOfProjectCardProps) => {
    const [filterDepartmentFromStorage, setFilterDepartmentFromStorage] = useLocalStorage('projects-statistics-of-epartments', []);

    const [filterDepartment, setFilterDepartment] = useState<{ value: string; label: string }[]>(filterDepartmentFromStorage);
    const [open, setOpen] = useState(false);

    const theme = useTheme();

    const heightRef = useRef<number>(366);
    const { Summary } = TEXT_CONFIG_SCREEN.monthlyEffort;
    const wrapChartRef = (instance: HTMLDivElement | null) => {
        const divHeight = instance?.clientHeight || 0;
        if (divHeight > heightRef.current) {
            heightRef.current = divHeight;
        }
    };

    const deptType = useMemo(() => data[0]?.projectType || [], [data]);

    const maxY = useMemo(() => {
        const projectsNumber = Math.max(
            ...(data.length ? data.flatMap((item) => item?.projectType?.map((prj) => Number(prj.total) || 0)) : [4])
        );

        let max = Number.isInteger(projectsNumber) && projectsNumber > 4 ? projectsNumber : 4;

        do {
            max++;
        } while (max % 5 !== 0);

        return max;
    }, [data]);

    const handleOpen = () => {
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const handleChangeFilterDepartment = (departments: { value: string; label: string }[]) => {
        setFilterDepartment(departments);
        setFilterDepartmentFromStorage(departments);
    };

    useEffect(() => {
        if (data && !filterDepartmentFromStorage?.length) {
            setFilterDepartment(data.map((prj) => ({ value: prj.department, label: prj.department })));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    return isLoading ? (
        <SkeletonSummaryCard />
    ) : (
        <MainCard
            sx={{ marginBottom: theme.spacing(gridSpacing) }}
            title={<FormattedMessage id={Summary + 'number-of-projects'} />}
            secondary={
                data.length ? (
                    <IconButton size="small" onClick={handleOpen}>
                        <SystemConfig />
                    </IconButton>
                ) : null
            }
        >
            {data?.length ? (
                <Grid container spacing={2} alignItems="center">
                    <Grid container spacing={2} sx={{ height: heightRef.current, overflowY: 'auto', mt: 0 }}>
                        {data
                            .filter((item) => filterDepartment.some((dept) => dept.value === item.department))
                            .map((item, index) => (
                                <Grid
                                    key={index}
                                    item
                                    xs={12}
                                    lg={4}
                                    sx={{ pt: '0 !important' }}
                                    ref={index === 0 ? wrapChartRef : undefined}
                                >
                                    <Chart
                                        height={350}
                                        type="bar"
                                        series={[
                                            {
                                                name: 'Number of projects',
                                                data: item.projectType.map((prj) => ({ x: prj.type, y: prj.total || 0 }))
                                            }
                                        ]}
                                        options={{
                                            colors: deptType.map((x) => x.color || '#ffffff'),
                                            chart: {
                                                toolbar: {
                                                    show: false
                                                }
                                            },
                                            plotOptions: {
                                                bar: {
                                                    distributed: true,
                                                    borderRadius: 0,
                                                    dataLabels: {
                                                        position: 'top'
                                                    }
                                                }
                                            },
                                            legend: {
                                                show: false
                                            },
                                            dataLabels: {
                                                enabled: true,
                                                offsetY: -10,
                                                style: {
                                                    fontSize: '0.75rem',
                                                    colors: ['#304758']
                                                }
                                            },
                                            xaxis: {
                                                position: 'top',
                                                axisTicks: {
                                                    show: false
                                                },
                                                labels: {
                                                    show: false
                                                }
                                            },
                                            yaxis: {
                                                min: 0,
                                                max: maxY,
                                                tickAmount: maxY
                                            },
                                            title: {
                                                text: item.department,
                                                floating: true,
                                                offsetY: 330,
                                                align: 'center',
                                                style: {
                                                    color: '#444'
                                                }
                                            },
                                            grid: {
                                                show: true
                                            }
                                        }}
                                    />
                                </Grid>
                            ))}
                    </Grid>
                    <Grid item xs={12} lg={12}>
                        <Grid container>
                            {deptType.map((item, index) => (
                                <Grid item xs={12} lg={3} key={index}>
                                    <Grid container spacing={2} sx={{ alignItems: 'center' }}>
                                        <Grid item>
                                            <FiberManualRecordIcon sx={{ color: item.color || '#ffffff' }} />
                                        </Grid>
                                        <Grid item xs zeroMinWidth>
                                            <Grid container spacing={1}>
                                                <Grid item xs zeroMinWidth>
                                                    <Typography align="left" variant="body2">
                                                        {item.type}
                                                    </Typography>
                                                </Grid>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>
                </Grid>
            ) : null}
            {open && (
                <SettingProjectTypeByDepartment
                    open={open}
                    data={data}
                    handleClose={handleCloseDialog}
                    filterDepartment={filterDepartment}
                    handleChangeFilterDepartment={handleChangeFilterDepartment}
                />
            )}
        </MainCard>
    );
};

export default NumberOfProjectCard;
