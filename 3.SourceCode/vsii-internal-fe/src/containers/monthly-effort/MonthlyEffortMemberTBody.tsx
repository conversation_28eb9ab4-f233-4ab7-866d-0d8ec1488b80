import { Fragment } from 'react';

// material-ui
import { TableBody, TableCell, TableRow, Tooltip } from '@mui/material';

// project imports
import { ROW_COLOR } from 'constants/Common';
import { IMonthlyEffortMember, IProjectVerified } from 'types';
import { compareStyleEffortMember } from 'utils/common';

// third party
import { IntlShape, useIntl } from 'react-intl';

interface IMonthlyEffortMemberTBodyProps {
    pageNumber: number;
    pageSize: number;
    members: IMonthlyEffortMember[];
}

const statusColors = (differenceHours: string) => {
    return +differenceHours === 0 ? ROW_COLOR.ENOUGH : +differenceHours < 0 ? ROW_COLOR.NOT_ENOUGH : ROW_COLOR.EXCEED;
};

const renderProjectCell = (projects: IProjectVerified[], intl: IntlShape) => {
    return projects.map((item, key) => (
        <Fragment key={key}>
            <span>{item.project}</span>(
            <Tooltip
                placement="top"
                title={
                    <span>
                        {intl.formatMessage({ id: 'effort-pm-verified' })}/<br />
                        {intl.formatMessage({ id: 'effort-in-month' })}
                    </span>
                }
            >
                <span style={compareStyleEffortMember(item.effortVerified, item.effort)}>{`${item.effortVerified}/${item.effort}, `}</span>
            </Tooltip>
            <Tooltip
                placement="top"
                title={
                    <span>
                        {intl.formatMessage({ id: 'payable-ot-verified' })}/<br />
                        {intl.formatMessage({ id: 'payable-ot' })}
                    </span>
                }
            >
                <span style={compareStyleEffortMember(item.payOTVerified, item.payOT)}>{`${item.payOTVerified}/${item.payOT}, `}</span>
            </Tooltip>
            <Tooltip
                placement="top"
                title={
                    <span>
                        {intl.formatMessage({ id: 'non-payable-ot-verified' })}/<br />
                        {intl.formatMessage({ id: 'non-payable-ot' })}
                    </span>
                }
            >
                <span style={compareStyleEffortMember(item.nonPayVerified, item.nonPay)}>{`${item.nonPayVerified}/${item.nonPay}`}</span>
            </Tooltip>
            )
            <br />
        </Fragment>
    ));
};

const MonthlyEffortMemberTBody = (props: IMonthlyEffortMemberTBodyProps) => {
    const { pageNumber, pageSize, members } = props;
    const intl = useIntl();

    return (
        <TableBody>
            {members.map((member, key) => (
                <TableRow
                    key={key}
                    sx={{
                        backgroundColor: statusColors(member.differenceHours)
                    }}
                >
                    <TableCell>{pageSize * pageNumber + key + 1}</TableCell>
                    <TableCell>
                        {member.firstName} {member.lastName}
                    </TableCell>
                    <TableCell>{member.memberCode}</TableCell>
                    <TableCell>{member.rank}</TableCell>
                    <TableCell>{member.department}</TableCell>
                    <TableCell>{member.effortInMonth}</TableCell>
                    <TableCell>{member.differenceHours}</TableCell>
                    <TableCell>{member.projectListVerifieds && renderProjectCell(member.projectListVerifieds, intl)}</TableCell>
                </TableRow>
            ))}
        </TableBody>
    );
};

export default MonthlyEffortMemberTBody;
