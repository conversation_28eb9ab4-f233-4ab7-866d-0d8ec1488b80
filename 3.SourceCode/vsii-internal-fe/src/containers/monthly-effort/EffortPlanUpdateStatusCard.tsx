import { useEffect, useState } from 'react';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import EditTwoToneIcon from '@mui/icons-material/EditTwoTone';
import { Typography, Grid, IconButton } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/system';
import Chart from 'react-apexcharts';

import { IMonthlyEffortSummaryConfig, effortPlanUpdateStatusColors } from 'pages/monthly-effort/Config';
import SkeletonSummaryCard from 'components/cards/Skeleton/SummaryCard';
import { checkAllowedPermission } from 'utils/authorization';
import { PERMISSIONS } from 'constants/Permission';
import UpdateEffortPlan from './UpdateEffortPlan';
import MainCard from 'components/cards/MainCard';
import { gridSpacing } from 'store/constant';
import { IEffortPlan } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// =========================|| EFFORT PLAN UPDATE STATUS CHART CARD ||========================= //

interface IEffortPlanUpdateStatusCardProps {
    data: IEffortPlan[];
    isLoading: boolean;
    conditions: IMonthlyEffortSummaryConfig;
    fetchData: () => Promise<void>;
}

const EffortPlanUpdateStatusCard = ({ isLoading, data, conditions, fetchData }: IEffortPlanUpdateStatusCardProps) => {
    const [filterDepartment, setFilterDepartment] = useState<IEffortPlan[]>([]);
    const [open, setOpen] = useState<boolean>(false);

    const theme = useTheme();

    const intl = useIntl();

    const { monthlyEffort } = PERMISSIONS.report;

    const { Summary } = TEXT_CONFIG_SCREEN.monthlyEffort;

    const handleOpenDialog = () => {
        setOpen(true);
    };

    const isLargScreen = useMediaQuery(theme.breakpoints.between('xs', 'xl'));

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const handleChangeFilterDepartment = (departments: IEffortPlan[]) => {
        setFilterDepartment(departments);
    };

    useEffect(() => {
        setFilterDepartment(data?.filter((item) => item.show));
    }, [data]);

    return isLoading ? (
        <SkeletonSummaryCard />
    ) : (
        <MainCard
            sx={{
                marginBottom: theme.spacing(gridSpacing),
                overflow: 'unset',
                height: '100%'
            }}
            contentSX={{
                height: '100%',
                justifyContent: 'center',
                alignItem: 'center',
                display: 'flex'
            }}
            title={<FormattedMessage id={Summary + 'update-effort-plan'} />}
            secondary={
                checkAllowedPermission(monthlyEffort.editEffortPlan) && (
                    <IconButton size="small" onClick={handleOpenDialog}>
                        <EditTwoToneIcon fontSize="small" />
                    </IconButton>
                )
            }
        >
            <Grid container spacing={2} sx={{ marginTop: theme.spacing(gridSpacing) }}>
                <Grid item xs={12} lg={12} sx={{ alignItems: 'center' }}>
                    <Chart
                        options={{
                            colors: effortPlanUpdateStatusColors.map((x) => x.color || '#ffffff'),
                            chart: {
                                type: 'bar',
                                stacked: true,
                                stackType: '100%',
                                toolbar: { show: false }
                            },
                            legend: {
                                show: false
                            },
                            xaxis: {
                                categories: filterDepartment.map((item) => item.department),
                                labels: {
                                    style: { fontWeight: 600 }
                                }
                            },
                            dataLabels: {
                                formatter: function (value: number) {
                                    return value.toFixed(1) + '%';
                                }
                            },
                            yaxis: {
                                tickAmount: 5,
                                labels: {
                                    formatter: (value: number) => {
                                        return `${value}`;
                                    }
                                }
                            }
                        }}
                        series={effortPlanUpdateStatusColors.map((cate) => ({
                            name: `${intl.formatMessage({ id: cate.name })} ${conditions.month}`,
                            data: filterDepartment.map((item) => (cate.name === 'send-report' ? item.sentReport : item.notSentReport))
                        }))}
                        type="bar"
                        height={isLargScreen ? 240 : ''}
                    />
                </Grid>
                <Grid item xs={12} lg={12}>
                    <Grid container alignItems="left" flexDirection="column-reverse">
                        {effortPlanUpdateStatusColors.map((item, index) => (
                            <Grid item xs={12} lg={12} key={index}>
                                <Grid container spacing={2} alignItems="center" justifyContent="center">
                                    <Grid item>
                                        <FiberManualRecordIcon sx={{ color: item.color || '#ffffff' }} />
                                    </Grid>
                                    <Grid item xs zeroMinWidth>
                                        <Grid container spacing={1}>
                                            <Grid item xs zeroMinWidth>
                                                <Typography align="left" variant="body2">
                                                    <FormattedMessage id={`${Summary}${item.name}`} /> {conditions.month}
                                                </Typography>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
            </Grid>

            {/* Update Effort Plan Dialog */}
            {open ? (
                <UpdateEffortPlan
                    open={open}
                    data={data}
                    handleClose={handleCloseDialog}
                    filterDepartment={filterDepartment}
                    handleChangeFilterDepartment={handleChangeFilterDepartment}
                    fetchData={fetchData}
                />
            ) : null}
        </MainCard>
    );
};

export default EffortPlanUpdateStatusCard;
