import { FormattedMessage } from 'react-intl';

// material-ui
import { IMonthlyEffortConfig, monthlyEffortConfig, monthlyEffortDepartmentMemberSchema } from 'pages/monthly-effort/Config';
import { E_IS_LOGTIME, TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import ErrorIcon from '@mui/icons-material/Error';
import { Label } from 'components/extended/Form';
import { Grid, Typography } from '@mui/material';
import { Button } from 'components';

// project imports
import { Department, Member, Months, SearchForm, TimeStatus, Years } from '../search';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { IMember } from 'types/member';
import { IOption } from 'types';
interface IMonthlyEffortMemberSearchProps {
    formReset: IMonthlyEffortConfig;
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    handleChangeMonth?: (value: string) => void;
    handleChangeTimeStatus?: (value: string) => void;
    handleChangeDepartmentId?: (value: string) => void;
    handleChangeMember?: (value: IMember) => void;
}

const MonthlyEffortMemberSearch = (props: IMonthlyEffortMemberSearchProps) => {
    const {
        formReset,
        months,
        handleChangeYear,
        handleSearch,
        handleChangeMonth,
        handleChangeTimeStatus,
        handleChangeDepartmentId,
        handleChangeMember
    } = props;

    const { effortbymember } = TEXT_CONFIG_SCREEN.monthlyEffort;
    return (
        <SearchForm
            defaultValues={monthlyEffortConfig}
            formSchema={monthlyEffortDepartmentMemberSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2}>
                    <Years handleChangeYear={handleChangeYear} label={effortbymember + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Months months={months} onChange={handleChangeMonth} isFilter year={formReset.year} label={effortbymember + 'month'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <TimeStatus onChange={handleChangeTimeStatus} label={effortbymember + 'timesheet-status'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Department onChange={handleChangeDepartmentId} label={effortbymember + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Member
                        isLogTime={E_IS_LOGTIME.YES}
                        autoFilter={formReset}
                        handleChange={handleChangeMember}
                        findAllType="SCREEN_EFFORT"
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={effortbymember + 'members'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={effortbymember + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyEffortMemberSearch;
