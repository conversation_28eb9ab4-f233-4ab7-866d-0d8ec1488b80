import { useState } from 'react';
import { <PERSON>rid, <PERSON>ack, Button, TableContainer, Table, TableHead, TableBody, TableRow, TableCell } from '@mui/material';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';
import { LoadingButton } from '@mui/lab';

import { IEffortPlan, IResponseList, IUpdateEffortPlanResponse } from 'types';
import { Checkbox, FormProvider, Input } from 'components/extended/Form';
import { updateEffortPlanFormSchema } from 'pages/monthly-effort/Config';
import { openSnackbar } from 'store/slice/snackbarSlice';
import sendRequest from 'services/ApiService';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';
import { useAppDispatch } from 'app/hooks';
import Api from 'constants/Api';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// ==============================|| UPDATE EFFORT PLAN DIALOG ||============================== //

interface IUpdateEffortPlanProps {
    open: boolean;
    data: IEffortPlan[];
    handleClose: () => void;
    filterDepartment: IEffortPlan[];
    handleChangeFilterDepartment: (departments: IEffortPlan[]) => void;
    fetchData: () => Promise<void>;
}

const UpdateEffortPlan = ({
    open,
    data,
    filterDepartment,
    handleChangeFilterDepartment,
    handleClose,
    fetchData
}: IUpdateEffortPlanProps) => {
    const [loading, setLoading] = useState(false);
    const [modalFilterDepartment, setModalFilterDepartment] = useState<IEffortPlan[]>(filterDepartment);

    const dispatch = useAppDispatch();

    const { Summary } = TEXT_CONFIG_SCREEN.monthlyEffort;

    const methods = useForm({
        defaultValues: {
            effortPlanDepts: data
        },
        resolver: yupResolver(updateEffortPlanFormSchema),
        mode: 'all'
    });

    const handleSubmit = async (values: { effortPlanDepts: IEffortPlan[] }) => {
        handleChangeFilterDepartment(modalFilterDepartment);
        setLoading(true);
        const response: IResponseList<IUpdateEffortPlanResponse> = await sendRequest(
            Api.monthly_efford.postUpdateEffordPlan,
            values.effortPlanDepts
        );

        dispatch(
            openSnackbar({
                open: true,
                message: response?.status ? 'update-success' : 'update-effort-plan-error',
                variant: 'alert',
                alert: { color: response?.status ? 'success' : 'error' }
            })
        );

        if (response?.status) {
            await fetchData();
        }
        setLoading(false);
        handleClose();
    };

    return (
        <Modal isOpen={open} title={Summary + 'effort-plan'} onClose={handleClose}>
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing}>
                    <TableContainer
                        sx={{
                            minHeight: '10vh',
                            maxHeight: '50vh'
                        }}
                    >
                        <Table stickyHeader aria-label="sticky table" size="small" sx={{ '& td, & th': { border: 0 } }}>
                            <TableHead>
                                <TableRow>
                                    <TableCell sx={{ width: '12%', px: '6px' }} />
                                    <TableCell sx={{ width: '40%', px: '6px' }} align="center">
                                        <FormattedMessage id={Summary + 'sent-monthly'} />
                                    </TableCell>
                                    <TableCell sx={{ width: '40%', px: '6px' }} align="center">
                                        <FormattedMessage id={Summary + 'not-sent-monthly'} />
                                    </TableCell>
                                    <TableCell sx={{ width: '8%', px: '6px' }}>
                                        <FormattedMessage id={Summary + 'show-chart'} />
                                    </TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {data.map((item, index) => {
                                    const fieldName = `effortPlanDepts[${index}]`;

                                    return (
                                        <TableRow
                                            key={fieldName}
                                            sx={{
                                                height: '75px',
                                                '& td:not(:first-of-type)': { verticalAlign: 'top' }
                                            }}
                                        >
                                            <TableCell sx={{ fontWeight: 500, width: '12%', p: '6px 6px 25px 5px' }}>
                                                {item.department}
                                            </TableCell>
                                            <TableCell sx={{ width: '40%', px: '6px' }}>
                                                <Grid container>
                                                    <Input name={`${fieldName}.sentReport`} type="number" />
                                                </Grid>
                                            </TableCell>
                                            <TableCell sx={{ width: '40%', px: '6px' }}>
                                                <Input name={`${fieldName}.notSentReport`} type="number" />
                                            </TableCell>
                                            <TableCell sx={{ width: '8%', px: 0 }} align="center">
                                                <Checkbox
                                                    name={`${fieldName}.show`}
                                                    sx={{
                                                        mr: 0
                                                    }}
                                                    valueChecked={modalFilterDepartment?.some((dept) => dept.id === item.id)}
                                                    handleChange={(e) => {
                                                        setModalFilterDepartment((prev) => {
                                                            if (e.target.checked) {
                                                                return !prev.some((dept) => dept.id === item.id) ? [...prev, item] : prev;
                                                            } else {
                                                                return prev.filter((dept) => dept.id !== item.id);
                                                            }
                                                        });
                                                    }}
                                                    disabled={
                                                        (modalFilterDepartment.length >= 5 &&
                                                            !modalFilterDepartment.some((dept) => dept.id === item.id)) ||
                                                        (modalFilterDepartment.length <= 1 &&
                                                            modalFilterDepartment.some((dept) => dept.id === item.id))
                                                    }
                                                />
                                            </TableCell>
                                        </TableRow>
                                    );
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                    <Grid item xs={12}>
                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                            <Button color="error" onClick={handleClose}>
                                <FormattedMessage id={Summary + 'cancel'} />
                            </Button>
                            <LoadingButton variant="contained" type="submit" loading={loading}>
                                <FormattedMessage id={Summary + 'submit'} />
                            </LoadingButton>
                        </Stack>
                    </Grid>
                </Grid>
            </FormProvider>
        </Modal>
    );
};

export default UpdateEffortPlan;
