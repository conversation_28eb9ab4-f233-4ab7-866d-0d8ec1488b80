import { useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { Grid } from '@mui/material';
// material-ui

// project imports
import { IMonthlyEffortProjectConfig, monthlyEffortOmReportSchema, monthlyEffortOrmReportConfig } from 'pages/monthly-effort/Config';
import { getProjectAllForOption } from 'store/slice/monthlyEffortSlice';
import { Department, SearchForm, Months, Years } from '../search';
import ReportName from 'containers/search/ReportName';
import { convertMonthFromToDate } from 'utils/date';
import { Label } from 'components/extended/Form';
import { useAppDispatch } from 'app/hooks';
import { Button } from 'components';
import { IOption } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IMonthlyEffortORMReportSearchProps {
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    formReset: IMonthlyEffortProjectConfig;
}

const MonthlyEffortORMReportSearch = (props: IMonthlyEffortORMReportSearchProps) => {
    const { months, handleChangeYear, handleSearch, formReset } = props;

    const [month, setMonth] = useState({ fromDate: '', toDate: '' });

    const { ORMReport } = TEXT_CONFIG_SCREEN.generalReport;

    const handleMonthChange = (value: string) => {
        const getMonth = months.filter((month) => {
            return month.value === value;
        });

        return setMonth(convertMonthFromToDate(getMonth[0].label));
    };

    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(getProjectAllForOption({ type: 'month', value: month }));
    }, [dispatch, month]);

    return (
        <SearchForm
            defaultValues={monthlyEffortOrmReportConfig}
            formSchema={monthlyEffortOmReportSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Department name="department" label={ORMReport + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Years handleChangeYear={handleChangeYear} label={ORMReport + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Months months={months} onChange={handleMonthChange} isFilter label={ORMReport + 'month'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <ReportName name="reportName" label={<FormattedMessage id={ORMReport + 'report-name'} />} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={ORMReport + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyEffortORMReportSearch;
