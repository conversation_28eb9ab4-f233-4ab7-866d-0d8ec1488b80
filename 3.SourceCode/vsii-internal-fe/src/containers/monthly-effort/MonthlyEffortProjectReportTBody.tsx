import { useCallback, useState } from 'react';
import { ButtonBase, TableBody, TableCell, TableRow } from '@mui/material';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import DownloadingIcon from '@mui/icons-material/Downloading';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import EditIcon from '@mui/icons-material/Edit';
import { FormattedMessage } from 'react-intl';
import { Box } from '@mui/system';

import ReviewsIcon from '@mui/icons-material/Reviews';

import { closeConfirm, openConfirm } from 'store/slice/confirmSlice';
import { IMonthlyEffortProjectReport, IResponseList } from 'types';
import { checkAllowedPermission } from 'utils/authorization';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import TableEmpty from 'components/extended/Table/Empty';
import { openSnackbar } from 'store/slice/snackbarSlice';
import { authSelector } from 'store/slice/authSlice';
import { PERMISSIONS } from 'constants/Permission';
import sendRequest from 'services/ApiService';
import { exportDocument } from 'utils/common';
import { dateFormat } from 'utils/date';
import Api from 'constants/Api';

interface IMonthlyEffortProjectReportTBodyProps {
    projects: IMonthlyEffortProjectReport[];
    sendataReport?: (report: IMonthlyEffortProjectReport) => Promise<void>;
    resetDataTable: () => void;
}

const MonthlyEffortProjectReportTBody = (props: IMonthlyEffortProjectReportTBodyProps) => {
    const { projects, sendataReport, resetDataTable } = props;
    const theme = useTheme();
    const [projectCell, setProjectCell] = useState<any>(null);
    const [deptIdCell, setDeptIdCell] = useState<any>(null);
    const { userInfo } = useAppSelector(authSelector);
    const dispatch = useAppDispatch();

    const projectCellRef = useCallback((domNode: any) => {
        if (domNode) {
            setProjectCell(domNode.getBoundingClientRect());
        }
    }, []);

    const deptIdRef = useCallback((domNode: any) => {
        if (domNode) {
            setDeptIdCell(domNode.getBoundingClientRect());
        }
    }, []);

    const matches = useMediaQuery(theme.breakpoints.up('md'));
    const hanldeSendReportInfor = (report: IMonthlyEffortProjectReport) => {
        sendataReport?.(report);
    };

    const handleDownloading = async (id: string) => {
        await exportDocument(`${Api.monthly_efford.getDownloadReport.url}/${id}`);
    };
    // delete
    const deleteReport = async (id: string) => {
        const response: IResponseList<IMonthlyEffortProjectReport> = await sendRequest(Api.monthly_efford.deleteReport(id));
        const status = response?.status;
        if (status) {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'delete-success',
                    variant: 'alert',
                    alert: { color: 'success' }
                })
            );
            dispatch(closeConfirm());
            resetDataTable();
        } else {
            dispatch(
                openSnackbar({
                    open: true,
                    message: 'delete-error',
                    variant: 'alert',
                    alert: { color: 'error' }
                })
            );
            dispatch(closeConfirm());
        }
    };
    const handleOpenConfirm = (item: IMonthlyEffortProjectReport, type?: string) => {
        dispatch(
            openConfirm({
                open: true,
                title: <FormattedMessage id="warning" />,
                content: <FormattedMessage id="delete-repord" />,
                handleConfirm: () => deleteReport(item.id)
            })
        );
    };

    return (
        <>
            {!projects.length && <TableEmpty />}
            {projects.length && (
                <TableBody>
                    {projects.map((item, key) => (
                        <TableRow key={key} style={{ background: 'white' }}>
                            <TableCell
                                ref={projectCellRef}
                                sx={{ position: 'sticky', left: !!matches ? 0 : 'unset', background: 'inherit' }}
                            >
                                {key + 1}
                            </TableCell>
                            <TableCell
                                align="left"
                                ref={deptIdRef}
                                sx={{
                                    position: 'sticky',
                                    left: !!matches ? projects && projectCell?.width : 'unset',
                                    background: 'inherit'
                                }}
                            >
                                {item.projectName}
                            </TableCell>
                            <TableCell
                                sx={{
                                    position: 'sticky',
                                    left: !!matches ? projects && projectCell?.width + deptIdCell?.width : 'unset',
                                    background: 'inherit',
                                    whiteSpace: 'nowrap'
                                }}
                            >
                                {`${item.firstNamePM} ${item.lastNamePM}`}
                            </TableCell>
                            <TableCell>{item.projectPhase}</TableCell>
                            <TableCell
                                sx={{
                                    color: item.projectProgressAssessment?.toLowerCase() === 'behind schedule' ? '#FF0000' : '',
                                    textTransform: 'capitalize'
                                }}
                            >
                                {item.projectProgressAssessment}
                            </TableCell>
                            <TableCell>{item.department}</TableCell>
                            <TableCell>{item.projectType}</TableCell>
                            <TableCell>{dateFormat(item.startDate)}</TableCell>
                            <TableCell>{dateFormat(item.endDate)}</TableCell>
                            <TableCell>{item.userUpdated}</TableCell>
                            <TableCell>{dateFormat(item.lastUpdated)}</TableCell>

                            <TableCell>
                                <TableCell sx={{ border: 'none' }}>
                                    <Box display="flex" gap={2}>
                                        <ButtonBase onClick={() => hanldeSendReportInfor(item)}>
                                            {userInfo?.role?.find((item) => item.groupId === 'Admin') ||
                                            item.userCreated === userInfo?.userName ||
                                            item.userPM === userInfo?.userName ? (
                                                <EditIcon />
                                            ) : (
                                                <ReviewsIcon />
                                            )}
                                        </ButtonBase>

                                        {checkAllowedPermission(PERMISSIONS.report.generalReport.projectReport.download) && (
                                            <ButtonBase onClick={() => handleDownloading(item.id)}>
                                                <DownloadingIcon />
                                            </ButtonBase>
                                        )}

                                        {item.userUpdated === userInfo?.userName &&
                                            checkAllowedPermission(PERMISSIONS.report.generalReport.projectReport.delete) && (
                                                <ButtonBase onClick={() => handleOpenConfirm(item)}>
                                                    <HighlightOffIcon />
                                                </ButtonBase>
                                            )}
                                    </Box>
                                </TableCell>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            )}
        </>
    );
};

export default MonthlyEffortProjectReportTBody;
