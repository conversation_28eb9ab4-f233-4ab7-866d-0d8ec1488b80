import { useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';

// material-ui
import ErrorIcon from '@mui/icons-material/Error';
import { Grid, Typography } from '@mui/material';

// project imports
import { Button } from 'components';
import { Label } from 'components/extended/Form';
import { IMonthlyEffortConfig, monthlyEffortConfig, monthlyEffortDepartmentProjectSchema } from 'pages/monthly-effort/Config';
import { IOption } from 'types';
import { Months, Project, SearchForm, Years } from '../search';
import { convertMonthFromToDate } from 'utils/date';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import ColorNoteTooltip from 'components/ColorNoteTooltip';

interface IMonthlyEffortMemberProjectSearchProps {
    formReset: IMonthlyEffortConfig;
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
}

const MonthlyEffortMemberProjectSearch = (props: IMonthlyEffortMemberProjectSearchProps) => {
    const { formReset, months, handleChangeYear, handleSearch } = props;

    const monthOnload = months.find((item) => item.value === formReset.month);

    const [month, setMonth] = useState({ fromDate: '', toDate: '' });

    const { effortbymember } = TEXT_CONFIG_SCREEN.monthlyEffort;

    const handleMonthChange = (value: string) => {
        const getMonth = months.find((month) => month.value === value);

        if (getMonth) {
            setMonth(convertMonthFromToDate(getMonth.label));
        }
    };
    useEffect(() => {
        if (monthOnload) {
            setMonth(convertMonthFromToDate(monthOnload.label as string));
        }
    }, [monthOnload]);

    return (
        <SearchForm
            defaultValues={monthlyEffortConfig}
            formSchema={monthlyEffortDepartmentProjectSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Years handleChangeYear={handleChangeYear} label={effortbymember + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Months months={months} onChange={handleMonthChange} isFilter label={effortbymember + 'month'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Project
                        isDefaultAll
                        month={month}
                        isNotStatus
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={effortbymember + 'projects'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={effortbymember + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyEffortMemberProjectSearch;
