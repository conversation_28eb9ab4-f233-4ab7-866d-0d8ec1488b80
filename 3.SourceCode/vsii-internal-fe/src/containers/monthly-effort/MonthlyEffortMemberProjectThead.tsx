// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormattedMessage } from 'react-intl';

const MonthlyEffortProjectThead = () => {
    const { effortbymember } = TEXT_CONFIG_SCREEN.monthlyEffort;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'projects'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'department'} />
                </TableCell>
                <TableCell sx={{ fontWeight: '700 !important' }}>
                    <FormattedMessage id={effortbymember + 'total-effort'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default MonthlyEffortProjectThead;
