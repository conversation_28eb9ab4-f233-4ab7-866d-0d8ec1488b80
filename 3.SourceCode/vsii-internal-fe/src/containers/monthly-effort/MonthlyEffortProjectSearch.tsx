import { FormattedMessage } from 'react-intl';
import { useEffect } from 'react';
// material-ui
import { <PERSON>rid, <PERSON><PERSON>, SelectChangeEvent, Typography } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';

// project imports
import { IMonthlyEffortProjectConfig, monthlyEffortProjectConfig, monthlyEffortProjectSchema } from 'pages/monthly-effort/Config';
import { getProjectAllForOption, monthlyEffortSelector } from 'store/slice/monthlyEffortSlice';
import { DEFAULT_VALUE_OPTION, TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import { Department, SearchForm, Months, Years, ProjectType, Billable } from '../search';
import { Autocomplete, Label } from 'components/extended/Form';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { searchFormConfig } from 'containers/search/Config';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { Button } from 'components';
import { IOption } from 'types';

interface IMonthlyEffortProjectSearchProps {
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    formReset: IMonthlyEffortProjectConfig;
    handleChangeProject?: (data: any) => void;
    handleChangeMonth?: (value: any) => void;
    month?: { fromDate: string; toDate: string };
    handleChangeDept?: (value: string) => void;
    handleChangeProjectType?: (e: SelectChangeEvent<unknown>) => void;
    handleChangeBillable?: (e: SelectChangeEvent<unknown>) => void;
}

const MonthlyEffortProjectSearch = (props: IMonthlyEffortProjectSearchProps) => {
    const {
        months,
        handleChangeYear,
        handleSearch,
        formReset,
        handleChangeProject,
        handleChangeProjectType,
        handleChangeBillable,
        handleChangeMonth,
        month,
        handleChangeDept
    } = props;

    const { projectOptions } = useAppSelector(monthlyEffortSelector);

    const dispatch = useAppDispatch();

    const { monthlyEffortProject } = TEXT_CONFIG_SCREEN.monthlyEffort;

    useEffect(() => {
        if (month?.fromDate && month?.toDate && formReset) {
            dispatch(
                getProjectAllForOption({
                    type: 'month',
                    value: month,
                    dept: formReset.departmentId,
                    projectType: formReset.projectType,
                    billable: formReset.billable,
                    color: true
                })
            );
        }
    }, [dispatch, month, formReset]);

    return (
        <SearchForm
            defaultValues={monthlyEffortProjectConfig}
            formSchema={monthlyEffortProjectSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={1.75}>
                    <Years handleChangeYear={handleChangeYear} label={monthlyEffortProject + 'year'} />
                </Grid>
                <Grid item xs={12} lg={1.75}>
                    <Months
                        months={months}
                        onChange={handleChangeMonth}
                        isFilter
                        year={formReset.year}
                        label={monthlyEffortProject + 'month'}
                    />
                </Grid>
                <Grid item xs={12} lg={1.75}>
                    <Department onChange={handleChangeDept} label={monthlyEffortProject + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={1.75}>
                    <ProjectType handleChangeProjectType={handleChangeProjectType} label={monthlyEffortProject + 'project-type'} />
                </Grid>

                <Grid item xs={12} lg={1.75}>
                    <Autocomplete
                        options={projectOptions}
                        name={searchFormConfig.project.name}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={monthlyEffortProject + 'projects'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                        groupBy={(option: IOption) => option.typeCode}
                        isDefaultAll
                        PopperComponent={(props) => (
                            <Popper {...props} style={{ width: 250 }}>
                                {props.children}
                            </Popper>
                        )}
                        handleChange={handleChangeProject}
                    />
                </Grid>
                <Grid item xs={12} lg={1.75}>
                    <Billable
                        defaultOption={DEFAULT_VALUE_OPTION}
                        handleChangeBillable={handleChangeBillable}
                        label={<FormattedMessage id={monthlyEffortProject + 'billable'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={1.5}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={monthlyEffortProject + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyEffortProjectSearch;
