import { Button, <PERSON>alogActions, Grid, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { useForm } from 'react-hook-form';

import { CheckboxGroup, FormProvider } from 'components/extended/Form';
import { IProjectTypeByDepartment } from 'types';
import Modal from 'components/extended/Modal';
import { gridSpacing } from 'store/constant';

interface ISettingProjectTypeByDepartmentProps {
    open: boolean;
    handleClose: () => void;
    data: IProjectTypeByDepartment[];
    filterDepartment: { value: string; label: string }[];
    handleChangeFilterDepartment: (departments: { value: string; label: string }[]) => void;
}

const SettingProjectTypeByDepartment = ({
    open,
    data,
    handleClose,
    filterDepartment,
    handleChangeFilterDepartment
}: ISettingProjectTypeByDepartmentProps) => {
    const methods = useForm({
        defaultValues: {
            departments: filterDepartment
        }
    });

    const handleSubmit = (values: { departments: { value: string; label: string }[] }) => {
        handleChangeFilterDepartment(values.departments);
        handleClose();
    };

    return (
        <Modal isOpen={open} title="setting-project-type-by-department" onClose={handleClose} maxWidth="xs">
            <FormProvider formReturn={methods} onSubmit={handleSubmit}>
                <Grid container spacing={gridSpacing}>
                    <Grid item xs={12}>
                        <Typography
                            sx={{
                                color: '#000000',
                                fontSize: 14
                            }}
                        >
                            <FormattedMessage id="select-project-type-by-department" />
                        </Typography>
                    </Grid>
                    <Grid
                        item
                        xs={12}
                        sx={{
                            minHeight: '10vh',
                            maxHeight: '50vh',
                            overflowX: 'auto'
                        }}
                    >
                        <CheckboxGroup
                            name="departments"
                            labelPlacement="start"
                            labelOptionSx={{
                                color: '#000000',
                                fontWeight: 500
                            }}
                            options={data.map((item) => ({ label: item.department, value: item.department }))}
                            config={{ value: 'value', label: 'label' }}
                            sx={{
                                display: 'flex',
                                ml: 0
                            }}
                            loadMore={false}
                        />
                    </Grid>
                </Grid>

                <DialogActions sx={{ mt: 5 }}>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id="cancel" />
                    </Button>
                    <Button disabled={methods.watch('departments')?.length < 1} variant="contained" type="submit">
                        <FormattedMessage id="submit" />
                    </Button>
                </DialogActions>
            </FormProvider>
        </Modal>
    );
};

export default SettingProjectTypeByDepartment;
