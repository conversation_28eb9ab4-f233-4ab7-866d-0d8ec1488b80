import { Grid, Typography, List, ListItem, ListItemText } from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { createSearchParams, useNavigate } from 'react-router-dom';
import { FormattedMessage } from 'react-intl';
import Chart from 'react-apexcharts';

import { IMonthlyEffortSummaryConfig } from 'pages/monthly-effort/Config';
import SkeletonSummaryCard from 'components/cards/Skeleton/SummaryCard';
import MainCard from 'components/cards/MainCard';
import { IEffortByProjectType } from 'types';
import { ROUTER } from 'constants/Routers';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// =========================|| ACTUAL EFFORT ALLOCATION CARD ||========================= //

interface IActualEffortAllocationCardProps {
    isLoading: boolean;
    conditions: IMonthlyEffortSummaryConfig;
    data: IEffortByProjectType[];
}

const ActualEffortAllocationCard = ({ isLoading, data = [], conditions }: IActualEffortAllocationCardProps) => {
    const navigate = useNavigate();

    const { Summary } = TEXT_CONFIG_SCREEN.monthlyEffort;
    return isLoading ? (
        <SkeletonSummaryCard />
    ) : (
        <MainCard title={<FormattedMessage id={Summary + 'effort-allocation'} />}>
            <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} lg={4}>
                    <List>
                        <ListItem
                            sx={{
                                '& .MuiTypography-root': {
                                    fontWeight: '700 !important'
                                }
                            }}
                            secondaryAction={
                                <Typography>
                                    <FormattedMessage id={Summary + 'effort-md'} />
                                </Typography>
                            }
                        >
                            <ListItemText>
                                <FormattedMessage id={Summary + 'type'} />
                            </ListItemText>
                        </ListItem>
                        {data.map((item, index) => (
                            <ListItem key={index} secondaryAction={<Typography>{item.effort}</Typography>}>
                                <ListItemText>{item.type}</ListItemText>
                            </ListItem>
                        ))}
                    </List>
                </Grid>
                <Grid item xs={12} lg={8}>
                    <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} lg={6}>
                            <Chart
                                options={{
                                    colors: data.map((item) => item.color || '#ffffff'),
                                    responsive: [
                                        {
                                            breakpoint: 1920,
                                            options: {
                                                chart: { width: 350 }
                                            }
                                        }
                                    ],
                                    legend: { show: false },
                                    chart: {
                                        events: {
                                            dataPointSelection: (_event: any, _chartContext: any, config: any) => {
                                                const index: number = config.dataPointIndex;
                                                navigate({
                                                    pathname: `/${ROUTER.reports.monthly_effort.index}/${ROUTER.reports.monthly_effort.project}`,
                                                    search: `?${createSearchParams({
                                                        ...conditions,
                                                        projectType: data[index].type
                                                    } as any)}`
                                                });
                                            },
                                            dataPointMouseEnter: function (event: any) {
                                                event.target.style.cursor = 'pointer';
                                            }
                                        }
                                    },
                                    labels: data.map((item) => item.type)
                                }}
                                series={data.map((item) => item.effort)}
                                type="pie"
                            />
                        </Grid>
                        <Grid item xs={12} lg={6}>
                            {data.map((item, index) => (
                                <Grid item xs={12} lg={12} key={index}>
                                    <Grid container spacing={2} alignItems="center" justifyContent="center">
                                        <Grid item>
                                            <FiberManualRecordIcon sx={{ color: item.color || '#ffffff' }} />
                                        </Grid>
                                        <Grid item xs zeroMinWidth>
                                            <Grid container spacing={1}>
                                                <Grid item xs zeroMinWidth>
                                                    <Typography align="left" variant="body2">
                                                        {item.type}
                                                    </Typography>
                                                </Grid>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
        </MainCard>
    );
};

export default ActualEffortAllocationCard;
