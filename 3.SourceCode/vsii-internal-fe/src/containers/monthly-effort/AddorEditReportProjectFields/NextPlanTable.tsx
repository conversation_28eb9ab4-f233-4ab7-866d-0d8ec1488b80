import ControlPointOutlinedIcon from '@mui/icons-material/ControlPointOutlined';
import HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import TableContainer from '@mui/material/TableContainer';
import { ButtonBase, Typography } from '@mui/material';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { FormattedMessage } from 'react-intl';
import Table from '@mui/material/Table';
import Paper from '@mui/material/Paper';
import { Box } from '@mui/system';

import { DatePicker, Input } from 'components/extended/Form';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

type NextPlanTableProps = {
    methods: UseFormReturn<any>;
    disableEdit: boolean;
};

export default function NextPlanTable({ methods, disableEdit }: NextPlanTableProps) {
    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const {
        fields: milestoneValues,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'monthlyReport.nextPlan'
    });

    const handleAddRows = () => {
        const rows = {
            task: '',
            comment: '',
            startDate: null,
            dueDate: null
        };
        append(rows);
    };

    const handleRemoveMilestone = (index: number) => {
        remove(index);
    };

    return (
        <Box className="sale-table">
            <ButtonBase onClick={handleAddRows} sx={{ display: disableEdit ? 'none' : 'block' }}>
                <ControlPointOutlinedIcon />
            </ButtonBase>
            <TableContainer component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'task-name'} /> <Typography color="#e53935"> *</Typography>
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'tentative-start-date'} />
                                </Typography>
                            </TableCell>

                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'comment'} />
                                </Typography>
                            </TableCell>

                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id="rp-wer-member-comment" />
                                </Typography>
                            </TableCell>

                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'actions'} />
                                </Typography>
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {milestoneValues.map((item, index) => (
                            <TableRow key={item.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                                <TableCell align="center" component="th" scope="row">
                                    <Input name={`monthlyReport.nextPlan.${index}.task`} required disabled={disableEdit} />
                                </TableCell>

                                <TableCell align="center">
                                    <DatePicker name={`monthlyReport.nextPlan.${index}.startDate`} disabled={disableEdit} />
                                </TableCell>
                                <TableCell align="center">
                                    <DatePicker name={`monthlyReport.nextPlan.${index}.dueDate`} disabled={disableEdit} />
                                </TableCell>
                                <TableCell align="center">
                                    <Input
                                        name={`monthlyReport.nextPlan.${index}.comment`}
                                        textFieldProps={{ multiline: true }}
                                        disabled={disableEdit}
                                    />
                                </TableCell>

                                <TableCell align="center">
                                    <ButtonBase
                                        onClick={() => handleRemoveMilestone(index)}
                                        sx={{ display: disableEdit ? 'none' : 'block' }}
                                    >
                                        <HighlightOffOutlinedIcon />
                                    </ButtonBase>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </Box>
    );
}
