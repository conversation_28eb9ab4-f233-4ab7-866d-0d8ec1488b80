import HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';
import ControlPointOutlinedIcon from '@mui/icons-material/ControlPointOutlined';
import { useFieldArray, useFormContext } from 'react-hook-form';
import TableContainer from '@mui/material/TableContainer';
import { ButtonBase, Typography } from '@mui/material';
import TableBody from '@mui/material/TableBody';
import TableHead from '@mui/material/TableHead';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { FormattedMessage } from 'react-intl';
import Table from '@mui/material/Table';
import Paper from '@mui/material/Paper';
import { Box } from '@mui/system';

import { Input, NumericFormatCustom } from 'components/extended/Form';
import { MONEY_PLACEHOLDER, TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ISaleandUpSaleTableProps {
    disableEdit: boolean;
}

export default function SaleandUpSaleTable({ disableEdit }: ISaleandUpSaleTableProps) {
    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const methods = useFormContext();
    const {
        fields: milestoneValues,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'monthlyReport.saleUpSalesEntityList'
    });
    const handleAddRows = () => {
        const rows = {
            oppotunitiesExpand: '',
            changedOfSale: '',
            comment: '',
            potential_revenueVND: 0
        };
        append(rows);
    };
    const handleRemoveMilestone = (index: number) => {
        remove(index);
    };

    return (
        <Box className="sale-table">
            <ButtonBase onClick={handleAddRows} sx={{ display: disableEdit ? 'none' : 'block' }}>
                <ControlPointOutlinedIcon />
            </ButtonBase>
            <TableContainer component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'oppotunities-expand'} />{' '}
                                    <Typography color="#e53935"> *</Typography>
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'change-of-sale'} />
                                </Typography>
                            </TableCell>

                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'potential-revenue'} /> (VND)
                                </Typography>
                            </TableCell>

                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'comment'} />
                                </Typography>
                            </TableCell>

                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'actions'} />
                                </Typography>
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {milestoneValues.map((item, index) => (
                            <TableRow key={item.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                                <TableCell align="center" component="th" scope="row">
                                    <Input
                                        name={`monthlyReport.saleUpSalesEntityList.${index}.oppotunitiesExpand`}
                                        required
                                        disabled={disableEdit}
                                    />
                                </TableCell>
                                <TableCell align="center">
                                    <Input name={`monthlyReport.saleUpSalesEntityList.${index}.changedOfSale`} disabled={disableEdit} />
                                </TableCell>
                                <TableCell align="center">
                                    <Input
                                        required
                                        textFieldProps={{
                                            placeholder: MONEY_PLACEHOLDER,
                                            InputProps: {
                                                inputComponent: NumericFormatCustom as any
                                            }
                                        }}
                                        name={`monthlyReport.saleUpSalesEntityList.${index}.potential_revenueVND`}
                                        disabled={disableEdit}
                                    />
                                </TableCell>
                                <TableCell align="center">
                                    <Input
                                        name={`monthlyReport.saleUpSalesEntityList.${index}.comment`}
                                        textFieldProps={{ multiline: true }}
                                        disabled={disableEdit}
                                    />
                                </TableCell>

                                <TableCell align="center">
                                    <ButtonBase
                                        onClick={() => handleRemoveMilestone(index)}
                                        sx={{ display: disableEdit ? 'none' : 'block' }}
                                    >
                                        <HighlightOffOutlinedIcon />
                                    </ButtonBase>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </Box>
    );
}
