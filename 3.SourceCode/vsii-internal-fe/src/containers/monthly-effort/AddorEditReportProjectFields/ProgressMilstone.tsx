import React, { useEffect, useState } from 'react';
import { Box, Grid, Typography } from '@mui/material';
import { useFormContext } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

import { PERCENT_PLACEHOLDER, PROJECT_IMPLEMENTATION_PHASE, PROJECT_PROGRESS_ASSESSMENT, TEXT_CONFIG_SCREEN } from 'constants/Common';
import { Input, PercentageFormat, Select } from 'components/extended/Form';
import { gridSpacing } from 'store/constant';

interface IProgressMilstoneProps {
    disableEdit: boolean;
}

const ProgressMilstone = ({ disableEdit }: IProgressMilstoneProps) => {
    const methods = useFormContext();
    const [assesment, setAssement] = useState('');
    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    useEffect(() => {
        if (methods.getValues('monthlyReport.progressMilestone.implPhase') === 'Deployment') {
            setAssement('Deployment');
        } else {
            methods.setValue('monthlyReport.progressMilestone.progressAssesment', '');
        }
    }, [methods]);

    return (
        <Grid container spacing={gridSpacing} paddingX={3} rowGap={2}>
            <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', paddingX: 2 }}>
                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>
                        <FormattedMessage id={project_report + 'project-implementation-phase'} />
                        <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                    </Typography>
                    <Box sx={{ flexBasis: '60%' }}>
                        <Select
                            name="monthlyReport.progressMilestone.implPhase"
                            selects={PROJECT_IMPLEMENTATION_PHASE}
                            handleChange={(e: any) => setAssement(e.target.value === 'Deployment' ? e.target.value : '')}
                            disabled={disableEdit}
                        />
                    </Box>
                </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', paddingX: 2 }}>
                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>
                        <FormattedMessage id={project_report + 'work-completed'} /> <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                    </Typography>
                    <Box sx={{ flexBasis: '60%' }}>
                        <Input
                            name="monthlyReport.progressMilestone.workCompleted"
                            textFieldProps={{
                                placeholder: PERCENT_PLACEHOLDER,
                                InputProps: {
                                    inputComponent: PercentageFormat as any
                                }
                            }}
                            disabled={disableEdit}
                        />
                    </Box>
                </Box>
            </Grid>

            {assesment && (
                <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', paddingX: 2 }}>
                        <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>
                            <FormattedMessage id={project_report + 'project-progress-assessment'} />
                            <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                        </Typography>
                        <Box sx={{ flexBasis: '60%' }}>
                            <Select
                                name="monthlyReport.progressMilestone.progressAssesment"
                                selects={PROJECT_PROGRESS_ASSESSMENT}
                                disabled={disableEdit}
                            />
                        </Box>
                    </Box>
                </Grid>
            )}

            <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>
                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>
                        <FormattedMessage id={project_report + 'finished-main-tasks-in-month'} />{' '}
                        <Typography sx={{ color: '#D02C2C' }}>*</Typography>
                    </Typography>
                    <Box sx={{ flexBasis: '60%' }}>
                        <Input
                            name="monthlyReport.progressMilestone.finishedTasks"
                            textFieldProps={{ multiline: true, rows: 3 }}
                            disabled={disableEdit}
                        />
                    </Box>
                </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>
                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>
                        <FormattedMessage id={project_report + 'delayed-not-finished'} />
                    </Typography>
                    <Box sx={{ flexBasis: '60%' }}>
                        <Input
                            name="monthlyReport.progressMilestone.delayNotFinishPlan"
                            textFieldProps={{ multiline: true, rows: 3 }}
                            disabled={disableEdit}
                        />
                    </Box>
                </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>
                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>
                        <FormattedMessage id={project_report + 'completed-milestones'} />
                    </Typography>
                    <Box sx={{ flexBasis: '60%' }}>
                        <Input
                            name="monthlyReport.progressMilestone.completedMilestones"
                            textFieldProps={{ multiline: true, rows: 3 }}
                            disabled={disableEdit}
                        />
                    </Box>
                </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'start', paddingX: 2 }}>
                    <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333', display: 'flex', gap: 1 }}>
                        <FormattedMessage id={project_report + 'next-milestone'} />
                    </Typography>
                    <Box sx={{ flexBasis: '60%' }}>
                        <Input
                            name="monthlyReport.progressMilestone.nextMilestone"
                            textFieldProps={{ multiline: true, rows: 3 }}
                            disabled={disableEdit}
                        />
                    </Box>
                </Box>
            </Grid>
        </Grid>
    );
};

export default ProgressMilstone;
