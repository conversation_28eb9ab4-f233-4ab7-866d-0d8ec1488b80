import HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';
import ControlPointOutlinedIcon from '@mui/icons-material/ControlPointOutlined';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import TableContainer from '@mui/material/TableContainer';
import { Input, Select } from 'components/extended/Form';
import { ButtonBase, Typography } from '@mui/material';
import { PROJECT_REPORT_TYPE, TEXT_CONFIG_SCREEN } from 'constants/Common';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import TableRow from '@mui/material/TableRow';
import { FormattedMessage } from 'react-intl';
import Table from '@mui/material/Table';
import Paper from '@mui/material/Paper';
import { Box } from '@mui/system';

type IssueandRiskTableProps = {
    methods: UseFormReturn<any>;
    disableEdit: boolean;
};
export default function IssueandRiskTable({ methods, disableEdit }: IssueandRiskTableProps) {
    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const {
        fields: milestoneValues,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'monthlyReport.issueRiskEntityList'
    });
    const handleAddRows = () => {
        const rows = {
            description: '',
            type: 0,
            rootCause: '',
            proposedSolution: ''
        };
        append(rows);
    };
    const handleRemoveMilestone = (index: number) => {
        remove(index);
    };
    return (
        <Box className="issuea-table">
            <ButtonBase onClick={handleAddRows} sx={{ display: disableEdit ? 'none' : 'block' }}>
                <ControlPointOutlinedIcon />
            </ButtonBase>
            <TableContainer component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'desciption'} /> <Typography color="#e53935"> *</Typography>
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'type'} /> <Typography color="#e53935"> *</Typography>
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'root-cause'} />
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'proposed-solution'} />
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'actions'} />
                                </Typography>
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {milestoneValues.map((item, index) => (
                            <TableRow key={item.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                                <TableCell align="center" component="th" scope="row">
                                    <Input name={`monthlyReport.issueRiskEntityList.${index}.description`} disabled={disableEdit} />
                                </TableCell>

                                <TableCell align="center">
                                    <Select
                                        isMultipleLanguage
                                        selects={PROJECT_REPORT_TYPE}
                                        name={`monthlyReport.issueRiskEntityList.${index}.type`}
                                        disabled={disableEdit}
                                    />
                                </TableCell>
                                <TableCell align="center">
                                    <Input
                                        name={`monthlyReport.issueRiskEntityList.${index}.rootCause`}
                                        textFieldProps={{ multiline: true }}
                                        disabled={disableEdit}
                                    />
                                </TableCell>
                                <TableCell align="center">
                                    <Input
                                        name={`monthlyReport.issueRiskEntityList.${index}.proposedSolution`}
                                        textFieldProps={{ multiline: true }}
                                        disabled={disableEdit}
                                    />
                                </TableCell>

                                <TableCell align="center">
                                    <ButtonBase
                                        onClick={() => handleRemoveMilestone(index)}
                                        sx={{ display: disableEdit ? 'none' : 'block' }}
                                    >
                                        <HighlightOffOutlinedIcon />
                                    </ButtonBase>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </Box>
    );
}
