import { Box, Grid, Typography } from '@mui/material';
import { UseFormReturn } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';

import { Months, Project, Years } from 'containers/search';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import MilestoneListTable from './MilestoneListTable';
import { Input } from 'components/extended/Form';
import { gridSpacing } from 'store/constant';
import { IOption } from 'types';

type ProjectReportInfoProps = {
    methods: UseFormReturn<any>;
    disabledProject: boolean;
    handleChangeYear: (e: any) => void;
    handleMonthChange: (value: string) => void;
    handleChangeProject: (item: IOption) => void;
    months: IOption[];
    month: { fromDate: string; toDate: string };
    disableEdit: boolean;
    setProjects: React.Dispatch<React.SetStateAction<IOption[]>>;
};

const ProjectReportInfo = ({
    methods,
    disabledProject,
    handleChangeYear,
    handleMonthChange,
    handleChangeProject,
    months,
    month,
    disableEdit,
    setProjects
}: ProjectReportInfoProps) => {
    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;
    return (
        <Grid container spacing={gridSpacing}>
            <Grid item xs={12} sm={7} sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ flexBasis: '30%', marginRight: '16px', fontSize: '14px', color: '#333' }}>
                        <FormattedMessage id={project_report + 'year'} />
                    </Typography>
                    <Box sx={{ flexBasis: '70%' }}>
                        <Years disableLabel name="projectReportInfo.year" handleChangeYear={handleChangeYear} disabled={disableEdit} />
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ flexBasis: '30%', marginRight: '16px', fontSize: '14px', color: '#333' }}>
                        <FormattedMessage id={project_report + 'month'} />
                    </Typography>
                    <Box sx={{ flexBasis: '70%' }}>
                        <Months
                            name="projectReportInfo.month"
                            disabledLabel
                            months={months}
                            onChange={handleMonthChange}
                            disabled={disableEdit}
                        />
                    </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ flexBasis: '30%', marginRight: '16px', fontSize: '14px', color: '#333' }}>
                        <FormattedMessage id={project_report + 'projects'} />
                    </Typography>
                    <Box sx={{ flexBasis: '70%' }}>
                        <Project
                            disabled={disabledProject}
                            isDefaultAll
                            month={month}
                            name="projectReportInfo.projectId"
                            isNotStatus
                            handleChange={handleChangeProject}
                            label=""
                            setProjectsAll={setProjects}
                        />
                    </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ flexBasis: '30%', marginRight: '16px', fontSize: '14px', color: '#333' }}>
                        <FormattedMessage id={project_report + 'project-type'} />
                    </Typography>
                    <Box sx={{ flexBasis: '70%' }}>
                        <Input disabled name="projectReportInfo.projectType" />
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ flexBasis: '30%', marginRight: '16px', fontSize: '14px', color: '#333' }}>
                        <FormattedMessage id={project_report + 'project-manager'} />
                    </Typography>
                    <Box sx={{ flexBasis: '70%' }}>
                        <Input disabled name="projectReportInfo.userNamePM" />
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ flexBasis: '30%', marginRight: '16px', fontSize: '14px', color: '#333' }}>
                        <FormattedMessage id={project_report + 'start-date'} />
                    </Typography>
                    <Box sx={{ flexBasis: '70%' }}>
                        <Input disabled name="projectReportInfo.startDate" />
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ flexBasis: '30%', marginRight: '16px', fontSize: '14px', color: '#333' }}>
                        <FormattedMessage id={project_report + 'end-date'} />
                    </Typography>
                    <Box sx={{ flexBasis: '70%' }}>
                        <Input disabled name="projectReportInfo.endDate" />
                    </Box>
                </Box>
            </Grid>

            <Grid item xs={12}>
                <Typography
                    sx={(theme) => ({
                        color: theme.palette.primary.main,
                        fontWeight: 600
                    })}
                    mb={2}
                    mt={2}
                >
                    <FormattedMessage id={project_report + 'milestone-list-approved-by-bod-customer'} />
                </Typography>

                <MilestoneListTable methods={methods} disabled={disableEdit} />
            </Grid>
        </Grid>
    );
};

export default ProjectReportInfo;
