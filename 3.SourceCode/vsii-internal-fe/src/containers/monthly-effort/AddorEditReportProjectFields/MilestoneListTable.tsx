import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import { DatePicker, Input, Select } from 'components/extended/Form';
import HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';
import { ButtonBase, Typography } from '@mui/material';
import { PROJECT_REPORT_STATUS, TEXT_CONFIG_SCREEN } from 'constants/Common';
import ControlPointOutlinedIcon from '@mui/icons-material/ControlPointOutlined';
import { Box } from '@mui/system';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
type MilestoneListTableProps = {
    methods: UseFormReturn<any>;
    disabled: boolean;
};
export default function MilestoneListTable({ methods, disabled }: MilestoneListTableProps) {
    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const {
        fields: milestoneValues,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'projectReportInfo.milestoneApproveEntityList'
    });
    const handleAddRows = () => {
        const rows = {
            date: '',
            status: 0,
            milestone: '',
            releasePackage: ''
        };
        append(rows);
    };

    const handleRemoveMilestone = (index: number) => {
        remove(index);
    };
    return (
        <Box className="milestone-table">
            <ButtonBase onClick={handleAddRows} sx={{ display: disabled ? 'none' : 'block' }}>
                <ControlPointOutlinedIcon />
            </ButtonBase>

            <TableContainer component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'milestone'} /> <Typography color="#e53935"> *</Typography>
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'date'} /> <Typography color="#e53935"> *</Typography>
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'release-package'} />
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'status'} />
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'actions'} />
                                </Typography>
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {milestoneValues.map((item, index) => (
                            <TableRow key={item.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                                <TableCell align="center" component="th" scope="row">
                                    <Input name={`projectReportInfo.milestoneApproveEntityList.${index}.milestone`} required />
                                </TableCell>
                                <TableCell align="center">
                                    <DatePicker name={`projectReportInfo.milestoneApproveEntityList.${index}.date`} required />
                                </TableCell>
                                <TableCell align="center">
                                    <Input name={`projectReportInfo.milestoneApproveEntityList.${index}.releasePackage`} />
                                </TableCell>
                                <TableCell align="center">
                                    <Select
                                        isMultipleLanguage
                                        selects={PROJECT_REPORT_STATUS}
                                        name={`projectReportInfo.milestoneApproveEntityList.${index}.status`}
                                    />
                                </TableCell>
                                <TableCell align="center">
                                    <ButtonBase onClick={() => handleRemoveMilestone(index)}>
                                        <HighlightOffOutlinedIcon />
                                    </ButtonBase>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </Box>
    );
}
