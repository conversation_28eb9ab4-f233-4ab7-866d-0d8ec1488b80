import HighlightOffOutlinedIcon from '@mui/icons-material/HighlightOffOutlined';
import ControlPointOutlinedIcon from '@mui/icons-material/ControlPointOutlined';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { ButtonBase, Grid, Typography } from '@mui/material';
import TableContainer from '@mui/material/TableContainer';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableBody from '@mui/material/TableBody';
import { FormattedMessage } from 'react-intl';
import TableRow from '@mui/material/TableRow';
import Table from '@mui/material/Table';
import Paper from '@mui/material/Paper';
import { Box } from '@mui/system';

import { Input } from 'components/extended/Form';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
type ResourceTableProps = {
    methods: UseFormReturn<any>;
    disableEdit: boolean;
};
export default function ResourceTable({ methods, disableEdit }: ResourceTableProps) {
    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const {
        fields: milestoneValues,
        append,
        remove
    } = useFieldArray({
        control: methods.control,
        name: 'monthlyReport.resource.reviewEntityList'
    });

    const handleAddRows = () => {
        const rows = {
            description: '',
            resourceReview: ''
        };
        append(rows);
    };

    const handleRemoveMilestone = (index: number) => {
        remove(index);
    };

    return (
        <Box className="resource-table">
            <Grid container mb={2}>
                <Grid item xs={12} lg={5}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography sx={{ flexBasis: '40%', marginRight: '16px', color: '#333' }}>
                            <FormattedMessage id={project_report + 'total-headcounts'} />
                        </Typography>
                        <Box sx={{ flexBasis: '60%' }}>
                            <Input name="monthlyReport.resource.totalHC" disabled />
                        </Box>
                    </Box>
                </Grid>
            </Grid>
            <ButtonBase onClick={handleAddRows} sx={{ display: disableEdit ? 'none' : 'block' }}>
                <ControlPointOutlinedIcon />
            </ButtonBase>
            <TableContainer component={Paper}>
                <Table sx={{ minWidth: 650 }} aria-label="simple table">
                    <TableHead>
                        <TableRow>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'resource-review'} />
                                    <Typography color="#e53935"> *</Typography>
                                </Typography>
                            </TableCell>
                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'desciption'} />
                                </Typography>
                            </TableCell>

                            <TableCell align="center">
                                <Typography
                                    display="flex"
                                    justifyContent="center"
                                    gap={1}
                                    sx={(theme) => ({
                                        color: theme.palette.primary.main
                                    })}
                                >
                                    <FormattedMessage id={project_report + 'actions'} />
                                </Typography>
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {milestoneValues.map((item, index) => (
                            <TableRow key={item.id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                                <TableCell align="center" component="th" scope="row">
                                    <Input
                                        name={`monthlyReport.resource.reviewEntityList.${index}.resourceReview`}
                                        disabled={disableEdit}
                                    />
                                </TableCell>

                                <TableCell align="center">
                                    <Input
                                        name={`monthlyReport.resource.reviewEntityList.${index}.description`}
                                        textFieldProps={{ multiline: true }}
                                        disabled={disableEdit}
                                    />
                                </TableCell>

                                <TableCell align="center">
                                    <ButtonBase
                                        onClick={() => handleRemoveMilestone(index)}
                                        sx={{ display: disableEdit ? 'none' : 'block' }}
                                    >
                                        <HighlightOffOutlinedIcon />
                                    </ButtonBase>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </Box>
    );
}
