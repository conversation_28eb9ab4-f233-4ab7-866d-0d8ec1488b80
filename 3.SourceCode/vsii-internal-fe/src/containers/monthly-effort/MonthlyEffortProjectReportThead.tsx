import { useCallback, useState } from 'react';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IMonthlyEffortProjectReportTheadProps {
    currentYear: number;
    projectLength: number;
}

function MonthlyEffortProjectReportThead({ currentYear, projectLength }: IMonthlyEffortProjectReportTheadProps) {
    const [projectCell, setProjectCell] = useState<any>(null);
    const [deptIdCell, setDeptIdCell] = useState<any>(null);
    const theme = useTheme();

    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const projectCellRef = useCallback(
        (domNode: any) => {
            if (projectLength > 0 && domNode) {
                setProjectCell(domNode.getBoundingClientRect());
            }
        },
        [projectLength]
    );

    const deptIdRef = useCallback((domNode: any) => {
        if (domNode) {
            setDeptIdCell(domNode.getBoundingClientRect());
        }
    }, []);

    const matches = useMediaQuery(theme.breakpoints.up('md'));
    return (
        <TableHead>
            <TableRow>
                <TableCell ref={projectCellRef} sx={{ left: !!matches ? 0 : 'unset', zIndex: 3 }}>
                    <FormattedMessage id={project_report + 'no'} />
                </TableCell>
                <TableCell
                    align="left"
                    ref={deptIdRef}
                    sx={{ left: !!matches ? (projectLength > 0 ? projectCell?.width : 'unset') : 'unset', minWidth: '200px', zIndex: 3 }}
                >
                    <FormattedMessage id={project_report + 'project-name'} />
                </TableCell>
                <TableCell sx={{ left: !!matches ? projectLength > 0 && projectCell?.width + deptIdCell?.width : 'unset', zIndex: 3 }}>
                    <FormattedMessage id={project_report + 'project-manager'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={project_report + 'project-implementation-phase'} />
                </TableCell>
                <TableCell rowSpan={3}>
                    <FormattedMessage id={project_report + 'project-progress-assessment'} />
                </TableCell>
                <TableCell rowSpan={3}>
                    <FormattedMessage id={project_report + 'dept'} />
                </TableCell>
                <TableCell rowSpan={3}>
                    <FormattedMessage id={project_report + 'project-type'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={project_report + 'start-date'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={project_report + 'end-date'} />
                </TableCell>

                <TableCell rowSpan={2}>
                    <FormattedMessage id={project_report + 'user-update'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={project_report + 'last-update'} />
                </TableCell>
                <TableCell rowSpan={2} align="center">
                    <FormattedMessage id={project_report + 'actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
}
export default MonthlyEffortProjectReportThead;
