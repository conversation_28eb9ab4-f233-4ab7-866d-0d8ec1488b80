// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormattedMessage } from 'react-intl';

const MonthlyEffortMemberThead = () => {
    const { effortbymember } = TEXT_CONFIG_SCREEN.monthlyEffort;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'level'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'department'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'effort-in-month'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'difference-hours'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={effortbymember + 'projects'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default MonthlyEffortMemberThead;
