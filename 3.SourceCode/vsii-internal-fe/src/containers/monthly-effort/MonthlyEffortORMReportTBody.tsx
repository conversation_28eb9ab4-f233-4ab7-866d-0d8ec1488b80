// materia-ui
import DownloadForOfflineOutlinedIcon from '@mui/icons-material/DownloadForOfflineOutlined';
import { ButtonBase, TableBody, TableCell, TableRow } from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';

// project imports
import DeleteOrmReport from 'containers/administration/DeleteOrmReport';
import { checkAllowedPermission } from 'utils/authorization';
import { IOrmReport } from 'pages/monthly-effort/Config';
import TableEmpty from 'components/extended/Table/Empty';
import { authSelector } from 'store/slice/authSlice';
import { PERMISSIONS } from 'constants/Permission';
import { IMonthlyEffortProjectTotal } from 'types';
import { exportDocument } from 'utils/common';
import { useAppSelector } from 'app/hooks';
import { dateFormat } from 'utils/date';
import Api from 'constants/Api';

interface IMonthlyEffortORMReportTBodyProps {
    ormReports: IOrmReport[];
    total?: IMonthlyEffortProjectTotal;
    updateTable: () => void;
}

const MonthlyEffortORMReportTBody = (props: IMonthlyEffortORMReportTBodyProps) => {
    const { ormReports, updateTable } = props;

    const theme = useTheme();
    const { generalReport } = PERMISSIONS.report;
    const { userInfo } = useAppSelector(authSelector);

    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const handleDownloadOrmReport = async (id: string) => {
        exportDocument(Api.monthly_efford.downloadOrmReport(id).url);
    };

    return (
        <>
            {!ormReports.length ? (
                <TableEmpty />
            ) : (
                <TableBody>
                    {ormReports.map((item, key) => (
                        <TableRow key={key} style={{ background: 'white' }} id={item.id}>
                            <TableCell sx={{ position: 'sticky', left: !!matches ? 0 : 'unset', background: 'inherit' }}>
                                {key + 1}
                            </TableCell>
                            <TableCell>{item.reportName}</TableCell>
                            <TableCell>{item.month}</TableCell>
                            <TableCell>{item.department}</TableCell>
                            <TableCell>{item.uploadUser}</TableCell>
                            <TableCell>{dateFormat(item.createdAt)}</TableCell>

                            <TableCell>
                                <TableCell
                                    sx={{
                                        border: 'none',
                                        display: 'flex',
                                        gap: 1
                                    }}
                                >
                                    {checkAllowedPermission(generalReport.downloadORMReport) && (
                                        <ButtonBase onClick={() => handleDownloadOrmReport(item.docCode)}>
                                            <DownloadForOfflineOutlinedIcon />
                                        </ButtonBase>
                                    )}
                                    {userInfo?.userName === item.uploadUser && checkAllowedPermission(generalReport.deleteORMReport) && (
                                        <DeleteOrmReport id={item.id} updateTable={updateTable} />
                                    )}
                                </TableCell>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            )}
        </>
    );
};

export default MonthlyEffortORMReportTBody;
