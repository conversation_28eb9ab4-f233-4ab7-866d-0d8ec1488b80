// material-ui
import { TableCell, TableHead, TableRow, Typography } from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IMonthlyEffortORMReportTheadProps {
    currentYear: number;
    projectLength: number;
}

function MonthlyEffortORMReportThead({ currentYear, projectLength }: IMonthlyEffortORMReportTheadProps) {
    const theme = useTheme();

    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const { ORMReport } = TEXT_CONFIG_SCREEN.generalReport;
    return (
        <TableHead>
            <TableRow>
                <TableCell rowSpan={2} sx={{ left: !!matches ? 0 : 'unset', zIndex: 3 }}>
                    <Typography
                        sx={(theme) => ({
                            fontWeight: 'bold'
                        })}
                    >
                        <FormattedMessage id={ORMReport + 'no'} />
                    </Typography>
                </TableCell>
                <TableCell rowSpan={2}>
                    <Typography
                        sx={(theme) => ({
                            fontWeight: 'bold'
                        })}
                    >
                        <FormattedMessage id={ORMReport + 'report-name'} />
                    </Typography>
                </TableCell>

                <TableCell rowSpan={2}>
                    <Typography
                        sx={(theme) => ({
                            fontWeight: 'bold'
                        })}
                    >
                        <FormattedMessage id={ORMReport + 'month'} />
                    </Typography>
                </TableCell>
                <TableCell rowSpan={2}>
                    <Typography
                        sx={(theme) => ({
                            fontWeight: 'bold'
                        })}
                    >
                        <FormattedMessage id={ORMReport + 'department'} />
                    </Typography>
                </TableCell>
                <TableCell rowSpan={2}>
                    <Typography
                        sx={(theme) => ({
                            fontWeight: 'bold'
                        })}
                    >
                        <FormattedMessage id={ORMReport + 'upload-user'} />
                    </Typography>
                </TableCell>

                <TableCell rowSpan={2}>
                    <Typography
                        sx={(theme) => ({
                            fontWeight: 'bold'
                        })}
                    >
                        <FormattedMessage id={ORMReport + 'last-update'} />
                    </Typography>
                </TableCell>
                <TableCell rowSpan={2}></TableCell>
            </TableRow>
        </TableHead>
    );
}
export default MonthlyEffortORMReportThead;
