import { useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { Grid, Typography } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';

import { SearchForm, Months, Years, Member, Project } from '../search';
import { searchFormConfig } from 'containers/search/Config';
import { convertMonthFromToDate } from 'utils/date';
import { Label } from 'components/extended/Form';
import { Button } from 'components';
import { IOption } from 'types';
import {
    IMonthlyEffortProjectReportConfig,
    monthlyEffortProjectReportDefault,
    monthlyEffortProjectReportSchema
} from 'pages/monthly-effort/Config';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';

interface Props {
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    formReset: IMonthlyEffortProjectReportConfig;
}

const ProjectReportSearch = (props: Props) => {
    const { months, handleChangeYear, handleSearch, formReset } = props;
    const monthOnload = months.find((item) => item.value === formReset.month);
    const [month, setMonth] = useState({ fromDate: '', toDate: '' });

    const { project_report } = TEXT_CONFIG_SCREEN.generalReport;

    const handleMonthChange = (value: string) => {
        const getMonth = months?.filter((month) => month.value === value);
        if (getMonth) {
            setMonth(convertMonthFromToDate(getMonth[0].label));
        }
    };

    useEffect(() => {
        if (monthOnload) {
            setMonth(convertMonthFromToDate(monthOnload.label));
        }
    }, [monthOnload]);

    return (
        <SearchForm
            defaultValues={monthlyEffortProjectReportDefault}
            formSchema={monthlyEffortProjectReportSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.5}>
                    <Years handleChangeYear={handleChangeYear} label={project_report + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <Months months={months} onChange={handleMonthChange} isFilter label={project_report + 'month'} />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <Project
                        isDefaultAll
                        month={month}
                        name="project"
                        projectAuthorization="false"
                        isNotStatus
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={project_report + 'projects'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                    />
                </Grid>
                <Grid item xs={12} lg={2.5}>
                    <Member
                        name={searchFormConfig.projectManager.name}
                        label={<FormattedMessage id={project_report + 'project-manager'} />}
                        findAllType="SCREEN_EFFORT"
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={project_report + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default ProjectReportSearch;
