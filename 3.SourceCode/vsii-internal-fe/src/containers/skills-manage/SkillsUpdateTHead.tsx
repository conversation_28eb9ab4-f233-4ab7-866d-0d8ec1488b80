/* eslint-disable prettier/prettier */
// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { PERMISSIONS } from 'constants/Permission';
import { FormattedMessage } from 'react-intl';

// project imports
import { checkAllowedPermission } from 'utils/authorization';

const SkillsUpdateTHead = () => {
    const { skillsUpdate } = PERMISSIONS.report.skillManage;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.skillsUpdate + 'no'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.skillsUpdate + 'member-code'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.skillsUpdate + 'members'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.skillsUpdate + 'title'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.skillsUpdate + 'dept'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={salesReport.skillsUpdate + 'status'} />
                </TableCell>
                {checkAllowedPermission(skillsUpdate.edit) ||
                checkAllowedPermission(skillsUpdate.download) ||
                checkAllowedPermission(skillsUpdate.viewCV) ? (
                    <TableCell align="center">
                        <FormattedMessage id={salesReport.skillsUpdate + 'actions'} />
                    </TableCell>
                ) : (
                    <></>
                )}
            </TableRow>
        </TableHead>
    );
};

export default SkillsUpdateTHead;
