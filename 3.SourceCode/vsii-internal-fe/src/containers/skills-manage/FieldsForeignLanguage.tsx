// material-ui
import { IconButton, SelectChangeEvent, Stack, TableCell, TableRow } from '@mui/material';

// project imports
import { Checkbox } from 'components/extended/Form';
import { EXPERT_LEVEL } from 'constants/Common';
import { Language } from 'containers/search';
import { IOption } from 'types';
import ExpertLevel from './ExpertLevel';
import InputTable from './InputTable';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { DeleteTwoToneIcon } from 'assets/images/icons';

interface IFieldsForeignLanguageProps {
    index: number;
    handleRemove: (index: number, idHexString?: string | null) => void;
    languageSelect: IOption[];
    idHexString?: string | null;
    handleChangeLanguage: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>) => void;
}

const FieldsForeignLanguage = (props: IFieldsForeignLanguageProps) => {
    const { index, handleRemove, idHexString, languageSelect, handleChangeLanguage } = props;

    return (
        <TableRow sx={{ position: 'relative', '& td': { textAlign: 'center', height: 0 } }}>
            <TableCell></TableCell>
            <TableCell sx={{ textAlign: 'left !important' }}>
                <Language
                    name={`foreignLanguage.${index}.name`}
                    isShowAll={false}
                    languageSelect={languageSelect}
                    handleChangeLanguage={handleChangeLanguage}
                />
            </TableCell>
            <TableCell>
                <InputTable name={`foreignLanguage.${index}.experiences`} textAlign="center" />
            </TableCell>
            <TableCell>
                <InputTable name={`foreignLanguage.${index}.lastedUsed`} textAlign="center" />
            </TableCell>
            <TableCell colSpan={5} sx={{ padding: 0 }}>
                <ExpertLevel name={`foreignLanguage.${index}.level`} options={EXPERT_LEVEL} />
                <Stack
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        right: '-100px',
                        transform: 'translateY(-50%)',
                        '& .Mui-checked': {
                            color: '#9e9e9e !important'
                        }
                    }}
                    direction="row"
                    justifyContent="space-between"
                    spacing={2}
                >
                    <IconButton onClick={() => handleRemove(index, idHexString)}>
                        <DeleteTwoToneIcon fontSize="small" />
                    </IconButton>
                    <div>
                        <Checkbox
                            name={`foreignLanguage.${index}.visible`}
                            checkboxProps={{
                                icon: <VisibilityOff fontSize="small" />,
                                checkedIcon: <Visibility fontSize="small" />
                            }}
                        />
                    </div>
                </Stack>
            </TableCell>
        </TableRow>
    );
};

export default FieldsForeignLanguage;
