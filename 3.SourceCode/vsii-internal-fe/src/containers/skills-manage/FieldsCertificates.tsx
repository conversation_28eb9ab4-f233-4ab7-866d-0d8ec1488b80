// material-ui
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, TableCell, TableRow } from '@mui/material';

// project imports
import InputTable from './InputTable';
import { Checkbox } from 'components/extended/Form';

// assets
import { DeleteTwoToneIcon } from 'assets/images/icons';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

interface IFieldsCertificatesProps {
    index: number;
    handleRemove: (index: number, idHexString?: string | null) => void;
    idHexString?: string | null;
}

const FieldsCertificates = (props: IFieldsCertificatesProps) => {
    const { index, handleRemove, idHexString } = props;

    return (
        <TableRow sx={{ position: 'relative' }}>
            <TableCell className="from-to-date-col vertical-align-top">
                <InputTable name={`certificates.${index}.fromDate`} placeholder="Fill date" label="Date" />
                {/* <InputTable name={`certificates.${index}.toDate`} placeholder="Fill to" label="To" required /> */}
            </TableCell>
            <TableCell className="vertical-align-top">
                <InputTable name={`certificates.${index}.organization`} label="Organization" required />
                <InputTable name={`certificates.${index}.name`} label="Certificate Name" />
                <InputTable name={`certificates.${index}.qualification`} label="Qualifications" />
                <Stack
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        right: '-100px',
                        transform: 'translateY(-50%)',
                        '& .Mui-checked': {
                            color: '#9e9e9e !important'
                        }
                    }}
                    direction="row"
                    justifyContent="space-between"
                    spacing={2}
                >
                    <IconButton onClick={() => handleRemove(index, idHexString)}>
                        <DeleteTwoToneIcon fontSize="small" />
                    </IconButton>
                    <div>
                        <Checkbox
                            name={`certificates.${index}.visible`}
                            checkboxProps={{
                                icon: <VisibilityOff fontSize="small" />,
                                checkedIcon: <Visibility fontSize="small" />
                            }}
                        />
                    </div>
                </Stack>
            </TableCell>
        </TableRow>
    );
};

export default FieldsCertificates;
