// material-ui
import { Grid } from '@mui/material';

// project imports
import { SearchForm, Skill, TitleCode, LevelSkill, Degree, Member } from '../search';
import { Label } from 'components/extended/Form';
import {
    ISkillsReportSearchDefaultValue,
    skillsReportSearchDefaultValue,
    skillsReportSearchDefaultValueSchema
} from 'pages/skills-manage/Config';
import { Button } from 'components';

//third-party
import { FormattedMessage } from 'react-intl';
import { IOption, ISkillsReport, ITitleCode } from 'types';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ISkillsReportSearchProps {
    formReset: ISkillsReportSearchDefaultValue;
    handleSearch: (value: any) => void;
    handleChangeTitleCode?: (value: ITitleCode) => void;
    handleChangeSkill?: (value: IOption[]) => void;
    handleChangeLevel?: (value: string[]) => void;
    handleChangeDegree?: (value: string[]) => void;
    handleChangeMember?: (value: ISkillsReport) => void;
}

const SkillsReportSearch = (props: ISkillsReportSearchProps) => {
    const { formReset, handleSearch, handleChangeTitleCode, handleChangeMember, handleChangeSkill, handleChangeLevel, handleChangeDegree } =
        props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={skillsReportSearchDefaultValue}
            formSchema={skillsReportSearchDefaultValueSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2}>
                    <TitleCode handleChange={handleChangeTitleCode} label={<FormattedMessage id={salesReport.skillsReport + 'title'} />} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Skill handleChange={handleChangeSkill} label={salesReport.skillsReport + 'skill-name'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <LevelSkill handleChange={handleChangeLevel} label={salesReport.skillsReport + 'skill-level'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Degree handleChange={handleChangeDegree} label={salesReport.skillsReport + 'degree'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Member
                        isUserName
                        isFindSkill
                        autoFilter={formReset}
                        handleChange={handleChangeMember}
                        name="userName"
                        label={<FormattedMessage id={salesReport.skillsReport + 'members'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.skillsReport + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default SkillsReportSearch;
