// material-ui
import { I<PERSON>B<PERSON>on, Stack, TableCell, TableRow } from '@mui/material';

// project imports
import { Checkbox } from 'components/extended/Form';
import InputTable from './InputTable';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import { DeleteTwoToneIcon } from 'assets/images/icons';

interface IFieldsCoursesAttendedProps {
    index: number;
    handleRemove: (index: number, idHexString?: string | null) => void;
    idHexString?: string | null;
}

const FieldsCoursesAttended = (props: IFieldsCoursesAttendedProps) => {
    const { index, handleRemove, idHexString } = props;

    return (
        <TableRow sx={{ position: 'relative' }}>
            <TableCell className="from-to-date-col vertical-align-top">
                <InputTable name={`coursesAttended.${index}.fromDate`} placeholder="Fill date" label="Date" />
                {/* <InputTable name={`coursesAttended.${index}.toDate`} placeholder="Fill to" label="To" required /> */}
            </TableCell>
            <TableCell className="vertical-align-top">
                <InputTable name={`coursesAttended.${index}.schoolCollege`} label="School/College" required />
                <InputTable name={`coursesAttended.${index}.name`} label="Course name" />
                <InputTable name={`coursesAttended.${index}.qualification`} placeholder="Enter" label="Qualifications" />
                <Stack
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        right: '-100px',
                        transform: 'translateY(-50%)',
                        '& .Mui-checked': {
                            color: '#9e9e9e !important'
                        }
                    }}
                    direction="row"
                    justifyContent="space-between"
                    spacing={2}
                >
                    <IconButton onClick={() => handleRemove(index, idHexString)}>
                        <DeleteTwoToneIcon fontSize="small" />
                    </IconButton>
                    <div>
                        <Checkbox
                            name={`coursesAttended.${index}.visible`}
                            checkboxProps={{
                                icon: <VisibilityOff fontSize="small" />,
                                checkedIcon: <Visibility fontSize="small" />
                            }}
                        />
                    </div>
                </Stack>
            </TableCell>
        </TableRow>
    );
};

export default FieldsCoursesAttended;
