// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

// third party
import { FormattedMessage } from 'react-intl';

const SkillsReportThead = () => {
    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead
            sx={{
                position: 'sticky',
                top: '0',
                zIndex: '99'
            }}
        >
            <TableRow>
                <TableCell></TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.skillsReport + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.skillsReport + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.skillsReport + 'members'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.skillsReport + 'title'} />
                </TableCell>
                <TableCell sx={{ padding: '0' }} colSpan={3}>
                    <TableRow
                        sx={{
                            display: 'table',
                            width: '100%',
                            '.MuiTableCell-root': {
                                borderBottom: 'none'
                            }
                        }}
                    >
                        <TableCell align="center" width={'33.333%'}>
                            <FormattedMessage id={salesReport.skillsReport + 'skill-name'} />
                        </TableCell>
                        <TableCell align="center" width={'33.333%'}>
                            <FormattedMessage id={salesReport.skillsReport + 'skill-level'} />
                        </TableCell>
                        <TableCell align="center" width={'33.333%'}>
                            <FormattedMessage id={salesReport.skillsReport + 'skill-experience'} />
                        </TableCell>
                    </TableRow>
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.skillsReport + 'degree'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={salesReport.skillsReport + 'status'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default SkillsReportThead;
