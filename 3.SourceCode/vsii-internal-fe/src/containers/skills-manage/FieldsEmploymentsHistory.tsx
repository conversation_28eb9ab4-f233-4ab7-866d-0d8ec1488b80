// material-ui
import { <PERSON><PERSON>B<PERSON>on, Stack, TableCell, TableRow } from '@mui/material';

// project imports
import InputTable from './InputTable';
import { Checkbox } from 'components/extended/Form';

// assets
import { DeleteTwoToneIcon } from 'assets/images/icons';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

interface IFieldsEmploymentsHistoryProps {
    index: number;
    handleRemove: (index: number, idHexString?: string | null) => void;
    idHexString?: string | null;
}

const FieldsEmploymentsHistory = (props: IFieldsEmploymentsHistoryProps) => {
    const { index, handleRemove, idHexString } = props;

    return (
        <TableRow sx={{ position: 'relative' }}>
            <TableCell className="from-to-date-col vertical-align-top">
                <InputTable name={`employmentsHistory.${index}.fromDate`} placeholder="Fill from" label="From" required />
                <InputTable name={`employmentsHistory.${index}.toDate`} placeholder="Fill to" label="To" />
            </TableCell>
            <TableCell className="vertical-align-top">
                <InputTable name={`employmentsHistory.${index}.company`} label="Company" required />
                <InputTable name={`employmentsHistory.${index}.jobTitle`} label="Job title" />
                <InputTable
                    name={`employmentsHistory.${index}.jobDescription`}
                    label="Job description"
                    textFieldProps={{ multiline: true }}
                />
                <Stack
                    sx={{
                        position: 'absolute',
                        top: '50%',
                        right: '-100px',
                        transform: 'translateY(-50%)',
                        '& .Mui-checked': {
                            color: '#9e9e9e !important'
                        }
                    }}
                    direction="row"
                    justifyContent="space-between"
                    spacing={2}
                >
                    <IconButton onClick={() => handleRemove(index, idHexString)}>
                        <DeleteTwoToneIcon fontSize="small" />
                    </IconButton>
                    <div>
                        <Checkbox
                            name={`employmentsHistory.${index}.visible`}
                            checkboxProps={{
                                icon: <VisibilityOff fontSize="small" />,
                                checkedIcon: <Visibility fontSize="small" />
                            }}
                        />
                    </div>
                </Stack>
            </TableCell>
        </TableRow>
    );
};

export default FieldsEmploymentsHistory;
