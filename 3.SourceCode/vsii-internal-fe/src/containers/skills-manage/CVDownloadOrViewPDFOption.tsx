// material-ui
import { But<PERSON>, Stack } from '@mui/material';
import { FormProvider, Radio } from 'components/extended/Form';

// project imports
import Modal from 'components/extended/Modal';
import { CV_DOWNLOAD_OPTION } from 'constants/Common';
import { ICVDownloadOrViewPDFDefaultValues } from 'pages/skills-manage/Config';
import { UseFormReturn } from 'react-hook-form';

// third party
import { FormattedMessage } from 'react-intl';
import { gridSpacing } from 'store/constant';

interface ICVDownloadOrViewPDFOptionProps {
    open: boolean;
    formReturn: UseFormReturn<ICVDownloadOrViewPDFDefaultValues>;
    title: string;
    handleClose: () => void;
    handleDownloadOrViewPDF: (values: ICVDownloadOrViewPDFDefaultValues) => void;
}

const CVDownloadOrViewPDFOption = (props: ICVDownloadOrViewPDFOptionProps) => {
    const { open, handleClose, formReturn, handleDownloadOrViewPDF, title } = props;

    const handleSubmit = (values: ICVDownloadOrViewPDFDefaultValues) => {
        handleDownloadOrViewPDF(values);
    };

    return (
        <Modal isOpen={open} title={title} onClose={handleClose} maxWidth="xs">
            <FormProvider formReturn={formReturn} onSubmit={handleSubmit}>
                <Radio name="typeTemplate" options={CV_DOWNLOAD_OPTION} isHorizontal={false} />
                <Stack direction="row" spacing={1} justifyContent="center" sx={{ mt: gridSpacing }}>
                    <Button color="error" onClick={handleClose}>
                        <FormattedMessage id="cancel" />
                    </Button>
                    <Button variant="contained" type="submit">
                        <FormattedMessage id="ok" />
                    </Button>
                </Stack>
            </FormProvider>
        </Modal>
    );
};

export default CVDownloadOrViewPDFOption;
