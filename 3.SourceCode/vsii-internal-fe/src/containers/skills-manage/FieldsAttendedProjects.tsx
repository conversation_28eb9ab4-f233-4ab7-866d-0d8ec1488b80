import { useState } from 'react';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';

// material-ui
import { Collapse, IconButton, Stack, Table, TableBody, TableCell, TableRow } from '@mui/material';

// project imports
import InputTable from './InputTable';
import { Checkbox } from 'components/extended/Form';

// assets
import { DeleteTwoToneIcon } from 'assets/images/icons';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

interface IFieldsAttendedProjectsProps {
    index: number;
    handleRemove: (index: number, idHexString?: string | null) => void;
    idHexString?: string | null;
    isLastItem: boolean;
}

const FieldsAttendedProjects = (props: IFieldsAttendedProjectsProps) => {
    const { index, handleRemove, idHexString, isLastItem } = props;

    const [open, setOpen] = useState<boolean>(!idHexString);

    return (
        <>
            <TableRow
                sx={{
                    width: '100%',
                    position: 'relative'
                }}
            >
                <TableCell className="from-to-date-col vertical-align-top" rowSpan={2}>
                    <InputTable name={`attendedProjects.${index}.fromDate`} placeholder="Fill from" label="From" required />
                    <InputTable name={`attendedProjects.${index}.toDate`} placeholder="Fill to" label="To" required />
                </TableCell>
                <TableCell className="personal-detail-2rd-col" sx={{ height: '67.5px', borderBottom: 'none !important' }}>
                    Project name:
                </TableCell>
                <TableCell sx={{ height: '67.5px', borderBottom: 'none !important' }}>
                    <InputTable name={`attendedProjects.${index}.projectName`} textFieldProps={{ multiline: true }} />
                    <Stack
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: '-30px',
                            transform: 'translateY(-50%)',
                            '& .Mui-checked': {
                                color: '#9e9e9e !important'
                            }
                        }}
                        direction="row"
                        justifyContent="space-between"
                        spacing={2}
                    >
                        <IconButton sx={{ p: 0 }} aria-label="expand row" size="small" onClick={() => setOpen(!open)}>
                            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                        </IconButton>
                    </Stack>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell
                    sx={{
                        height: open ? undefined : 0,
                        p: 0,
                        borderTop: 'none !important',
                        borderBottom: isLastItem ? undefined : 'none !important'
                    }}
                    colSpan={2}
                >
                    <Collapse in={open} timeout="auto" unmountOnExit sx={{ borderCollapse: 'collapse' }}>
                        <Table>
                            <TableBody
                                sx={{
                                    '& td': {
                                        padding: '6px 16px'
                                    }
                                }}
                            >
                                <TableRow>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Company:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important' }}>
                                        <InputTable name={`attendedProjects.${index}.company`} textFieldProps={{ multiline: true }} />
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Client:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important' }}>
                                        <InputTable name={`attendedProjects.${index}.client`} textFieldProps={{ multiline: true }} />
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Project size:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important' }}>
                                        <InputTable name={`attendedProjects.${index}.projectSize`} textFieldProps={{ multiline: true }} />
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Position:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important' }}>
                                        <InputTable name={`attendedProjects.${index}.position`} textFieldProps={{ multiline: true }} />
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Responsibilities:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important' }}>
                                        <InputTable
                                            name={`attendedProjects.${index}.responsibilities`}
                                            textFieldProps={{ multiline: true }}
                                        />
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Project description:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important' }}>
                                        <InputTable name={`attendedProjects.${index}.description`} textFieldProps={{ multiline: true }} />
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Used progamming languages:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important' }}>
                                        <InputTable name={`attendedProjects.${index}.languages`} textFieldProps={{ multiline: true }} />
                                    </TableCell>
                                </TableRow>
                                <TableRow sx={{ position: 'relative' }}>
                                    <TableCell
                                        className="personal-detail-2rd-col vertical-align-top"
                                        sx={{
                                            width: '219.5px !important',
                                            borderLeft: 'none !important',
                                            borderBottom: 'none !important'
                                        }}
                                        colSpan={1}
                                    >
                                        Used technologies:
                                    </TableCell>
                                    <TableCell colSpan={1} sx={{ borderRight: 'none !important', borderBottom: 'none !important' }}>
                                        <InputTable name={`attendedProjects.${index}.technologies`} textFieldProps={{ multiline: true }} />
                                        <Stack
                                            sx={{
                                                position: 'absolute',
                                                top: '50%',
                                                right: '-100px',
                                                transform: 'translateY(-50%)',
                                                '& .Mui-checked': {
                                                    color: '#9e9e9e !important'
                                                }
                                            }}
                                            direction="row"
                                            justifyContent="space-between"
                                            spacing={2}
                                        >
                                            <IconButton onClick={() => handleRemove(index, idHexString)}>
                                                <DeleteTwoToneIcon fontSize="small" />
                                            </IconButton>
                                            <div>
                                                <Checkbox
                                                    name={`attendedProjects.${index}.visible`}
                                                    checkboxProps={{
                                                        icon: <VisibilityOff fontSize="small" />,
                                                        checkedIcon: <Visibility fontSize="small" />
                                                    }}
                                                />
                                            </div>
                                        </Stack>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </Collapse>
                </TableCell>
            </TableRow>
        </>
    );
};

export default FieldsAttendedProjects;
