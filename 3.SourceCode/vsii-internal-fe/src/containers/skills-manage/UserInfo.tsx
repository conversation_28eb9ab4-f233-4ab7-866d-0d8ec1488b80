/* eslint-disable prettier/prettier */

// material-ui
import { Grid, useTheme } from '@mui/material';

// react-hook-form
import { useFormContext } from 'react-hook-form';

// project-import
import MainCard from 'components/cards/MainCard';
import { Input } from 'components/extended/Form';
import { Member, Status } from 'containers/search';
import { gridSpacing } from 'store/constant';
import { IMember } from 'types/member';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormattedMessage } from 'react-intl';

interface IUserInfoProps {
    isUpdate?: boolean;
}

const UserInfo = (props: IUserInfoProps) => {
    const { isUpdate } = props;
    const { setValue } = useFormContext();
    const theme = useTheme();

    const { salesReport } = TEXT_CONFIG_SCREEN;

    const handleChange = (memberInfo: IMember) => {
        setValue(
            'personalDetail',
            {
                idHexStringUser: memberInfo
                    ? {
                          value: memberInfo.idHexString,
                          label: `${memberInfo.firstName} ${memberInfo.lastName}`
                      }
                    : null,
                memberCode: memberInfo?.memberCode || '',
                department: memberInfo?.departmentId || '',
                title: memberInfo?.titleCode || '',
                fullNameEn: memberInfo ? `${memberInfo.firstName} ${memberInfo.lastName}` : '',
                firstName: memberInfo?.firstName || '',
                lastName: memberInfo?.lastName || '',
                status: memberInfo?.status || '',
                userName: memberInfo?.userName || ''
            },
            { shouldValidate: true }
        );
    };

    return (
        <MainCard sx={{ marginBottom: theme.spacing(gridSpacing) }} title="User Info">
            <Grid container alignItems="flex-start" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <Member
                        isDefaultAll
                        name="personalDetail.idHexStringUser"
                        handleChange={handleChange}
                        required
                        disabled={isUpdate}
                        isFindAll
                        isIdHexString
                        label={<FormattedMessage id={salesReport.skillsUpdate + 'members'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Input
                        name="personalDetail.memberCode"
                        label={<FormattedMessage id={salesReport.skillsUpdate + 'member-code'} />}
                        disabled
                        required
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Input name="personalDetail.department" label={<FormattedMessage id={salesReport.skillsUpdate + 'dept'} />} disabled />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Input name="personalDetail.title" label={<FormattedMessage id={salesReport.skillsUpdate + 'title'} />} disabled />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Status name="personalDetail.status" disabled isShowAll={false} label={salesReport.skillsUpdate + 'status'} />
                </Grid>
            </Grid>
        </MainCard>
    );
};

export default UserInfo;
