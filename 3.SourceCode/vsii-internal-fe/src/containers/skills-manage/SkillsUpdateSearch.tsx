// material-ui
import { Grid } from '@mui/material';

// project imports
import { ISkillsUpdateSearchConfig, skillsUpdateSearchConfig, skillsUpdateSearchSchema } from 'pages/skills-manage/Config';
import { Department, Member, SearchForm, Status, TitleCode } from 'containers/search';
import { searchFormConfig } from 'containers/search/Config';
import { Label } from 'components/extended/Form';
import { Button } from 'components';
import { ITitleCode } from 'types';

// third party
import { FormattedMessage } from 'react-intl';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface ISkillsUpdateSearchProps {
    formReset: ISkillsUpdateSearchConfig;
    handleSubmit: (data: ISkillsUpdateSearchConfig) => void;
    handleChangeTitleCode?(value: ITitleCode): void;
    handleChangeDepartmentId?: (value: string) => void;
    handleChangeStatus?: (value: string) => void;
    handleChangeMember?: (value: ISkillsUpdateSearchConfig) => void;
}

const SkillsUpdateSearch = (props: ISkillsUpdateSearchProps) => {
    const { formReset, handleSubmit, handleChangeTitleCode, handleChangeDepartmentId, handleChangeStatus, handleChangeMember } = props;

    const { salesReport } = TEXT_CONFIG_SCREEN;

    return (
        <SearchForm
            defaultValues={skillsUpdateSearchConfig}
            formSchema={skillsUpdateSearchSchema}
            handleSubmit={handleSubmit}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={2.4}>
                    <TitleCode
                        handleChange={handleChangeTitleCode}
                        label={<FormattedMessage id={salesReport.skillsUpdate + 'member-code'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Department onChange={handleChangeDepartmentId} label={salesReport.skillsUpdate + 'dept'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Status onChange={handleChangeStatus} label={salesReport.skillsUpdate + 'status'} />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Member
                        handleChange={handleChangeMember}
                        isFindSkill
                        autoFilter={formReset}
                        isUserName
                        name={searchFormConfig.userName.name}
                        label={<FormattedMessage id={salesReport.skillsUpdate + 'members'} />}
                    />
                </Grid>
                <Grid item xs={12} lg={2.4}>
                    <Label label="&nbsp;" />
                    {/* not done */}
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={salesReport.skillsUpdate + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default SkillsUpdateSearch;
