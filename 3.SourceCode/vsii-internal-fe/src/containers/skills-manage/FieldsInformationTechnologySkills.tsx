// react-hook-form
import { useEffect, useState } from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';

// material-ui
import { IconButton, SelectChangeEvent, Stack, TableCell, TableRow } from '@mui/material';

// project imports
import { Checkbox } from 'components/extended/Form';
import Api from 'constants/Api';
import { EXPERT_LEVEL } from 'constants/Common';
import { Technologies } from 'containers/search';
import Skills from 'containers/search/Skills';
import { skillsDefaultValue } from 'pages/skills-manage/Config';
import { IOption } from 'types';
import ExpertLevel from './ExpertLevel';
import InputTable from './InputTable';
import sendRequest from 'services/ApiService';

// assets
import { DeleteTwoToneIcon } from 'assets/images/icons';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

interface IFieldsInformationTechnologySkillsProps {
    indexTechnology: number;
    handleRemove: (index: number, techGroup?: any) => void;
    methods: UseFormReturn<any>;
    technologiesSelect: IOption[];
    handleChangeTech?: (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>, index?: number) => void;
    techName: string;
    techNameList?: { name: string; value: string; index?: number }[];
    setTechnologySkill?: React.Dispatch<React.SetStateAction<string[]>>;
    handleChangeMainSkill?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const FieldsInformationTechnologySkills = (props: IFieldsInformationTechnologySkillsProps) => {
    // ============ props ============
    const {
        indexTechnology,
        handleRemove,
        methods,
        handleChangeTech,
        techName,
        setTechnologySkill,
        technologiesSelect,
        handleChangeMainSkill
    } = props;

    // ============ Select Option ============
    const [technologySkillSelect, setTechnologySkillSelect] = useState<IOption[]>([]);
    const [technologySkillSelected, setTechnologySkillSelected] = useState<IOption[]>([]);

    // Get Api skill by technology
    const getAPISkillByTech = async () => {
        if (techName && techName !== '') {
            const response = await sendRequest(Api.skills_manage.getSkillByTechnology, {
                techName: techName
            });
            const { result, status } = response;
            if (result && status) {
                const { result } = response;
                const arrSkill: IOption[] = [];
                result.content.forEach((item: { skillName: string }) => {
                    let skillOption = {
                        value: item.skillName,
                        label: item.skillName
                    };
                    arrSkill.push(skillOption);
                });
                arrSkill.sort((a, b) => a.label.localeCompare(b.label));
                setTechnologySkillSelect(arrSkill);
                if (fieldsTechnologySkills.length > 0) {
                    const cloneArrTechnologySkills: IOption[] = [];
                    fieldsTechnologySkills.forEach((x: any) => {
                        let option: IOption = {
                            value: x.techName,
                            label: x.techName,
                            id: x.id
                        };
                        cloneArrTechnologySkills.push(option);
                    });
                    setTechnologySkillSelected(cloneArrTechnologySkills);
                    setTechnologySkillSelect(
                        arrSkill.map((x) => {
                            return { ...x, disabled: cloneArrTechnologySkills.some((y) => x.value === y.value) };
                        })
                    );
                }
            }
        } else {
            setTechnologySkillSelect([]);
        }
    };
    // Technology Skills
    const {
        fields: fieldsTechnologySkills,
        append: appendTechnologySkills,
        remove: removeTechnologySkills
    } = useFieldArray({
        control: methods.control,
        name: `technologySkill.${indexTechnology}.technologies`
    });

    // chagne technologies
    const handleChangeTechnologies = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>, index?: number) => {
        handleChangeTech && handleChangeTech(e, index);
        const technologiesArr = methods.getValues(`technologySkill.${indexTechnology}.technologies`);
        if (technologiesArr.length > 0) {
            methods.setValue(`technologySkill.${indexTechnology}.technologies`, skillsDefaultValue);
        }
    };

    // handle remove
    const handleRemoveTechnologySkills = (index: number, tech: any) => {
        const id = tech.id;
        const idHexString = tech?.idHexString;
        if (idHexString && setTechnologySkill) setTechnologySkill((tech) => [...tech, idHexString]);
        if (id) {
            const updateArr = technologySkillSelected.filter((x) => {
                return x.id !== id;
            });
            setTechnologySkillSelected(updateArr);
            const updateArrLanguage = technologySkillSelect.map((x) => {
                return { ...x, disabled: updateArr.some((y: any) => x.value === y.value) };
            });
            setTechnologySkillSelect(updateArrLanguage);
        }
        removeTechnologySkills(index);
    };

    // chage skill
    const handleTechnologySkill = (e: React.ChangeEvent<HTMLSelectElement> | SelectChangeEvent<unknown>, index: number, tech: any) => {
        const value = e?.target.value;
        const id = tech.id;
        const cloneArrSkill = [...technologySkillSelect];
        const cloneArrSkillSelected = [...technologySkillSelected];
        // Tìm phần tử trong mảng
        const indexMainSkill = cloneArrSkillSelected.findIndex((it) => it.id === id);
        if (indexMainSkill === -1) {
            const addOption: any = {
                ...cloneArrSkill.find((x) => {
                    return x.value === value;
                }),
                id
            };

            cloneArrSkillSelected.push(addOption);
            setTechnologySkillSelected(cloneArrSkillSelected);

            const updateArrLanguage = cloneArrSkill.map((x) => {
                return { ...x, disabled: cloneArrSkillSelected.some((y) => x.value === y.value) };
            });
            setTechnologySkillSelect(updateArrLanguage);
        } else {
            const updateArrSelected: any = cloneArrSkillSelected.map((x) => {
                if (x.id === id) {
                    return { ...x, value };
                }
                return x;
            });
            setTechnologySkillSelected(updateArrSelected);

            const updateArrLanguage = cloneArrSkill.map((x) => {
                return { ...x, disabled: updateArrSelected.some((y: any) => x.value === y.value) };
            });
            setTechnologySkillSelect(updateArrLanguage);
        }
    };

    // ============ useEffect ============
    useEffect(() => {
        getAPISkillByTech();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [techName]);

    return (
        <>
            <TableRow sx={{ position: 'relative' }}>
                <TableCell colSpan={9}>
                    <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '20%' }}>
                        <Technologies
                            isShowAll={false}
                            technologies={technologiesSelect}
                            handleChangeTechnologies={(e) => handleChangeTechnologies(e, indexTechnology)}
                            name={`technologySkill.${indexTechnology}.technologyName`}
                        />
                        {fieldsTechnologySkills.length < technologySkillSelect.length && (
                            <IconButton size="small" onClick={() => appendTechnologySkills(skillsDefaultValue)}>
                                <AddCircleOutlineIcon />
                            </IconButton>
                        )}
                    </Stack>
                    <IconButton
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            right: '-50px',
                            transform: 'translateY(-50%)'
                        }}
                        size="small"
                        onClick={() => handleRemove(indexTechnology)}
                    >
                        <DeleteTwoToneIcon fontSize="small" />
                    </IconButton>
                </TableCell>
            </TableRow>
            {fieldsTechnologySkills?.map((tech: any, index) => (
                <TableRow key={tech.id} sx={{ '& td': { textAlign: 'center', height: 0 }, position: 'relative' }}>
                    <TableCell>
                        <Checkbox
                            name={`technologySkill.${indexTechnology}.technologies.${index}.mainSkill`}
                            sx={{ '&': { margin: 0 } }}
                            handleChange={handleChangeMainSkill}
                        />
                    </TableCell>
                    <TableCell sx={{ textAlign: 'left !important' }}>
                        <Skills
                            name={`technologySkill.${indexTechnology}.technologies.${index}.techName`}
                            handleTechnologySkill={(e) => handleTechnologySkill(e, index, tech)}
                            technologySkillSelect={technologySkillSelect}
                        />
                    </TableCell>
                    <TableCell>
                        <InputTable name={`technologySkill.${indexTechnology}.technologies.${index}.experiences`} textAlign="center" />
                    </TableCell>
                    <TableCell>
                        <InputTable name={`technologySkill.${indexTechnology}.technologies.${index}.lastedUsed`} textAlign="center" />
                    </TableCell>
                    <TableCell colSpan={5} sx={{ padding: 0 }}>
                        <ExpertLevel name={`technologySkill.${indexTechnology}.technologies.${index}.level`} options={EXPERT_LEVEL} />
                        <Stack
                            sx={{
                                position: 'absolute',
                                top: '50%',
                                right: '-100px',
                                transform: 'translateY(-50%)',
                                '& .Mui-checked': {
                                    color: '#9e9e9e !important'
                                }
                            }}
                            direction="row"
                            justifyContent="space-between"
                            spacing={2}
                        >
                            <IconButton onClick={() => handleRemoveTechnologySkills(index, tech)}>
                                <DeleteTwoToneIcon fontSize="small" />
                            </IconButton>
                            <div>
                                <Checkbox
                                    name={`technologySkill.${indexTechnology}.technologies.${index}.visible`}
                                    checkboxProps={{
                                        icon: <VisibilityOff fontSize="small" />,
                                        checkedIcon: <Visibility fontSize="small" />
                                    }}
                                />
                            </div>
                        </Stack>
                    </TableCell>
                </TableRow>
            ))}
            <TableRow>
                <TableCell colSpan={9}>&nbsp;</TableCell>
            </TableRow>
        </>
    );
};

export default FieldsInformationTechnologySkills;
