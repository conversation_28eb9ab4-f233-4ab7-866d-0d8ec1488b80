import React, { SyntheticEvent } from 'react';

// material-ui
import { useTheme } from '@mui/material';

// project imports
import { openDeniedPermission, closeDeniedPermission } from 'store/slice/deniedPermissionSlice';
import { checkAllowedPermission, checkAllowedTab } from 'utils/authorization';
import MainCard from 'components/cards/MainCard';
import { Tabs } from 'components/extended/Tabs';
import { gridSpacing } from 'store/constant';
import { useAppDispatch } from 'app/hooks';
import { ITabs } from 'types';

interface ITabCustomProps {
    value: number;
    handleChange: (event: SyntheticEvent, value: number) => void;
    tabs: ITabs[];
}

const TabCustom = (props: ITabCustomProps) => {
    const theme = useTheme();
    const { value, handleChange, tabs } = props;

    const dispatch = useAppDispatch();

    const handleChangeTab = (e: SyntheticEvent, value: number) => {
        const permission_key = tabs.find((tab) => tab.value === value)?.permission_key;

        if (permission_key && !checkAllowedPermission(permission_key)) {
            dispatch(openDeniedPermission(true));
        } else {
            dispatch(closeDeniedPermission());
        }

        handleChange(e, value);
    };

    return (
        <MainCard
            sx={{
                marginBottom: theme.spacing(gridSpacing),
                border: 'none',
                '& .MuiCardContent-root': {
                    padding: '0 !important'
                }
            }}
        >
            <Tabs tabValue={checkAllowedTab(tabs).length === 1 ? 0 : value} tabList={tabs} onChange={handleChangeTab} />
        </MainCard>
    );
};

export default TabCustom;
