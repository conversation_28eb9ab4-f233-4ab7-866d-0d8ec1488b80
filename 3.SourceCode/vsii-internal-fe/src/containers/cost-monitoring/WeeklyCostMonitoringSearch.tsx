import { useEffect } from 'react';
import { Grid, SelectChangeEvent, Typography } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import { FormattedMessage } from 'react-intl';

import { ICostMonitoringFilterConfig, costMonitoringFilterConfig, costMonitoringFilterSchema } from 'pages/cost-monitoring/Config';
import { costAndEffortMonitoringSelector, getCostMonitoringProjectOption } from 'store/slice/costAndEffortMonitoringSlice';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import { Autocomplete, Label } from 'components/extended/Form';
import { searchFormConfig } from 'containers/search/Config';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { Weeks, SearchForm, Years } from '../search';
import { Button } from 'components';
import { IOption } from 'types';

interface IWeeklyCostMonitoringSearchProps {
    formReset: ICostMonitoringFilterConfig;
    weeks: IOption[];
    handleSearch: (value: ICostMonitoringFilterConfig) => void;
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
}

const WeeklyCostMonitoringSearch = ({ formReset, weeks, handleChangeYear, handleSearch }: IWeeklyCostMonitoringSearchProps) => {
    const { projectOptions } = useAppSelector(costAndEffortMonitoringSelector);

    const { weeklyMonitoring } = TEXT_CONFIG_SCREEN.costAndEffortMonitoring;

    const dispatch = useAppDispatch();

    const handleChangeWeek = (week: string | number) => {
        dispatch(getCostMonitoringProjectOption({ type: 'week', value: week, color: true }));
    };

    useEffect(() => {
        dispatch(getCostMonitoringProjectOption({ type: 'week', value: costMonitoringFilterConfig.week, color: true }));
    }, [dispatch, formReset]);

    return (
        <SearchForm
            defaultValues={costMonitoringFilterConfig}
            formSchema={costMonitoringFilterSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Years handleChangeYear={handleChangeYear} ignoreDefault label={weeklyMonitoring + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Weeks weeks={weeks} onChange={handleChangeWeek} label={weeklyMonitoring + 'weeks'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Autocomplete
                        options={projectOptions}
                        name={searchFormConfig.project.name}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={weeklyMonitoring + 'projects'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                        groupBy={(option: IOption) => option.typeCode}
                        isDefaultAll
                        isDisableClearable
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={weeklyMonitoring + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default WeeklyCostMonitoringSearch;
