import { useEffect, useState } from 'react';
import { Grid, SelectChangeEvent, Typography } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import { FormattedMessage } from 'react-intl';

import { costAndEffortMonitoringSelector, getCostMonitoringProjectOptionByFixCost } from 'store/slice/costAndEffortMonitoringSlice';
import { ICostMonitoringFilterConfig, costMonitoringFilterConfig, costMonitoringFilterSchema } from 'pages/cost-monitoring/Config';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import { Autocomplete, Label } from 'components/extended/Form';
import { searchFormConfig } from 'containers/search/Config';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { SearchForm, Years, Months } from '../search';
import { Button } from 'components';
import { IOption } from 'types';

interface IMonthlyCostMonitoringSearchProps {
    formReset: ICostMonitoringFilterConfig;
    months: IOption[];
    handleSearch: (value: ICostMonitoringFilterConfig) => void;
    handleChangeYear: (e: SelectChangeEvent<unknown>) => void;
    fixCost: boolean;
}

const MonthlyCostMonitoringSearch = ({ formReset, months, fixCost, handleChangeYear, handleSearch }: IMonthlyCostMonitoringSearchProps) => {
    const { projectOptions } = useAppSelector(costAndEffortMonitoringSelector);
    const [month, setMonth] = useState('');
    const dispatch = useAppDispatch();
    const currentMonth = months.find((month) => month.value === costMonitoringFilterConfig.month);

    const { monthlyMonitoring } = TEXT_CONFIG_SCREEN.costAndEffortMonitoring;

    const handleChangeMonth = (value: string) => {
        const getMonth = months.find((month) => month.value === value);

        if (getMonth) {
            setMonth(getMonth.label);
        }
    };

    useEffect(() => {
        dispatch(
            getCostMonitoringProjectOptionByFixCost({
                type: 'month',
                value: month ? month : (currentMonth?.label as string),
                fixCost,
                color: true
            })
        );
    }, [month, dispatch, fixCost, currentMonth]);

    return (
        <SearchForm
            defaultValues={costMonitoringFilterConfig}
            formSchema={costMonitoringFilterSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Years handleChangeYear={handleChangeYear} ignoreDefault label={monthlyMonitoring + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Months
                        months={months}
                        onChange={handleChangeMonth}
                        isFilter
                        isShow13MonthSalary
                        year={formReset.year}
                        label={monthlyMonitoring + 'month'}
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Autocomplete
                        options={projectOptions}
                        name={searchFormConfig.project.name}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={monthlyMonitoring + 'project'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                        groupBy={(option: IOption) => option.typeCode}
                        isDefaultAll
                        isDisableClearable
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={monthlyMonitoring + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyCostMonitoringSearch;
