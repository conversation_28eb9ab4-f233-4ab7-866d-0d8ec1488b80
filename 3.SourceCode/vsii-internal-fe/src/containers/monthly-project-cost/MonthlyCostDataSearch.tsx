import { FormattedMessage } from 'react-intl';
// material-ui
import ErrorIcon from '@mui/icons-material/Error';
import { Grid, Typography } from '@mui/material';

// project imports
import { IMonthlyCostDataConfig, monthlyCostDataConfig, monthlyCostDataSchema } from 'pages/monthly-project-cost/Config';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import { Project, SearchForm, Months, Years } from '../search';
import ColorNoteTooltip from 'components/ColorNoteTooltip';
import { Label } from 'components/extended/Form';
import { Button } from 'components';
import { IOption } from 'types';

interface IMonthlyCostDataSearchProps {
    formReset: IMonthlyCostDataConfig;
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    handleChangeMonth: (e: any) => void;
    month: any;
}

const MonthlyCostDataSearch = (props: IMonthlyCostDataSearchProps) => {
    const { formReset, months, handleChangeYear, handleSearch, handleChangeMonth, month } = props;

    const { monthlyCost } = TEXT_CONFIG_SCREEN.monthlyProjectCost;

    return (
        <SearchForm
            defaultValues={monthlyCostDataConfig}
            formSchema={monthlyCostDataSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container alignItems="center" spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Years handleChangeYear={handleChangeYear} label={monthlyCost + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Months
                        months={months}
                        isShow13MonthSalary
                        onChange={handleChangeMonth}
                        isFilter
                        year={formReset.year}
                        label={monthlyCost + 'month'}
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Project
                        isNotStatus
                        month={month}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={monthlyCost + 'project'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button type="submit" size="medium" children={<FormattedMessage id={monthlyCost + 'search'} />} variant="contained" />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyCostDataSearch;
