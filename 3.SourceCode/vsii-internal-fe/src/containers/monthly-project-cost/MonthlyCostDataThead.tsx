// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';
import { FormattedMessage } from 'react-intl';

function MonthlyCostDataThead() {
    const { monthlyCost } = TEXT_CONFIG_SCREEN.monthlyProjectCost;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'project'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'project-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'overhead-allocated-amt'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'salary-cost'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'total-cost'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'year'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'month'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'created'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'creator'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'last-update'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyCost + 'user-update'} />
                </TableCell>
                <TableCell align="center">
                    <FormattedMessage id={monthlyCost + 'actions'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
}
export default MonthlyCostDataThead;
