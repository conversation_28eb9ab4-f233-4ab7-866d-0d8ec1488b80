/* eslint-disable react-hooks/exhaustive-deps */
// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { useCallback, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

interface IMonthlyProjectCostSummaryProps {
    year: number;
    projectLength: number;
    fixCost: boolean;
}

const MonthlyProjectCostSummaryThead = (props: IMonthlyProjectCostSummaryProps) => {
    const { year, projectLength, fixCost } = props;
    const theme = useTheme();
    const [dimensions, setDimensions] = useState<any>(null);
    const matches = useMediaQuery(theme.breakpoints.up('md'));
    const [projectNameCell, setProjectNameCell] = useState<any>(null);

    const callBackRef = useCallback(
        (domNode: any) => {
            if (domNode) {
                setDimensions(domNode.getBoundingClientRect());
            }
        },
        [projectLength]
    );

    const projectNameRef = useCallback(
        (domNode: any) => {
            if (domNode) {
                setProjectNameCell(domNode.getBoundingClientRect());
            }
        },
        [projectLength]
    );

    const { monthlyProjectCost } = TEXT_CONFIG_SCREEN;

    return (
        <TableHead>
            <TableRow>
                <TableCell ref={projectNameRef} rowSpan={2} sx={{ left: !!matches ? 0 : 'unset', zIndex: 3 }}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'project'} />
                </TableCell>
                <TableCell
                    rowSpan={2}
                    sx={{
                        left: !!matches ? projectNameCell?.width : 'unset',
                        zIndex: 3
                    }}
                >
                    <FormattedMessage id={monthlyProjectCost.summary + 'project-type'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'department'} />

                    <FormattedMessage id="department" />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'contract-no'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'contract-size'} />
                </TableCell>
                <TableCell rowSpan={2}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'license'} />
                </TableCell>
                <TableCell rowSpan={2} align="center" sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'expense'} /> {year - 2}
                </TableCell>
                <TableCell rowSpan={2} align="center" sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'expense'} /> {year - 1}
                </TableCell>
                {fixCost ? (
                    <TableCell rowSpan={2} align="center" sx={{ whiteSpace: 'nowrap', color: '#D9001B !important' }}>
                        <FormattedMessage id={monthlyProjectCost.summary + 'budget'} /> {year}
                    </TableCell>
                ) : null}
                <TableCell align="center" rowSpan={1} colSpan={13}>
                    {year}
                </TableCell>
                <TableCell
                    rowSpan={2}
                    align="center"
                    sx={{
                        color: '#D9001B !important',
                        right: fixCost ? (!!matches ? dimensions?.width : 'unset') : !!matches ? 0 : 'unset',
                        zIndex: 3
                    }}
                >
                    <FormattedMessage id={monthlyProjectCost.summary + 'total-cost'} />
                    <br /> {year}
                </TableCell>
                {fixCost ? (
                    <TableCell rowSpan={2} ref={callBackRef} sx={{ right: !!matches ? 0 : 'unset', zIndex: 3 }}>
                        <FormattedMessage id={monthlyProjectCost.summary + 'remaining-accumulation'} />
                    </TableCell>
                ) : null}
            </TableRow>
            <TableRow
                sx={{
                    '& .MuiTableCell-root': {
                        position: 'sticky',
                        top: dimensions && dimensions.height / 2
                    }
                }}
            >
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'jan'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'feb'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'mar'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'apr'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'may'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'jun'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'jul'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'aug'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'sep'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'oct'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'nov'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'dec'} />
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                    <FormattedMessage id={monthlyProjectCost.summary + 'thirteenth-salary'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default MonthlyProjectCostSummaryThead;
