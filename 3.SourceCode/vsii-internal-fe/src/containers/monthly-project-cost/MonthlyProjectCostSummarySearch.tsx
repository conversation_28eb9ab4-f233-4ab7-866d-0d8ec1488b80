import { useEffect } from 'react';
import { FormattedMessage } from 'react-intl';

// material-ui
import { Grid, SelectChangeEvent, Typography } from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';
import { Button } from 'components';

// project imports
import { costAndEffortMonitoringSelector, getCostMonitoringProjectOptionByFixCost } from 'store/slice/costAndEffortMonitoringSlice';
import { Department, Months, ProjectType, SearchForm, Years } from '../search';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import { Autocomplete, Label } from 'components/extended/Form';
import { useAppDispatch, useAppSelector } from 'app/hooks';
import { searchFormConfig } from 'containers/search/Config';
import { IOption } from 'types';
import {
    IMonthlyProjectCostSummaryConfig,
    monthlyProjectCostSummarySchema,
    monthlyProjectCostSummaryConfig
} from 'pages/monthly-project-cost/Config';
import ColorNoteTooltip from 'components/ColorNoteTooltip';

interface IMonthlyProjectCostSummarySearchProps {
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    formReset: IMonthlyProjectCostSummaryConfig;
    fixCost: boolean;
    handleChangeMonth?: (value: string) => void;
    handleChangeProject?: (data: any) => void;
    handleChangeDept?: (value: string) => void;
    handleChangeProjectType?: (e: SelectChangeEvent<unknown>) => void;
}

const MonthlyProjectCostSummarySearch = (props: IMonthlyProjectCostSummarySearchProps) => {
    const {
        months,
        handleChangeYear,
        handleSearch,
        formReset,
        fixCost,
        handleChangeMonth,
        handleChangeDept,
        handleChangeProjectType,
        handleChangeProject
    } = props;

    const { projectOptions } = useAppSelector(costAndEffortMonitoringSelector);
    const monthOnload = months.find((item) => item.value === formReset.month);
    const dispatch = useAppDispatch();

    const { monthlyProjectCost } = TEXT_CONFIG_SCREEN;

    useEffect(() => {
        const month = months.find((item) => item.value === formReset.month);
        dispatch(
            getCostMonitoringProjectOptionByFixCost({
                type: 'month',
                value: month ? month.label : (monthOnload?.label as string),
                fixCost,
                dept: formReset.departmentId,
                projectType: formReset.projectType,
                color: true
            })
        );
    }, [dispatch, fixCost, monthOnload, formReset, months]);

    return (
        <SearchForm
            defaultValues={monthlyProjectCostSummaryConfig}
            formSchema={monthlyProjectCostSummarySchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container spacing={2}>
                <Grid item xs={12} lg={2}>
                    <Years handleChangeYear={handleChangeYear} label={monthlyProjectCost.summary + 'year'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Months
                        months={months}
                        isShow13MonthSalary
                        onChange={handleChangeMonth}
                        isFilter
                        year={formReset.year}
                        label={monthlyProjectCost.summary + 'month'}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Department onChange={handleChangeDept} label={monthlyProjectCost.summary + 'department'} />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <ProjectType
                        fixCost={fixCost}
                        handleChangeProjectType={handleChangeProjectType}
                        label={monthlyProjectCost.summary + 'project-type'}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Autocomplete
                        options={projectOptions}
                        name={searchFormConfig.project.name}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={monthlyProjectCost.summary + 'project'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                        groupBy={(option: IOption) => option.typeCode}
                        isDefaultAll
                        handleChange={handleChangeProject}
                    />
                </Grid>
                <Grid item xs={12} lg={2}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={monthlyProjectCost.summary + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default MonthlyProjectCostSummarySearch;
