import { TableCell, TableRow } from '@mui/material';

import { IDetailReportByMonth } from 'types';
import { formatPrice } from 'utils/common';

interface IDetailReportByMonthTFooterProps {
    projects: IDetailReportByMonth[];
}

const DetailReportByMonthTFooter = ({ projects }: IDetailReportByMonthTFooterProps) => {
    const total = projects.reduce(
        (prev, next) => ({
            overheadAllocatedAmt: prev.overheadAllocatedAmt + (next.overheadAllocatedAmt || 0),
            salaryCost: prev.salaryCost + (next.salaryCost || 0),
            totalCost: prev.totalCost + (next.totalCost || 0)
        }),
        { overheadAllocatedAmt: 0, salaryCost: 0, totalCost: 0 }
    );

    return (
        <TableRow sx={{ '& .MuiTableCell-root': { fontWeight: '600', color: '#000' } }}>
            <TableCell colSpan={3}>&nbsp;</TableCell>
            <TableCell align="center">Total</TableCell>
            <TableCell align="center">{formatPrice(total.overheadAllocatedAmt)}</TableCell>
            <TableCell align="center">{formatPrice(total.salaryCost)}</TableCell>
            <TableCell align="center">{formatPrice(total.totalCost)}</TableCell>
        </TableRow>
    );
};

export default DetailReportByMonthTFooter;
