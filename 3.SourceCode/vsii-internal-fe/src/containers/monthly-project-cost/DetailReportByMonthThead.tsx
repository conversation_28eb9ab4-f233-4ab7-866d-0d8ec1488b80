// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const DetailReportByMonthThead = () => {
    const theme = useTheme();
    const matches = useMediaQuery(theme.breakpoints.up('md'));

    const { monthlyProjectCost } = TEXT_CONFIG_SCREEN;
    return (
        <TableHead>
            <TableRow
                sx={{
                    '& .MuiTableCell-root': {
                        textAlign: 'center',
                        fontWeight: '700'
                    }
                }}
            >
                <TableCell
                    sx={{
                        textAlign: 'left !important',
                        left: !!matches ? 0 : 'unset',
                        zIndex: 3
                    }}
                >
                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'project'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'department'} />
                </TableCell>
                <TableCell sx={{ textAlign: 'left !important' }}>
                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'project-type'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'total-effort-md'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'overhead-allocated-amt'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'salary-cost'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'total-cost'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default DetailReportByMonthThead;
