import { FormattedMessage } from 'react-intl';

// material-ui
import ErrorIcon from '@mui/icons-material/Error';
import { Grid, Typography } from '@mui/material';
import { Button } from 'components';

// project imports
import { Label } from 'components/extended/Form';
import { detailReportByMonthConfig, detailReportByMonthSchema } from 'pages/monthly-project-cost/Config';
import { IOption } from 'types';
import { Months, Project, SearchForm, Years } from '../search';
import { TEXT_CONFIG_SCREEN, TEXT_INPUT_COLOR_EFFORT_INCURRED } from 'constants/Common';
import ColorNoteTooltip from 'components/ColorNoteTooltip';

interface IDetailReportByMonthSearchProps {
    months: IOption[];
    handleChangeYear: (e: any) => void;
    handleSearch: (value: any) => void;
    formReset: any;
    handleChangeMonth: (e: any) => void;
    month: any;
}

const DetailReportByMonthSearch = (props: IDetailReportByMonthSearchProps) => {
    const { months, handleChangeYear, handleSearch, formReset, handleChangeMonth, month } = props;
    const { monthlyProjectCost } = TEXT_CONFIG_SCREEN;
    return (
        <SearchForm
            defaultValues={detailReportByMonthConfig}
            formSchema={detailReportByMonthSchema}
            handleSubmit={handleSearch}
            formReset={formReset}
        >
            <Grid container spacing={2}>
                <Grid item xs={12} lg={3}>
                    <Years handleChangeYear={handleChangeYear} label={monthlyProjectCost.detailReportByMonth + 'year'} />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Months
                        months={months}
                        isShow13MonthSalary
                        onChange={handleChangeMonth}
                        isFilter
                        year={formReset.year}
                        label={monthlyProjectCost.detailReportByMonth + 'month'}
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Project
                        isNotStatus
                        month={month}
                        label={
                            <Typography display="flex" gap={0.5}>
                                <FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'project'} />
                                <ColorNoteTooltip notes={TEXT_INPUT_COLOR_EFFORT_INCURRED}>
                                    <ErrorIcon sx={{ fontSize: 15 }} />
                                </ColorNoteTooltip>
                            </Typography>
                        }
                    />
                </Grid>
                <Grid item xs={12} lg={3}>
                    <Label label="&nbsp;" />
                    <Button
                        type="submit"
                        size="medium"
                        children={<FormattedMessage id={monthlyProjectCost.detailReportByMonth + 'search'} />}
                        variant="contained"
                    />
                </Grid>
            </Grid>
        </SearchForm>
    );
};

export default DetailReportByMonthSearch;
