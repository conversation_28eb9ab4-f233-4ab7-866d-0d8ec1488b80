import { FormattedMessage } from 'react-intl';

// material-ui
import { TableCell, TableHead, TableRow } from '@mui/material';
import { TEXT_CONFIG_SCREEN } from 'constants/Common';

const ProjectHeadCountThead = () => {
    const { monthlyProjectCost } = TEXT_CONFIG_SCREEN;
    return (
        <TableHead>
            <TableRow>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'no'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'member-code'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'full-name'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'headcount'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'from-date'} />
                </TableCell>
                <TableCell>
                    <FormattedMessage id={monthlyProjectCost.summary + 'to-date'} />
                </TableCell>
            </TableRow>
        </TableHead>
    );
};

export default ProjectHeadCountThead;
