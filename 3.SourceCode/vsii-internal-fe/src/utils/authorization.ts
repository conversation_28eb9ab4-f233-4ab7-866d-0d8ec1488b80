import { MAIN_FUNCTION_LIST } from 'constants/Permission';
import { store } from 'app/store';
import { ITabs } from 'types';

export function userAuthorization(permissionsRequired?: string[]) {
    let isAllowFunctions: boolean = false;

    if (!permissionsRequired?.length) {
        return { isAllowFunctions };
    }
    const { userInfo } = store.getState().auth;

    const groups: string[] = userInfo?.featureList || userInfo?.functionList || userInfo?.groups || [];

    if (groups.length) {
        isAllowFunctions = MAIN_FUNCTION_LIST.some((mainFnc) =>
            mainFnc.functions.some((fnc) =>
                groups.some((fncOfUser) => fnc === fncOfUser && permissionsRequired && permissionsRequired.includes(fncOfUser))
            )
        );
    }

    return { isAllowFunctions };
}

export function checkAllowedPermission(permission_key: string): boolean {
    const userPermissions = store.getState().auth.userInfo?.groups || [];

    return userPermissions.includes(permission_key);
}

export function checkAllowedTab(tabs: ITabs[], tabValueParam?: number) {
    const tabValue: number[] = [];

    if (tabValueParam && tabValueParam > Math.max(...tabs.map((tab) => tab.value || 0))) {
        return tabValue;
    }

    tabs.forEach((item) => {
        if (item.permission_key && (checkAllowedPermission(item.permission_key) || !store.getState().auth.userInfo?.isLdap)) {
            if (item.value === tabValueParam) {
                tabValue.unshift(item.value);
            } else {
                tabValue.push(item.value);
            }
        }
    });

    return tabValue;
}
