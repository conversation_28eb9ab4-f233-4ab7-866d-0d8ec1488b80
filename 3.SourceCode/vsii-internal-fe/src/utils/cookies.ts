import CryptoJS from 'crypto-js';

import { cryptoJSConfigMode, privateKeyHasLogin } from 'index';

/**
 * Encryption
 * @param {string} text The string to be encrypted
 * @return {string} encrypted string
 */
export function encryptByAES(text: string): string {
    let cipher = CryptoJS.AES.encrypt(text, privateKeyHasLogin, cryptoJSConfigMode);
    return cipher.toString();
}

/**
 * Decrypt
 * @param {string} cipherText ciphertext
 * @return {string} Decrypted string
 */
export function decryptByAES(cipherText: string): string {
    let cipher = CryptoJS.AES.decrypt(cipherText, privateKeyHasLogin, cryptoJSConfigMode);
    return cipher.toString(CryptoJS.enc.Utf8);
}

/**
 * Get cookie by key
 * @param name
 * @returns
 */
export function getCookieByKey(name: string): string {
    const cookie: any = {};
    document.cookie.split(';').forEach(function (el) {
        const [k, v] = el.split('=');
        cookie[k.trim()] = v;
    });
    return cookie[name];
}

/**
 * Delete cookie by key
 * @param name
 */
export function deleteCookieByKey(name: string) {
    document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;';
}

// Clear all cookies
export function clearAllCookies() {
    const cookies = document.cookie.split('; ');
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i];
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
        document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;';
    }
}

/**
 * setCookieByKeyObject
 * @param obj
 */
export function setCookieByKeyObject(obj: any) {
    const now = new Date();
    now.setMonth(now.getMonth() + 1);
    const expirationDate = now.toUTCString();

    Object.keys(obj).forEach((key) => {
        document.cookie = `${key}=${obj[key]}; path=/; expires=${expirationDate}`;
    });
}
