export const ROUTER = {
    home: {
        index: '/'
    },
    authentication: {
        login: 'login',
        confirmEmail: 'confirm-email',
        register: 'register',
        forgot: 'forgot',
        forgotPassword: 'forgot-password'
    },
    administration: {
        manage_user: 'manage-user',
        manage_project: 'manage-project',
        manage_holiday: 'manage-holiday',
        manage_special_hours: 'manage-special-hours',
        manage_group: 'manage-group',
        system_config: 'system-config',
        manage_rank: 'manage-rank',
        email_config: 'email-config',
        cv_config: 'cv-config',
        exchange_rate_config: 'exchange-rate-config',
        manage_department: 'manage-department',
        project_type_config: 'project-type-config',
        title_config: 'title-config',
        non_billables_config: 'non-billables-config',
        flexible_reporting: {
            index: 'flexible-reporting',
            column_config: 'column-config',
            text_config: 'text-config',
            flexible_reporting_config: 'flexible-reporting-config'
        }
    },
    workingCalendar: {
        register_working_calendar: 'register-working-calendar',
        manage_leave_days: 'manage-leave-days',
        manage_leaves: 'manage-leaves',
        manage_ot: 'manage-ot-requests',
        manage_resignation: 'manage-resignation',
        manage_leave_requests: 'manage-leave-requests'
    },
    reports: {
        weekly_effort: 'weekly-effort',
        monthly_effort: {
            index: 'monthly-effort',
            summary: 'summary',
            project: 'project',
            department_member: 'department-member'
        },
        general_report: {
            index: 'general-report',
            product: 'product-report',
            project_report: 'project-report',
            orm_report: 'orm-report'
        },

        non_billable_monitoring: {
            index: 'non-billable-monitoring',
            non_billable_by_member: 'non-billable-by-member',
            non_billable_cost_by_week: 'nonbill-ratio-chart'
        },
        resources_in_project: 'resources-in-project',
        cost_monitoring: {
            index: 'cost-monitoring',
            weekly_cost: 'weekly-monitoring',
            monthly_cost: 'monthly-monitoring'
        },
        monthly_project_cost: {
            index: 'monthly-project-cost',
            summary: 'summary',
            detail_report_by_month: 'detail-report-by-month',
            monthly_cost_data: 'monthly-cost-data'
        },
        sales: {
            index: 'sales',
            monthly_production_performance: 'monthly-production-performance',
            sales_lead: 'sales-lead',
            monitor_bidding_package: 'monitor-bidding-package',
            project_reference: 'project-reference',
            sales_pipeline: {
                index: 'sales-pipeline',
                summary: 'summary',
                on_going: 'on-going',
                bidding: 'all',
                budgeting_plan: 'budgeting-plan'
            }
        },
        skills_manage: {
            index: 'manage-skills',
            skills_update: 'skills-update',
            skills_report: 'skills-report',
            cv: 'cv'
        }
    }
};
