// project imports
import { IOption, IPaginationParam, IPaginationResponse, ITreeItem, ITabs } from 'types';
import { PERMISSIONS } from './Permission';

// PERMISSION
const { weeklyEffort, monthlyEffort, nonBillable, cvConfig } = PERMISSIONS.report;

const { saleList, monitorBiddingPackage } = PERMISSIONS.sale;

// LAYOUT
export const LAYOUT_CONST = {
    VERTICAL_LAYOUT: 'vertical',
    HORIZONTAL_LAYOUT: 'horizontal',
    DEFAULT_DRAWER: 'default',
    MINI_DRAWER: 'mini-drawer'
};
// text config
export const TEXT_CONFIG_SCREEN = {
    sidebar: 'menu-',
    salesReport: {
        allSalesPineline: 'all-sale-pipeline',
        salesOnGoing: 'on-going',
        budgetingPlan: 'budgeting-plan',
        summary: 'sale-summary',
        monthlyProductionPerformance: 'monthly-production-performance',
        salesLead: 'sales-lead-',
        monitorBiddingPackages: 'monitor-bidding-packages-',
        projectReference: 'project-reference-',
        skillsUpdate: 'skill-manager-skill-update-',
        skillsReport: 'skill-manager-skill-report-'
    },
    monthlyProjectCost: {
        summary: 'monthly-project-cost-summary-',
        detailReportByMonth: 'monthly-project-cost-detail-report-',
        monthlyCost: 'monthly-project-cost-monthly-cost-'
    },
    costAndEffortMonitoring: {
        weeklyMonitoring: 'cost-effort-monitoring-weekly-monitoring-',
        monthlyMonitoring: 'cost-effort-monitoring-monthly-monitoring-'
    },
    resourcesInProject: 'resources-in-projects-',

    nonBillablemonitoring: {
        nBMByMember: 'non-billable-monitoring-nbm-by-member-',
        NBMRatioChart: 'non-billable-monitoring-nbm-ratio-chart-'
    },
    monthlyEffort: {
        Summary: 'monthly-effort-summary-',
        monthlyEffortProject: 'monthly-effort-project-',
        effortbymember: 'monthly-effort-member-'
    },
    Weeklyeffort: 'weekly-effort-',
    generalReport: {
        ORMReport: 'general-report-orm-',
        ProductReport: 'general-report-product-',
        project_report: 'general-report-project-'
    },
    workingCalendar: {
        manage_resignation: 'manage-resignation-',
        manage_leaves: 'manage-leaves-',
        manage_leave_days: 'manage-leave-days-',
        register_working_calendar: 'register-working-calendar-',
        manage_ot: 'manage-ot-'
    },
    administration: {
        Non_billables_Config: 'non-billables-config-',
        title_config: 'title-config-',
        project_type_config: 'project-type-config-',
        manage_department: 'manage-department-',
        exchange_rate_config: 'exchange-rate-config-',
        cv_config: 'cv-config-',
        email_config: 'email-config-',
        system_config: 'system-config-',
        Manage_rank: 'manage-rank-',
        manage_group: 'manage-group-',
        manage_special_hours: 'manage-special-hours-',
        Manage_holidays: 'manage-holidays-',
        manage_project: 'manage-project-',
        manage_user: 'manage-user-',
        flexibleReport: {
            column_config: 'column-config-',
            Flexible_reporting_configuration: 'flexible-reporting-configuration-',
            language_config: 'language-config-'
        }
    },
    dashboard: 'dashboard-'
};

// THEME
export const THEME_CONST = {
    gridSpacing: 3,
    drawerWidth: 260,
    appDrawerWidth: 320
};

// pagination default
export const paginationResponseDefault: IPaginationResponse = {
    pageNumber: 0,
    pageSize: 10,
    totalElement: 0,
    totalPage: 0
};

export const paginationParamDefault: IPaginationParam = {
    page: 0,
    size: 10
};

// date format
export const DATE_FORMAT = {
    DDMMYYYY: 'DD/MM/YYYY', //default
    ddMMyyyy: 'dd/MM/yyyy',
    MMM: 'MMM', // 3 first letter of month
    MMMM: 'MMMM', // month
    DDMMYYYYHHmmss: 'DD/MM/YYYY HH:mm:ss',
    HHmmssDDMMYYYY: 'HH:mm:ss DD/MM/YYYY',
    DoMMMYY: 'Do MMM YY' // 13th Jul 2023
};

// search form
// defaultValue option
export const DEFAULT_VALUE_OPTION: IOption = { value: '', label: 'select-all' };
export const DEFAULT_VALUE_OPTION_SELECT: IOption = { value: '', label: 'select-option' };
export const DEFAULT_VALUE_MEMBER_OPTION: IOption = { value: '', label: '-- All ---' };

// time status
export const TIME_STATUS: IOption[] = [
    { value: 'enough', label: 'enough' },
    { value: 'not-enough', label: 'not-enough' },
    { value: 'exceed', label: 'exceed' }
];

//
export const PROJECT_REPORT_STATUS: IOption[] = [
    { value: 1, label: 'Done' },
    { value: 2, label: 'In Progress' },
    { value: 0, label: 'N/A' }
];
export const PROJECT_REPORT_TYPE: IOption[] = [
    { value: 'issue', label: 'Issue' },
    { value: 'risk', label: 'Risk' }
];

export const PROJECT_PROGRESS_ASSESSMENT: IOption[] = [
    { value: 'Ahead of Schedule', label: 'Ahead of Schedule' },
    { value: 'On Schedule', label: 'On Schedule' },
    { value: 'Maybe Delayed', label: 'Maybe Delayed' },
    { value: 'Behind Schedule', label: 'Behind Schedule' }
];

export const PROJECT_IMPLEMENTATION_PHASE: IOption[] = [
    { value: 'Golive & Maintenance', label: 'Golive & Maintenace' },
    { value: 'Deployment', label: 'Deployment' }
];

// time status
export const TIME_STATUS_BY_NON_BILL: IOption[] = [
    DEFAULT_VALUE_OPTION,
    { value: 'notLogtime', label: 'staff-no-logtime', color: '#ff9393' },
    { value: 'projectPRD', label: 'staff-join-PRD', color: '#A9D08E' },
    { value: 'timeEffort50', label: 'effort-of-staff-50', color: '#FFE699' },
    { value: 'timeEffort80', label: 'effort-of-staff-80', color: '#F8CBAD' }
];

// user status
export const STATUS: IOption[] = [DEFAULT_VALUE_OPTION, { value: '1', label: 'active' }, { value: '3', label: 'in-active' }];

export const STATUS_USER = {
    active: 1,
    inActive: 3
};

// period option
export const PERIOD: IOption[] = [
    { value: 'month', label: 'month' },
    { value: 'week', label: 'weeks' }
];

// user title
export const USER_TITLE: IOption[] = [
    { value: 'SEP', label: 'SEP' },
    { value: 'SSE', label: 'SSE' },
    { value: 'PRG', label: 'PRG' }
];

// user title
export const USER_LEVEL: IOption[] = [
    { value: 'SEP', label: 'SEP' },
    { value: 'SSE', label: 'SSE' },
    { value: 'PRG', label: 'PRG' }
];

// row color
export enum ROW_COLOR {
    ENOUGH = '#FFFFFF',
    NOT_ENOUGH = '#FEFFD9',
    EXCEED = '#FFDADA'
}

// effort report tabs
export const weeklyEffortReportTabs: ITabs[] = [
    { name: 'weekly-effort-members', permission_key: weeklyEffort.viewMember, value: 0 },
    { name: 'weekly-effort-projects', permission_key: weeklyEffort.viewProject, value: 1 },
    { name: 'weekly-effort-project-detail', permission_key: weeklyEffort.viewProjectDetail, value: 2 }
];

// cv config
export const cvReportTabs: ITabs[] = [
    { name: 'cv-config-technology', permission_key: cvConfig.viewTechnology, value: 0 },
    { name: 'cv-config-language', permission_key: cvConfig.viewLanguage, value: 1 },
    { name: 'cv-config-reference', permission_key: cvConfig.viewReference, value: 2 }
];

export const monthlyEffortReportTabs: ITabs[] = [
    { name: 'monthly-effort-member-members', permission_key: monthlyEffort.viewDepartmentMember.viewTabMembes, value: 0 },
    { name: 'monthly-effort-member-projects', permission_key: monthlyEffort.viewDepartmentMember.viewTabProjects, value: 1 }
];

// add/edit user tabs
export const addOrEditUserTabs: ITabs[] = [
    { name: 'manage-user-user-info', value: 0 },
    { name: 'manage-user-group', value: 1 },
    { name: 'manage-user-on-outboard-info', value: 2 },
    { name: 'manage-user-title', value: 3 },
    { name: 'manage-user-billable', value: 4 },
    { name: 'manage-user-project-permission', value: 5 }
];

// money format placehoder
export const MONEY_PLACEHOLDER = '0,XXX,XXX';

// project status
export const STATUS_PROJECT_OPTIONS: IOption[] = [
    DEFAULT_VALUE_OPTION,
    { value: '1', label: 'active' },
    { value: '5', label: 'closed' },
    { value: '9', label: 'archived' }
];

export const STATUS_PROJECT: any = {
    1: 'Active',
    5: 'Closed',
    9: 'Archived'
};

// 13th month salary
export const MONTH_SALARY_13TH_OPTION = { value: '13', label: '13th Month Salary' };

// group config checkbox
export const GROUP_OPTION_CONFIG = { value: 'groupId', label: 'groupName' };

// project config checkbox
export const PROJECT_OPTION_CONFIG = { value: 'projectId', label: 'projectName' };

// billable
export const BILLABLE_OPTIONS: IOption[] = [
    { value: 'billable', label: 'Billable' },
    { value: 'nonbillable', label: 'Nonbillable' },
    { value: 'Billable_Signed the contract', label: 'Billable_Signed the contract' },
    { value: 'Billable_Not yet sign the contract', label: 'Billable_Not yet sign the contract' }
];

// edit project tabs
export const projectEditTabs: ITabs[] = [
    { name: 'manage-project-project-info', value: 0 },
    { name: 'manage-project-headcount', value: 1 },
    { name: 'manage-project-quota-update-history', value: 2 }
];

// headcount
export const HEADCOUNT_OPTIONS: IOption[] = [
    { value: 'MainHC', label: 'Main HC' },
    { value: 'SubHC', label: 'Sub HC' }
];

// list project team tabs

// non-Billable tabs
export const nonBillMonitoringTabs: ITabs[] = [
    { name: 'non-billable-monitoring-nbm-by-member-nbm-by-member', permission_key: nonBillable.viewMember, value: 0 },
    { name: 'non-billable-monitoring-nbm-by-member-warning-nbm-member', permission_key: nonBillable.viewWarning, value: 1 }
];

// list monthly project cost tabs
export const listMonthlyProjectCostTabs: ITabs[] = [
    { name: 'monthly-project-cost-summary-fix-cost', value: 0 },
    { name: 'monthly-project-cost-summary-others', value: 1 }
];

// list monthly monitoring / cost & effort monitoring tabs
export const listMonthlyMonitoringCostTabs: ITabs[] = [
    { name: 'cost-effort-monitoring-monthly-monitoring-fixed-cost', value: 0 },
    { name: 'cost-effort-monitoring-monthly-monitoring-others', value: 1 }
];

// status login
export enum ESTATUS_LOGIN {
    NOT_YET,
    SUCCESS
}

// public url
export const PUBLIC_URL = `${process.env.PUBLIC_URL}`;

// log time value
export enum E_IS_LOGTIME {
    YES = 'Yes',
    NO = 'No',
    ALL = ''
}

// percent format placeholder
export const PERCENT_PLACEHOLDER = 'XXX%';
export const PERCENT_PLACEHOLDER_MIN_VALUE = 'Enter min value %';
export const PERCENT_PLACEHOLDER_MAX_VALUE = 'Enter max value %';

// special hours type
export const TYPE_SPECIAL_HOURS_OPTIONS: IOption[] = [
    { value: '1', label: 'post-pregnancy' },
    { value: '2', label: 'maternity' },
    { value: '3', label: 'unpaid-leave' },
    { value: '4', label: 'other' }
];

//Manage holiday
export const TYPE_HOLIDAY_OPTIONS: IOption[] = [
    { value: '1', label: 'annual' },
    { value: '0', label: 'current-year' }
];

export const TYPE_INPUT: IOption[] = [
    { value: 'text', label: 'Text' },
    { value: 'select', label: 'Select' },
    { value: 'number', label: 'Number' },
    { value: 'date', label: 'Date' }
];

export const HOLIDAY_OPTIONS: IOption[] = [DEFAULT_VALUE_OPTION, { value: '1', label: 'Hàng năm' }, { value: '5', label: 'Năm hiện tại' }];

// saveOrUpdateGroup tabs
export const saveOrUpdateGroupTabs: ITabs[] = [
    { name: 'manage-group-group-info', value: 0 },
    { name: 'manage-group-permission', value: 1 }
];
export const saveOrUpdateGroupAssignedUserTabs: ITabs[] = [
    { name: 'manage-group-group-info', value: 0 },
    { name: 'manage-group-permission', value: 1 },
    { name: 'manage-group-assigned-user', value: 2 }
];

// default permission
export const TREEITEM_DEFAULT_VALUE: ITreeItem = {
    value: 'ALL',
    name: 'all',
    children: []
};

// tree view
export const PERMISSION_EXPANDED_DEFAULT_VALUE = ['ALL', 'REPORT', 'ADMIN'];

// Month list
export const MONTHS = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

// Productivity type
export const PRODUCTIVITY_TYPE: IOption[] = [
    { value: 'ITO', label: 'ITO - Outsource trong nước' },
    { value: 'O.ITO', label: 'O.ITO - Outsource nước ngoài' },
    { value: 'SI', label: 'SI - Tích hợp phần mềm' },
    { value: 'Product', label: 'Product - Dự án sản phẩm' },
    { value: 'MTN.App', label: 'MTN.App - Bảo trì ứng dụng' },
    { value: 'MTN.Lic', label: 'MTN.Lic - Bảo trì License' }
];

// Contract Type
export const CONTRACT_TYPE: IOption[] = [
    { value: 'TM', label: 'TM' },
    { value: 'Fixed Cost', label: 'Fixed Cost' }
];

// Role Type
export const ROLE_TYPE: IOption[] = [
    { value: 'PM', label: 'PM' },
    { value: 'Team Lead', label: 'Team Lead' },
    { value: 'SSE', label: 'SSE' },
    { value: 'SE', label: 'SE' },
    { value: 'BA', label: 'BA' },
    { value: 'Tester', label: 'Tester' },
    { value: 'Others', label: 'Others' }
];

// Payment Term
export const PAYMENT_TERM: IOption[] = [
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' }
];

export const MONTHLY_BILLABLE: IOption[] = [
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' },
    { value: '5', label: '5' },
    { value: '6', label: '6' },
    { value: '7', label: '7' },
    { value: '8', label: '8' },
    { value: '9', label: '9' },
    { value: '10', label: '10' },
    { value: '11', label: '11' },
    { value: '12', label: '12' }
];

// report type
export enum REPORT_TYPE {
    RP_MONTHLY = 'RP_MONTHLY',
    RP_WEEK = 'RP_WEEK',
    RP_NON_BILLABLE_MONITORING = 'RP_WEEK_NON_BILLABLE_MONITORING',
    RP_LIST_PROJECT_TEAM = 'RP_WEEK_LIST_PROJECT_TEAM',
    RP_WEEKLY_COST_MONITORING = 'RP_WEEKLY_COST_MONITORING',
    RRP_MONTHLY_COST_MONITORING = 'RP_MONTHLY_COST_MONITORING'
}

//sales lead
export const STATUS_REQUESTS_CHECKING: IOption[] = [
    { value: 'Not Start', label: 'not-start' },
    { value: 'Inprogress', label: 'inprogress' },
    { value: 'Stop', label: 'stop' }
];

export const POSSIBILITY: IOption[] = [
    { value: 'High', label: 'high' },
    { value: 'Normal', label: 'normal' },
    { value: 'Low', label: 'low' }
];

export const WORK_TYPE: IOption[] = [
    { value: 'Remote', label: 'remote' },
    { value: 'Onsite', label: 'onsite' }
];

export const saleListTabs: ITabs[] = [
    { name: 'sales_lead_requests_requests-checking', permission_key: saleList.viewRequests, value: 0 },
    { name: 'sales_lead_supplier_supplier-checking', permission_key: saleList.viewSupplier, value: 1 }
];

// search params key
export const SEARCH_PARAM_KEY = {
    // ====== Common =======
    tab: 'tab',
    page: 'page',
    size: 'size',
    year: 'year',
    month: 'month',
    week: 'week',
    fromDate: 'fromDate',
    toDate: 'toDate',
    supplierName: 'supplierName',
    partnerName: 'partnerName',
    picUserName: 'picUserName',
    receivedDate: 'receivedDate',
    departmentId: 'departmentId',
    timeStatus: 'timeStatus',
    status: 'status',
    type: 'type',
    titleCode: 'titleCode',
    titleName: 'titleName',
    dept: 'dept',
    projectLabel: 'projectLabel',
    projectValue: 'projectValue',
    projectTypeCode: 'projectTypeCode',
    projectManagerLabel: 'projectManagerLabel',
    projectManagerValue: 'projectManagerValue',
    // ====== Project =======
    projectType: 'projectType',
    projectId: 'projectId',
    projectName: 'projectName',
    projectManager: 'projectManager',
    projectManagerId: 'projectManagerId',
    technology: 'technology',
    domain: 'domain',
    // ====== User =======
    userId: 'userId',
    fullname: 'fullname',
    userName: 'userName',
    memberCode: 'memberCode',
    contractor: 'contractor',
    idHexString: 'idHexString',
    userIdHexString: 'userIdHexString',
    // ====== Group =======
    groupCode: 'code',
    groupName: 'name',
    // ====== Holiday =======
    holidayType: 'type',
    specialHoursType: 'type',
    // ====== Sale - Ongoing ======
    productionPerformanceIdHexString: 'productionPerformanceIdHexString',
    // ===== Skills manage =====
    skill: 'skill',
    level: 'level',
    degree: 'degree',
    // ===== Budgeting plan =====
    pipelineType: 'pipelineType',
    // ===== Monitor bidding packages ====
    biddingPackagesName: 'biddingPackagesName',
    address: 'address',
    packageName: 'packageName',
    // ======orm report========
    ormReportName: 'reportName',
    // ======flexiable report========
    flexibleReportId: 'flexibleReportId',
    flexibleColumnName: 'flexibleColumnName',
    reportId: 'reportId',
    textName: 'textName',
    flexibleColumnId: 'flexibleColumnId',
    code: 'code',
    searchMode: 'searchMode',
    customer: 'customer',
    searchModeOfCustomer: 'searchModeOfCustomer',
    searchModeOfProject: 'searchModeOfProject'
};

export const FIELD_BY_TAB_USERS = [
    {
        tabValue: 0,
        fields: ['departmentId', 'firstName', 'lastName', 'memberCode', 'userName', 'userLevel']
    },
    {
        tabValue: 2,
        fields: ['userOnboardHistoryList']
    },
    {
        tabValue: 3,
        fields: ['userRankHistoryList']
    },
    {
        tabValue: 4,
        fields: ['userBillableHistoryList']
    }
];

export const FIELD_BY_TAB_REPORT = [
    {
        tabValue: 0,
        fields: ['projectReportInfo']
    },
    {
        tabValue: 1,
        fields: ['monthlyReport']
    }
];

export const FIELD_BY_TAB_ONGOING = [
    {
        tabValue: 0,
        fields: ['projectInfo.status', 'projectInfo.productionPerformanceIdHexString']
    }
];
export const FIELD_BY_TAB_BIDDING = [
    {
        tabValue: 0,
        fields: ['project']
    },
    {
        tabValue: 1,
        fields: ['financialInfo']
    }
];

// group color
export const GROUP_COLOR_MONTH = '#fff2f2';

// Day list
export const DAY_LIST = [
    { value: 'MON', label: 'Monday' },
    { value: 'TUE', label: 'Tuesday' },
    { value: 'WED', label: 'Wednesday' },
    { value: 'THU', label: 'Thursday' },
    { value: 'FRI', label: 'Friday' },
    { value: 'SAT', label: 'Saturday' },
    { value: 'SUN', label: 'Sunday' }
];

// Email type
export enum EMAIL_TYPE {
    RP_WEEK = 'RP_WEEK',
    RP_MONTH = 'RP_MONTH'
}

export const EMAIL_TYPE_OPTIONS: IOption[] = [
    { value: EMAIL_TYPE.RP_WEEK, label: 'week' },
    { value: EMAIL_TYPE.RP_MONTH, label: 'month' }
];
// groupType select

export const GROUP_TYPE: IOption[] = [
    {
        value: 'BOD',
        label: 'BOD'
    },
    {
        value: 'HRD',
        label: 'HRD'
    },
    {
        value: 'PM',
        label: 'PM'
    }
];

// contractor select
export const CONTRACTOR: IOption[] = [
    { value: 'Yes', label: 'contractor-yes' },
    { value: 'No', label: 'contractor-no' }
];

// project permission options
export const PROJECT_PERMISSION_OPTIONS: IOption[] = [
    { value: 'Assigned', label: 'detail-by-project' },
    { value: 'All', label: 'all' }
];

// Email config options
export const EMAIL_CONFIG_OPTIONS: IOption[] = [
    { value: 'ACTIVE', label: 'active' },
    { value: 'INACTIVE', label: 'in-active' }
];

// Register working calendar
export const REGISTER_WORKING_CALENDAR_TYPE: IOption[] = [
    { value: 'WAO', label: 'work-at-office', color: '#FFC000' },
    { value: 'OS', label: 'onsite', color: '#E6EF17 ' },
    { value: 'X/2', label: 'half-day-leave', color: '#DCBEB2' },
    { value: 'TS', label: 'maternity-leave', color: '#C55A11' },
    { value: 'NC', label: 'wedding-vacation', color: '#D77B43' },
    { value: 'WFH', label: 'work-from-home', color: '#77A348' },
    { value: 'X', label: 'full-day-leave', color: '#E8CAAC' },
    { value: 'O', label: 'sick-leave', color: '#E70A17' },
    { value: 'NL', label: 'holiday', color: '#7C91BF' },
    { value: 'NB', label: 'compensatory-leave', color: '#979797' },
    { value: 'KL', label: 'unpaid-leave', color: '#CBD743' },
    { value: 'OT', label: 'over-time', color: '#F5DEB3' }
];

export enum TYPE_REGISTER_WORKING_CALENDAR {
    X = 'X',
    WAO = 'WAO'
}

// sale pipeline ongoing type options
export const SALE_PIPELINE_TYPE: IOption[] = [
    { value: 'Service-Sale', label: 'scs-service-prd-sales' },
    {
        value: 'I.ITO',
        label: 'international-ito'
    }
];

// sale pipe ongoing status options
export const SALE_PIPELINE_STATUS: IOption[] = [
    { value: 'On-going', label: 'on-going' },
    { value: 'Done', label: 'done' },
    { value: 'Pending', label: 'pending' },
    { value: 'NPL', label: 'npl' }
];

// sale pipeline ongoing add of edit tabs
export const editOnGoingTabs: ITabs[] = [
    { name: 'on-going-project-info', value: 0 },
    { name: 'on-going-financial-info', value: 1 },
    { name: 'on-going-detailed-allocation', value: 2 },
    { name: 'on-going-other-info', value: 3 }
];

// month of year
export const MONTHS_OF_YEAR: any = {
    1: 'jan',
    2: 'feb',
    3: 'mar',
    4: 'apr',
    5: 'may',
    6: 'jun',
    7: 'jul',
    8: 'aug',
    9: 'sep',
    10: 'oct',
    11: 'nov',
    12: 'dec'
};

// HC month of year
export const HC_ON_GOING_MONTHS_OF_YEAR: any = {
    1: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-jan',
    2: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-feb',
    3: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-mar',
    4: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-apr',
    5: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-may',
    6: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-jun',
    7: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-jul',
    8: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-aug',
    9: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-sep',
    10: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-oct',
    11: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-nov',
    12: TEXT_CONFIG_SCREEN.salesReport.salesOnGoing + '-dec'
};

// HC all sales pipline month of year
export const HC_ASPL_MONTHS_OF_YEAR: any = {
    1: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-jan',
    2: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-feb',
    3: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-mar',
    4: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-apr',
    5: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-may',
    6: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-jun',
    7: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-jul',
    8: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-aug',
    9: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-sep',
    10: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-oct',
    11: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-nov',
    12: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-dec'
};
// sale pipe budgeting plan service type
export const SALE_PIPELINE_BUDGETING_PLAN_SERVICE_TYPE: IOption[] = [
    { value: 'ITO', label: 'ito' },
    { value: 'O.ITO', label: 'o-ito' },
    { value: 'SI', label: 'si' },
    { value: 'MTN.App', label: 'mtn-app' },
    { value: 'MTN.Lic', label: 'mtn-lic' },
    { value: 'Product', label: 'product' }
];

// sale pipeline budgeting edit tabs
export const EditBudgetingPlanTabs: ITabs[] = [
    { name: 'budgeting-plan-project-info', value: 0 },
    { name: 'budgeting-plan-project-KPI-score', value: 1 },
    { name: 'budgeting-plan-project-KPI-bonus', value: 2 }
];

export const FIELD_BY_TAB_BUDGETING_PLAN = [
    {
        tabValue: 0,
        fields: ['budgeting-plan-project-info', 'budgeting-plan-project-KPI-score', 'budgeting-plan-project-KPI-bonus']
    }
];

// riskFactor format placeholder
export const RISK_FACTOR_PLACEHOLDER = 'XX%';

//bidding chua co
//bidding chua co
export const addOrEditBiddingTabs: ITabs[] = [
    { name: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-project-info', value: 0 },
    { name: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-financial-info', value: 1 },
    { name: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-detailed-allocation', value: 2 },
    { name: TEXT_CONFIG_SCREEN.salesReport.allSalesPineline + '-other-info', value: 3 }
];

export const BIDDING_TYPE: IOption[] = [
    { value: 's123', label: 'SCS service and PRD sales' },
    { value: 'International ITO', label: 'International ITO' }
];

export const SERVICE_TYPE: IOption[] = [
    { value: 'SI', label: 'SI' },
    { value: 'ITO', label: 'ITO' },
    { value: 'O.ITO', label: 'O.ITO' },
    { value: 'Product', label: 'Product' },
    { value: 'MTN.App', label: 'MTN.App' },
    { value: 'MTN.Lic', label: 'MTN.Lic' }
];

export const BIDDING_STATUS: IOption[] = [
    { value: 'Bidding', label: 'bidding' },
    { value: 'Contract', label: 'contract' },
    { value: 'Loss', label: 'loss' },
    { value: 'Failed', label: 'failed' }
];

export const CURRENCY: IOption[] = [
    { value: 'USD', label: 'USD' },
    { value: 'VND', label: 'VND' },
    { value: 'JPY', label: 'JPY' }
];

export enum DEPARTMENTS {
    SCS = 'SCS',
    PRD = 'PRD',
    SMD = 'SMD'
}

export enum CONTRACT_TYPE_SALE_REPORT {
    TM = 'TM',
    FIXED_COST = 'Fixed Cost'
}
export const ADDED_EBITDA: IOption[] = [
    { value: 'Yes', label: 'Yes' },
    { value: 'No', label: 'No' }
];

// skill report
export const SKILL: IOption[] = [
    { value: 'Test', label: 'Test' },
    { value: '.NET', label: '.NET' },
    { value: 'Analysis', label: 'Analysis' }
];

// Data source
export const DATA_SOURCE_USING_LDAP: IOption[] = [
    { value: 'redmine', label: 'Redmine' },
    { value: 'excel', label: 'Excel' }
];

export const DATA_SOURCE_NOT_USING_LDAP: IOption[] = [{ value: 'excel', label: 'Excel' }];

export const LEVEL: IOption[] = [
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' },
    { value: '5', label: '5' }
];

export const DEGREE: IOption[] = [
    { value: 'diploma-degree', label: 'diploma-degree' },
    { value: 'college-degree', label: 'college-degree' },
    { value: 'post-graduated', label: 'post-graduated' },
    { value: 'other', label: 'other' }
];

export const EXPERT_LEVEL: IOption[] = [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
    { value: 5, label: '5' }
];

export const DEGREE_OPTION_RADIO: IOption[] = [
    {
        value: 'diploma-degree',
        label: 'Diploma degree'
    },
    {
        value: 'college-degree',
        label: 'College degree'
    },
    {
        value: 'post-graduated-degree',
        label: 'Post graduated'
    },
    {
        value: 'other',
        label: 'Other'
    }
];

export const DEFAULT_IMAGE_IMAGE_JPG_BASE64 = 'data:image/jpg;base64,';

// Unit
export enum UNIT_SALE_REPORT {
    MAN_MONTH = 'Man-month',
    MAN_DAY = 'Man-day',
    MAN_HOUR = 'Man-hours'
}
// UNIT SELECT OPTION
export const UNIT_SELECT_OPTION: IOption[] = [
    { label: 'Man-month', value: 'Man-month' },
    { label: 'Man-day', value: 'Man-day' },
    { label: 'Man-hours', value: 'Man-hours' }
];

// Department Bidding
export const DEPARTMENT_BIDDING_OPTION: IOption[] = [
    { label: 'SCS', value: 'SCS' },
    { label: 'PRD', value: 'PRD' },
    { label: 'SMD', value: 'SMD' }
];

// Status Bidding
export enum E_BIDDING_STATUS {
    CONTRACT = 'Contract',
    BIDDING = 'Bidding',
    LOSS = 'Loss',
    FAILED = 'Failed'
}

export enum SERVICE_TYPE_STATUS {
    PRODUCT = 'Product'
}

// ENUM SCREEN SALES YEAR OPTION
export enum E_SCREEN_SALES_YEAR {
    ALL = 'all',
    BIDDING = 'bidding',
    ON_GOING = 'on-going',
    PRODUCTION_PERFORMANCE = 'production-performance',
    SALES_PIPELINE_SUMMARY = 'sales-pipeline-summary',
    BUDGETING_PLAN = 'budgeting-plan'
}

// TYPE BUDGETING PLAN SELECT OPTION
export const TYPE_BUDGETING_PLAN_SELECT_OPTION: IOption[] = [
    { label: 'On-going', value: 'On-going' },
    { label: 'Bidding', value: 'Bidding' }
];

// ENUM COMMENT TYPE SALES PIPELINE
export enum E_COMMENT_TYPE_SALES_PIPELINE {
    PROJECT_NAME = 'ProjectName',
    MANAGEMENT_REVENUE = 'ManagementRevenue',
    ACCOUNTANT_REVENUE = 'AccountantRevenue',
    PAID = 'Paid',
    LICENSE_FEE = 'LicenseFee',
    SIZE_VND = 'SizeVND'
}

// ENUM TYPE SET HCs
export enum E_SALES_BIDDING_TYPE_SET_HCINFO {
    ESTIMATE = 'estimate'
}

// USER LEVEL OPTIONS
export const USER_LEVEL_OPTIONS: IOption[] = [
    {
        label: 'management-level',
        value: 'management-level'
    },
    {
        label: 'staff-level',
        value: 'staff-level'
    }
];

// LEAVES TYPE OPTIONS
export const LEAVES_TYPE_OPTIONS: IOption[] = [
    {
        label: 'full-annual-leave',
        value: 'full-annual-leave'
    },
    {
        label: 'half-annual-leave',
        value: 'half-annual-leave'
    },
    {
        label: 'wedding-leave',
        value: 'wedding-leave'
    },
    {
        label: 'maternity-leave',
        value: 'maternity-leave'
    },
    {
        label: 'half-sick-leave',
        value: 'half-sick-leave'
    },
    {
        label: 'full-sick-leave',
        value: 'full-sick-leave'
    },
    {
        label: 'bereavement-leave',
        value: 'bereavement-leave'
    },
    {
        label: 'accrued-leave',
        value: 'accrued-leave'
    },
    {
        label: 'compensatory-leave',
        value: 'compensatory-leave'
    },
    {
        label: 'long-service-leave',
        value: 'long-service-leave'
    },
    {
        label: 'unpaid-leave',
        value: 'unpaid-leave'
    }
];
export const OT_TYPE_OPTIONS: IOption[] = [
    {
        label: 'overtime-reason-1',
        value: 'overtime-reason-1'
    },
    {
        label: 'overtime-reason-2',
        value: 'overtime-reason-2'
    }
];
// STATUS LEAVES OPTIONS
export const LEAVES_STATUS_OPTIONS: IOption[] = [
    {
        label: 'awaiting-approve-direct-manager',
        value: 'awaiting-approve-direct-manager'
    },
    {
        label: 'awaiting-approve-next-manager',
        value: 'awaiting-approve-next-manager'
    },
    {
        label: 'awaiting-approve-hr',
        value: 'awaiting-approve-hr'
    },
    {
        label: 'approved',
        value: 'approved'
    },
    {
        label: 'declined',
        value: 'declined'
    }
];

export enum EApproveStatus {
    APPROVED = 'approved',
    SAVE_DRAFT = 'draft',
    AWAITING_QLTT = 'awaiting-approve-direct-manager',
    AWAITING_QLKT = 'awaiting-approve-next-manager',
    AWAITING_HR = 'awaiting-approve-hr',
    AWAITING_APPROVE = 'awaiting-approve',
    DECLINED = 'declined'
}

export const CV_DOWNLOAD_OPTION: IOption[] = [
    {
        label: 'company-template',
        value: 1
    },
    {
        label: 'smd-template',
        value: 2
    }
];

//Monitor Bidding Package

export const saleMonitorBiddingPackageTabs: ITabs[] = [
    { name: 'monitor-bidding-packages-report-bidding-report', permission_key: monitorBiddingPackage.viewPackagesBiddingReport, value: 0 },
    {
        name: 'monitor-bidding-packages-tracking-bidding-tracking',
        permission_key: monitorBiddingPackage.viewPackagesBiddingTracking,
        value: 1
    }
];

// Bidding package status
// TODO:
export const BIDDING_PACKAGE_STATUS: IOption[] = [
    { value: 'Đã đóng thầu', label: 'Đã đóng thầu' },
    { value: 'Chưa đóng thầu', label: 'Chưa đóng thầu' }
];

export const TEXT_INPUT_COLOR_EFFORT_INCURRED: IOption[] = [
    { value: 'no effort incurred', label: 'no-effort-incurred', color: '#212121' },
    { value: 'effort incurred', label: 'effort-incurred', color: '#ff00ff' }
];

export const TEXT_INPUT_COLOR_VERIFY_TIMESHEET: IOption[] = [
    { value: 'No effort incurred', label: 'no-effort-incurred', color: '#212121' },
    {
        value: 'PM has verified timesheet/QA has not verified timesheet',
        label: 'pm-has-verified-timesheet-qa-has-not-verified',
        color: '#7C91BF'
    },
    { value: 'PM has not verified timesheet', label: 'pm-has-not-verified-timesheet', color: '#E70A17' },
    { value: 'QA has verified timesheet', label: 'qa-has-verified-timesheet', color: '#ff00ff' }
];
// AddOrEditOvertimeReportModal

export const overtimeReasonOptions: IOption[] = [
    DEFAULT_VALUE_OPTION_SELECT,
    { value: 'overtime-reason-1', label: 'manage-ot-reason-project' },
    { value: 'overtime-reason-2', label: 'manage-ot-reason-urgent' }
];

export const compensateTypeOptions: IOption[] = [
    DEFAULT_VALUE_OPTION_SELECT,
    { value: 'overtime-compensate-1', label: 'manage-ot-compensate-immediate' },
    { value: 'overtime-compensate-2', label: 'manage-ot-compensate-annual' }
];

export const GROUP_ID_APPROVER = {
    MANAGEMENT_LEVEL: '0002',
    HR: '0005'
};

export enum ELeaveType {
    FULL_ANNUAL = 'full-annual-leave',
    HALF_ANNUAL = 'half-annual-leave',
    FULL_SICK = 'full-sick-leave',
    HALF_SICK = 'half-sick-leave',
    SPECIAL = 'special-leave'
}

export const STATUS_COLORS = {
    [EApproveStatus.APPROVED]: '#3163D4',
    [EApproveStatus.DECLINED]: '#A10000',
    [EApproveStatus.AWAITING_QLTT]: '#616161',
    [EApproveStatus.AWAITING_QLKT]: '#616161',
    [EApproveStatus.AWAITING_HR]: '#616161',
    [EApproveStatus.AWAITING_APPROVE]: '#616161',
    [EApproveStatus.SAVE_DRAFT]: 'textPrimary'
};

export const MESSAGE_APPROVE_RESPONSE = {
    SUCCESS: 'approve-success',
    ERROR: 'overtime-approve-error'
};

export const approveStatusList: EApproveStatus[] = [
    EApproveStatus.AWAITING_HR,
    EApproveStatus.AWAITING_QLKT,
    EApproveStatus.APPROVED,
    EApproveStatus.DECLINED,
    EApproveStatus.AWAITING_QLTT
];
export const isAwaitingApproval = (status?: string) =>
    status === EApproveStatus.AWAITING_HR || status === EApproveStatus.AWAITING_QLTT || status === EApproveStatus.AWAITING_QLKT;

export const isApprovedOrAwaiting = (status?: string) => isAwaitingApproval(status) || status === EApproveStatus.APPROVED;

export const isDeclined = (status?: string) => status === EApproveStatus.DECLINED;
export const isApproved = (status?: string) => status === EApproveStatus.APPROVED;

export const isAwaitingQLTT = (status?: string) => status === EApproveStatus.AWAITING_QLTT;
export const isAwaitingQLKT = (status?: string) => status === EApproveStatus.AWAITING_QLKT;
export const isAwaitingHR = (status?: string) => status === EApproveStatus.AWAITING_HR;
// flexible report config
export const SUM_TYPE_OPTIONS: IOption[] = [
    { value: 'sizeUSD', label: 'Size USD' },
    { value: 'sizeVND', label: 'ize VND' }
];
export const SUM_TYPE: IOption = { value: 'sum', label: 'Sum' };

// equal condition
export const EQUAL_CONDITION: IOption[] = [
    { value: 'is', label: 'is' },
    { value: 'is not', label: 'is not' }
];

// text condition
export const TEXT_CONDITIONS: IOption[] = [
    { value: 'is', label: 'is' },
    { value: 'is not', label: 'is not' },
    { value: 'start with', label: 'start with' },
    { value: 'end with', label: 'end with' },
    { value: 'contains', label: 'contains' }
];

// Inequalities Condition
export const Inequalities_Condition: IOption[] = [
    { value: '>=', label: '>=' },
    { value: '<=', label: '<=' },
    { value: '>', label: '>' },
    { value: '<', label: '<' },
    { value: '=', label: '=' },
    { value: 'between', label: 'Between' }
];
export const CONDITION_STATUS: IOption[] = [
    { value: 'on-going', label: 'On-going' },
    { value: 'done', label: 'Done' },
    { value: 'pending', label: 'Pending' },
    { value: 'npl', label: 'NPL' }
];

export const SIGN_CAlCULATE: IOption[] = [
    { value: '+', label: '+' },
    { value: '-', label: '-' },
    { value: '*', label: '*' },
    { value: '/', label: '/' }
];

export const LANGUAGE_LIST: IOption[] = [
    { value: 'en', label: 'English (UK)' },
    { value: 'en-us', label: 'English (US)' },
    { value: 'zh', label: 'Mandarin (CN)' },
    { value: 'hi', label: 'Hindi (IN)' },
    { value: 'es', label: 'Spanish (ES)' },
    { value: 'es-mx', label: 'Spanish (MX)' },
    { value: 'fr', label: 'French (FR)' },
    { value: 'fr-ca', label: 'French (CA)' },
    { value: 'ar', label: 'Arabic (SA)' },
    { value: 'bn', label: 'Bengali (BD)' },
    { value: 'ru', label: 'Russian (RU)' },
    { value: 'pt', label: 'Portuguese (PT)' },
    { value: 'pt-br', label: 'Portuguese (BR)' },
    { value: 'id', label: 'Indonesian (ID)' },
    { value: 'ja', label: 'Japanese (JP)' },
    { value: 'de', label: 'German (DE)' },
    { value: 'ko', label: 'Korean (KR)' },
    { value: 'vi', label: 'Vietnamese (VN)' },
    { value: 'it', label: 'Italian (IT)' },
    { value: 'tr', label: 'Turkish (TR)' },
    { value: 'th', label: 'Thai (TH)' },
    { value: 'fa', label: 'Persian (IR)' },
    { value: 'sw', label: 'Swahili (KE)' },
    { value: 'nl', label: 'Dutch (NL)' },
    { value: 'pl', label: 'Polish (PL)' },
    { value: 'el', label: 'Greek (GR)' },
    { value: 'cs', label: 'Czech (CZ)' },
    { value: 'ro', label: 'Romanian (RO)' },
    { value: 'hu', label: 'Hungarian (HU)' },
    { value: 'sv', label: 'Swedish (SE)' },
    { value: 'da', label: 'Danish (DK)' },
    { value: 'no', label: 'Norwegian (NO)' },
    { value: 'fi', label: 'Finnish (FI)' },
    { value: 'he', label: 'Hebrew (IL)' },
    { value: 'ta', label: 'Tamil (IN)' },
    { value: 'te', label: 'Telugu (IN)' },
    { value: 'mr', label: 'Marathi (IN)' },
    { value: 'ml', label: 'Malayalam (IN)' },
    { value: 'pa', label: 'Punjabi (IN)' },
    { value: 'gu', label: 'Gujarati (IN)' },
    { value: 'kn', label: 'Kannada (IN)' },
    { value: 'am', label: 'Amharic (ET)' },
    { value: 'my', label: 'Burmese (MM)' },
    { value: 'lo', label: 'Lao (LA)' },
    { value: 'km', label: 'Khmer (KH)' },
    { value: 'zu', label: 'Zulu (ZA)' },
    { value: 'xh', label: 'Xhosa (ZA)' },
    { value: 'so', label: 'Somali (SO)' },
    { value: 'yi', label: 'Yiddish (US)' },
    { value: 'sk', label: 'Slovak (SK)' },
    { value: 'sr', label: 'Serbian (RS)' },
    { value: 'hr', label: 'Croatian (HR)' },
    { value: 'bs', label: 'Bosnian (BA)' },
    { value: 'sq', label: 'Albanian (AL)' },
    { value: 'ka', label: 'Georgian (GE)' },
    { value: 'hy', label: 'Armenian (AM)' },
    { value: 'az', label: 'Azerbaijani (AZ)' },
    { value: 'kk', label: 'Kazakh (KZ)' },
    { value: 'uz', label: 'Uzbek (UZ)' },
    { value: 'mn', label: 'Mongolian (MN)' },
    { value: 'bo', label: 'Tibetan (CN)' },
    { value: 'ms', label: 'Malay (MY)' },
    { value: 'tl', label: 'Tagalog (PH)' },
    { value: 'ht', label: 'Haitian Creole (HT)' },
    { value: 'mk', label: 'Macedonian (MK)' },
    { value: 'cy', label: 'Welsh (GB)' },
    { value: 'ga', label: 'Irish (IE)' },
    { value: 'gd', label: 'Scots Gaelic (GB)' },
    { value: 'is', label: 'Icelandic (IS)' },
    { value: 'lb', label: 'Luxembourgish (LU)' },
    { value: 'eu', label: 'Basque (ES)' },
    { value: 'co', label: 'Corsican (FR)' },
    { value: 'gl', label: 'Galician (ES)' },
    { value: 'mg', label: 'Malagasy (MG)' },
    { value: 'si', label: 'Sinhala (LK)' },
    { value: 'mi', label: 'Maori (NZ)' },
    { value: 'to', label: 'Tongan (TO)' },
    { value: 'sm', label: 'Samoan (WS)' },
    { value: 'fj', label: 'Fijian (FJ)' }
];
export const SEARCH_MODE: IOption[] = [
    { value: 'contains', label: 'Contains' },
    { value: 'prefix', label: 'Prefix' }
];
