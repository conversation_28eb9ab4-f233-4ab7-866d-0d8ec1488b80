<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON>g Test</title>
</head>
<body>
    <h1><PERSON><PERSON> Debug Test</h1>
    <div id="results"></div>
    
    <script>
        async function testCookies() {
            const results = document.getElementById('results');
            
            // Test 1: Check current cookies
            results.innerHTML += '<h2>1. Current Cookies:</h2>';
            results.innerHTML += '<pre>' + document.cookie + '</pre>';
            
            // Test 2: Test login
            results.innerHTML += '<h2>2. Testing Login...</h2>';
            try {
                const loginResponse = await fetch('http://localhost:3001/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        username: 'test_user', // Replace with your test credentials
                        password: 'password123'
                    })
                });
                
                results.innerHTML += '<p>Login Status: ' + loginResponse.status + '</p>';
                
                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    results.innerHTML += '<pre>' + JSON.stringify(loginData, null, 2) + '</pre>';
                    
                    // Test 3: Check cookies after login
                    results.innerHTML += '<h2>3. Cookies After Login:</h2>';
                    results.innerHTML += '<pre>' + document.cookie + '</pre>';
                    
                    // Test 4: Test /auth/me
                    results.innerHTML += '<h2>4. Testing /auth/me...</h2>';
                    const meResponse = await fetch('http://localhost:3001/api/v1/auth/me', {
                        method: 'GET',
                        credentials: 'include'
                    });
                    
                    results.innerHTML += '<p>Me Status: ' + meResponse.status + '</p>';
                    
                    if (meResponse.ok) {
                        const meData = await meResponse.json();
                        results.innerHTML += '<pre>' + JSON.stringify(meData, null, 2) + '</pre>';
                    } else {
                        results.innerHTML += '<p>Error: ' + await meResponse.text() + '</p>';
                    }
                } else {
                    results.innerHTML += '<p>Login failed: ' + await loginResponse.text() + '</p>';
                }
            } catch (error) {
                results.innerHTML += '<p>Error: ' + error.message + '</p>';
            }
        }
        
        // Run test on page load
        testCookies();
    </script>
</body>
</html>
