"use client";

import { useEffect } from "react";
import { useAuthStore } from "@/stores/user-store";

/**
 * HydrationProvider - Manages Zustand store hydration to prevent SSR/client mismatches
 *
 * This component ensures that:
 * 1. Zustand stores are properly rehydrated after Next.js hydration
 * 2. No hydration mismatches occur between server and client
 * 3. Authentication state is properly initialized
 *
 * Based on Zustand best practices for Next.js 14 App Router
 */
export function HydrationProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    console.log("[HydrationProvider] Starting store rehydration...");

    // Manually trigger rehydration for auth store
    useAuthStore.persist.rehydrate();

    console.log("[HydrationProvider] Store rehydration completed");
  }, []);

  return <>{children}</>;
}

/**
 * Alternative approach: Component-level hydration guard
 * Use this for components that need to wait for hydration
 */
export function withHydration<T extends object>(
  Component: React.ComponentType<T>
) {
  return function HydratedComponent(props: T) {
    const isInitialized = useAuthStore((state) => state.isInitialized);

    if (!isInitialized) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
