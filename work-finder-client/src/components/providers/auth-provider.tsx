"use client";

import { useEffect, ReactNode } from "react";
import { useAuthStore } from "@/stores/user-store";

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Modern AuthProvider with improved hydration handling
 *
 * This component:
 * 1. Manages authentication state initialization
 * 2. Prevents flash content during auth verification
 * 3. Works with the new store architecture and hydration system
 * 4. Provides proper loading states
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const checkAuth = useAuthStore((state) => state.checkAuth);
  const rehydrate = useAuthStore((state) => state.rehydrate);
  const isInitialized = useAuthStore((state) => state.isInitialized);
  const status = useAuthStore((state) => state.status);

  useEffect(() => {
    const initializeAuth = async () => {
      console.log("[AuthProvider] Starting authentication initialization...");

      try {
        // First, trigger store rehydration
        console.log("[AuthProvider] Triggering store rehydration...");
        rehydrate();

        // Then check authentication with server
        console.log("[AuthProvider] Checking authentication with server...");
        await checkAuth();

        console.log("[AuthProvider] Authentication initialization completed");
      } catch (error) {
        console.error(
          "[AuthProvider] Authentication initialization failed:",
          error
        );
        // Error is handled by the store
      }
    };

    // Only initialize if not already initialized
    if (!isInitialized) {
      initializeAuth();
    }
  }, [isInitialized, checkAuth, rehydrate]);

  // Show loading state while authentication is being verified
  if (!isInitialized || (status === "loading" && !isInitialized)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Initializing...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Hook to check if authentication has been initialized
 * @deprecated Use useIsInitialized from the store instead
 */
export function useAuthInitialized() {
  return useAuthStore((state) => state.isInitialized);
}
