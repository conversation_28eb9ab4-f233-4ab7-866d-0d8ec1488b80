import { create } from "zustand";
import { devtools, persist, createJSONStorage } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { toast } from "sonner";
import type { User, UserProfile } from "@/types/user";
import {
  loginUser,
  registerUser,
  getCurrentUser,
  logoutUser,
  type LoginRequest,
  type RegisterRequest,
} from "@/lib/api/auth";
import { authTranslations } from "@/lib/i18n/store-translations";

// Authentication states for better state management
type AuthStatus =
  | "idle"
  | "loading"
  | "authenticated"
  | "unauthenticated"
  | "error";

// Hydration states to handle SSR/client sync
type HydrationStatus = "pending" | "hydrating" | "hydrated";

// Error types for better error handling
interface AuthError {
  code: string;
  message: string;
  timestamp: number;
}

// Main authentication store interface
interface AuthStore {
  // Core State
  user: User | null;
  status: AuthStatus;
  hydrationStatus: HydrationStatus;
  error: AuthError | null;

  // Computed Properties
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitialized: boolean;
  userDisplayName: string;

  // Authentication Actions
  login: (data: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;

  // State Management Actions
  checkAuth: () => Promise<void>;
  setUser: (user: User) => void;
  updateProfile: (profile: Partial<UserProfile>) => void;
  clearAuth: () => void;
  clearError: () => void;

  // Hydration Management
  rehydrate: () => void;
  setHydrated: () => void;
}

// Helper function to compute user display name
const getUserDisplayName = (user: User | null): string => {
  if (!user) return "";
  return user.profile?.firstName || user.name || user.email || "User";
};

// Helper function to create auth error
const createAuthError = (code: string, message: string): AuthError => ({
  code,
  message,
  timestamp: Date.now(),
});

// Helper function to update computed properties
const updateComputedProperties = (state: any) => {
  state.isAuthenticated =
    state.status === "authenticated" && state.user !== null;
  state.isLoading = state.status === "loading";
  state.isInitialized = state.hydrationStatus === "hydrated";
  state.userDisplayName = getUserDisplayName(state.user);
};

// Create the authentication store with modern patterns
export const useAuthStore = create<AuthStore>()(
  devtools(
    persist(
      immer((set) => ({
        // Initial State
        user: null,
        status: "idle" as AuthStatus,
        hydrationStatus: "pending" as HydrationStatus,
        error: null,

        // Computed Properties
        isAuthenticated: false,
        isLoading: false,
        isInitialized: false,
        userDisplayName: "",

        // Authentication Actions
        login: async (data: LoginRequest) => {
          try {
            set((state) => {
              state.status = "loading";
              state.error = null;
              updateComputedProperties(state);
            });

            const user = await loginUser(data);

            set((state) => {
              state.user = user;
              state.status = "authenticated";
              updateComputedProperties(state);
            });

            toast.success(authTranslations.loginSuccess());
          } catch (error: unknown) {
            const authError = createAuthError(
              "LOGIN_FAILED",
              error instanceof Error ? error.message : "Login failed"
            );

            set((state) => {
              state.status = "error";
              state.error = authError;
              updateComputedProperties(state);
            });

            throw error;
          }
        },

        register: async (data: RegisterRequest) => {
          try {
            set((state) => {
              state.status = "loading";
              state.error = null;
              updateComputedProperties(state);
            });

            const user = await registerUser(data);

            set((state) => {
              state.user = user;
              state.status = "authenticated";
              updateComputedProperties(state);
            });

            toast.success(authTranslations.registerSuccess());
          } catch (error: unknown) {
            const authError = createAuthError(
              "REGISTER_FAILED",
              error instanceof Error ? error.message : "Registration failed"
            );

            set((state) => {
              state.status = "error";
              state.error = authError;
              updateComputedProperties(state);
            });

            throw error;
          }
        },

        logout: async () => {
          try {
            set((state) => {
              state.status = "loading";
              state.error = null;
              updateComputedProperties(state);
            });

            await logoutUser();

            set((state) => {
              state.user = null;
              state.status = "unauthenticated";
              updateComputedProperties(state);
            });

            toast.success(authTranslations.logoutSuccess());
          } catch (error: unknown) {
            // Even if logout fails on server, clear user locally for security
            set((state) => {
              state.user = null;
              state.status = "unauthenticated";
              state.error = createAuthError(
                "LOGOUT_FAILED",
                error instanceof Error ? error.message : "Logout failed"
              );
              updateComputedProperties(state);
            });
          }
        },

        // State Management Actions
        checkAuth: async () => {
          try {
            console.log("[AuthStore] Starting authentication check...");

            set((state) => {
              state.status = "loading";
              state.error = null;
            });

            const user = await getCurrentUser();

            console.log("[AuthStore] Authentication check successful:", {
              name: user.name,
              email: user.email,
              role: user.role,
            });

            set((state) => {
              state.user = user;
              state.status = "authenticated";
              state.hydrationStatus = "hydrated";
            });
          } catch (error) {
            console.error("[AuthStore] Authentication check failed:", error);

            set((state) => {
              state.user = null;
              state.status = "unauthenticated";
              state.hydrationStatus = "hydrated";
              state.error = createAuthError(
                "AUTH_CHECK_FAILED",
                error instanceof Error
                  ? error.message
                  : "Authentication check failed"
              );
            });
          }
        },

        setUser: (user: User) => {
          set((state) => {
            state.user = user;
            state.status = "authenticated";
          });
        },

        updateProfile: (profileUpdates: Partial<UserProfile>) => {
          set((state) => {
            if (state.user) {
              state.user.profile = {
                ...state.user.profile,
                ...profileUpdates,
              };
            }
          });
        },

        clearAuth: () => {
          set((state) => {
            state.user = null;
            state.status = "unauthenticated";
            state.error = null;
          });
        },

        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },

        // Hydration Management
        rehydrate: () => {
          set((state) => {
            state.hydrationStatus = "hydrating";
          });
        },

        setHydrated: () => {
          set((state) => {
            state.hydrationStatus = "hydrated";
          });
        },
      })),
      {
        name: "auth-store",
        storage: createJSONStorage(() => localStorage),
        skipHydration: true, // Prevent automatic hydration
        partialize: (state) => ({
          user: state.user,
          // Only persist user data, not loading/error states
        }),
      }
    ),
    {
      name: "auth-store",
    }
  )
);

// Optimized selector hooks for better performance
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () =>
  useAuthStore((state) => state.isAuthenticated);
export const useAuthStatus = () => useAuthStore((state) => state.status);
export const useAuthError = () => useAuthStore((state) => state.error);
export const useUserDisplayName = () =>
  useAuthStore((state) => state.userDisplayName);
export const useIsInitialized = () =>
  useAuthStore((state) => state.isInitialized);

// Legacy compatibility
export const useUserStore = useAuthStore;
